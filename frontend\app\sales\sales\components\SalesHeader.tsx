import React, { useEffect } from 'react';
import { FiShopping<PERSON><PERSON>, FiList, FiPlus } from 'react-icons/fi';
import { updateUrlParams } from '../salesHelpers';

// View modes
type ViewMode = 'pos' | 'list';

interface SalesHeaderProps {
  viewMode: ViewMode;
  onViewModeChange: (mode: ViewMode) => void;
  onNewSale?: () => void;
}

export const SalesHeader: React.FC<SalesHeaderProps> = ({ 
  viewMode, 
  onViewModeChange,
  onNewSale 
}) => {
  const handleViewToggle = (mode: ViewMode) => {
    onViewModeChange(mode);
    if (mode === 'pos') {
      updateUrlParams({ view: 'pos', edit: '' }); // Clear edit parameter when switching to POS
    } else {
      updateUrlParams({ view: '', edit: '' }); // Clear both view and edit params for list mode
    }
  };

  const handleNewSale = () => {
    if (onNewSale) {
      onNewSale();
    } else {
      // Default behavior: switch to POS mode
      handleViewToggle('pos');
    }
  };

  // F2 keyboard shortcut for new sale
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'F2') {
        event.preventDefault();
        handleNewSale();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [onNewSale]);

  return (
    <div className="flex justify-between items-center">
      {/* Title and Subtitle Section - Left side */}
      <div className="flex flex-col items-start flex-1">
        <h1 className="text-2xl font-bold text-gray-900 leading-tight">Sales</h1>
        <p className="text-sm text-gray-600 leading-tight">
          {viewMode === 'pos' ? 'Point of Sale - Process transactions' : 'Sales Management - Manage sales'}
        </p>
      </div>
      
      {/* View Toggle Buttons - Centered */}
      <div className="flex items-center bg-gray-100 rounded-lg p-1">
        <button
          onClick={() => handleViewToggle('list')}
          className={`flex items-center px-4 py-2 rounded-md text-sm font-medium transition-colors ${
            viewMode === 'list' 
              ? 'bg-blue-600 text-white shadow-sm' 
              : 'text-gray-600 hover:text-gray-900'
          }`}
        >
          <FiList className="w-4 h-4 mr-2" />
          List
        </button>
        <button
          onClick={() => handleViewToggle('pos')}
          className={`flex items-center px-4 py-2 rounded-md text-sm font-medium transition-colors ${
            viewMode === 'pos' 
              ? 'bg-blue-600 text-white shadow-sm' 
              : 'text-gray-600 hover:text-gray-900'
          }`}
        >
          <FiShoppingCart className="w-4 h-4 mr-2" />
          POS
        </button>
      </div>

      {/* New Sale Button - Right side */}
      <div className="flex-1 flex justify-end">
        <button
          onClick={handleNewSale}
          className="inline-flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-colors duration-200 font-medium text-sm"
          title="Start a new sale (F2)"
        >
          <FiPlus className="w-4 h-4" />
          <span>New Sale</span>
          <span className="text-xs bg-green-500 px-1.5 py-0.5 rounded text-green-100">F2</span>
        </button>
      </div>
    </div>
  );
}; 