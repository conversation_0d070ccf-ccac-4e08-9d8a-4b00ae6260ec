import { <PERSON><PERSON>ty, PrimaryColumn, Column, CreateDateColumn, UpdateDateColumn, Index } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Uuid7 } from '../utils/uuid7';

@Entity('warehouses')
export class Warehouse {
  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the warehouse (primary key)",
  })
  @PrimaryColumn('uuid')
  id: string;

  @ApiProperty({
    example: "Acme Inc.",
    description: "Warehouse name",
    required: false,
  })
  @Column({ nullable: true })
  name?: string;

  @ApiProperty({
    example: "Some description",
    description: "Warehouse description",
    required: false,
  })
  @Column({ nullable: true })
  description?: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the user who owns this warehouse",
  })
  @Column('uuid')
  @Index()
  userUuid: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the main storage location for this warehouse",
    required: false,
  })
  @Column('uuid', { nullable: true })
  @Index()
  mainStorageUuid?: string;

  @ApiProperty({ example: false, description: "Soft delete flag" })
  @Column({ default: false })
  isDeleted: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Helper method to generate UUID
  static generateId(): string {
    return new Uuid7().toString();
  }

  // Helper method to generate UUID
  static fromBinary(binary: any): string {
    if (!binary) return null;
    try {
      return new Uuid7(binary).toString();
    } catch {
      return null;
    }
  }
} 