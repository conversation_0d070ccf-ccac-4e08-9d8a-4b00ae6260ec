import {
  Injectable,
  NotFoundException,
  BadRequestException,
  Inject,
  forwardRef,
} from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository, Like, ILike, Between, MoreThanOrEqual, LessThanOrEqual, In } from "typeorm";
import { UsersService } from "../users/users.service";
import { Sale, SaleStatus, PaymentMethods } from "./sale.entity";
import { SaleItem } from "./sale-item.entity";
import { Customer } from "../customers/customer.entity";
import { Product } from "../products/product.entity";
import { 
  EMPTY_STRING_FILTER, 
  EMPTY_UUID_FILTER,
  MIN_NUMBER_FILTER,
  MAX_NUMBER_FILTER,
} from "./sales.constants";
import { Uuid7 } from "../utils/uuid7";
import {
  SaleResponseDto,
  toSaleResponseDto,
  SalesListResponseDto,
} from "./dto/sales-response.dto";
import { InventoryService } from "../inventory/inventory.service.typeorm";
import { StorageType } from "../inventory/storage_type.enum";
import { CreateStockAdjustmentDto } from "../inventory/dto/create-stock-adjustment.dto";
import { CustomerCreditService } from "../customers/customer-credit.service.typeorm";

type ProductWithUuid = { uuid: string; name: string };

@Injectable()
export class SalesService {
  constructor(
    @InjectRepository(Sale)
    private saleRepository: Repository<Sale>,
    @InjectRepository(SaleItem)
    private saleItemRepository: Repository<SaleItem>,
    @InjectRepository(Customer)
    private customerRepository: Repository<Customer>,
    @InjectRepository(Product)
    private productRepository: Repository<Product>,
    private usersService: UsersService,
    private inventoryService: InventoryService,
    private customerCreditService: CustomerCreditService,
  ) {}

  /**
   * Utility function to round numbers to 2 decimal places
   * This ensures consistent precision for all monetary calculations
   */
  private roundToTwoDecimals(value: number): number {
    return Math.round(value * 100) / 100;
  }

  /**
   * Create a new Sale with required validation for customer and items.
   */
  async create(
    userUuid: string,
    warehouseUuid: string,
    customerUuid: string | undefined,
    items: any[] | undefined,
    useTax: boolean = false,
    taxRate: number = 0.1,
    status: SaleStatus = SaleStatus.UNPAID,
    paymentMethod?: PaymentMethods,
    amountPaid?: number,
  ) {
    console.log("[Sales Service] Creating sale with params:", {
      userUuid,
      warehouseUuid,
      customerUuid,
      itemsCount: items?.length || 0,
      useTax,
      taxRate,
      status,
    });

    const now = new Date();

    // Validate required fields
    if (!userUuid || !warehouseUuid) {
      throw new BadRequestException(
        "userUuid and warehouseUuid are required to create a sale",
      );
    }

    if (!items || items.length === 0) {
      throw new BadRequestException("At least one item is required to create a sale");
    }

    // Check user exists
    const user = await this.usersService.findOne(userUuid);
    if (!user) throw new NotFoundException("User not found");

    // Check customer exists if provided
    let customer: Customer | null = null;
    if (customerUuid) {
      customer = await this.customerRepository.findOne({
        where: { id: customerUuid, isDeleted: false }
      });
      if (!customer) throw new NotFoundException("Customer not found");
    }

    // Validate items and get product information
    const validatedItems = await this.validateAndTransformItems(items);

    // Calculate totals
    const subtotal = this.roundToTwoDecimals(
      validatedItems.reduce((sum, item) => sum + item.lineTotal, 0)
    );
    const taxAmount = useTax ? this.calculateTaxAmount(subtotal, taxRate) : 0;
    const totalAmount = this.roundToTwoDecimals(subtotal + taxAmount);
    const finalAmountPaid = amountPaid || 0;
    
    // Prevent overpayment
    if (finalAmountPaid > totalAmount) {
      throw new BadRequestException(
        `Payment amount (${finalAmountPaid}) cannot exceed total sale amount (${totalAmount}). Overpayment is not allowed.`
      );
    }
    
    const balanceDue = this.roundToTwoDecimals(totalAmount - finalAmountPaid);

    // Generate invoice number
    const timestamp = now.getTime();
    const invoiceNumber = `INV-${timestamp}`;

    // Create sale entity
    const sale = this.saleRepository.create({
      id: Sale.generateId(),
      invoiceNumber,
      customerUuid: customerUuid || null,
      customerName: customer?.name || null,
      customerFiscalId: customer?.fiscalId || null,
      customerRc: customer?.rc || null,
      customerArticleNumber: customer?.articleNumber || null,
      subtotal,
      useTax,
      taxRate,
      taxAmount,
      totalAmount,
      amountPaid: finalAmountPaid,
      balanceDue,
      paymentMethod: paymentMethod || PaymentMethods.CASH,
      paymentDate: finalAmountPaid > 0 ? [now] : [],
      invoiceDate: now,
      dueDate: new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
      status,
      createdBy: userUuid,
      updatedBy: userUuid,
      isDeleted: false,
    });

    // Save sale
    const savedSale = await this.saleRepository.save(sale);

    // Create sale items
    const saleItems = validatedItems.map(item => 
      this.saleItemRepository.create({
        id: SaleItem.generateId(),
        saleUuid: savedSale.id,
        productUuid: item.productUuid,
        name: item.name,
        quantity: item.quantity,
        unitPrice: item.unitPrice,
        lineTotal: item.lineTotal,
        taxAmount: item.taxAmount || 0,
      })
    );

    await this.saleItemRepository.save(saleItems);

    // Handle stock adjustments if not cancelled
    if (status !== SaleStatus.CANCELLED) {
      const storageUuid = await this.findDefaultWarehouseStorage(warehouseUuid);
      await this.createStockAdjustmentsForSale(
        validatedItems,
        storageUuid,
        userUuid,
        warehouseUuid,
        `Sale transaction - Invoice ${invoiceNumber}`
      );
    }

    // Return the created sale
    return this.findOne(savedSale.id);
  }

  /**
   * Calculate tax amount based on subtotal and tax rate
   */
  private calculateTaxAmount(subtotal: number, taxRate: number): number {
    return this.roundToTwoDecimals(subtotal * taxRate);
  }

  /**
   * Recalculate sale totals based on items
   */
  private recalculateSaleTotals(sale: Sale): void {
    // This would need to be implemented based on the items
    // For now, we'll keep the existing totals
  }

  /**
   * Find the default storage for a warehouse
   */
  private async findDefaultWarehouseStorage(
    warehouseUuid: string,
  ): Promise<string> {
    // This would need to be implemented based on your storage structure
    // For now, return a placeholder
    return warehouseUuid; // This should be the actual storage UUID
  }

  /**
   * Validate stock availability for sale items
   */
  private async validateStockAvailability(
    items: any[],
    storageUuid: string,
  ): Promise<void> {
    // This would need to be implemented based on your inventory structure
    // For now, we'll skip validation
  }

  /**
   * Create stock adjustments for sale
   */
  private async createStockAdjustmentsForSale(
    items: any[],
    storageUuid: string,
    userUuid: string,
    warehouseUuid: string,
    reason: string = "Sale transaction",
  ): Promise<void> {
    // This would need to be implemented based on your inventory structure
    // For now, we'll skip stock adjustments
  }

  /**
   * Find all sales with filtering and pagination
   */
  async findAll({
    customerUuid = EMPTY_UUID_FILTER,
    status = EMPTY_STRING_FILTER,
    createdFrom,
    createdTo,
    page = 1,
    limit = 10,
    warehouseUuid,
    invoiceNumber,
    paymentMethod,
    startDate,
    endDate,
    minAmount = MIN_NUMBER_FILTER,
    maxAmount = MAX_NUMBER_FILTER,
  }: {
    customerUuid?: string;
    status?: string;
    createdFrom?: Date;
    createdTo?: Date;
    page?: number;
    limit?: number;
    warehouseUuid?: string;
    invoiceNumber?: string;
    paymentMethod?: string;
    startDate?: Date;
    endDate?: Date;
    minAmount?: number;
    maxAmount?: number;
  } = {}): Promise<SalesListResponseDto> {
    const queryBuilder = this.saleRepository
      .createQueryBuilder('sale')
      .leftJoinAndSelect('sale.saleItems', 'saleItems')
      .where('sale.isDeleted = :isDeleted', { isDeleted: false });

    // Apply filters
    if (customerUuid !== EMPTY_UUID_FILTER) {
      queryBuilder.andWhere('sale.customerUuid = :customerUuid', { customerUuid });
    }

    if (status !== EMPTY_STRING_FILTER) {
      queryBuilder.andWhere('sale.status = :status', { status });
    }

    if (createdFrom) {
      queryBuilder.andWhere('sale.createdAt >= :createdFrom', { createdFrom });
    }

    if (createdTo) {
      queryBuilder.andWhere('sale.createdAt <= :createdTo', { createdTo });
    }

    if (invoiceNumber) {
      queryBuilder.andWhere('sale.invoiceNumber ILIKE :invoiceNumber', { 
        invoiceNumber: `%${invoiceNumber}%` 
      });
    }

    if (paymentMethod) {
      queryBuilder.andWhere('sale.paymentMethod = :paymentMethod', { paymentMethod });
    }

    if (startDate) {
      queryBuilder.andWhere('sale.invoiceDate >= :startDate', { startDate });
    }

    if (endDate) {
      queryBuilder.andWhere('sale.invoiceDate <= :endDate', { endDate });
    }

    if (minAmount !== MIN_NUMBER_FILTER) {
      queryBuilder.andWhere('sale.totalAmount >= :minAmount', { minAmount });
    }

    if (maxAmount !== MAX_NUMBER_FILTER) {
      queryBuilder.andWhere('sale.totalAmount <= :maxAmount', { maxAmount });
    }

    // Apply pagination
    const skip = (page - 1) * limit;
    queryBuilder.skip(skip).take(limit);

    // Order by creation date (newest first)
    queryBuilder.orderBy('sale.createdAt', 'DESC');

    // Get the sales without items first to get the total count
    const [salesWithoutItems, total] = await queryBuilder.getManyAndCount();
    
    // Then load the sales with their items using the repository
    const sales = await this.saleRepository.find({
      where: { id: In(salesWithoutItems.map(s => s.id)) },
      relations: ['saleItems'],
      order: { createdAt: 'DESC' },
    });



    // Transform to response DTOs
    const saleDtos = await this.enrichSalesData(sales);

    return new SalesListResponseDto(
      saleDtos,
      total,
      page,
      limit,
      Math.ceil(total / limit)
    );
  }

  /**
   * Find sales by warehouse
   */
  async findByWarehouse(warehouseUuid: string): Promise<SaleResponseDto[]> {
    const sales = await this.saleRepository.find({
      where: { isDeleted: false },
      relations: ['saleItems'],
      order: { createdAt: 'DESC' },
    });

    return this.enrichSalesData(sales);
  }

  /**
   * Find one sale by UUID
   */
  async findOne(uuid: string): Promise<SaleResponseDto> {
    const sale = await this.saleRepository.findOne({
      where: { id: uuid, isDeleted: false },
      relations: ['saleItems'],
    });

    if (!sale) {
      throw new NotFoundException("Sale not found");
    }

    const enrichedSales = await this.enrichSalesData([sale]);
    return enrichedSales[0];
  }

  /**
   * Enrich sales data with additional information
   */
  private async enrichSalesData(
    sales: Sale[],
  ): Promise<SaleResponseDto[]> {
    // Get all unique UUIDs for batch queries
    const customerUuids = [...new Set(sales.map(s => s.customerUuid).filter(Boolean))];
    const createdByUuids = [...new Set(sales.map(s => s.createdBy))];
    const updatedByUuids = [...new Set(sales.map(s => s.updatedBy))];
    const orderUuids = [...new Set(sales.map(s => s.orderUuid).filter(Boolean))];
    
    // Get all product UUIDs from sale items
    const productUuids = [...new Set(
      sales.flatMap(sale => 
        sale.saleItems?.map(item => item.productUuid).filter(Boolean) || []
      )
    )];

    // Batch fetch related data
    const [customers, users, orders, products] = await Promise.all([
      customerUuids.length > 0 
        ? this.customerRepository.find({ where: { id: In(customerUuids) } })
        : [],
      createdByUuids.length > 0 || updatedByUuids.length > 0
        ? Promise.all([
            ...createdByUuids.map(uuid => this.usersService.findOne(uuid)),
            ...updatedByUuids.map(uuid => this.usersService.findOne(uuid)),
          ])
        : [],
      [], // Orders would need to be fetched if you have an orders service
      productUuids.length > 0
        ? this.productRepository.find({ where: { id: In(productUuids) } })
        : [],
    ]);

    // Create lookup maps with proper typing
    const customerMap = new Map<string, any>();
    customers.forEach(c => customerMap.set(c.id, c));
    
    const userMap = new Map<string, any>();
    users.forEach((user: any) => {
      if (user) userMap.set(user.id, user);
    });
    
    const productMap = new Map<string, any>();
    products.forEach(p => productMap.set(p.id, p));

    // Transform sales to DTOs
    return sales.map(sale => {
      const customer = sale.customerUuid ? customerMap.get(sale.customerUuid) : null;
      const createdByUser = userMap.get(sale.createdBy);
      const updatedByUser = userMap.get(sale.updatedBy);

      // Create a map of product UUIDs to product names
      const productNameMap = new Map<string, string>();
      products.forEach(p => productNameMap.set(p.id, p.name));

      // Create the base sale response DTO
      const saleResponse = toSaleResponseDto(
        sale,
        customer?.name || "Unknown Customer",
        createdByUser?.name || "Unknown User",
        updatedByUser?.name || "Unknown User",
        productNameMap
      );
      
      // Add enriched data as additional properties
      return {
        ...saleResponse,
        customer: customer ? {
          uuid: customer.id,
          name: customer.name,
          fiscalId: customer.fiscalId,
          rc: customer.rc,
          articleNumber: customer.articleNumber,
        } : null,
        createdByUser: createdByUser ? {
          uuid: createdByUser.id,
          name: createdByUser.name,
          email: createdByUser.email,
        } : null,
        updatedByUser: updatedByUser ? {
          uuid: updatedByUser.id,
          name: updatedByUser.name,
          email: updatedByUser.email,
        } : null,
      };
    });
  }

  /**
   * Update a sale
   */
  async update(uuid: string, data: any, userUuid: string): Promise<SaleResponseDto> {
    console.log('[Sales Service] Starting sale update for UUID:', uuid);
    console.log('[Sales Service] Update data received:', JSON.stringify(data, null, 2));
    
    const sale = await this.saleRepository.findOne({
      where: { id: uuid, isDeleted: false },
    });

    if (!sale) {
      console.error('[Sales Service] Sale not found for UUID:', uuid);
      throw new NotFoundException("Sale not found");
    }

    console.log('[Sales Service] Found existing sale:', {
      id: sale.id,
      status: sale.status,
      totalAmount: sale.totalAmount,
      amountPaid: sale.amountPaid,
      balanceDue: sale.balanceDue,
      customerUuid: sale.customerUuid
    });

    // Check if payment-related fields are being updated
    const isPaymentUpdate = data.amountPaid !== undefined || data.paymentMethod !== undefined;
    
    // Check if tax-related fields are being updated
    const isTaxUpdate = data.useTax !== undefined || data.taxRate !== undefined;
    
    // Check if items are being updated
    const isItemsUpdate = data.items !== undefined && Array.isArray(data.items);
    
    // Extract items early so we can use them consistently
    const updateItems = data.items;
    
    console.log('[Sales Service] Update analysis:', {
      saleId: sale.id,
      isPaymentUpdate,
      isTaxUpdate,
      isItemsUpdate,
      itemsCount: data.items?.length || 0,
      updateData: data,
      currentTotal: sale.totalAmount,
      currentAmountPaid: sale.amountPaid
    });
    
    console.log('[Sales Service] Items data received:', {
      hasItems: updateItems !== undefined,
      isArray: Array.isArray(updateItems),
      itemsLength: updateItems?.length || 0,
      itemsSample: updateItems?.slice(0, 2) || []
    });
    
    // Update sale fields (excluding items which are handled separately)
    const { items: _, ...updateData } = data;
    Object.assign(sale, updateData);
    sale.updatedAt = new Date();

    // If items are being updated, replace all items
    if (isItemsUpdate) {
      try {
        console.log('[Sales Service] Updating sale items');
        
        // Delete existing items
        await this.saleItemRepository.delete({ saleUuid: sale.id });
        
        // Validate and transform new items
        const validatedItems = await this.validateAndTransformItems(updateItems);
        
        // Create new sale items
        const saleItems = validatedItems.map(item => 
          this.saleItemRepository.create({
            id: SaleItem.generateId(),
            saleUuid: sale.id,
            productUuid: item.productUuid,
            name: item.name,
            quantity: item.quantity,
            unitPrice: item.unitPrice,
            lineTotal: item.lineTotal,
            taxAmount: item.taxAmount || 0,
          })
        );
        
        await this.saleItemRepository.save(saleItems);
        
        console.log('[Sales Service] Updated sale items:', saleItems.length);
      } catch (error) {
        console.error('[Sales Service] Error updating items:', error);
        throw new BadRequestException(`Failed to update sale items: ${error.message}`);
      }
    }

    // IMPORTANT: Recalculate totals BEFORE payment validation
    // If tax is being updated or items are being updated, recalculate totals
    if (isTaxUpdate || isItemsUpdate) {
      try {
        console.log('[Sales Service] Recalculating totals due to tax or items update');
        
        let saleItems: any[];
        
        console.log('[Sales Service] Decision logic:', {
          isItemsUpdate,
          hasItems: updateItems !== undefined,
          itemsLength: updateItems?.length || 0,
          condition1: isItemsUpdate && updateItems && updateItems.length > 0,
          condition2: isItemsUpdate,
          willUseNewItems: isItemsUpdate && updateItems && updateItems.length > 0,
          willUseDatabaseItems: !(isItemsUpdate && updateItems && updateItems.length > 0)
        });
        
        if (isItemsUpdate && updateItems && updateItems.length > 0) {
          // Use the NEW items that were sent in the update request
          console.log('[Sales Service] Using NEW items from update request:', updateItems.length);
          saleItems = updateItems;
        } else if (isItemsUpdate) {
          // Items update was requested but no items provided
          console.error('[Sales Service] Items update requested but no items provided in request');
          throw new BadRequestException('Items update requested but no items provided. Please include the updated items in the request.');
        } else {
          // Fallback to current sale items from database (for tax-only updates)
          console.log('[Sales Service] Using current sale items from database');
          saleItems = await this.saleItemRepository.find({
            where: { saleUuid: sale.id },
          });
        }
        
        console.log('[Sales Service] Found sale items:', saleItems.length);
        
        if (saleItems.length === 0) {
          throw new BadRequestException('Cannot update sale: No sale items found. The sale appears to be empty.');
        }
        
        console.log('[Sales Service] Sale items details:', saleItems.map(item => ({
          id: item.id || 'new-item',
          name: item.name,
          quantity: item.quantity,
          unitPrice: item.unitPrice,
          lineTotal: item.lineTotal || item.totalPrice, // Handle both field names
          lineTotalType: typeof (item.lineTotal || item.totalPrice)
        })));
        
        // Recalculate subtotal with validation
        let subtotal = 0;
        try {
          subtotal = this.roundToTwoDecimals(
            saleItems.reduce((sum, item) => {
              // Handle both lineTotal (from database) and totalPrice (from frontend)
              const itemTotal = item.lineTotal || item.totalPrice;
              console.log(`[Sales Service] Processing item: ${item.name}, lineTotal: ${item.lineTotal}, totalPrice: ${item.totalPrice}, using: ${itemTotal}, type: ${typeof itemTotal}`);
              
              if (itemTotal === null || itemTotal === undefined) {
                console.error('[Sales Service] Item has null/undefined lineTotal/totalPrice:', item);
                throw new BadRequestException(`Item "${item.name}" has no line total. Please check the sale items data.`);
              }
              
              let lineTotal: number;
              if (typeof itemTotal === 'string') {
                lineTotal = parseFloat(itemTotal);
                if (isNaN(lineTotal)) {
                  console.error('[Sales Service] Item has invalid string lineTotal/totalPrice:', item);
                  throw new BadRequestException(`Item "${item.name}" has invalid line total string: "${itemTotal}". Cannot parse to number.`);
                }
              } else if (typeof itemTotal === 'number') {
                lineTotal = itemTotal;
              } else {
                console.error('[Sales Service] Item has invalid lineTotal/totalPrice type:', item);
                throw new BadRequestException(`Item "${item.name}" has invalid line total type: ${typeof itemTotal}. Expected a number or string.`);
              }
              
              if (isNaN(lineTotal)) {
                console.error('[Sales Service] Item has invalid lineTotal/totalPrice:', item);
                throw new BadRequestException(`Item "${item.name}" has invalid line total: "${itemTotal}". Expected a valid number.`);
              }
              
              if (lineTotal < 0) {
                console.error('[Sales Service] Item has negative lineTotal/totalPrice:', item);
                throw new BadRequestException(`Item "${item.name}" has negative line total: ${lineTotal}. Line totals cannot be negative.`);
              }
              
              console.log(`[Sales Service] Adding lineTotal ${lineTotal} to sum ${sum}`);
              return sum + lineTotal;
            }, 0)
          );
        } catch (error) {
          if (error instanceof BadRequestException) {
            throw error; // Re-throw our custom error
          }
          console.error('[Sales Service] Unexpected error during subtotal calculation:', error);
          throw new BadRequestException(`Failed to calculate sale subtotal: ${error.message}`);
        }
        
        // Validate subtotal
        if (isNaN(subtotal)) {
          console.error('[Sales Service] NaN subtotal calculated from items:', saleItems);
          throw new BadRequestException('Invalid sale items - cannot calculate subtotal');
        }
        
        // Recalculate tax amount
        let taxAmount = 0;
        if (sale.useTax) {
          if (sale.taxRate === null || sale.taxRate === undefined || isNaN(sale.taxRate)) {
            console.error('[Sales Service] Invalid tax rate:', sale.taxRate);
            throw new BadRequestException(`Invalid tax rate: ${sale.taxRate}. Tax rate must be a valid number.`);
          }
          if (sale.taxRate < 0 || sale.taxRate > 1) {
            console.error('[Sales Service] Tax rate out of range:', sale.taxRate);
            throw new BadRequestException(`Tax rate ${sale.taxRate} is out of range. Must be between 0 and 1.`);
          }
          taxAmount = this.calculateTaxAmount(subtotal, sale.taxRate);
        }
        
        // Recalculate total amount
        const totalAmount = this.roundToTwoDecimals(subtotal + taxAmount);
        
        // Validate total amount
        if (isNaN(totalAmount)) {
          console.error('[Sales Service] NaN totalAmount calculated:', { subtotal, taxAmount });
          throw new BadRequestException('Invalid calculation - cannot determine total amount');
        }
        
        console.log('[Sales Service] Recalculated values:', {
          subtotal,
          taxAmount,
          totalAmount,
          useTax: sale.useTax,
          taxRate: sale.taxRate,
          amountPaid: sale.amountPaid
        });
        
        // Update the sale with recalculated values
        sale.subtotal = subtotal;
        sale.taxAmount = taxAmount;
        sale.totalAmount = totalAmount;
        
        console.log('[Sales Service] Updated sale with new totals');
        console.log('[Sales Service] Final sale totals after recalculation:', {
          subtotal: sale.subtotal,
          taxAmount: sale.taxAmount,
          totalAmount: sale.totalAmount,
          useTax: sale.useTax,
          taxRate: sale.taxRate,
          itemsSource: isItemsUpdate && updateItems && updateItems.length > 0 ? 'new-items-from-request' : 'database-items'
        });
      } catch (error) {
        console.error('[Sales Service] Error recalculating totals:', error);
        throw new BadRequestException(`Failed to recalculate sale totals: ${error.message}`);
      }
    }

    // NOW validate payment amount AFTER totals are recalculated
    // This ensures we validate against the correct total amount after any item/tax changes
    if (isPaymentUpdate) {
      try {
        console.log('[Sales Service] Processing payment update');
        console.log('[Sales Service] Payment update data:', {
          amountPaid: data.amountPaid,
          paymentMethod: data.paymentMethod,
          currentTotalAmount: sale.totalAmount,
          currentAmountPaid: sale.amountPaid
        });
        
        // Validate amount paid
        if (data.amountPaid !== undefined) {
          console.log('[Sales Service] Validating amount paid:', {
            amountPaid: data.amountPaid,
            totalAmount: sale.totalAmount,
            isOverpayment: data.amountPaid > sale.totalAmount,
            saleId: sale.id,
            saleStatus: sale.status,
            subtotal: sale.subtotal,
            taxAmount: sale.taxAmount,
            useTax: sale.useTax,
            taxRate: sale.taxRate
          });
          
          if (data.amountPaid < 0) {
            console.error('[Sales Service] Negative amount paid detected:', data.amountPaid);
            throw new BadRequestException("Amount paid cannot be negative");
          }
          
          // Prevent overpayment - amount paid cannot exceed total amount
          if (data.amountPaid > sale.totalAmount) {
            console.error('[Sales Service] Overpayment detected:', {
              amountPaid: data.amountPaid,
              totalAmount: sale.totalAmount,
              difference: data.amountPaid - sale.totalAmount,
              saleId: sale.id,
              saleStatus: sale.status
            });
            throw new BadRequestException(
              `Payment amount (${data.amountPaid}) cannot exceed total sale amount (${sale.totalAmount}). Overpayment is not allowed.`
            );
          }
          
          console.log('[Sales Service] Amount paid validation passed');
        }
        
        const balanceDue = this.roundToTwoDecimals(sale.totalAmount - sale.amountPaid);
        console.log('[Sales Service] Calculated balance due:', {
          totalAmount: sale.totalAmount,
          amountPaid: sale.amountPaid,
          balanceDue: balanceDue
        });
        
        if (isNaN(balanceDue)) {
          console.error('[Sales Service] NaN balanceDue in payment update:', { totalAmount: sale.totalAmount, amountPaid: sale.amountPaid });
          throw new BadRequestException('Invalid calculation - cannot determine balance due');
        }
        sale.balanceDue = balanceDue;
        
        // Update payment date if amount paid is being set
        if (data.amountPaid !== undefined && data.amountPaid > 0) {
          sale.paymentDate = sale.paymentDate || [];
          sale.paymentDate.push(new Date());
          console.log('[Sales Service] Updated payment date');
        }

        // Update status based on payment
        const oldStatus = sale.status;
        if (sale.balanceDue <= 0) {
          sale.status = SaleStatus.PAID;
        } else if (sale.amountPaid > 0) {
          sale.status = SaleStatus.PARTIALLY_PAID;
        } else {
          sale.status = SaleStatus.UNPAID;
        }
        
        console.log('[Sales Service] Status updated:', {
          oldStatus: oldStatus,
          newStatus: sale.status,
          balanceDue: sale.balanceDue,
          amountPaid: sale.amountPaid
        });
        
      } catch (error) {
        console.error('[Sales Service] Error processing payment update:', error);
        throw error; // Re-throw the error as it's already a BadRequestException
      }
    }

    try {
      console.log('[Sales Service] About to save updated sale with data:', {
        id: sale.id,
        status: sale.status,
        totalAmount: sale.totalAmount,
        amountPaid: sale.amountPaid,
        balanceDue: sale.balanceDue,
        subtotal: sale.subtotal,
        taxAmount: sale.taxAmount,
        useTax: sale.useTax,
        taxRate: sale.taxRate,
        paymentMethod: sale.paymentMethod
      });
      
      const updatedSale = await this.saleRepository.save(sale);
      console.log('[Sales Service] Successfully saved updated sale with ID:', updatedSale.id);
      
      const result = await this.findOne(updatedSale.id);
      console.log('[Sales Service] Returning updated sale data');
      return result;
    } catch (error) {
      console.error('[Sales Service] Error saving updated sale:', error);
      console.error('[Sales Service] Error details:', {
        message: error.message,
        code: error.code,
        constraint: error.constraint,
        detail: error.detail
      });
      
      // Check if it's a NaN error
      if (error.message && error.message.includes('DECIMAL does not support NaN')) {
        console.error('[Sales Service] NaN detected in sale data:', {
          subtotal: sale.subtotal,
          taxAmount: sale.taxAmount,
          totalAmount: sale.totalAmount,
          balanceDue: sale.balanceDue,
          amountPaid: sale.amountPaid
        });
        throw new BadRequestException(
          'Sale update failed: Invalid numeric values detected. This usually means the sale items have corrupted data. ' +
          'Please check the sale items and try again, or contact support if the problem persists.'
        );
      }
      
      throw new BadRequestException(`Failed to save sale update: ${error.message}`);
    }
  }

  /**
   * Update sale status
   */
  async updateSaleStatus(
    uuid: string,
    newStatus: SaleStatus,
    userUuid: string,
  ): Promise<SaleResponseDto> {
    const sale = await this.saleRepository.findOne({
      where: { id: uuid, isDeleted: false },
    });

    if (!sale) {
      throw new NotFoundException("Sale not found");
    }

    sale.status = newStatus;
    sale.updatedAt = new Date();
    sale.updatedBy = userUuid;

    const updatedSale = await this.saleRepository.save(sale);
    return this.findOne(updatedSale.id);
  }

  /**
   * Remove a sale (soft delete)
   */
  async remove(uuid: string): Promise<{ message: string }> {
    const sale = await this.saleRepository.findOne({
      where: { id: uuid, isDeleted: false },
    });

    if (!sale) {
      throw new NotFoundException("Sale not found");
    }

    sale.isDeleted = true;
    sale.updatedAt = new Date();
    await this.saleRepository.save(sale);

    return { message: "Sale deleted successfully" };
  }

  /**
   * Add products to a sale
   */
  async addProducts(uuid: string, items: any[]): Promise<SaleResponseDto> {
    const sale = await this.saleRepository.findOne({
      where: { id: uuid, isDeleted: false },
    });

    if (!sale) {
      throw new NotFoundException("Sale not found");
    }

    // Validate and transform items
    const validatedItems = await this.validateAndTransformItems(items);

    // Create new sale items
    const saleItems = validatedItems.map(item => 
      this.saleItemRepository.create({
        id: SaleItem.generateId(),
        saleUuid: sale.id,
        productUuid: item.productUuid,
        name: item.name,
        quantity: item.quantity,
        unitPrice: item.unitPrice,
        lineTotal: item.lineTotal,
        taxAmount: item.taxAmount || 0,
      })
    );

    await this.saleItemRepository.save(saleItems);

    // Recalculate totals
    const allItems = await this.saleItemRepository.find({
      where: { saleUuid: sale.id },
    });

    const subtotal = this.roundToTwoDecimals(
      allItems.reduce((sum, item) => sum + item.lineTotal, 0)
    );
    const taxAmount = sale.useTax ? this.calculateTaxAmount(subtotal, sale.taxRate) : 0;
    const totalAmount = this.roundToTwoDecimals(subtotal + taxAmount);
    const balanceDue = this.roundToTwoDecimals(totalAmount - sale.amountPaid);

    sale.subtotal = subtotal;
    sale.taxAmount = taxAmount;
    sale.totalAmount = totalAmount;
    sale.balanceDue = balanceDue;
    sale.updatedAt = new Date();

    await this.saleRepository.save(sale);

    return this.findOne(sale.id);
  }

  /**
   * Validate and transform items
   */
  private async validateAndTransformItems(items: any[]): Promise<any[]> {
    const productUuids = items.map(item => item.productUuid);
    const products = await this.productRepository.find({
      where: { id: In(productUuids), isDeleted: false },
    });

    const productMap = new Map(products.map(p => [p.id, p]));

    return items.map(item => {
      const product = productMap.get(item.productUuid);
      if (!product) {
        throw new BadRequestException(`Product with UUID ${item.productUuid} not found`);
      }

      const quantity = parseFloat(item.quantity);
      const unitPrice = parseFloat(item.unitPrice);
      const calculatedLineTotal = this.roundToTwoDecimals(quantity * unitPrice);
      
      // Check if frontend provided totalPrice and if it matches our calculation
      let lineTotal = calculatedLineTotal;
      if (item.totalPrice !== undefined && item.totalPrice !== null) {
        const frontendTotalPrice = parseFloat(item.totalPrice);
        const difference = Math.abs(frontendTotalPrice - calculatedLineTotal);
        
        console.log(`[Sales Service] Item ${product.name}: frontend totalPrice=${frontendTotalPrice}, calculated=${calculatedLineTotal}, difference=${difference}`);
        
        // If the difference is very small (rounding error), use frontend value
        if (difference < 0.01) {
          lineTotal = frontendTotalPrice;
          console.log(`[Sales Service] Using frontend totalPrice for ${product.name}: ${lineTotal}`);
        } else {
          console.warn(`[Sales Service] Frontend totalPrice (${frontendTotalPrice}) differs significantly from calculated (${calculatedLineTotal}) for ${product.name}. Using calculated value.`);
        }
      }

      return {
        productUuid: item.productUuid,
        name: product.name,
        quantity,
        unitPrice,
        lineTotal,
        taxAmount: item.taxAmount || 0,
      };
    });
  }

  /**
   * Set payment for a sale
   */
  async setPayment(uuid: string, paymentMethod: string, amountPaid: number): Promise<SaleResponseDto> {
    const sale = await this.saleRepository.findOne({
      where: { id: uuid, isDeleted: false },
    });

    if (!sale) {
      throw new NotFoundException("Sale not found");
    }

    sale.paymentMethod = paymentMethod as PaymentMethods;
    sale.amountPaid = this.roundToTwoDecimals(amountPaid);
    sale.balanceDue = this.roundToTwoDecimals(sale.totalAmount - sale.amountPaid);
    sale.paymentDate = sale.paymentDate || [];
    sale.paymentDate.push(new Date());

    // Update status based on payment
    if (sale.balanceDue <= 0) {
      sale.status = SaleStatus.PAID;
    } else if (sale.amountPaid > 0) {
      sale.status = SaleStatus.PARTIALLY_PAID;
    }

    sale.updatedAt = new Date();
    await this.saleRepository.save(sale);

    return this.findOne(sale.id);
  }

  /**
   * Cancel a sale
   */
  async cancelSale(uuid: string, userUuid: string): Promise<SaleResponseDto> {
    const sale = await this.saleRepository.findOne({
      where: { id: uuid, isDeleted: false },
    });

    if (!sale) {
      throw new NotFoundException("Sale not found");
    }

    if (sale.status === SaleStatus.CANCELLED) {
      throw new BadRequestException("Sale is already cancelled");
    }

    sale.status = SaleStatus.CANCELLED;
    sale.updatedAt = new Date();
    sale.updatedBy = userUuid;

    await this.saleRepository.save(sale);

    return this.findOne(sale.id);
  }

  /**
   * Fix data inconsistencies by recalculating balance due for all sales
   * This method should be called to fix existing data issues
   */
  async fixSalesDataInconsistencies(): Promise<{ fixed: number; total: number }> {
    const sales = await this.saleRepository.find({
      where: { isDeleted: false },
    });

    let fixedCount = 0;
    const totalCount = sales.length;

    for (const sale of sales) {
      let correctBalanceDue = this.roundToTwoDecimals(sale.totalAmount - sale.amountPaid);
      
      // If there's a negative balance due (overpayment), set it to 0 and adjust amount paid
      if (correctBalanceDue < 0) {
        console.warn(`Fixing overpayment for sale ${sale.id}: amount paid ${sale.amountPaid} exceeds total ${sale.totalAmount}`);
        sale.amountPaid = sale.totalAmount; // Set amount paid to total amount
        correctBalanceDue = 0; // Set balance due to 0
      }
      
      const correctStatus = this.determineSaleStatus(sale.amountPaid, correctBalanceDue);
      
      let needsUpdate = false;

      // Check if balance due is incorrect
      if (Math.abs(sale.balanceDue - correctBalanceDue) > 0.01) {
        sale.balanceDue = correctBalanceDue;
        needsUpdate = true;
      }

      // Check if status is incorrect
      if (sale.status !== correctStatus) {
        sale.status = correctStatus;
        needsUpdate = true;
      }

      if (needsUpdate) {
        sale.updatedAt = new Date();
        await this.saleRepository.save(sale);
        fixedCount++;
      }
    }

    return { fixed: fixedCount, total: totalCount };
  }

  /**
   * Determine the correct sale status based on amount paid and balance due
   */
  private determineSaleStatus(amountPaid: number, balanceDue: number): SaleStatus {
    if (balanceDue <= 0) {
      return SaleStatus.PAID;
    } else if (amountPaid > 0) {
      return SaleStatus.PARTIALLY_PAID;
    } else {
      return SaleStatus.UNPAID;
    }
  }

  /**
   * Fix sale items with string lineTotal values
   */
  async fixSaleItemsLineTotal(uuid: string): Promise<any> {
    const sale = await this.saleRepository.findOne({
      where: { id: uuid, isDeleted: false },
    });

    if (!sale) {
      throw new NotFoundException("Sale not found");
    }

    const saleItems = await this.saleItemRepository.find({
      where: { saleUuid: sale.id },
    });

    let fixedCount = 0;
    for (const item of saleItems) {
      if (typeof item.lineTotal === 'string') {
        const numericLineTotal = parseFloat(item.lineTotal);
        if (!isNaN(numericLineTotal)) {
          item.lineTotal = numericLineTotal;
          await this.saleItemRepository.save(item);
          fixedCount++;
          console.log(`[Sales Service] Fixed lineTotal for item ${item.name}: "${item.lineTotal}" -> ${numericLineTotal}`);
        }
      }
    }

    return {
      saleId: sale.id,
      fixedCount,
      totalItems: saleItems.length,
      message: `Fixed ${fixedCount} items with string lineTotal values`
    };
  }

  /**
   * Debug sale items data
   */
  async debugSaleItems(uuid: string): Promise<any> {
    const sale = await this.saleRepository.findOne({
      where: { id: uuid, isDeleted: false },
    });

    if (!sale) {
      throw new NotFoundException("Sale not found");
    }

    const saleItems = await this.saleItemRepository.find({
      where: { saleUuid: sale.id },
    });

    return {
      saleId: sale.id,
      saleInfo: {
        subtotal: sale.subtotal,
        taxAmount: sale.taxAmount,
        totalAmount: sale.totalAmount,
        amountPaid: sale.amountPaid,
        balanceDue: sale.balanceDue,
        useTax: sale.useTax,
        taxRate: sale.taxRate,
        status: sale.status
      },
      itemsCount: saleItems.length,
      items: saleItems.map(item => ({
        id: item.id,
        name: item.name,
        quantity: item.quantity,
        unitPrice: item.unitPrice,
        lineTotal: item.lineTotal,
        lineTotalType: typeof item.lineTotal,
        calculatedLineTotal: item.quantity * item.unitPrice
      }))
    };
  }
} 