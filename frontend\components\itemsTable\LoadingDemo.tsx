import React, { useState, useEffect } from 'react';
import ItemsTable from './ItemsTable';
import TableActionButtons from './TableActionButtons';

interface DemoItem {
  id: number;
  name: string;
  status: string;
  price: number;
  category: string;
  description: string;
}

const LoadingDemo: React.FC = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [data, setData] = useState<DemoItem[]>([]);
  const [animationSpeed, setAnimationSpeed] = useState('normal');

  // Simulate data loading
  useEffect(() => {
    const loadData = async () => {
      setIsLoading(true);
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // Enhanced mock data
      const mockData: DemoItem[] = [
        { id: 1, name: 'MacBook Pro 16"', status: 'Active', price: 2499.99, category: 'Electronics', description: 'High-performance laptop for professionals' },
        { id: 2, name: 'Wireless Headphones', status: 'Inactive', price: 199.99, category: 'Audio', description: 'Premium noise-cancelling headphones' },
        { id: 3, name: 'Ergonomic Office Chair', status: 'Active', price: 399.99, category: 'Furniture', description: 'Comfortable chair for long work sessions' },
        { id: 4, name: 'Smart Watch Series 8', status: 'Active', price: 449.99, category: 'Wearables', description: 'Advanced fitness and health tracking' },
        { id: 5, name: 'Mechanical Keyboard', status: 'Active', price: 149.99, category: 'Accessories', description: 'RGB backlit gaming keyboard' },
        { id: 6, name: 'Coffee Maker Pro', status: 'Inactive', price: 89.99, category: 'Appliances', description: 'Programmable coffee brewing system' },
        { id: 7, name: 'Desk Lamp LED', status: 'Active', price: 79.99, category: 'Lighting', description: 'Adjustable brightness desk lamp' },
        { id: 8, name: 'Bluetooth Speaker', status: 'Active', price: 129.99, category: 'Audio', description: 'Portable waterproof speaker' },
      ];
      
      setData(mockData);
      setIsLoading(false);
    };

    loadData();
  }, []);

  const handleReload = () => {
    setData([]);
    setIsLoading(true);
    
    // Simulate reload with variable timing
    const loadTime = animationSpeed === 'fast' ? 1000 : animationSpeed === 'slow' ? 4000 : 2000;
    
    setTimeout(() => {
      const mockData: DemoItem[] = [
        { id: 1, name: 'MacBook Pro 16"', status: 'Active', price: 2499.99, category: 'Electronics', description: 'High-performance laptop for professionals' },
        { id: 2, name: 'Wireless Headphones', status: 'Inactive', price: 199.99, category: 'Audio', description: 'Premium noise-cancelling headphones' },
        { id: 3, name: 'Ergonomic Office Chair', status: 'Active', price: 399.99, category: 'Furniture', description: 'Comfortable chair for long work sessions' },
        { id: 4, name: 'Smart Watch Series 8', status: 'Active', price: 449.99, category: 'Wearables', description: 'Advanced fitness and health tracking' },
        { id: 5, name: 'Mechanical Keyboard', status: 'Active', price: 149.99, category: 'Accessories', description: 'RGB backlit gaming keyboard' },
        { id: 6, name: 'Coffee Maker Pro', status: 'Inactive', price: 89.99, category: 'Appliances', description: 'Programmable coffee brewing system' },
        { id: 7, name: 'Desk Lamp LED', status: 'Active', price: 79.99, category: 'Lighting', description: 'Adjustable brightness desk lamp' },
        { id: 8, name: 'Bluetooth Speaker', status: 'Active', price: 129.99, category: 'Audio', description: 'Portable waterproof speaker' },
      ];
      
      setData(mockData);
      setIsLoading(false);
    }, loadTime);
  };

  const handleEdit = (item: DemoItem) => {
    console.log('Edit:', item);
    // Add a visual feedback animation
    const button = document.activeElement as HTMLButtonElement;
    if (button) {
      button.style.transform = 'scale(1.2)';
      setTimeout(() => {
        button.style.transform = '';
      }, 200);
    }
  };

  const handleDelete = (item: DemoItem) => {
    console.log('Delete:', item);
    // Add a visual feedback animation
    const button = document.activeElement as HTMLButtonElement;
    if (button) {
      button.style.transform = 'scale(1.2)';
      setTimeout(() => {
        button.style.transform = '';
      }, 200);
    }
  };

  const columns = [
    { 
      key: 'name', 
      header: 'Product Name',
      cellClassName: 'text-gray-900 font-semibold text-left',
      headerClassName: 'text-left'
    },
    { 
      key: 'category', 
      header: 'Category',
      cellClassName: 'text-gray-600',
      render: (value: string) => (
        <span className="inline-flex px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800">
          {value}
        </span>
      )
    },
    { 
      key: 'status', 
      header: 'Status',
      render: (value: string) => (
        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
          value === 'Active' 
            ? 'bg-green-100 text-green-800' 
            : 'bg-red-100 text-red-800'
        }`}>
          {value}
        </span>
      )
    },
    { 
      key: 'price', 
      header: 'Price',
      render: (value: number) => (
        <span className="font-semibold text-gray-900">
          ${value.toLocaleString(undefined, { minimumFractionDigits: 2 })}
        </span>
      )
    },
    { 
      key: 'description', 
      header: 'Description',
      cellClassName: 'text-gray-500 text-sm max-w-xs truncate',
      headerClassName: 'text-left'
    },
    {
      key: 'actions',
      header: 'Actions',
      render: (_: any, row: DemoItem) => (
        <TableActionButtons
          onEdit={() => handleEdit(row)}
          onDelete={() => handleDelete(row)}
          onView={() => console.log('View QR:', row)}
        />
      ),
      headerClassName: 'text-center',
      cellClassName: 'text-center align-middle',
    },
  ];

  return (
    <div className="p-6 max-w-7xl mx-auto">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-4 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
          🎨 ItemsTable Cool Animations Demo
        </h1>
        <p className="text-gray-600 mb-6 text-lg">
          Experience the enhanced ItemsTable with spectacular animations including wave shimmer effects, 
          staggered fade-ins, bouncy transitions, and interactive hover effects!
        </p>
        
        <div className="flex flex-wrap gap-4 items-center mb-6">
          <button
            onClick={handleReload}
            className="bg-gradient-to-r from-blue-500 to-blue-600 text-white px-6 py-3 rounded-lg hover:from-blue-600 hover:to-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transform transition-all duration-200 hover:scale-105 shadow-lg disabled:opacity-50 disabled:cursor-not-allowed"
            disabled={isLoading}
          >
            {isLoading ? (
              <div className="flex items-center gap-2">
                <div className="cell-loading"></div>
                Loading...
              </div>
            ) : (
              '🔄 Reload Data'
            )}
          </button>
          
          <div className="flex items-center gap-2">
            <label className="text-sm font-medium text-gray-700">Animation Speed:</label>
            <select
              value={animationSpeed}
              onChange={(e) => setAnimationSpeed(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="fast">Fast (1s)</option>
              <option value="normal">Normal (2s)</option>
              <option value="slow">Slow (4s)</option>
            </select>
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <div className="bg-gradient-to-br from-green-50 to-green-100 p-4 rounded-lg border border-green-200">
            <h3 className="font-semibold text-green-800 mb-2">✨ Loading Features</h3>
            <ul className="text-sm text-green-700 space-y-1">
              <li>• Wave shimmer skeleton</li>
              <li>• Floating skeleton rows</li>
              <li>• Gradient animations</li>
              <li>• Sparkle effects</li>
            </ul>
          </div>
          
          <div className="bg-gradient-to-br from-blue-50 to-blue-100 p-4 rounded-lg border border-blue-200">
            <h3 className="font-semibold text-blue-800 mb-2">🎭 Transition Effects</h3>
            <ul className="text-sm text-blue-700 space-y-1">
              <li>• Bouncy fade-in</li>
              <li>• Staggered row animation</li>
              <li>• Container breathing</li>
              <li>• Smooth table entrance</li>
            </ul>
          </div>
          
          <div className="bg-gradient-to-br from-purple-50 to-purple-100 p-4 rounded-lg border border-purple-200">
            <h3 className="font-semibold text-purple-800 mb-2">🎯 Interactive Elements</h3>
            <ul className="text-sm text-purple-700 space-y-1">
              <li>• Enhanced hover effects</li>
              <li>• Button scale animations</li>
              <li>• Ripple effects</li>
              <li>• Gradient borders</li>
            </ul>
          </div>
        </div>
      </div>

      <ItemsTable
        columns={columns}
        data={data}
        isLoading={isLoading}
        skeletonRows={8}
        noDataText="No items found. Try reloading the data!"
        containerClassName="bg-white rounded-xl shadow-lg border border-gray-200 w-full"
      />
      
      <div className="mt-8 p-6 bg-gradient-to-br from-gray-50 to-gray-100 rounded-lg border border-gray-200">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">🚀 Animation Features Showcase</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 className="font-medium text-gray-700 mb-2">Loading State Animations:</h4>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• <strong>Wave Shimmer:</strong> Skeleton rows with moving light waves</li>
              <li>• <strong>Floating Effect:</strong> Subtle up/down movement for skeleton rows</li>
              <li>• <strong>Gradient Wave:</strong> Multi-color gradient moving across skeleton elements</li>
              <li>• <strong>Sparkle Effect:</strong> Shimmering highlight overlay</li>
              <li>• <strong>Container Breathing:</strong> Gentle pulsing shadow effect</li>
            </ul>
          </div>
          
          <div>
            <h4 className="font-medium text-gray-700 mb-2">Interaction Animations:</h4>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• <strong>Staggered Fade-in:</strong> Rows appear one by one with delays</li>
              <li>• <strong>Bouncy Entrance:</strong> Table slides up with bounce effect</li>
              <li>• <strong>Hover Lift:</strong> Rows lift up with shadow on hover</li>
              <li>• <strong>Gradient Border:</strong> Blue gradient line appears on row hover</li>
              <li>• <strong>Button Scaling:</strong> Action buttons scale up on hover</li>
              <li>• <strong>Ripple Effect:</strong> Expanding circle effect on button interaction</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoadingDemo; 