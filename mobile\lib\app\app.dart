import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../core/constants/app_constants.dart';
import '../shared/services/auth_service.dart';
import '../shared/services/permission_service.dart';
import '../features/auth/login_page.dart';
import 'theme.dart';

class DidoDistributionApp extends ConsumerWidget {
  const DidoDistributionApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) => MaterialApp(
      title: AppConstants.appName,
      debugShowCheckedModeBanner: false,
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: ThemeMode.system,
      home: const AuthWrapper(),
    );
}

class AuthWrapper extends StatefulWidget {
  const AuthWrapper({super.key});

  @override
  State<AuthWrapper> createState() => _AuthWrapperState();
}

class _AuthWrapperState extends State<AuthWrapper> {
  final AuthService _authService = AuthService();
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    _initializeAuth();
  }

  Future<void> _initializeAuth() async {
    await _authService.init();
    setState(() {
      _isInitialized = true;
    });
  }

  @override
  Widget build(BuildContext context) {
    if (!_isInitialized) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    return ListenableBuilder(
      listenable: _authService,
      builder: (context, child) {
        if (_authService.isAuthenticated) {
          return const HomePage();
        } else {
          return const LoginPage();
        }
      },
    );
  }
}

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  int _selectedIndex = 0;
  final PermissionService _permissionService = PermissionService();
  final AuthService _authService = AuthService();

  List<NavigationItem> _availableNavItems = [];
  List<Widget> _pages = [];
  List<BottomNavigationBarItem> _navBarItems = [];

  @override
  void initState() {
    super.initState();
    _setupNavigation();
  }

  void _setupNavigation() {
    debugPrint('🔵 HOME PAGE: Setting up navigation');
    
    // Debug user information
    _authService.debugUserInfo();
    
    _availableNavItems = _permissionService.getAvailableNavigationItems();
    debugPrint('🟢 HOME PAGE: Available navigation items: ${_availableNavItems.map((i) => i.name).toList()}');
    _pages = _availableNavItems.map(_getPageForNavItem).toList();
    _navBarItems = _availableNavItems.map(_getNavBarItemForNavItem).toList();
  }

  Widget _getPageForNavItem(NavigationItem item) {
    switch (item) {
      case NavigationItem.dashboard:
        return const DashboardPage();
      case NavigationItem.sales:
        return _authService.isMobileSaleAgent 
            ? const MobileSaleAgentPage()
            : const SalesPage();
      case NavigationItem.inventory:
        return const InventoryPage();
      case NavigationItem.logistics:
        return const LogisticsPage();
      case NavigationItem.purchasing:
        return const PurchasingPage();
      case NavigationItem.reports:
        return const ReportsPage();
      case NavigationItem.settings:
        return const SettingsPage();
    }
  }

  BottomNavigationBarItem _getNavBarItemForNavItem(NavigationItem item) {
    switch (item) {
      case NavigationItem.dashboard:
        return const BottomNavigationBarItem(
          icon: Icon(Icons.dashboard),
          label: 'Dashboard',
        );
      case NavigationItem.sales:
        return BottomNavigationBarItem(
          icon: Icon(_authService.isMobileSaleAgent ? Icons.person_pin : Icons.point_of_sale),
          label: _authService.isMobileSaleAgent ? 'Sales Agent' : 'Sales',
        );
      case NavigationItem.inventory:
        return const BottomNavigationBarItem(
          icon: Icon(Icons.inventory),
          label: 'Inventory',
        );
      case NavigationItem.logistics:
        return const BottomNavigationBarItem(
          icon: Icon(Icons.local_shipping),
          label: 'Logistics',
        );
      case NavigationItem.purchasing:
        return const BottomNavigationBarItem(
          icon: Icon(Icons.shopping_cart),
          label: 'Purchasing',
        );
      case NavigationItem.reports:
        return const BottomNavigationBarItem(
          icon: Icon(Icons.analytics),
          label: 'Reports',
        );
      case NavigationItem.settings:
        return const BottomNavigationBarItem(
          icon: Icon(Icons.settings),
          label: 'Settings',
        );
    }
  }

  @override
  Widget build(BuildContext context) {
    // Check if user has any valid navigation items
    if (_navBarItems.isEmpty) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Dido Distribution'),
          centerTitle: true,
          actions: [
            IconButton(
              icon: const Icon(Icons.logout),
              onPressed: _authService.logout,
            ),
          ],
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error_outline,
                size: 64,
                color: Colors.red,
              ),
              const SizedBox(height: 16),
              const Text(
                'No access permissions',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              const Text(
                'Your account has been created but no role has been assigned yet.',
                style: TextStyle(fontSize: 14, color: Colors.grey),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(16),
                margin: const EdgeInsets.symmetric(horizontal: 32),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Account Details:',
                      style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Role: ${_authService.currentUser?.roles.isNotEmpty == true ? _authService.currentUser!.roles.first.name : 'No role assigned'}',
                      style: const TextStyle(fontSize: 12, color: Colors.grey),
                    ),
                    if (_authService.currentUser?.warehouseUuid != null)
                      Text(
                        'Warehouse: ${_authService.currentUser!.warehouseUuid}',
                        style: const TextStyle(fontSize: 12, color: Colors.grey),
                      ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
              const Text(
                'Please contact your administrator to assign you a role.',
                style: TextStyle(fontSize: 14, color: Colors.orange),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: _authService.init,
                child: const Text('Refresh Account'),
              ),
              const SizedBox(height: 8),
              ElevatedButton(
                onPressed: _authService.logout,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                ),
                child: const Text('Logout'),
              ),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      body: _pages[_selectedIndex],
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        currentIndex: _selectedIndex,
        onTap: (index) {
          setState(() {
            _selectedIndex = index;
          });
        },
        items: _navBarItems,
      ),
    );
  }
}

// Placeholder pages for each section
class DashboardPage extends StatelessWidget {
  const DashboardPage({super.key});

  @override
  Widget build(BuildContext context) {
    final authService = AuthService();
    final permissionService = PermissionService();

    return Scaffold(
      appBar: AppBar(
        title: const Text('Dashboard'),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: authService.logout,
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Welcome, ${authService.currentUser?.displayName ?? 'User'}',
              style: Theme.of(context).textTheme.headlineMedium,
            ),
            const SizedBox(height: 8),
            Text(
              'Role: ${authService.currentUser?.roles.isNotEmpty == true ? authService.currentUser!.roles.first.name : 'No role assigned'}',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 24),
            Expanded(
              child: GridView.count(
                crossAxisCount: 2,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
                children: _buildDashboardCards(context, authService, permissionService),
              ),
            ),
          ],
        ),
      ),
    );
  }

  List<Widget> _buildDashboardCards(
    BuildContext context,
    AuthService authService,
    PermissionService permissionService,
  ) {
    final cards = <Widget>[];

    // Admin dashboard cards
    if (authService.isAdmin) {
      cards.addAll([
        _buildDashboardCard(
          context,
          'Products',
          Icons.inventory_2,
          '1,234',
          Colors.blue,
        ),
        _buildDashboardCard(
          context,
          'Sales Today',
          Icons.trending_up,
          '\$12,345',
          Colors.green,
        ),
        _buildDashboardCard(
          context,
          'Low Stock',
          Icons.warning,
          '23',
          Colors.orange,
        ),
        _buildDashboardCard(
          context,
          'Active Vans',
          Icons.local_shipping,
          '8',
          Colors.purple,
        ),
        _buildDashboardCard(
          context,
          'Warehouses',
          Icons.warehouse,
          '3',
          Colors.teal,
        ),
        _buildDashboardCard(
          context,
          'Users',
          Icons.people,
          '42',
          Colors.indigo,
        ),
      ]);
    }
    // Mobile Sale Agent dashboard cards
    else if (authService.isMobileSaleAgent) {
      cards.addAll([
        _buildDashboardCard(
          context,
          'My Sales',
          Icons.point_of_sale,
          '\$2,345',
          Colors.green,
        ),
        _buildDashboardCard(
          context,
          'Clients',
          Icons.people,
          '18',
          Colors.blue,
        ),
        _buildDashboardCard(
          context,
          'Missions',
          Icons.assignment,
          '5',
          Colors.orange,
        ),
        _buildDashboardCard(
          context,
          'Messages',
          Icons.message,
          '3',
          Colors.purple,
        ),
      ]);
    }
    // Manager dashboard cards
    else if (authService.isManager) {
      cards.addAll([
        _buildDashboardCard(
          context,
          'Products',
          Icons.inventory_2,
          '1,234',
          Colors.blue,
        ),
        _buildDashboardCard(
          context,
          'Sales Today',
          Icons.trending_up,
          '\$12,345',
          Colors.green,
        ),
        _buildDashboardCard(
          context,
          'Low Stock',
          Icons.warning,
          '23',
          Colors.orange,
        ),
        _buildDashboardCard(
          context,
          'Team Performance',
          Icons.analytics,
          '87%',
          Colors.purple,
        ),
      ]);
    }
    // Default cards for other roles
    else {
      cards.addAll([
        _buildDashboardCard(
          context,
          'Products',
          Icons.inventory_2,
          '1,234',
          Colors.blue,
        ),
        _buildDashboardCard(
          context,
          'Sales Today',
          Icons.trending_up,
          '\$12,345',
          Colors.green,
        ),
      ]);
    }

    return cards;
  }

  Widget _buildDashboardCard(
    BuildContext context,
    String title,
    IconData icon,
    String value,
    Color color,
  ) => Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 48,
              color: color,
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: Theme.of(context).textTheme.titleMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
            Text(
              value,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: color,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
}

class InventoryPage extends StatelessWidget {
  const InventoryPage({super.key});

  @override
  Widget build(BuildContext context) => Scaffold(
      appBar: AppBar(
        title: const Text('Inventory'),
        centerTitle: true,
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.inventory_2,
              size: 64,
              color: Colors.blue,
            ),
            SizedBox(height: 16),
            Text(
              'Inventory Management',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text(
              'Coming Soon',
              style: TextStyle(fontSize: 16, color: Colors.grey),
            ),
          ],
        ),
      ),
    );
}

class SalesPage extends StatelessWidget {
  const SalesPage({super.key});

  @override
  Widget build(BuildContext context) => Scaffold(
      appBar: AppBar(
        title: const Text('Sales'),
        centerTitle: true,
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.point_of_sale,
              size: 64,
              color: Colors.green,
            ),
            SizedBox(height: 16),
            Text(
              'Sales & POS',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text(
              'Coming Soon',
              style: TextStyle(fontSize: 16, color: Colors.grey),
            ),
          ],
        ),
      ),
    );
}

class LogisticsPage extends StatelessWidget {
  const LogisticsPage({super.key});

  @override
  Widget build(BuildContext context) => Scaffold(
      appBar: AppBar(
        title: const Text('Logistics'),
        centerTitle: true,
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.local_shipping,
              size: 64,
              color: Colors.purple,
            ),
            SizedBox(height: 16),
            Text(
              'Logistics & Delivery',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text(
              'Coming Soon',
              style: TextStyle(fontSize: 16, color: Colors.grey),
            ),
          ],
        ),
      ),
    );
}

class PurchasingPage extends StatelessWidget {
  const PurchasingPage({super.key});

  @override
  Widget build(BuildContext context) => Scaffold(
      appBar: AppBar(
        title: const Text('Purchasing'),
        centerTitle: true,
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.shopping_cart,
              size: 64,
              color: Colors.orange,
            ),
            SizedBox(height: 16),
            Text(
              'Purchasing & Suppliers',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text(
              'Coming Soon',
              style: TextStyle(fontSize: 16, color: Colors.grey),
            ),
          ],
        ),
      ),
    );
}

class MobileSaleAgentPage extends StatelessWidget {
  const MobileSaleAgentPage({super.key});

  @override
  Widget build(BuildContext context) {
    final authService = AuthService();
    final permissionService = PermissionService();

    return Scaffold(
      appBar: AppBar(
        title: const Text('Sales Agent'),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: authService.logout,
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Welcome, ${authService.currentUser?.displayName ?? 'Agent'}',
              style: Theme.of(context).textTheme.headlineMedium,
            ),
            const SizedBox(height: 24),
            Expanded(
              child: GridView.count(
                crossAxisCount: 2,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
                children: [
                  _buildFeatureCard(
                    context,
                    'Clients',
                    Icons.people,
                    'Manage customer relationships',
                    Colors.blue,
                    () => _navigateToClients(context),
                  ),
                  _buildFeatureCard(
                    context,
                    'Sales',
                    Icons.point_of_sale,
                    'Process sales and orders',
                    Colors.green,
                    () => _navigateToSales(context),
                  ),
                  _buildFeatureCard(
                    context,
                    'My Stats',
                    Icons.analytics,
                    'View performance metrics',
                    Colors.orange,
                    () => _navigateToStats(context),
                  ),
                  _buildFeatureCard(
                    context,
                    'Maps & Routes',
                    Icons.map,
                    'Navigate to customers',
                    Colors.purple,
                    () => _navigateToMaps(context),
                  ),
                  _buildFeatureCard(
                    context,
                    'Missions',
                    Icons.assignment,
                    'View assigned tasks',
                    Colors.red,
                    () => _navigateToMissions(context),
                  ),
                  _buildFeatureCard(
                    context,
                    'Messages',
                    Icons.message,
                    'Team communication',
                    Colors.teal,
                    () => _navigateToMessages(context),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeatureCard(
    BuildContext context,
    String title,
    IconData icon,
    String description,
    Color color,
    VoidCallback onTap,
  ) => Card(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                size: 48,
                color: color,
              ),
              const SizedBox(height: 12),
              Text(
                title,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 4),
              Text(
                description,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey[600],
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );

  void _navigateToClients(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Clients feature coming soon')),
    );
  }

  void _navigateToSales(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Sales feature coming soon')),
    );
  }

  void _navigateToStats(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Stats feature coming soon')),
    );
  }

  void _navigateToMaps(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Maps feature coming soon')),
    );
  }

  void _navigateToMissions(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Missions feature coming soon')),
    );
  }

  void _navigateToMessages(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Messages feature coming soon')),
    );
  }
}

class ReportsPage extends StatelessWidget {
  const ReportsPage({super.key});

  @override
  Widget build(BuildContext context) => Scaffold(
      appBar: AppBar(
        title: const Text('Reports'),
        centerTitle: true,
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.analytics,
              size: 64,
              color: Colors.blue,
            ),
            SizedBox(height: 16),
            Text(
              'Reports & Analytics',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text(
              'Coming Soon',
              style: TextStyle(fontSize: 16, color: Colors.grey),
            ),
          ],
        ),
      ),
    );
}

class SettingsPage extends StatelessWidget {
  const SettingsPage({super.key});

  @override
  Widget build(BuildContext context) {
    final authService = AuthService();

    return Scaffold(
      appBar: AppBar(
        title: const Text('Settings'),
        centerTitle: true,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // User info card
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    CircleAvatar(
                      radius: 40,
                      backgroundColor: Theme.of(context).primaryColor,
                      child: Text(
                        authService.currentUser?.displayName.substring(0, 1).toUpperCase() ?? 'U',
                        style: const TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      authService.currentUser?.displayName ?? 'User',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      authService.currentUser?.email ?? '',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.grey[600],
                      ),
                    ),
                    const SizedBox(height: 8),
                    Chip(
                      label: Text(
                        authService.currentUser?.roles.first.name ?? 'User',
                        style: const TextStyle(color: Colors.white),
                      ),
                      backgroundColor: Theme.of(context).primaryColor,
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 24),
            
            // Logout button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: authService.logout,
                icon: const Icon(Icons.logout),
                label: const Text('Logout'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
} 