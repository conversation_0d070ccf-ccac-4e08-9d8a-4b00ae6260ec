"use client";
import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import ProtectedRoute from "@/components/ProtectedRoute";
import { FiUser } from 'react-icons/fi';

// Import POS components directly
import { POSComponent } from '../sales/components/POSComponent';
import { useSalesPOSState } from '../sales/hooks/useSalesPOSState';

export default function CashRegister() {
  const { user } = useAuth();
  const warehouseUuid = user?.warehouseUuid;
  const posState = useSalesPOSState();

  // Handle sale completion
  const handleSaleComplete = () => {
    console.log('[CashRegister] Sale completed');
    // Could add navigation or other actions here
  };

  // Handle new sale
  const handleNewSale = () => {
    console.log('[CashRegister] Starting new sale');
    // Trigger new sale in POS component
    window.dispatchEvent(new CustomEvent('newSaleRequest'));
  };

  if (!warehouseUuid) {
    return (
      <ProtectedRoute>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <FiUser className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No warehouse assigned</h3>
            <p className="mt-1 text-sm text-gray-500">Please contact your administrator.</p>
          </div>
        </div>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute>
      <div className="h-full flex flex-col">
        {/* Header */}
        <div className="py-3 px-6 bg-white border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Cash Register</h1>
              <p className="text-sm text-gray-600">Point of Sale System</p>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-600">
                Warehouse: {user?.warehouseName || 'Unknown'}
              </span>
            </div>
          </div>
        </div>

        {/* POS Content */}
        <div className="flex-1">
          <POSComponent 
            onSaleComplete={handleSaleComplete} 
            onNewSale={handleNewSale}
            posState={posState}
          />
        </div>
      </div>
    </ProtectedRoute>
  );
}
