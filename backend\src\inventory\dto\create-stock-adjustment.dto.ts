import {
  IsNotEmpty,
  IsString,
  IsNumber,
  IsInt,
  IsUUID,
  IsOptional,
  NotEquals,
} from "class-validator";

import { ApiProperty } from "@nestjs/swagger";

export class CreateStockAdjustmentDto {
  @ApiProperty({
    description: "UUID of the user responsible for the adjustment",
    format: "uuid",
  })
  @IsUUID()
  @IsNotEmpty()
  userUuid: string;

  @ApiProperty({ description: "UUID of the warehouse", format: "uuid" })
  @IsUUID()
  @IsNotEmpty()
  warehouseUuid: string;

  @ApiProperty({
    description: "UUID of the storage where adjustment is made",
    format: "uuid",
  })
  @IsUUID()
  @IsNotEmpty()
  storageUuid: string;

  @ApiProperty({
    description: "UUID of the product being adjusted",
    format: "uuid",
  })
  @IsUUID()
  @IsNotEmpty()
  productUuid: string;

  @ApiProperty({
    description: "The adjusted quantity (cannot be 0)",
    example: 10,
  })
  @IsInt()
  @NotEquals(0, { message: "quantityAdjusted cannot be zero" })
  quantityAdjusted: number;

  @ApiProperty({
    description: "Reason for adjustment",
    example: "Inventory correction",
    required: false,
  })
  @IsString()
  @IsOptional()
  reason?: string;
}
