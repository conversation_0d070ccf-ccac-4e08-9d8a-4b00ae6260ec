import React from 'react';
import { Supplier } from '../types';

interface SuppliersTableProps {
  suppliers: Supplier[];
  onEdit: (supplier: Supplier) => void;
  onDelete: (supplier: Supplier) => void;
  loading: boolean;
}

export default function SuppliersTable({ suppliers, onEdit, onDelete, loading }: SuppliersTableProps) {
  return (
    <div className="overflow-x-auto">
      <table className="min-w-full bg-white rounded shadow">
        <thead>
          <tr>
            <th className="px-4 py-2 text-left">Name</th>
            <th className="px-4 py-2 text-left">Email</th>
            <th className="px-4 py-2 text-left">Phone</th>
            <th className="px-4 py-2 text-left">Address</th>
            <th className="px-4 py-2 text-left">Actions</th>
          </tr>
        </thead>
        <tbody>
          {loading ? (
            <tr><td colSpan={5} className="text-center py-4">Loading...</td></tr>
          ) : suppliers.length === 0 ? (
            <tr><td colSpan={5} className="text-center py-4 text-gray-500">No suppliers found.</td></tr>
          ) : suppliers.map(supplier => (
            <tr key={supplier.uuid} className="hover:bg-gray-50">
              <td className="px-4 py-2">{supplier.name}</td>
              <td className="px-4 py-2">{supplier.email || '-'}</td>
              <td className="px-4 py-2">{supplier.phone || '-'}</td>
              <td className="px-4 py-2">{supplier.address || '-'}</td>
              <td className="px-4 py-2 flex gap-2">
                <button onClick={() => onEdit(supplier)} className="text-blue-600 hover:underline">Edit</button>
                <button onClick={() => onDelete(supplier)} className="text-red-600 hover:underline">Delete</button>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
}
