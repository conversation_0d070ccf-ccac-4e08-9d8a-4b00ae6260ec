import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Patch,
  BadRequestException,
  UsePipes,
  ValidationPipe,
  Query,
  UseGuards,
  ParseUUIDPipe,
} from "@nestjs/common";
import { SetWarehouseNameDto } from "./dto/set-warehouse-name.dto";
import { SetMainStorageDto } from "./dto/set-main-storage.dto";
import { WarehousesService } from "./warehouses.service";
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiQuery,
  ApiBody,
} from "@nestjs/swagger";
import { Warehouse } from "./warehouse.entity";
import { PaginationQueryDto } from "../dto/pagination.dto";
import { WarehouseFilterDto } from "./dto/warehouse-filter.dto";
import { CreateWarehouseDto } from "./dto/create-warehouse.dto";
import { UpdateWarehouseDto } from "./dto/update-warehouse.dto";
@ApiTags("warehouses")
@Controller("warehouses")
export class WarehousesController {
  constructor(private readonly warehousesService: WarehousesService) {}

  /**
   * Find warehouses by query parameters (currently supports exact name match).
   * Example: GET /warehouses/search?name=Acme Warehouse
   */
  @Get("search")
  @ApiOperation({ summary: "Search warehouses by query parameters" })
  @ApiResponse({
    status: 200,
    description: "Array of warehouses matching query",
    type: [Warehouse],
  })
  async search(@Query("name") name: string): Promise<any[]> {
    if (!name) {
      throw new BadRequestException('Query parameter "name" is required');
    }
    return this.warehousesService.findByName(name);
  }

  @Post()
  @ApiOperation({ summary: "Create a new warehouse" })
  @ApiBody({ type: CreateWarehouseDto })
  @ApiResponse({
    status: 201,
    description: "Warehouse created",
    type: Warehouse,
  })
  async create(@Body() createWarehouseDto: CreateWarehouseDto) {
    return this.warehousesService.create(createWarehouseDto);
  }

  @Get("list")
  @ApiOperation({ summary: "List non-deleted warehouses with pagination" })
  @ApiQuery({
    name: "page",
    type: Number,
    required: false,
    description: "Page number (1-based)",
    example: 1,
  })
  @ApiQuery({
    name: "limit",
    type: Number,
    required: false,
    description: "Number of items per page (max 100)",
    example: 10,
  })
  @ApiQuery({
    name: "userUuid",
    type: String,
    required: false,
    description: "Optional user UUID to filter warehouses by owner",
    example: "uuid-v7-string",
  })
  @ApiResponse({
    status: 200,
    description: "Paginated list of non-deleted warehouses",
    type: [Warehouse],
  })
  async findAllNonDeleted(
    @Query() paginationQuery: PaginationQueryDto,
    @Query("userUuid") userUuid?: string,
  ) {
    const { page = 1, limit = 10 } = paginationQuery;
    return this.warehousesService.findAllNonDeleted(page, limit, userUuid);
  }

  @Get("list-raw")
  @ApiOperation({ summary: "List all warehouses (including deleted)" })
  @ApiQuery({
    name: "userUuid",
    type: String,
    required: false,
    description: "Optional user UUID to filter warehouses by owner",
    example: "uuid-v7-string",
  })
  @ApiResponse({
    status: 200,
    description: "List of all warehouses",
    type: [Warehouse],
  })
  async findAllRaw(@Query("userUuid") userUuid?: string) {
    return this.warehousesService.findAllRaw(userUuid);
  }

  @Get("filter")
  @ApiOperation({
    summary: "Filter warehouses by name and userUuid with pagination",
    description:
      "Filter warehouses by name (partial, case-insensitive) and/or userUuid with pagination support.",
  })
  @ApiResponse({
    status: 200,
    description: "Paginated filtered list of warehouses.",
  })
  async filterByName(@Query() filterDto: WarehouseFilterDto) {
    const { name, userUuid, page = 1, limit = 20 } = filterDto;
    return this.warehousesService.filterByName(name, page, limit, userUuid);
  }

  @Get("count")
  @ApiOperation({
    summary: "Get total count of warehouses",
    description:
      "Returns the total count of non-deleted warehouses, optionally filtered by userUuid.",
  })
  @ApiQuery({
    name: "userUuid",
    type: String,
    required: false,
    description: "Optional user UUID to filter warehouses by owner",
    example: "uuid-v7-string",
  })
  @ApiResponse({
    status: 200,
    description: "Total count of warehouses.",
    schema: {
      type: "object",
      properties: { count: { type: "number", example: 42 } },
    },
  })
  async count(@Query("userUuid") userUuid?: string) {
    const count = await this.warehousesService.count(userUuid);
    return { count };
  }

  @Get(":uuid")
  @ApiOperation({ summary: "Get a warehouse by UUID" })
  @ApiResponse({ status: 200, description: "Warehouse found", type: Warehouse })
  async findOne(@Param("uuid", ParseUUIDPipe) uuid: string) {
    return this.warehousesService.findOne(uuid);
  }

  @Get(":uuid/main-storage")
  @ApiOperation({ summary: "Get the main storage for a warehouse" })
  @ApiResponse({
    status: 200,
    description: "Main storage found",
    schema: {
      type: "object",
      properties: {
        mainStorageUuid: { type: "string", example: "uuid-v7-string" },
      },
    },
  })
  async getMainStorage(@Param("uuid", ParseUUIDPipe) uuid: string) {
    const warehouse = await this.warehousesService.findOne(uuid);
    return {
      mainStorageUuid: warehouse.mainStorageUuidString,
    };
  }

  @Put(":uuid")
  @ApiOperation({ summary: "Update a warehouse by UUID" })
  @ApiBody({ type: UpdateWarehouseDto })
  @ApiResponse({
    status: 200,
    description: "Warehouse updated",
    type: Warehouse,
  })
  async update(
    @Param("uuid", ParseUUIDPipe) uuid: string,
    @Body() updateWarehouseDto: UpdateWarehouseDto,
  ) {
    return this.warehousesService.update(uuid, updateWarehouseDto);
  }

  @Patch(":uuid/name")
  @ApiOperation({ summary: "Set the name of a warehouse" })
  @ApiResponse({
    status: 200,
    description: "Warehouse name updated",
    type: Warehouse,
  })
  @UsePipes(new ValidationPipe({ whitelist: true, forbidNonWhitelisted: true }))
  async setName(
    @Param("uuid", ParseUUIDPipe) uuid: string,
    @Body() setWarehouseNameDto: SetWarehouseNameDto,
  ) {
    return this.warehousesService.setName(
      uuid,
      setWarehouseNameDto.name.trim(),
    );
  }

  @Patch(":uuid/main-storage")
  @ApiOperation({ summary: "Update the main storage UUID for a warehouse" })
  @ApiBody({ type: SetMainStorageDto })
  @ApiResponse({
    status: 200,
    description: "Warehouse main storage updated",
    type: Warehouse,
  })
  @UsePipes(new ValidationPipe({ whitelist: true, forbidNonWhitelisted: true }))
  async setMainStorage(
    @Param("uuid", ParseUUIDPipe) uuid: string,
    @Body() setMainStorageDto: SetMainStorageDto,
  ) {
    return this.warehousesService.update(uuid, { mainStorageUuid: setMainStorageDto.mainStorageUuid });
  }

  @Delete(":uuid")
  @ApiOperation({ summary: "Delete a warehouse by UUID" })
  @ApiResponse({ status: 204, description: "Warehouse deleted" })
  async remove(@Param("uuid", ParseUUIDPipe) uuid: string) {
    await this.warehousesService.remove(uuid);
    return { message: "Warehouse deleted" };
  }
}
