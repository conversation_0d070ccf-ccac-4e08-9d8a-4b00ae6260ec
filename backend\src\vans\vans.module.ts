import { Modu<PERSON> } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { VansController } from "./vans.controller";
import { VansService } from "./vans.service.typeorm";
import { Van } from "./van.entity";
import { Storage } from "../inventory/storage.entity";
import { Warehouse } from "../warehouses/warehouse.entity";

@Module({
  imports: [
    TypeOrmModule.forFeature([Van, Storage, Warehouse]),
  ],
  controllers: [VansController],
  providers: [VansService],
  exports: [VansService],
})
export class VansModule {}
