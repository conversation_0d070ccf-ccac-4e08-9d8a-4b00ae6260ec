import { ApiProperty } from "@nestjs/swagger";
import { IsOptional, IsInt, Min, Max } from "class-validator";
import { Type } from "class-transformer";

export class ProductCategoryPaginationDto {
  @ApiProperty({
    example: 1,
    description: "Page number (1-based)",
    minimum: 1,
    required: false,
    default: 1,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  page?: number = 1;

  @ApiProperty({
    example: 10,
    description: "Number of items per page",
    minimum: 1,
    maximum: 1000,
    required: false,
    default: 10,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @Max(1000)
  limit?: number = 10;
}

export class ProductCategoryListResponseDto {
  @ApiProperty({
    example: [
      {
        uuid: "018ea2bb-6b8d-7e9a-8a2a-7e9a8a2a7e9a",
        name: "Electronics",
        createdAt: "2024-01-15T10:30:00.000Z",
        updatedAt: "2024-01-15T10:30:00.000Z",
      },
    ],
    description: "Array of product categories",
  })
  data: any[];

  @ApiProperty({
    example: 1,
    description: "Current page number",
  })
  page: number;

  @ApiProperty({
    example: 10,
    description: "Number of items per page",
  })
  limit: number;

  @ApiProperty({
    example: 25,
    description: "Total number of items",
  })
  total: number;

  @ApiProperty({
    example: 3,
    description: "Total number of pages",
  })
  totalPages: number;
}
