# YugabyteDB Configuration
YUGABYTE_HOST=localhost
YUGABYTE_PORT=5433
YUGABYTE_DATABASE=yugabyte
YUGABYTE_USER=yugabyte
YUGABYTE_PASSWORD=password

# Google OAuth Configuration
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GOOGLE_CALLBACK_URL=http://localhost:8000/auth/google/callback

# JWT Secret (generate a secure random string)
JWT_SECRET=your_jwt_secret_here

# Troubleshooting Google OAuth Issues:
# 1. "Invalid Google ID token" - Check that GOOGLE_CLIENT_ID matches your Google Cloud Console
# 2. "Token audience mismatch" - Verify the client ID in your mobile app matches GOOGLE_CLIENT_ID
# 3. "Token used too early/late" - Check system clock synchronization
# 4. "Invalid issuer" - Ensure you're using the correct Google OAuth endpoints