"use client";
import React, { useState, useEffect } from 'react';

interface NetworkStatusCheckerProps {
  onStatusChange?: (status: 'checking' | 'good' | 'slow' | 'unstable' | 'offline') => void;
  showDetails?: boolean;
  className?: string;
}

export default function NetworkStatusChecker({ 
  onStatusChange, 
  showDetails = false, 
  className = "" 
}: NetworkStatusCheckerProps) {
  const [status, setStatus] = useState<'checking' | 'good' | 'slow' | 'unstable' | 'offline'>('checking');
  const [details, setDetails] = useState<string>('');
  const [lastCheck, setLastCheck] = useState<Date | null>(null);
  const [connectionType, setConnectionType] = useState<string>('');
  const [checkTimeout, setCheckTimeout] = useState<NodeJS.Timeout | null>(null);

  const checkNetworkStatus = async () => {
    setStatus('checking');
    setDetails('Checking network connectivity...');
    
    // Clear any existing timeout
    if (checkTimeout) {
      clearTimeout(checkTimeout);
    }
    
    // Set a timeout for the check
    const timeout = setTimeout(() => {
      setStatus('offline');
      setDetails('Network check timed out after 15 seconds. No internet connection detected.');
      onStatusChange?.('offline');
    }, 15000);
    
    setCheckTimeout(timeout);
    
    try {
      // Check connection type
      if ('connection' in navigator) {
        const conn = (navigator as any).connection;
        if (conn) {
          setConnectionType(`${conn.effectiveType || 'unknown'} (${conn.type || 'unknown'})`);
        }
      }

      // Test connection speed by making multiple requests with shorter timeouts
      const testUrls = [
        'https://www.google.com/favicon.ico',
        'https://www.cloudflare.com/favicon.ico',
        'https://www.github.com/favicon.ico'
      ];

      const results = await Promise.allSettled(
        testUrls.map(url => 
          fetch(url, { 
            method: 'HEAD', 
            mode: 'no-cors',
            cache: 'no-cache',
            signal: AbortSignal.timeout(5000) // 5 second timeout per request
          })
        )
      );

      const successfulRequests = results.filter(result => result.status === 'fulfilled').length;
      const totalRequests = results.length;
      
      // Clear the timeout since we got results
      clearTimeout(timeout);
      setCheckTimeout(null);
      
      setLastCheck(new Date());
      
      if (successfulRequests === 0) {
        setStatus('offline');
        setDetails('No internet connection detected. Please check your network connection.');
        onStatusChange?.('offline');
      } else if (successfulRequests < totalRequests) {
        setStatus('unstable');
        setDetails(`Unstable connection: ${successfulRequests}/${totalRequests} requests successful. Some requests failed.`);
        onStatusChange?.('unstable');
      } else {
        // All requests successful, now test speed
        const startTime = Date.now();
        try {
          await fetch('https://www.google.com/favicon.ico', { 
            method: 'HEAD', 
            mode: 'no-cors',
            cache: 'no-cache',
            signal: AbortSignal.timeout(3000) // 3 second timeout for speed test
          });
          const endTime = Date.now();
          const responseTime = endTime - startTime;
          
          if (responseTime < 1000) {
            setStatus('good');
            setDetails(`Good connection: ${responseTime}ms response time`);
            onStatusChange?.('good');
          } else if (responseTime < 3000) {
            setStatus('slow');
            setDetails(`Slow connection: ${responseTime}ms response time`);
            onStatusChange?.('slow');
          } else {
            setStatus('unstable');
            setDetails(`Very slow connection: ${responseTime}ms response time`);
            onStatusChange?.('unstable');
          }
        } catch (error) {
          setStatus('unstable');
          setDetails('Connection test failed');
          onStatusChange?.('unstable');
        }
      }
    } catch (error) {
      // Clear the timeout since we got an error
      clearTimeout(timeout);
      setCheckTimeout(null);
      
      setLastCheck(new Date());
      setStatus('offline');
      setDetails(`Network check failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      onStatusChange?.('offline');
    }
  };

  useEffect(() => {
    checkNetworkStatus();
    
    // Cleanup timeout on unmount
    return () => {
      if (checkTimeout) {
        clearTimeout(checkTimeout);
      }
    };
  }, []);

  const getStatusIcon = () => {
    switch (status) {
      case 'checking':
        return (
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
        );
      case 'good':
        return (
          <svg className="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
          </svg>
        );
      case 'slow':
        return (
          <svg className="w-4 h-4 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
        );
      case 'unstable':
        return (
          <svg className="w-4 h-4 text-orange-600" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
        );
      case 'offline':
        return (
          <svg className="w-4 h-4 text-red-600" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
          </svg>
        );
    }
  };

  const getStatusText = () => {
    switch (status) {
      case 'checking':
        return 'Checking network connection...';
      case 'good':
        return 'Network connection is good';
      case 'slow':
        return 'Network connection is slow';
      case 'unstable':
        return 'Network connection is unstable';
      case 'offline':
        return 'No network connection';
    }
  };

  const getStatusColor = () => {
    switch (status) {
      case 'checking':
        return 'text-blue-600';
      case 'good':
        return 'text-green-600';
      case 'slow':
        return 'text-yellow-600';
      case 'unstable':
        return 'text-orange-600';
      case 'offline':
        return 'text-red-600';
    }
  };

  const getConnectionAdvice = () => {
    switch (status) {
      case 'good':
        return null;
      case 'slow':
        return 'Your connection is slow. Authentication may take longer than usual.';
      case 'unstable':
        return 'Your connection is unstable. Consider using a wired connection or moving closer to your router.';
      case 'offline':
        return 'No internet connection detected. Please check your network settings.';
      default:
        return null;
    }
  };

  return (
    <div className={`flex items-center ${className}`}>
      <div className="mr-2">
        {getStatusIcon()}
      </div>
      <div className="flex-1">
        <div className={`text-sm font-medium ${getStatusColor()}`}>
          {getStatusText()}
        </div>
        {showDetails && details && (
          <div className="text-xs text-gray-500 mt-1">
            {details}
          </div>
        )}
        {showDetails && connectionType && (
          <div className="text-xs text-gray-400 mt-1">
            Connection: {connectionType}
          </div>
        )}
        {showDetails && lastCheck && (
          <div className="text-xs text-gray-400 mt-1">
            Last checked: {lastCheck.toLocaleTimeString()}
          </div>
        )}
        {showDetails && getConnectionAdvice() && (
          <div className="text-xs text-orange-600 mt-1 font-medium">
            {getConnectionAdvice()}
          </div>
        )}
      </div>
      <button
        onClick={checkNetworkStatus}
        disabled={status === 'checking'}
        className="ml-2 px-2 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded transition disabled:opacity-50"
        title="Refresh network status"
      >
        {status === 'checking' ? 'Checking...' : 'Refresh'}
      </button>
    </div>
  );
} 