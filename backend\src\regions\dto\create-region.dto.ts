import { IsString, IsOptional, <PERSON>UUI<PERSON>, <PERSON>N<PERSON><PERSON> } from "class-validator";
import { ApiProperty } from "@nestjs/swagger";

export class CreateRegionDto {
  @IsString()
  @ApiProperty({
    example: "Downtown District",
    description: "Region name (required)",
  })
  name: string;

  @IsString()
  @IsOptional()
  @ApiProperty({
    example: "Central business district with high customer density",
    description: "Region description (optional)",
    required: false,
  })
  description?: string;

  @IsNumber()
  @IsOptional()
  @ApiProperty({
    example: 40.7128,
    description: "Latitude coordinate for region center (optional)",
    required: false,
  })
  latitude?: number;

  @IsNumber()
  @IsOptional()
  @ApiProperty({
    example: -74.006,
    description: "Longitude coordinate for region center (optional)",
    required: false,
  })
  longitude?: number;

  @IsUUID("all")
  @ApiProperty({
    example: "uuid-v7-string",
    description: "UUID of the warehouse this region belongs to (required)",
  })
  warehouseUuid: string;
}
