import {
  IsString,
  IsOptional,
  IsUUID,
  IsArray,
  ValidateNested,
  ArrayMinSize,
  IsNumber,
} from "class-validator";
import { Type } from "class-transformer";
import { ApiProperty } from "@nestjs/swagger";

export class UpdateCustomerLocationDto {
  @IsNumber()
  @IsOptional()
  @ApiProperty({
    example: 40.7128,
    description: "Latitude coordinate of the customer location",
    required: false,
  })
  latitude?: number;

  @IsNumber()
  @IsOptional()
  @ApiProperty({
    example: -74.006,
    description: "Longitude coordinate of the customer location",
    required: false,
  })
  longitude?: number;

  @IsString()
  @IsOptional()
  @ApiProperty({
    example: "Customer A",
    description: "Name of the customer (optional)",
    required: false,
  })
  customerName?: string;
}

export class UpdateRouteDto {
  @IsString()
  @IsOptional()
  @ApiProperty({
    example: "Updated Morning Delivery Route",
    description: "Route name (optional)",
    required: false,
  })
  name?: string;

  @IsString()
  @IsOptional()
  @ApiProperty({
    example: "Updated optimized route for morning deliveries in downtown area",
    description: "Route description (optional)",
    required: false,
  })
  description?: string;

  @IsArray()
  @IsOptional()
  @ArrayMinSize(2, {
    message: "At least 2 customer locations are required to compute a route",
  })
  @ValidateNested({ each: true })
  @Type(() => UpdateCustomerLocationDto)
  @ApiProperty({
    example: [
      { latitude: 40.7128, longitude: -74.006, customerName: "Customer A" },
      { latitude: 40.7589, longitude: -73.9851, customerName: "Customer B" },
      { latitude: 40.7505, longitude: -73.9934, customerName: "Customer C" },
    ],
    description:
      "Array of customer locations to optimize route for (minimum 2 required if provided)",
    type: [UpdateCustomerLocationDto],
    required: false,
  })
  customerLocations?: UpdateCustomerLocationDto[];

  @IsUUID("all")
  @IsOptional()
  @ApiProperty({
    example: "uuid-v7-string",
    description: "UUID of the warehouse this route belongs to (optional)",
    required: false,
  })
  warehouseUuid?: string;
}
