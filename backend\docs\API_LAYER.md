# API Layer Patterns (DTO, Controller, Service)

This document provides canonical code patterns for the backend API layer, including DTOs, controllers, and services. Use these conventions for consistent, robust, and type-safe API development.

### User Context in Controllers
When JWT authentication is applied, the authenticated user is available in the request:
```typescript
@Get()
@UseGuards(JwtAuthGuard)
async findAll(@Request() req) {
  const user = req.user; // Authenticated user object
  // user.userUuid, user.email, user.warehouseUuid, etc.
  return this.service.findAll();
}
```

### Error Responses
- **401 Unauthorized**: No token, invalid token, or expired token
- **403 Forbidden**: User lacks required permissions (if role-based access is implemented)

## Pagination Requirement
All endpoints that return a list of items must implement pagination. This ensures efficient data transfer and consistent client experience, especially for large datasets. Use query parameters such as `page` and `limit` (or similar) to control pagination. Always return pagination metadata in the response.

### Required Pagination Response Fields
All paginated endpoints MUST return the following fields in the response:
- `totalPages`: Total number of pages available (calculated as `Math.ceil(total / limit)`)
- `totalEntries` or `total`: Total number of items across all pages
- `page`: Current page number
- `limit`: Number of items per page
- `hasNext`: Boolean indicating if there are more pages
- `hasPrev`: Boolean indicating if there are previous pages

### Standard Pagination Response Structure
Use the `PaginatedResponseDto<T>` class which automatically includes all required fields:
```typescript
{
  "data": [...], // Array of items
  "meta": {
    "total": 150,        // Total entries
    "page": 1,           // Current page
    "limit": 10,         // Items per page
    "totalPages": 15,    // Total pages
    "hasNext": true,     // Has next page
    "hasPrev": false     // Has previous page
  }
}
```

Filter endpoints that accept POST requests should include optional `page` and `limit` fields in the request DTO for consistent pagination handling.

## Filter Restrictions

1. **Always enforce `isDeleted: false`** in customer queries - never allow this as a filter parameter
2. **Validate filter DTOs** to ensure only allowed fields are accepted

## Numeric Filter Handling

### Overview
When implementing numeric filters (e.g., `minAmount`, `maxAmount`, `minQuantity`, `maxPrice`), always handle empty values, NaN, and provide appropriate fallbacks.

### Implementation Pattern

#### Constants
Define constants for minimum and maximum numeric values:
```typescript
// In your constants file (e.g., sales.constants.ts)
export const MIN_NUMBER_FILTER = Number.MIN_SAFE_INTEGER;
export const MAX_NUMBER_FILTER = Number.MAX_SAFE_INTEGER;
```

#### Controller Parameter Handling
- Accept numeric filters as `string` parameters to handle raw query values
- Parse and validate the values before passing to service
```typescript
@Query("minAmount") minAmount?: string,
@Query("maxAmount") maxAmount?: string,

// Parse and validate amount filters
const parsedMinAmount = minAmount ? parseFloat(minAmount) : undefined;
const parsedMaxAmount = maxAmount ? parseFloat(maxAmount) : undefined;
```

#### Service Filter Logic
- Use constants as default values in method signature
- Validate parsed values and handle NaN/empty cases
- Only add filters to query when valid values are provided
```typescript
async findAll({
  minAmount = MIN_NUMBER_FILTER,
  maxAmount = MAX_NUMBER_FILTER,
}: {
  minAmount?: number;
  maxAmount?: number;
} = {}) {
  // Handle amount filtering with proper validation
  const validMinAmount = typeof minAmount === 'number' && !isNaN(minAmount) ? minAmount : MIN_NUMBER_FILTER;
  const validMaxAmount = typeof maxAmount === 'number' && !isNaN(maxAmount) ? maxAmount : MAX_NUMBER_FILTER;
  
  // Only add amount filter if we have valid values and they're not the default constants
  if (validMinAmount !== MIN_NUMBER_FILTER || validMaxAmount !== MAX_NUMBER_FILTER) {
    query.totalAmount = {};
    if (validMinAmount !== MIN_NUMBER_FILTER) {
      query.totalAmount.$gte = validMinAmount;
    }
    if (validMaxAmount !== MAX_NUMBER_FILTER) {
      query.totalAmount.$lte = validMaxAmount;
    }
  }
}
```

### Benefits
- **Robust handling**: Empty values, NaN, and invalid inputs are handled gracefully
- **Performance**: No unnecessary database filters when values are empty/invalid
- **Consistency**: All numeric filters follow the same pattern across the application
- **Frontend friendly**: Frontend can send empty strings or omit parameters without errors

## UserUuid Requirements for Creation and Update Endpoints

### Overview
All creation and update endpoints must support tracking of which user performed the operation. This is implemented through the `userUuid` field in DTOs and proper handling in services.

### Implementation Pattern

#### DTOs
- **Create DTOs** must include an optional `userUuid` field
- **Update DTOs** must include an optional `userUuid` field
- Both fields should be validated as UUID format using `@IsUUID('7')` decorator (see note below).

## UUID Handling in API Endpoints

### DTO Validation
- Use `@IsUUID('7')` for UUID validation in DTOs to ensure UUIDv7 format. **Note:** This requires a custom validator, as class-validator only supports versions '3', '4', and '5' by default. Ensure your project includes a custom UUIDv7 validator.
- Always include `@ApiProperty` from `@nestjs/swagger` with clear `description` and `example`.
- DTOs and API always expose UUIDs as strings, never Binary or raw `_id`.

```typescript
// Example DTO (e.g., src/users/dto/user-response.dto.ts)
import { ApiProperty } from '@nestjs/swagger';
import { IsUUID, IsString, IsEmail } from 'class-validator';

export class UserResponseDto {
  @ApiProperty({
    description: 'Unique identifier for the user (UUIDv7)',
    example: '018a6f1b-8028-7fbf-9082-3a0810a76077',
    type: String,
  })
  @IsUUID('7') // Requires custom validator
  userUuid: string;

  @ApiProperty({ example: '<EMAIL>', type: String })
  @IsEmail()
  email: string;

  // ... other properties
}
```

### API Request Parameters (Path/Query)
- When UUIDs are part of a URL path (e.g., `/users/:userUuid`) or a query parameter, they are received as strings by the controller.
- Use NestJS's `ParseUUIDPipe` with `{ version: '7' }` for validation in `@Param()` or `@Query()`. **Note:** This requires a custom pipe for UUIDv7.

```typescript
// Example Controller (e.g., src/users/users.controller.ts)
import { Controller, Get, Param, ParseUUIDPipe, NotFoundException } from '@nestjs/common';
import { UsersService } from './users.service';
import { ApiTags, ApiOperation, ApiResponse, ApiParam } from '@nestjs/swagger';
import { UserResponseDto } from './dto/user-response.dto';

@ApiTags('Users')
@Controller('users')
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  @Get(':userUuid')
  @ApiOperation({ summary: 'Get a user by UUID' })
  @ApiParam({ name: 'userUuid', description: 'User UUIDv7 string', type: String, example: '018a6f1b-8028-7fbf-9082-3a0810a76077' })
  @ApiResponse({ status: 200, description: 'User found', type: UserResponseDto })
  @ApiResponse({ status: 404, description: 'User not found' })
  async findOne(@Param('userUuid', new ParseUUIDPipe({ version: '7' })) userUuid: string): Promise<UserResponseDto> {
    // 'userUuid' is a validated UUID string here
    const user = await this.usersService.findOne(userUuid);
    if (!user) {
      throw new NotFoundException('User not found');
    }
    return user;
  }
}
```

### Service Layer UUID Handling
- Service methods receive UUIDs as strings from controllers and use them directly in TypeORM queries.
- No conversion to binary or use of virtuals is needed; all UUIDs are handled as strings.

### Summary of UUID Transformations
- **Controller/DTO (Input to Service):** `string` (UUID from request) → used directly in TypeORM query
- **Service (Output to Controller/DTO):** `string` (UUID from DB/entity) → returned as is in API response

For detailed UUID schema patterns and storage guidelines, see [docs/UUID_USAGE_GUIDELINES.md](./UUID_USAGE_GUIDELINES.md).

---

**Note:** All UUID validation and transformation is performed at the application layer. PostgreSQL/YugabyteDB stores UUIDs as type `uuid`, but does not enforce the UUID version. Ensure your application logic enforces UUIDv7 compliance.


