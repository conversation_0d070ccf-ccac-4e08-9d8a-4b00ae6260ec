import { ApiProperty } from "@nestjs/swagger";

export class CreateRoleDto {
  @ApiProperty({ example: "Warehouse Manager", description: "Role name" })
  name: string;

  @ApiProperty({
    example: ["sales.view", "sales.edit"],
    description: "List of permissions",
    required: false,
  })
  permissions?: string[];

  @ApiProperty({
    example: "018ea2bb-6b8d-7e9a-8a2a-7e9a8a2a7e9a",
    description: "UUID of the warehouse this role belongs to",
  })
  warehouseUuid: string;
}
