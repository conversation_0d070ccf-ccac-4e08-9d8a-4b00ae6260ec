import { Module } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { PurchaseController } from "./purchase.controller";
import { PurchaseService } from "./purchase.service";
import { Purchase } from "./purchase.entity";
import { User } from "../users/user.entity";
import { Warehouse } from "../warehouses/warehouse.entity";
import { Supplier } from "../suppliers/supplier.entity";

@Module({
  imports: [TypeOrmModule.forFeature([Purchase, User, Warehouse, Supplier])],
  controllers: [PurchaseController],
  providers: [PurchaseService],
  exports: [PurchaseService],
})
export class PurchaseModule {}
