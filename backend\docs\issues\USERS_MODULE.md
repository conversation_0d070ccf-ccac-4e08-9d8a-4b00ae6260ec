# Users Module Documentation

**Location:** `backend/src/users/`

## Purpose
Manages user accounts, authentication, roles, and user-warehouse relationships.

## Structure
```
users/
├── users.controller.ts      # User CRUD operations
├── users.service.ts         # User business logic
├── users.schema.ts          # User data model
├── users.module.ts          # Module configuration
├── user_roles.service.ts    # Role management
├── user_roles.module.ts     # Role module
├── role.schema.ts           # Role data model
├── user_permissions.ts      # Permission definitions
├── default_roles.ts         # Default role configurations
└── dto/                     # Data transfer objects
    ├── create-user.dto.ts
    ├── update-user.dto.ts
    ├── update-user-and-password.dto.ts
    ├── change-password.dto.ts
    ├── create-role.dto.ts
    ├── update-role.dto.ts
    ├── assign-role.dto.ts
    ├── assign-van.dto.ts
    ├── update-warhouse.dto.ts
    ├── user.dto.ts
    ├── role.dto.ts
    └── update-company.dto.ts
```

## Key Features
- User CRUD operations
- Password management with bcrypt
- Role assignment and management
- Warehouse assignment
- Van assignment
- User type management (super/user)
- Soft delete functionality
- UUIDv7 primary keys

## Endpoints

### User Management
- `POST /users` - Create user
- `GET /users/list` - List all users (excluding deleted)
- `GET /users/by-warehouse/:warehouseUuid` - List users by warehouse
- `GET /users/list-raw` - List all users (including deleted)
- `GET /users/:uuid` - Get user by UUID
- `PATCH /users/:uuid/password` - Change user password
- `PATCH /users/:uuid/name` - Change user name
- `PATCH /users/:uuid/update-profile` - Update user profile
- `DELETE /users/:uuid` - Soft delete user
- `PATCH /users/:uuid/warehouse` - Change user warehouse
- `DELETE /users/all` - Hard delete all users

### Role Management
- `POST /users/roles` - Create a new role
- `GET /users/roles/warehouse/:warehouseUuid` - List roles by warehouse
- `GET /users/roles` - List all roles (excluding deleted)
- `GET /users/roles/raw` - List all roles (including deleted)
- `GET /users/permissions` - Get all available permissions
- `GET /users/roles/:uuid` - Get role by UUID
- `PATCH /users/roles/:uuid` - Update role
- `DELETE /users/roles/:uuid` - Delete role
- `PATCH /users/:uuid/assign-role` - Assign role to user
- `PATCH /users/:uuid/assign-van` - Assign van to user
- `GET /users/:uuid/role` - Get user's role

## Schema Fields

### User Schema
```typescript
{
  _id: Binary,                    // Primary key (UUIDv7)
  warehouseUuid: Binary,          // Warehouse reference
  email?: string,                 // User email (unique)
  name: string,                   // User name
  roleUuid?: Binary,              // Role reference
  userType: "super" | "user",     // User type
  vanUuid?: Binary,               // Van reference
  password?: string,              // Hashed password (bcrypt)
  isDeleted: boolean              // Soft delete flag
}
```

### Role Schema
```typescript
{
  _id: Binary,                    // Primary key (UUIDv7)
  name: string,                   // Role name
  permissions: string[],          // Array of permissions
  warehouseUuid: Binary,          // Warehouse reference
  isDeleted: boolean              // Soft delete flag
}
```

## Virtual Properties

### User Virtuals
- `uuid` - String representation of _id
- `warehouseUuidString` - String representation of warehouseUuid
- `roleUuidString` - String representation of roleUuid
- `vanUuidString` - String representation of vanUuid

### Role Virtuals
- `uuid` - String representation of _id
- `warehouseUuidString` - String representation of warehouseUuid

## DTOs

### Create User DTO
```typescript
{
  warehouseUuid: string,          // Required
  email?: string,                 // Optional
  name: string,                   // Required
  password?: string,              // Optional
  userType?: "super" | "user",    // Optional, defaults to "user"
  roleUuid?: string               // Optional
}
```

### Update User DTO
```typescript
{
  name: string                    // Required
}
```

### Change Password DTO
```typescript
{
  oldPassword: string,            // Required
  newPassword: string             // Required
}
```

### Create Role DTO
```typescript
{
  name: string,                   // Required
  permissions: string[],          // Required
  warehouseUuid: string           // Required
}
```

## Business Logic

### User Creation
- First user for a warehouse can be "super" user
- All subsequent users default to "user" type
- Password is hashed using bcrypt
- UUIDv7 is automatically generated
- User is assigned to specified warehouse

### Role Management
- Roles are warehouse-specific
- Permissions are predefined in `user_permissions.ts`
- Default roles are configured in `default_roles.ts`

### Password Management
- Passwords are hashed using bcrypt
- Password changes require old password verification
- Password validation ensures security

## Issues Found

### 🔴 Critical Issues
1. **Broken Virtual Implementation**
   - Location: `users.schema.ts` lines 75-85
   - Issue: `vanUuidString` virtual is incorrectly nested inside `uuid` virtual
   - Impact: Van UUID string representation will not work

2. **Spelling Error**
   - Location: `update-warhouse.dto.ts` (filename and class name)
   - Issue: "warhouse" instead of "warehouse"
   - Impact: API inconsistency and confusion

3. **Missing Timestamps**
   - Issue: No `createdAt` and `updatedAt` fields
   - Impact: No audit trail for user changes

4. **Missing Entity Fields**
   - Issue: No `createdBy` and `updatedBy` fields
   - Impact: No user tracking for creation/updates

### 🟡 Medium Priority Issues
1. **Inconsistent Schema Patterns**
   - Issue: Schema doesn't follow entity standards from `docs/ENTITY.md`
   - Impact: Inconsistent data model across modules

## Dependencies
- `@nestjs/mongoose` - MongoDB integration
- `bcrypt` - Password hashing
- `../utils/uuid7` - UUID generation and conversion

## Related Modules
- **Auth Module** - Uses user data for authentication
- **Warehouses Module** - References warehouse data
- **Vans Module** - References van data
- **Roles Module** - Manages role data

## Security Considerations
- Passwords are hashed using bcrypt
- User types control access levels
- Soft delete prevents data loss
- UUIDv7 provides secure identifiers

## Future Improvements
1. Fix virtual implementation bug
2. Correct spelling errors
3. Add missing entity fields
4. Implement proper audit trails
5. Add user activity logging 