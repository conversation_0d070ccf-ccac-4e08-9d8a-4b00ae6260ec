import 'package:flutter/material.dart';
import '../services/auth_service.dart';
import '../services/permission_service.dart';

/// Widget that protects content based on user permissions
class AuthGuard extends StatelessWidget {

  const AuthGuard({
    super.key,
    required this.child,
    this.requiredPermission,
    this.requiredRole,
    this.fallback,
  });
  final Widget child;
  final String? requiredPermission;
  final String? requiredRole;
  final Widget? fallback;

  @override
  Widget build(BuildContext context) {
    final authService = AuthService();
    final permissionService = PermissionService();

    // Check if user is authenticated
    if (!authService.isAuthenticated) {
      return fallback ?? const _UnauthorizedWidget();
    }

    // Check permission if required
    if (requiredPermission != null) {
      if (!permissionService.hasPermission(requiredPermission!)) {
        return fallback ?? const _UnauthorizedWidget();
      }
    }

    // Check role if required
    if (requiredRole != null) {
      if (!permissionService.hasRole(requiredRole!)) {
        return fallback ?? const _UnauthorizedWidget();
      }
    }

    return child;
  }
}

/// Widget that conditionally shows content based on permissions
class PermissionWidget extends StatelessWidget {

  const PermissionWidget({
    super.key,
    required this.child,
    this.permission,
    this.role,
    this.fallback,
  });
  final Widget child;
  final String? permission;
  final String? role;
  final Widget? fallback;

  @override
  Widget build(BuildContext context) {
    final permissionService = PermissionService();

    var hasAccess = true;

    // Check permission
    if (permission != null) {
      hasAccess = hasAccess && permissionService.hasPermission(permission!);
    }

    // Check role
    if (role != null) {
      hasAccess = hasAccess && permissionService.hasRole(role!);
    }

    if (hasAccess) {
      return child;
    } else {
      return fallback ?? const SizedBox.shrink();
    }
  }
}

/// Widget that shows different content based on user role
class RoleBasedWidget extends StatelessWidget {

  const RoleBasedWidget({
    super.key,
    this.adminWidget,
    this.mobileSaleAgentWidget,
    this.managerWidget,
    this.defaultWidget,
  });
  final Widget? adminWidget;
  final Widget? mobileSaleAgentWidget;
  final Widget? managerWidget;
  final Widget? defaultWidget;

  @override
  Widget build(BuildContext context) {
    final authService = AuthService();

    if (authService.isAdmin && adminWidget != null) {
      return adminWidget!;
    } else if (authService.isMobileSaleAgent && mobileSaleAgentWidget != null) {
      return mobileSaleAgentWidget!;
    } else if (authService.isManager && managerWidget != null) {
      return managerWidget!;
    } else {
      return defaultWidget ?? const SizedBox.shrink();
    }
  }
}

/// Default unauthorized widget
class _UnauthorizedWidget extends StatelessWidget {
  const _UnauthorizedWidget();

  @override
  Widget build(BuildContext context) => const Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.lock,
              size: 64,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text(
              'Access Denied',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.grey,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'You don\'t have permission to access this feature',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
} 