import { ApiProperty } from "@nestjs/swagger";
import {
  IsNotEmpty,
  IsString,
  IsUUID,
  IsOptional,
  IsNumber,
} from "class-validator";

export class CreateVanDto {
  @ApiProperty({ example: "Van 001", description: "Van name" })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    example: "d1e2f3a4-5678-4bcd-9e21-abcdef123456",
    description: "Warehouse UUID (required)",
  })
  @IsUUID()
  @IsNotEmpty()
  warehouseUuid: string;

  @ApiProperty({
    example: "ABC-123",
    description: "Van license plate",
    required: false,
  })
  @IsString()
  @IsOptional()
  licensePlate?: string;

  @ApiProperty({
    example: "Ford Transit",
    description: "Van model",
    required: false,
  })
  @IsString()
  @IsOptional()
  model?: string;

  @ApiProperty({ example: 2020, description: "Van year", required: false })
  @IsNumber()
  @IsOptional()
  year?: number;
}
