import { useState, useMemo } from 'react';
import { useQuery } from '@tanstack/react-query';
import { useAuth } from '@/contexts/AuthContext';
import { 
  getStockLevelsForStorage, 
  getStoragesByWarehouse, 
  getProductCategories,
  searchProducts,
  getProductsWithAdvancedFilters,
  Product, 
  StockLevel, 
  Storage,
  ProductFilterOptions,
  StockLevelWithProduct
} from '../api';
import { filterProducts } from '@/app/inventory/products/productsApi';
import { ProductCategory } from '../../products/productCategoriesApi';

interface StockLevelWithProductExtended extends StockLevelWithProduct {
  product: Product;
  sku?: string;
  barcode?: string;
  category?: string;
  price?: number;
  productCategoryUuidString?: string;
}

interface StockLevelsFilter {
  searchQuery: string;
  filterProductCategoryUuid: string;
  filterMinPrice: string;
  filterMaxPrice: string;
}

interface StockLevelsPagination {
  page: number;
  limit: number;
}

export const useStockLevelsData = () => {
  const { user, token } = useAuth();
  const warehouseUuid = user?.warehouseUuid;
  
  // State management
  const [selectedStorage, setSelectedStorage] = useState<string>("");
  const [filter, setFilter] = useState<StockLevelsFilter>({
    searchQuery: "",
    filterProductCategoryUuid: "",
    filterMinPrice: "",
    filterMaxPrice: ""
  });
  const [pagination, setPagination] = useState<StockLevelsPagination>({ page: 1, limit: 10 });

  // Memoize the filter and pagination objects to prevent infinite loops
  const filterString = JSON.stringify(filter);
  const paginationString = JSON.stringify(pagination);
  const memoizedFilter = useMemo(() => filter, [filterString]);
  const memoizedPagination = useMemo(() => pagination, [paginationString]);

  // Memoize the query key dependencies to prevent infinite loops
  const queryKey = useMemo(() => [
    'stockLevels', 
    warehouseUuid, 
    selectedStorage, 
    memoizedFilter, 
    memoizedPagination
  ], [warehouseUuid, selectedStorage, memoizedFilter, memoizedPagination]);

  // Query for stock levels data
  const { data: stockLevelsResponse, isLoading, error, refetch } = useQuery({
    queryKey,
    queryFn: async () => {
      if (!selectedStorage || !warehouseUuid) {
        return {
          data: [],
          total: 0,
          totalPages: 1,
          hasNext: false,
          hasPrev: false
        };
      }

      let productsResponse;
      
      // Use search if there's a search query, otherwise use basic filter
      if (memoizedFilter.searchQuery.trim()) {
        console.log('Using search with query:', memoizedFilter.searchQuery.trim());
        productsResponse = await searchProducts(
          warehouseUuid, 
          memoizedFilter.searchQuery.trim(), 
          memoizedPagination.page, 
          memoizedPagination.limit
        );
      } else {
        // Check if any filters are applied
        const hasFilters = memoizedFilter.filterProductCategoryUuid.trim() || 
                          memoizedFilter.filterMinPrice.trim() || 
                          memoizedFilter.filterMaxPrice.trim();
        
        if (hasFilters) {
          // Build filter options
          const filterOptions: ProductFilterOptions = {};
          if (memoizedFilter.filterProductCategoryUuid.trim()) {
            console.log('Adding category filter:', memoizedFilter.filterProductCategoryUuid.trim());
            filterOptions.productCategoryUuid = memoizedFilter.filterProductCategoryUuid.trim();
          }
          if (memoizedFilter.filterMinPrice.trim()) {
            const min = parseFloat(memoizedFilter.filterMinPrice);
            if (!isNaN(min)) filterOptions.minPrice = min;
          }
          if (memoizedFilter.filterMaxPrice.trim()) {
            const max = parseFloat(memoizedFilter.filterMaxPrice);
            if (!isNaN(max)) filterOptions.maxPrice = max;
          }
          
          console.log('Using advanced filter with options:', filterOptions);
          // Use advanced filtering endpoint that supports category filtering
          productsResponse = await getProductsWithAdvancedFilters(
            warehouseUuid, 
            filterOptions, 
            memoizedPagination.page, 
            memoizedPagination.limit
          );
        } else {
          // No filters applied, get all products
          console.log('No filters applied, getting all products');
          productsResponse = await filterProducts(warehouseUuid, {}, undefined, {
            page: memoizedPagination.page,
            limit: memoizedPagination.limit
          });
        }
      }
      
      // Get stock levels for all products in the storage
      const stockLevelsData = await getStockLevelsForStorage(selectedStorage);
      
      // Create a map of product UUID to stock level
      const stockLevelMap = new Map(
        stockLevelsData.map(stock => [stock.productUuid, stock])
      );
      
      // Get categories for mapping
      const categoriesData = await getProductCategories(warehouseUuid);
      const categoryMap = new Map(
        categoriesData.map(cat => [cat.uuid, cat.name])
      );
      
      // Handle different response formats
      const products = 'data' in productsResponse ? productsResponse.data : productsResponse;
      const total = 'total' in productsResponse ? productsResponse.total : productsResponse.meta?.total || 0;
      const hasNext = 'hasNext' in productsResponse ? productsResponse.hasNext : productsResponse.meta?.hasNext || false;
      const hasPrev = 'hasPrev' in productsResponse ? productsResponse.hasPrev : productsResponse.meta?.hasPrev || false;
      
      // Combine products with their stock levels
      const combinedData: StockLevelWithProductExtended[] = products.map(product => {
        const stockLevel = stockLevelMap.get(product.uuid);
        const categoryName = product.productCategoryUuidString ? categoryMap.get(product.productCategoryUuidString) : undefined;
        
        return {
          productUuid: product.uuid,
          productName: product.name,
          quantity: stockLevel?.quantity || 0,
          product: product,
          sku: product.sku,
          barcode: product.barcode,
          category: categoryName,
          price: product.price,
          productCategoryUuidString: product.productCategoryUuidString,
          productDescription: product.description,
          productSku: product.sku,
          productPrice: product.price,
          productCost: product.cost,
        };
      });
      
      return {
        data: combinedData,
        total,
        totalPages: Math.ceil(total / memoizedPagination.limit),
        hasNext,
        hasPrev
      };
    },
    enabled: !!warehouseUuid && !!selectedStorage && !!token,
  });

  // Query for storages
  const { data: storages = [], isLoading: isLoadingStorages } = useQuery({
    queryKey: ['storages', warehouseUuid],
    queryFn: async () => {
      if (!warehouseUuid) return [];
      const storagesData = await getStoragesByWarehouse(warehouseUuid);
      return storagesData.filter((storage: Storage) => !storage.isDeleted);
    },
    enabled: !!warehouseUuid && !!token,
  });

  // Query for categories
  const { data: categories = [], isLoading: isLoadingCategories } = useQuery({
    queryKey: ['productCategories', warehouseUuid],
    queryFn: async () => {
      if (!warehouseUuid) return [];
      return await getProductCategories(warehouseUuid);
    },
    enabled: !!warehouseUuid && !!token,
  });

  const stockLevels = stockLevelsResponse?.data || [];
  const total = stockLevelsResponse?.total || 0;
  const totalPages = stockLevelsResponse?.totalPages || 1;
  const hasNext = stockLevelsResponse?.hasNext || false;
  const hasPrev = stockLevelsResponse?.hasPrev || false;

  // Filter handlers
  const updateFilter = (newFilter: Partial<StockLevelsFilter>) => {
    setFilter(prev => ({ ...prev, ...newFilter }));
    setPagination(prev => ({ ...prev, page: 1 })); // Reset to first page when filtering
  };

  const clearFilters = () => {
    setFilter({
      searchQuery: "",
      filterProductCategoryUuid: "",
      filterMinPrice: "",
      filterMaxPrice: ""
    });
    setPagination({ page: 1, limit: 10 });
  };

  // Pagination handlers
  const handlePageChange = (page: number) => {
    if (page && !isNaN(page) && page > 0) {
      setPagination(prev => ({ ...prev, page }));
    } else {
      console.warn('Invalid page number:', page);
    }
  };

  // Storage handlers
  const handleStorageChange = (storageUuid: string) => {
    setSelectedStorage(storageUuid);
    setPagination({ page: 1, limit: 10 }); // Reset pagination when storage changes
  };

  // Check if any filters are active
  const hasActiveFilters = memoizedFilter.searchQuery || 
                          memoizedFilter.filterProductCategoryUuid || 
                          memoizedFilter.filterMinPrice || 
                          memoizedFilter.filterMaxPrice;

  return {
    // Data
    stockLevels,
    storages,
    categories,
    total,
    totalPages,
    hasNext,
    hasPrev,
    
    // State
    selectedStorage,
    filter: memoizedFilter,
    pagination: memoizedPagination,
    isLoading,
    isLoadingStorages,
    isLoadingCategories,
    error,
    
    // Actions
    updateFilter,
    clearFilters,
    handlePageChange,
    handleStorageChange,
    refetch,
    
    // Computed
    warehouseUuid,
    hasActiveFilters
  };
}; 