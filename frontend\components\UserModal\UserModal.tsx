import React, { useState, useEffect, useCallback, useRef } from 'react';
import { FiSearch, FiChevronLeft, FiChevronRight, FiLoader, FiX } from 'react-icons/fi';
import { useAuth } from '@/contexts/AuthContext';
import { fetchUsers } from '@/app/settings/users/usersApi';
import type { User } from '@/app/settings/users/usersApi';

export interface UserModalProps {
  isOpen: boolean;
  onSelect: (user: User) => void;
  onClose: () => void;
  disabled?: boolean;
  title?: string;
  warehouseUuid?: string; // Optional: if not provided, uses current user's warehouse
}

export function UserModal({
  isOpen,
  onSelect,
  onClose,
  disabled = false,
  title = 'Select User',
  warehouseUuid,
}: UserModalProps) {
  const { user: currentUser } = useAuth();
  const effectiveWarehouseUuid = warehouseUuid || currentUser?.warehouseUuid;
  
  const [searchTerm, setSearchTerm] = useState('');
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');
  const [users, setUsers] = useState<User[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalUsers, setTotalUsers] = useState(0);
  
  const pageSize = 20; // Users per page
  
  // Request debouncing
  const requestTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Handle escape key to close modal
  useEffect(() => {
    if (!isOpen) return;
    
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        e.preventDefault();
        onClose();
      }
    };
    
    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, onClose]);

  // Handle clicking outside modal to close
  const handleOverlayClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  // Load users function
  const loadUsers = useCallback(async (page: number, search: string = '') => {
    if (!effectiveWarehouseUuid) return;
    
    setIsLoading(true);
    setError(null);
    
    try {
      const allUsers = await fetchUsers(effectiveWarehouseUuid);
      
      // Filter users based on search term
      let filteredUsers = allUsers;
      if (search.trim()) {
        const searchLower = search.toLowerCase();
        filteredUsers = allUsers.filter(user => 
          user.name.toLowerCase().includes(searchLower) ||
          user.email?.toLowerCase().includes(searchLower) ||
          user.uuid.toLowerCase().includes(searchLower)
        );
      }
      
      // Calculate pagination
      const total = filteredUsers.length;
      const totalPages = Math.ceil(total / pageSize);
      const startIndex = (page - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      const paginatedUsers = filteredUsers.slice(startIndex, endIndex);
      
      setUsers(paginatedUsers);
      setTotalUsers(total);
      setTotalPages(totalPages);
      setCurrentPage(page);
    } catch (err) {
      console.error('Error loading users:', err);
      setError('Failed to load users. Please try again.');
    } finally {
      setIsLoading(false);
    }
  }, [effectiveWarehouseUuid]);

  // Load users when modal opens or search term changes
  useEffect(() => {
    if (!isOpen || !effectiveWarehouseUuid) return;
    
    loadUsers(1, debouncedSearchTerm);
  }, [isOpen, effectiveWarehouseUuid, loadUsers]);

  // Handle debounced search
  useEffect(() => {
    if (!isOpen || !effectiveWarehouseUuid) return;
    
    // Clear any existing timeout
    if (requestTimeoutRef.current) {
      clearTimeout(requestTimeoutRef.current);
    }
    
    // Set new timeout for debounced search
    requestTimeoutRef.current = setTimeout(() => {
      loadUsers(1, debouncedSearchTerm);
    }, 300);
    
    // Cleanup timeout on unmount or dependency change
    return () => {
      if (requestTimeoutRef.current) {
        clearTimeout(requestTimeoutRef.current);
      }
    };
  }, [debouncedSearchTerm, isOpen, effectiveWarehouseUuid, loadUsers]);

  // Cleanup effect for component unmount
  useEffect(() => {
    return () => {
      // Clear any pending timeouts
      if (requestTimeoutRef.current) {
        clearTimeout(requestTimeoutRef.current);
      }
    };
  }, []);

  // Handle page changes
  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= totalPages && !isLoading) {
      loadUsers(newPage, debouncedSearchTerm);
    }
  };

  // Reset state when modal opens
  useEffect(() => {
    if (isOpen) {
      setSearchTerm('');
      setDebouncedSearchTerm('');
      setError(null);
      setCurrentPage(1);
    }
  }, [isOpen]);

  // Update debounced search term when search term changes
  useEffect(() => {
    setDebouncedSearchTerm(searchTerm);
  }, [searchTerm]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" onClick={handleOverlayClick}>
      <div className="bg-white rounded-lg shadow-lg max-w-lg w-full mx-4 h-[600px] overflow-hidden flex flex-col">
        <div className="p-4 border-b border-gray-200 flex-shrink-0 flex items-center justify-between">
          <div>
            <h3 className="text-lg font-bold text-gray-900">
              {title}
            </h3>
            {totalUsers > 0 && (
              <p className="text-xs text-gray-600 mt-1">
                {totalUsers} users found
              </p>
            )}
          </div>
          <button
            onClick={onClose}
            className="p-1 rounded-full hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors"
            title="Close"
            aria-label="Close modal"
          >
            <FiX className="h-5 w-5 text-gray-500" />
          </button>
        </div>
        
        <div className="flex-1 flex flex-col min-h-0">
          {/* Scrollable Content Area */}
          <div className="flex-1 overflow-y-auto p-4">
            {/* Search Bar */}
            <div className="mb-5">
              <div className="relative">
                <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <input
                  type="text"
                  placeholder="Search users by name, email, or UUID..."
                  value={searchTerm}
                  onChange={(e) => {
                    if (disabled) return;
                    setSearchTerm(e.target.value);
                  }}
                  className={`w-full pl-9 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
                  autoFocus
                  disabled={disabled}
                />
                {isLoading && (
                  <FiLoader className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4 animate-spin" />
                )}
              </div>
            </div>

            {/* Error Display */}
            {error && (
              <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
                <div className="text-red-700 text-sm">
                  {error}
                </div>
              </div>
            )}

            {/* Loading State */}
            {isLoading && (
              <div className="flex items-center justify-center py-8">
                <FiLoader className="animate-spin h-6 w-6 text-blue-500" />
                <span className="ml-2 text-gray-600">Loading users...</span>
              </div>
            )}

            {/* User List */}
            {!isLoading && users.length > 0 && (
              <div className="space-y-2">
                {users.map((user) => (
                  <div
                    key={user.uuid}
                    className={`p-3 border border-gray-200 rounded-lg transition-all duration-200 ${
                      disabled 
                        ? 'opacity-50 cursor-not-allowed' 
                        : 'cursor-pointer hover:bg-gray-50 hover:border-blue-300'
                    }`}
                    onClick={() => {
                      if (disabled) return;
                      onSelect(user);
                      onClose();
                    }}
                  >
                    <div className="font-medium text-gray-900 text-sm">
                      {user.name}
                    </div>
                    <div className="text-xs text-gray-600 mt-1">
                      {user.email && `${user.email} • `}
                      {user.userType && `${user.userType} • `}
                      {user.uuid.substring(0, 8)}...
                    </div>
                  </div>
                ))}
              </div>
            )}
            
            {/* No Results */}
            {!isLoading && users.length === 0 && searchTerm && (
              <div className="text-center py-8">
                <p className="text-gray-500 text-sm">No users found matching "{searchTerm}"</p>
              </div>
            )}

            {/* Empty State */}
            {!isLoading && users.length === 0 && !searchTerm && (
              <div className="text-center py-8">
                <p className="text-gray-500 text-sm">No users available</p>
              </div>
            )}

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="flex items-center justify-between mt-4 pt-4 border-t border-gray-200">
                <button
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage <= 1 || isLoading || disabled}
                  className="flex items-center px-3 py-1 text-sm text-gray-600 hover:text-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <FiChevronLeft className="h-4 w-4 mr-1" />
                  Previous
                </button>
                
                <span className="text-sm text-gray-600">
                  Page {currentPage} of {totalPages}
                </span>
                
                <button
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage >= totalPages || isLoading || disabled}
                  className="flex items-center px-3 py-1 text-sm text-gray-600 hover:text-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Next
                  <FiChevronRight className="h-4 w-4 ml-1" />
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
} 