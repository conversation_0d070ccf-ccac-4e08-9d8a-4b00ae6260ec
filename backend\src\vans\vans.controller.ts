import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Query,
  Patch,
  Delete,
  UseGuards,
  ParseUUIDPipe,
} from "@nestjs/common";
import {
  ApiOperation,
  ApiBody,
  ApiOkResponse,
  ApiTags,
  ApiQuery,
} from "@nestjs/swagger";
import { VansService } from "./vans.service.typeorm";
import { CreateVanDto } from "./dto/create-van.dto";
import { UpdateVanDto } from "./dto/update-van.dto";
import { FilterVanDto } from "./dto/filter-van.dto";
import { VanDto } from "./dto/van.dto";
@ApiTags("vans")
@Controller("vans")
export class VansController {
  constructor(private readonly vansService: VansService) {}

  @Post()
  @ApiOperation({
    summary: "Create a new van with automatic truck storage creation",
  })
  @ApiBody({ type: CreateVanDto })
  @ApiOkResponse({ description: "Returns the created van.", type: VanDto })
  async createVan(@Body() dto: CreateVanDto): Promise<VanDto> {
    return this.vansService.createVan(dto);
  }

  @Get()
  @ApiOperation({ summary: "List vans with optional filtering" })
  @ApiQuery({
    name: "warehouseUuid",
    required: false,
    description: "Filter by warehouse UUID",
  })
  @ApiQuery({
    name: "name",
    required: false,
    description:
      "Filter by van name (partial match). Empty string returns all vans.",
  })
  @ApiQuery({
    name: "licensePlate",
    required: false,
    description: "Filter by license plate (partial match)",
  })
  @ApiQuery({
    name: "model",
    required: false,
    description: "Filter by model (partial match)",
  })
  @ApiOkResponse({ description: "Returns the list of vans.", type: [VanDto] })
  async listVans(@Query() filter: FilterVanDto): Promise<VanDto[]> {
    return this.vansService.listVans(filter);
  }

  @Get("raw-list")
  @ApiOperation({ summary: "List all vans (no filtering, admin/debug only)" })
  @ApiOkResponse({
    description: "Returns all vans, ignoring filters.",
    type: [VanDto],
  })
  async listAllVans(): Promise<VanDto[]> {
    return this.vansService.listAllVans();
  }

  @Get(":uuid")
  @ApiOperation({ summary: "Get a van by UUID" })
  @ApiOkResponse({
    description: "Returns the van with the specified UUID.",
    type: VanDto,
  })
  async getVanByUuid(@Param("uuid", ParseUUIDPipe) uuid: string): Promise<VanDto> {
    return this.vansService.getVanByUuid(uuid);
  }

  @Patch(":uuid")
  @ApiOperation({ summary: "Update a van (single endpoint for all fields)" })
  @ApiBody({ type: UpdateVanDto })
  @ApiOkResponse({ description: "Returns the updated van.", type: VanDto })
  async updateVan(
    @Param("uuid", ParseUUIDPipe) uuid: string,
    @Body() dto: UpdateVanDto,
  ): Promise<VanDto> {
    return this.vansService.updateVan(uuid, dto);
  }

  @Delete(":uuid")
  @ApiOperation({ summary: "Soft delete a van (sets isDeleted=true)" })
  @ApiOkResponse({ description: "Returns the soft-deleted van.", type: VanDto })
  async softDeleteVan(@Param("uuid", ParseUUIDPipe) uuid: string): Promise<VanDto> {
    return this.vansService.softDeleteVan(uuid);
  }

  @Delete("all")
  @ApiOperation({
    summary: "Soft-delete ALL vans (admin/debug only, dangerous)",
  })
  @ApiOkResponse({ description: "Returns the number of vans soft-deleted." })
  async deleteAllVans(): Promise<{ deletedCount: number }> {
    return this.vansService.deleteAllVans();
  }
}
