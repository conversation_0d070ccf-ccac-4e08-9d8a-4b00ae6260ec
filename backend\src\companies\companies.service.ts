import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";
import { Uuid7 } from "../utils/uuid7";
import { Company } from "./company.entity";
import { CreateCompanyDto } from "./dto/create-company.dto";
import { UpdateCompanyDto } from "./dto/update-company.dto";
import { CompanyResponseDto } from "./dto/company-response.dto";
import { UsersService } from "../users/users.service";

@Injectable()
export class CompaniesService {
  constructor(
    @InjectRepository(Company) private companyRepository: Repository<Company>,
    private usersService: UsersService,
  ) {}

  private toCompanyDto(company: Company): CompanyResponseDto {
    return {
      uuid: company.id,
      userUuidString: company.userUuid,
      name: company.name,
      nif: company.nif,
      rc: company.rc,
      articleNumber: company.articleNumber,
      address: company.address,
      website: company.website,
      phoneNumber: company.phoneNumber,
      isDeleted: company.isDeleted,
      createdAt: company.createdAt,
      updatedAt: company.updatedAt,
    };
  }

  async create(
    createCompanyDto: CreateCompanyDto,
  ): Promise<CompanyResponseDto> {
    try {
      const newCompany = this.companyRepository.create({
        id: Company.generateId(),
        userUuid: createCompanyDto.userUuid,
        name: createCompanyDto.name,
        nif: createCompanyDto.nif,
        rc: createCompanyDto.rc,
        articleNumber: createCompanyDto.articleNumber,
        address: createCompanyDto.address,
        website: createCompanyDto.website,
        phoneNumber: createCompanyDto.phoneNumber,
      });

      const savedCompany = await this.companyRepository.save(newCompany);
      return this.toCompanyDto(savedCompany);
    } catch (error) {
      if (error.code === '23505') { // PostgreSQL unique constraint violation
        throw new BadRequestException("Company with this NIF already exists");
      }
      throw error;
    }
  }

  async findAll(): Promise<CompanyResponseDto[]> {
    const companies = await this.companyRepository.find({
      where: { isDeleted: false }
    });
    return companies.map((company) => this.toCompanyDto(company));
  }

  async findByUserUuid(userUuid: string): Promise<CompanyResponseDto[]> {
    try {
      // First, check if the user exists and is a 'super' type user
      const user = await this.usersService.findByUuid(userUuid);
      if (!user) {
        throw new NotFoundException("User not found");
      }

      if (user.userType !== "super") {
        throw new BadRequestException("Only super users can have companies");
      }

      const companies = await this.companyRepository.find({
        where: {
          userUuid: userUuid,
          isDeleted: false,
        }
      });

      // If no companies exist for this user, create a default company
      if (companies.length === 0) {
        const defaultCompany = await this.createDefaultCompanyForUser(user);
        return [this.toCompanyDto(defaultCompany)];
      }

      return companies.map((company) => this.toCompanyDto(company));
    } catch (error) {
      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException("Invalid user UUID");
    }
  }

  async findOne(uuid: string): Promise<CompanyResponseDto> {
    try {
      const company = await this.companyRepository.findOne({
        where: {
          id: uuid,
          isDeleted: false,
        }
      });

      if (!company) {
        throw new NotFoundException("Company not found");
      }

      return this.toCompanyDto(company);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException("Invalid company UUID");
    }
  }

  async update(
    uuid: string,
    updateCompanyDto: UpdateCompanyDto,
  ): Promise<CompanyResponseDto> {
    try {
      const company = await this.companyRepository.findOne({
        where: {
          id: uuid,
          isDeleted: false,
        }
      });

      if (!company) {
        throw new NotFoundException("Company not found");
      }

      // Update only provided fields
      const updateData: any = {};
      if (updateCompanyDto.name !== undefined)
        updateData.name = updateCompanyDto.name;
      if (updateCompanyDto.nif !== undefined)
        updateData.nif = updateCompanyDto.nif;
      if (updateCompanyDto.rc !== undefined)
        updateData.rc = updateCompanyDto.rc;
      if (updateCompanyDto.articleNumber !== undefined)
        updateData.articleNumber = updateCompanyDto.articleNumber;
      if (updateCompanyDto.address !== undefined)
        updateData.address = updateCompanyDto.address;
      if (updateCompanyDto.website !== undefined)
        updateData.website = updateCompanyDto.website;
      if (updateCompanyDto.phoneNumber !== undefined)
        updateData.phoneNumber = updateCompanyDto.phoneNumber;

      await this.companyRepository.update(uuid, updateData);
      
      const updatedCompany = await this.companyRepository.findOne({
        where: { id: uuid }
      });

      return this.toCompanyDto(updatedCompany);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      if (error.code === '23505') { // PostgreSQL unique constraint violation
        throw new BadRequestException("Company with this NIF already exists");
      }
      throw new BadRequestException("Invalid company UUID");
    }
  }

  async remove(uuid: string): Promise<{ message: string }> {
    try {
      const company = await this.companyRepository.findOne({
        where: {
          id: uuid,
          isDeleted: false,
        }
      });

      if (!company) {
        throw new NotFoundException("Company not found");
      }

      await this.companyRepository.update(uuid, { isDeleted: true });

      return { message: "Company soft deleted successfully" };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException("Invalid company UUID");
    }
  }

  private async createDefaultCompanyForUser(
    user: any,
  ): Promise<Company> {
    const defaultCompany = this.companyRepository.create({
      id: Company.generateId(),
      userUuid: user.uuid,
      name: `${user.name}'s Company`,
      nif: `NIF-${user.uuid.substring(0, 8)}`,
      rc: `RC-${user.uuid.substring(0, 8)}`,
      articleNumber: `ART-${user.uuid.substring(0, 8)}`,
      address: "Default Address - Please Update",
      phoneNumber: "+0000000000",
      website: undefined, // Optional field
    });

    return await this.companyRepository.save(defaultCompany);
  }

  async deleteAllCompanies(): Promise<{
    message: string;
    deletedCount: number;
  }> {
    const result = await this.companyRepository.delete({});
    return {
      message: "All companies hard deleted successfully",
      deletedCount: result.affected || 0,
    };
  }
} 