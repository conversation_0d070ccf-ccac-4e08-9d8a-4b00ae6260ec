"use client";
import React, { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/contexts/AuthContext";
import { enhancedLogin, legacyLogin } from "./authApi";
import BackendS<PERSON>us<PERSON>hecker from "@/components/BackendStatusChecker";
import NetworkStatusChecker from "@/components/NetworkStatusChecker";
import { isConnectionUnstable, getConnectionAdvice } from "@/utils/networkUtils";

const BACKEND_URL = process.env.NEXT_PUBLIC_API_BASE_URL || process.env.NEXT_PUBLIC_BACKEND_URL || "http://localhost:8000";

// NOTE: The backend must redirect to /auth/callback with token and user as query params after Google login.
// Example: res.redirect(`https://your-frontend-domain.com/auth/callback?token=JWT_TOKEN&user=${encodeURIComponent(JSON.stringify(user))}`);

export default function AuthPage() {
  const router = useRouter();
  const { user, loading, logout, checkUserExists } = useAuth();
  const [checkingAuth, setCheckingAuth] = useState(true);
  const [googleLoading, setGoogleLoading] = useState(false);
  // State for custom login (moved to top)
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [loginError, setLoginError] = useState("");
  const [isLoggingIn, setIsLoggingIn] = useState(false);
  const [backendStatus, setBackendStatus] = useState<'checking' | 'online' | 'offline' | 'error'>('checking');
  const [networkStatus, setNetworkStatus] = useState<'checking' | 'good' | 'slow' | 'unstable' | 'offline'>('checking');
  const { login } = useAuth();

  // Check for error parameters in URL
  useEffect(() => {
    const params = new URLSearchParams(window.location.search);
    const error = params.get("error");
    if (error) {
      switch (error) {
        case "login_failed":
          setLoginError("Google sign-in failed. Please try again.");
          break;
        case "callback_failed":
          setLoginError("Authentication callback failed. This could be due to:\n\n1. Backend server not running\n2. Google OAuth configuration issues\n3. Network connectivity problems\n\nPlease check that:\n• Backend server is running on localhost:8000\n• Google OAuth credentials are properly configured\n• You have a stable internet connection");
          break;
        case "timeout":
          setLoginError("Authentication timed out. This could be due to:\n\n1. Slow or unstable internet connection\n2. Backend server not responding\n3. Google OAuth configuration issues\n\nPlease try again when your connection is more stable.");
          break;
        case "account_locked":
          setLoginError("Account is temporarily locked due to too many failed attempts. Please try again later.");
          break;
        case "rate_limited":
          setLoginError("Too many login attempts. Please wait before trying again.");
          break;
        default:
          setLoginError("An authentication error occurred. Please try again.");
      }
      // Clear the error from URL
      window.history.replaceState({}, document.title, window.location.pathname);
    }
  }, []);

  useEffect(() => {
    const verifyUser = async () => {
      if (!loading) {
        if (user) {
          // Check with backend if user still exists
          const exists = await checkUserExists(user.uuid || "");
          if (exists) {
            router.replace("/dashboard");
          } else {
            // Clear session and show error
            logout();
            setLoginError("Your session has expired. Please sign in again.");
          }
        } else {
          setCheckingAuth(false);
        }
      }
    };
    verifyUser();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [router, user, loading]);

  // Redirect to backend Google OAuth endpoint
  const handleGoogleLogin = async () => {
    setGoogleLoading(true);
    setLoginError("");
    
    // First check if backend is accessible
    if (backendStatus === 'offline') {
      setLoginError("Cannot connect to backend server. Please ensure the backend is running on localhost:8000 before trying Google sign-in.\n\nIf you just started the backend, wait a few seconds and try again.");
      setGoogleLoading(false);
      return;
    }
    
    // Check if network connection is unstable
    if (isConnectionUnstable()) {
      const advice = getConnectionAdvice();
      setLoginError(`Network connection issue detected. ${advice || 'Please check your internet connection and try again.'}`);
      setGoogleLoading(false);
      return;
    }
    
    console.log("Initiating Google OAuth flow...");
    console.log("Backend URL:", BACKEND_URL);
    console.log("Google OAuth endpoint:", `${BACKEND_URL}/auth/google`);
    
    // Set a timeout for the Google OAuth redirect
    const redirectTimeout = setTimeout(() => {
      if (googleLoading) {
        setGoogleLoading(false);
        setLoginError("Google sign-in is taking longer than expected. This could be due to:\n\n1. Slow or unstable internet connection\n2. Backend server not responding\n3. Google OAuth configuration issues\n\nPlease check that:\n• You have a stable internet connection\n• Backend server is running on localhost:8000\n• Google OAuth credentials are properly configured\n\nIf your internet is unstable, try:\n• Using a wired connection instead of WiFi\n• Moving closer to your router\n• Trying again when your connection is more stable");
      }
    }, 10000); // Reduced to 10 seconds for faster feedback
    
    try {
      // Double-check backend connectivity before redirecting
      try {
        const healthCheck = await fetch('/api/auth/health', {
          method: 'GET',
          headers: { 'Content-Type': 'application/json' },
          signal: AbortSignal.timeout(5000) // 5 second timeout
        });
        
        if (!healthCheck.ok) {
          throw new Error(`Backend health check failed: ${healthCheck.status}`);
        }
      } catch (healthError) {
        clearTimeout(redirectTimeout);
        setGoogleLoading(false);
        setLoginError("Backend server is not accessible. Please ensure:\n\n1. Backend server is running on localhost:8000\n2. No firewall is blocking the connection\n3. Check backend logs for any startup errors\n\nIf you just started the backend, wait a few seconds and try again.");
        return;
      }
      
      // Clear the timeout since we're about to redirect
      clearTimeout(redirectTimeout);
      
      // Redirect to Google OAuth
      window.location.href = `${BACKEND_URL}/auth/google`;
    } catch (error) {
      clearTimeout(redirectTimeout);
      console.error("Error redirecting to Google OAuth:", error);
      setGoogleLoading(false);
      setLoginError("Failed to redirect to Google sign-in. This might be due to network connectivity issues. Please try again when your connection is more stable.");
    }
  };

  if (checkingAuth || loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="loader mb-4" />
        <p className="text-lg font-medium">Checking authentication...</p>
      </div>
    );
  }

  // Enhanced login handler with better error handling
  const handleCustomLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoginError("");
    setIsLoggingIn(true);
    
    try {
      // Try enhanced login first
      const response = await enhancedLogin({
        identifier: email,
        password,
        clientIp: 'frontend', // Will be overridden by backend
        userAgent: navigator.userAgent,
      });

      // Enhanced login successful
      login(response.user, response.accessToken, response.refreshToken);
      router.replace("/dashboard");
      
    } catch (error) {
      console.error('Enhanced login failed:', error);
      
      // Check if it's a rate limiting or account lockout error
      if (error instanceof Error) {
        if (error.message.includes('Too many login attempts') || 
            error.message.includes('Account locked') ||
            error.message.includes('429') ||
            error.message.includes('423')) {
          setLoginError("Too many failed login attempts. Your account is temporarily locked. Please try again later.");
          return;
        }
        
        if (error.message.includes('Invalid credentials') || 
            error.message.includes('401')) {
          setLoginError("Invalid email or password. Please check your credentials and try again.");
          return;
        }
        
        // For other errors, try legacy login as fallback
        try {
          console.log('Trying legacy login as fallback...');
          const legacyResponse = await legacyLogin({
            identifier: email,
            password,
          });
          
          // Legacy login successful
          login(legacyResponse.user, legacyResponse.accessToken, legacyResponse.refreshToken || '');
          router.replace("/dashboard");
          return;
          
        } catch (legacyError) {
          console.error('Legacy login also failed:', legacyError);
          if (legacyError instanceof Error) {
            setLoginError(legacyError.message || "Login failed. Please check your credentials and try again.");
          } else {
            setLoginError("Login failed. Please try again.");
          }
        }
      } else {
        setLoginError("An unexpected error occurred. Please try again.");
      }
    } finally {
      setIsLoggingIn(false);
    }
  };

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gray-50">
      <div className="bg-white shadow-lg rounded-lg p-8 max-w-md w-full">
        <h1 className="text-2xl font-bold mb-6 text-center">Sign in to Dido Distribution</h1>
        
        {/* Status Indicators */}
        <div className="mb-4 space-y-2">
          <div className="p-3 rounded-lg text-sm bg-gray-50">
            <BackendStatusChecker 
              onStatusChange={setBackendStatus}
              showDetails={true}
            />
          </div>
          <div className="p-3 rounded-lg text-sm bg-gray-50">
            <NetworkStatusChecker 
              onStatusChange={setNetworkStatus}
              showDetails={true}
            />
          </div>
        </div>

        <form onSubmit={handleCustomLogin} className="space-y-4 mb-6">
          <input
            type="email"
            value={email}
            onChange={e => setEmail(e.target.value)}
            placeholder="Email"
            className="w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-200"
            required
            disabled={isLoggingIn}
          />
          <input
            type="password"
            value={password}
            onChange={e => setPassword(e.target.value)}
            placeholder="Password"
            className="w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-200"
            required
            disabled={isLoggingIn}
          />
          {loginError && (
            <div className="text-red-600 text-sm text-center p-3 bg-red-50 rounded-lg border border-red-200 whitespace-pre-line">
              {loginError}
            </div>
          )}
          <button
            type="submit"
            disabled={isLoggingIn || backendStatus === 'offline'}
            className="w-full py-3 px-4 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
          >
            {isLoggingIn ? (
              <>
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                Signing in...
              </>
            ) : (
              "Sign in"
            )}
          </button>
        </form>
        <div className="flex items-center my-4">
          <div className="flex-grow border-t border-gray-200" />
          <span className="mx-2 text-gray-400 text-xs">OR</span>
          <div className="flex-grow border-t border-gray-200" />
        </div>
        <button
          onClick={handleGoogleLogin}
          disabled={googleLoading || isLoggingIn || backendStatus === 'offline' || networkStatus === 'offline'}
          className="w-full flex items-center justify-center gap-2 py-3 px-4 border border-gray-300 rounded-lg bg-white hover:bg-gray-100 transition text-gray-700 font-medium shadow-sm disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {googleLoading ? (
            <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600"></div>
          ) : (
            <svg className="w-5 h-5" viewBox="0 0 48 48">
              <g>
                <path fill="#4285F4" d="M24 9.5c3.54 0 6.29 1.52 7.74 2.79l5.67-5.67C33.54 3.22 29.3 1 24 1 14.82 1 6.99 6.98 3.69 15.14l6.97 5.41C12.22 14.05 17.61 9.5 24 9.5z"/>
                <path fill="#34A853" d="M46.1 24.55c0-1.64-.15-3.22-.42-4.74H24v9.01h12.42c-.54 2.85-2.17 5.27-4.63 6.91l7.11 5.53C43.98 37.22 46.1 31.37 46.1 24.55z"/>
                <path fill="#FBBC05" d="M10.66 28.55a14.7 14.7 0 010-9.1l-6.97-5.41A23.99 23.99 0 001 24c0 3.87.92 7.53 2.55 10.76l7.10-6.21z"/>
                <path fill="#EA4335" d="M24 47c6.48 0 11.93-2.15 15.9-5.85l-7.11-5.53c-2.01 1.36-4.6 2.16-8.79 2.16-6.39 0-11.78-4.55-13.64-10.66l-7.10 6.21C6.99 41.02 14.82 47 24 47z"/>
                <path fill="none" d="M1 1h46v46H1z"/>
              </g>
            </svg>
          )}
          {googleLoading ? "Redirecting to Google..." : "Sign in with Google"}
        </button>
        
        {/* Troubleshooting Information */}
        {backendStatus === 'offline' && (
          <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
            <h3 className="text-sm font-medium text-yellow-800 mb-2">Backend Server Offline</h3>
            <p className="text-xs text-yellow-700">
              The backend server is not accessible. Please ensure:
            </p>
            <ul className="text-xs text-yellow-700 mt-1 list-disc list-inside">
              <li>Backend server is running on localhost:8000</li>
              <li>No firewall is blocking the connection</li>
              <li>Check backend logs for any startup errors</li>
            </ul>
          </div>
        )}
        
        {networkStatus === 'unstable' && (
          <div className="mt-4 p-3 bg-orange-50 border border-orange-200 rounded-lg">
            <h3 className="text-sm font-medium text-orange-800 mb-2">Unstable Network Connection</h3>
            <p className="text-xs text-orange-700">
              Your internet connection appears to be unstable. This may cause authentication issues:
            </p>
            <ul className="text-xs text-orange-700 mt-1 list-disc list-inside">
              <li>Try using a wired connection instead of WiFi</li>
              <li>Move closer to your router</li>
              <li>Wait a few minutes and try again</li>
              <li>Authentication may take longer than usual</li>
            </ul>
          </div>
        )}
        
        {networkStatus === 'slow' && (
          <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <h3 className="text-sm font-medium text-blue-800 mb-2">Slow Network Connection</h3>
            <p className="text-xs text-blue-700">
              Your internet connection is slow. Authentication may take longer than usual.
            </p>
          </div>
        )}
        
        <p className="mt-6 text-center text-sm text-gray-500">
          By signing in, you agree to our <a href="#" className="underline">Terms</a> and <a href="#" className="underline">Privacy Policy</a>.
        </p>
      </div>
    </div>
  );
}

