/* Map Styles for Route Management */

/* Custom marker styles */
.custom-marker {
  background: transparent;
  border: none;
}

.route-stop-marker {
  background: transparent;
  border: none;
}

/* Popup styles */
.customer-popup .leaflet-popup-content-wrapper {
  border-radius: 8px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.customer-popup .leaflet-popup-content {
  margin: 0;
  padding: 0;
}

.route-stop-popup .leaflet-popup-content-wrapper {
  border-radius: 8px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.route-stop-popup .leaflet-popup-content {
  margin: 0;
  padding: 0;
}

/* Map container styles */
.leaflet-container {
  font-family: inherit;
}

/* Ensure map controls are properly styled */
.leaflet-control-zoom {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.leaflet-control-zoom a {
  background-color: white;
  color: #374151;
  border: none;
  border-radius: 0;
}

.leaflet-control-zoom a:hover {
  background-color: #f3f4f6;
  color: #111827;
}

.leaflet-control-zoom a:first-child {
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
}

.leaflet-control-zoom a:last-child {
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
}

/* Attribution styles */
.leaflet-control-attribution {
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 4px;
  font-size: 11px;
  color: #6b7280;
}

/* Ensure proper z-index for overlays */
.leaflet-overlay-pane {
  z-index: 400;
}

.leaflet-marker-pane {
  z-index: 600;
}

.leaflet-popup-pane {
  z-index: 1000;
}

/* Ensure map doesn't interfere with modals */
.leaflet-container {
  z-index: 1;
}

/* Modal z-index override */
.modal-overlay {
  z-index: 9999 !important;
}

/* Customer name tooltips */
.customer-name-tooltip {
  background-color: rgba(255, 255, 255, 0.95) !important;
  color: #374151 !important;
  border: 1px solid #e5e7eb !important;
  border-radius: 4px !important;
  font-size: 12px !important;
  font-weight: 500 !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
  padding: 4px 8px !important;
  white-space: nowrap !important;
  pointer-events: none !important;
}

.customer-name-tooltip::before {
  border-top-color: #e5e7eb !important;
}

/* Customer name labels (fallback) */
.customer-name-label-container {
  background: transparent !important;
  border: none !important;
}

.customer-name-label {
  background-color: rgba(255, 255, 255, 0.95);
  color: #374151;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  border: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  white-space: nowrap;
  pointer-events: none;
  transform: translateY(-45px);
  z-index: 1000;
} 