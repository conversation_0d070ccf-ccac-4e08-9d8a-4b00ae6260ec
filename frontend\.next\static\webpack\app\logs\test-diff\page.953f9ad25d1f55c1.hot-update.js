"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/logs/test-diff/page",{

/***/ "(app-pages-browser)/./app/logs/components/LogDetailsModal.tsx":
/*!*************************************************!*\
  !*** ./app/logs/components/LogDetailsModal.tsx ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LogDetailsModal: function() { return /* binding */ LogDetailsModal; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_FiCalendar_FiChevronDown_FiChevronRight_FiDatabase_FiFileText_FiTag_FiUser_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=FiCalendar,FiChevronDown,FiChevronRight,FiDatabase,FiFileText,FiTag,FiUser,FiX!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n\nvar _s = $RefreshSig$();\n\n\nfunction LogDetailsModal(param) {\n    let { isOpen, log, onClose, userName } = param;\n    _s();\n    // State for expandable tabs\n    const [showOldDataTab, setShowOldDataTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showNewDataTab, setShowNewDataTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Handle escape key to close modal\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!isOpen) return;\n        const handleKeyDown = (e)=>{\n            if (e.key === \"Escape\") {\n                e.preventDefault();\n                onClose();\n            }\n        };\n        window.addEventListener(\"keydown\", handleKeyDown);\n        return ()=>window.removeEventListener(\"keydown\", handleKeyDown);\n    }, [\n        isOpen,\n        onClose\n    ]);\n    // Handle clicking outside modal to close\n    const handleOverlayClick = (e)=>{\n        if (e.target === e.currentTarget) {\n            onClose();\n        }\n    };\n    // Format date for display\n    const formatDate = (date)=>{\n        const dateObj = typeof date === \"string\" ? new Date(date) : date;\n        return dateObj.toLocaleString(\"en-US\", {\n            year: \"numeric\",\n            month: \"long\",\n            day: \"numeric\",\n            hour: \"2-digit\",\n            minute: \"2-digit\",\n            second: \"2-digit\"\n        });\n    };\n    // Get operation color based on operation type\n    const getOperationColor = (operation)=>{\n        const op = operation.toLowerCase();\n        if (op.includes(\"create\") || op.includes(\"add\")) {\n            return \"bg-green-100 text-green-800 border-green-200\";\n        } else if (op.includes(\"update\") || op.includes(\"edit\") || op.includes(\"modify\")) {\n            return \"bg-blue-100 text-blue-800 border-blue-200\";\n        } else if (op.includes(\"delete\") || op.includes(\"remove\")) {\n            return \"bg-red-100 text-red-800 border-red-200\";\n        } else if (op.includes(\"cancel\") || op.includes(\"reject\")) {\n            return \"bg-orange-100 text-orange-800 border-orange-200\";\n        } else if (op.includes(\"approve\") || op.includes(\"complete\")) {\n            return \"bg-purple-100 text-purple-800 border-purple-200\";\n        } else if (op.includes(\"transfer\") || op.includes(\"move\")) {\n            return \"bg-indigo-100 text-indigo-800 border-indigo-200\";\n        } else if (op.includes(\"adjust\") || op.includes(\"modify\")) {\n            return \"bg-yellow-100 text-yellow-800 border-yellow-200\";\n        } else {\n            return \"bg-gray-100 text-gray-800 border-gray-200\";\n        }\n    };\n    // Format JSON data for display\n    const formatJsonData = (data)=>{\n        try {\n            return JSON.stringify(data, null, 2);\n        } catch (error) {\n            return \"Invalid JSON data\";\n        }\n    };\n    // Check if the log data contains delta changes\n    const hasDeltaChanges = (data)=>{\n        return data && data.changes && typeof data.changes === \"object\";\n    };\n    // Extract raw old data from delta changes\n    const extractOldData = (data)=>{\n        if (!hasDeltaChanges(data)) return null;\n        const oldData = {};\n        Object.entries(data.changes).forEach((param)=>{\n            let [field, change] = param;\n            if (change && typeof change === \"object\" && \"before\" in change) {\n                oldData[field] = change.before;\n            }\n        });\n        return Object.keys(oldData).length > 0 ? oldData : null;\n    };\n    // Extract raw new data from delta changes\n    const extractNewData = (data)=>{\n        if (!hasDeltaChanges(data)) return null;\n        const newData = {};\n        Object.entries(data.changes).forEach((param)=>{\n            let [field, change] = param;\n            if (change && typeof change === \"object\" && \"after\" in change) {\n                newData[field] = change.after;\n            }\n        });\n        return Object.keys(newData).length > 0 ? newData : null;\n    };\n    // Sophisticated change viewer that analyzes data types and shows meaningful differences\n    const renderSophisticatedChanges = (before, after)=>{\n        // Handle null/undefined cases\n        if (before === null && after === null) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-gray-500 italic\",\n                children: \"No change (both null)\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                lineNumber: 120,\n                columnNumber: 14\n            }, this);\n        }\n        if (before === null) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 text-sm font-medium text-green-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"w-2 h-2 bg-green-500 rounded-full\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 13\n                            }, this),\n                            \"Added\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-green-50 border border-green-200 rounded p-3\",\n                        children: renderValue(after)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                        lineNumber: 130,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                lineNumber: 125,\n                columnNumber: 9\n            }, this);\n        }\n        if (after === null) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 text-sm font-medium text-red-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"w-2 h-2 bg-red-500 rounded-full\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 13\n                            }, this),\n                            \"Deleted\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-red-50 border border-red-200 rounded p-3\",\n                        children: renderValue(before)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                        lineNumber: 144,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                lineNumber: 139,\n                columnNumber: 9\n            }, this);\n        }\n        // Both values exist - analyze types\n        const beforeType = Array.isArray(before) ? \"array\" : typeof before;\n        const afterType = Array.isArray(after) ? \"array\" : typeof after;\n        // Type changed\n        if (beforeType !== afterType) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm font-medium text-orange-700\",\n                        children: [\n                            \"Type Changed: \",\n                            beforeType,\n                            \" → \",\n                            afterType\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs font-medium text-red-700 mb-1\",\n                                        children: [\n                                            \"Before (\",\n                                            beforeType,\n                                            \")\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-red-50 border border-red-200 rounded p-3\",\n                                        children: renderValue(before)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs font-medium text-green-700 mb-1\",\n                                        children: [\n                                            \"After (\",\n                                            afterType,\n                                            \")\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-green-50 border border-green-200 rounded p-3\",\n                                        children: renderValue(after)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                        lineNumber: 160,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                lineNumber: 158,\n                columnNumber: 9\n            }, this);\n        }\n        // Same type - handle specific comparisons\n        if (beforeType === \"array\") {\n            return renderArrayChanges(before, after);\n        } else if (beforeType === \"object\") {\n            return renderObjectChanges(before, after);\n        } else {\n            // Primitive values\n            if (before === after) {\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-gray-500 italic\",\n                    children: \"No change\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                    lineNumber: 186,\n                    columnNumber: 16\n                }, this);\n            }\n            return renderPrimitiveChange(before, after);\n        }\n    };\n    // Render delta changes in a user-friendly format\n    const renderDeltaChanges = (changes)=>{\n        const changeEntries = Object.entries(changes);\n        if (changeEntries.length === 0) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-500 text-sm\",\n                    children: \"No changes detected\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                    lineNumber: 201,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                lineNumber: 200,\n                columnNumber: 9\n            }, this);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: changeEntries.map((param)=>{\n                let [field, change] = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border border-gray-200 rounded-lg p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                            className: \"font-medium text-gray-900 mb-4 capitalize\",\n                            children: field.replace(/([A-Z])/g, \" $1\").replace(/^./, (str)=>str.toUpperCase())\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                            lineNumber: 211,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700\",\n                                    children: \"\\uD83D\\uDCCB Before/After Comparison\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 217,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-500 mb-3\",\n                                    children: \"Shows changes inline with strikethrough for removed content and highlighting for added content\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 220,\n                                    columnNumber: 17\n                                }, this),\n                                renderOverlappedValues(change.before, change.after)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                            lineNumber: 216,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, field, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                    lineNumber: 210,\n                    columnNumber: 13\n                }, this);\n            })\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n            lineNumber: 207,\n            columnNumber: 7\n        }, this);\n    };\n    if (!isOpen || !log) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n        onClick: handleOverlayClick,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg max-w-6xl w-full mx-4 max-h-[90vh] overflow-hidden flex flex-col\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 border-b border-gray-200 flex-shrink-0 flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-bold text-gray-900\",\n                                    children: \"Log Details\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 240,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600 mt-1\",\n                                    children: \"View detailed information about this log entry\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 243,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                            lineNumber: 239,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"p-2 rounded-full hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors\",\n                            title: \"Close\",\n                            \"aria-label\": \"Close modal\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCalendar_FiChevronDown_FiChevronRight_FiDatabase_FiFileText_FiTag_FiUser_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiX, {\n                                className: \"h-5 w-5 text-gray-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                lineNumber: 253,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                            lineNumber: 247,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                    lineNumber: 238,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 overflow-y-auto p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                    children: \"Basic Information\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 261,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2 flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCalendar_FiChevronDown_FiChevronRight_FiDatabase_FiFileText_FiTag_FiUser_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiTag, {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                            lineNumber: 267,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"Operation\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                    lineNumber: 266,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border \".concat(getOperationColor(log.operation)),\n                                                    children: log.operation\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                    lineNumber: 270,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                            lineNumber: 265,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2 flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCalendar_FiChevronDown_FiChevronRight_FiDatabase_FiFileText_FiTag_FiUser_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiDatabase, {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                            lineNumber: 278,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"Entity\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                    lineNumber: 277,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-900 font-medium\",\n                                                    children: log.entity\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                    lineNumber: 281,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                            lineNumber: 276,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2 flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCalendar_FiChevronDown_FiChevronRight_FiDatabase_FiFileText_FiTag_FiUser_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiUser, {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                            lineNumber: 287,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"User\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                    lineNumber: 286,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-900 font-medium\",\n                                                    children: userName || \"Unknown User\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                    lineNumber: 290,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-500 mt-1\",\n                                                    children: log.userUuid\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                    lineNumber: 293,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                            lineNumber: 285,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2 flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCalendar_FiChevronDown_FiChevronRight_FiDatabase_FiFileText_FiTag_FiUser_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiCalendar, {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                            lineNumber: 299,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"Timestamp\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                    lineNumber: 298,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-900\",\n                                                    children: formatDate(log.createdAt)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                    lineNumber: 302,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                            lineNumber: 297,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 263,\n                                    columnNumber: 13\n                                }, this),\n                                log.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2 flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCalendar_FiChevronDown_FiChevronRight_FiDatabase_FiFileText_FiTag_FiUser_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiFileText, {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                    lineNumber: 310,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Description\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                            lineNumber: 309,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-900 bg-gray-50 p-3 rounded-lg border\",\n                                            children: log.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                            lineNumber: 313,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 308,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                            lineNumber: 260,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                    children: log.data && hasDeltaChanges(log.data) ? \"Changes\" : \"Additional Data\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 322,\n                                    columnNumber: 13\n                                }, this),\n                                log.data ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: hasDeltaChanges(log.data) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-3\",\n                                                        children: [\n                                                            \"Field Changes (\",\n                                                            log.data.changeCount || 0,\n                                                            \" changes)\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                        lineNumber: 332,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    renderDeltaChanges(log.data.changes)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                lineNumber: 331,\n                                                columnNumber: 21\n                                            }, this),\n                                            Object.keys(log.data).some((key)=>![\n                                                    \"changes\",\n                                                    \"changedFields\",\n                                                    \"changeCount\"\n                                                ].includes(key)) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                        children: \"Additional Information\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                        lineNumber: 341,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gray-900 text-green-400 p-4 rounded-lg border overflow-x-auto\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                            className: \"text-sm font-mono whitespace-pre-wrap\",\n                                                            children: formatJsonData(Object.fromEntries(Object.entries(log.data).filter((param)=>{\n                                                                let [key] = param;\n                                                                return ![\n                                                                    \"changes\",\n                                                                    \"changedFields\",\n                                                                    \"changeCount\"\n                                                                ].includes(key);\n                                                            })))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                            lineNumber: 345,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                        lineNumber: 344,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                lineNumber: 340,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                        lineNumber: 329,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                children: \"JSON Data\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                lineNumber: 360,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-900 text-green-400 p-4 rounded-lg border overflow-x-auto\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                    className: \"text-sm font-mono whitespace-pre-wrap\",\n                                                    children: formatJsonData(log.data)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                    lineNumber: 364,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                lineNumber: 363,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                        lineNumber: 359,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 327,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCalendar_FiChevronDown_FiChevronRight_FiDatabase_FiFileText_FiTag_FiUser_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiDatabase, {\n                                            className: \"w-12 h-12 text-gray-300 mx-auto mb-3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                            lineNumber: 373,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-500 text-sm\",\n                                            children: \"No additional data available\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                            lineNumber: 374,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 372,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                            lineNumber: 321,\n                            columnNumber: 11\n                        }, this),\n                        log.data && hasDeltaChanges(log.data) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                    children: \"Raw Data\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 382,\n                                    columnNumber: 15\n                                }, this),\n                                extractOldData(log.data) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4 border border-gray-200 rounded-lg overflow-hidden\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowOldDataTab(!showOldDataTab),\n                                            className: \"w-full px-4 py-3 bg-gray-50 hover:bg-gray-100 flex items-center justify-between text-left transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        showOldDataTab ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCalendar_FiChevronDown_FiChevronRight_FiDatabase_FiFileText_FiTag_FiUser_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiChevronDown, {\n                                                            className: \"w-4 h-4 text-gray-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                            lineNumber: 393,\n                                                            columnNumber: 25\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCalendar_FiChevronDown_FiChevronRight_FiDatabase_FiFileText_FiTag_FiUser_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiChevronRight, {\n                                                            className: \"w-4 h-4 text-gray-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                            lineNumber: 395,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-gray-900\",\n                                                            children: \"Raw Old Data (Before Changes)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                            lineNumber: 397,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                    lineNumber: 391,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-gray-500 bg-red-100 px-2 py-1 rounded\",\n                                                    children: \"Original Values\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                    lineNumber: 399,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                            lineNumber: 387,\n                                            columnNumber: 19\n                                        }, this),\n                                        showOldDataTab && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-4 bg-gray-900 text-green-400 overflow-x-auto\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                className: \"text-sm font-mono whitespace-pre-wrap\",\n                                                children: formatJsonData(extractOldData(log.data) || {})\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                lineNumber: 405,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                            lineNumber: 404,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 386,\n                                    columnNumber: 17\n                                }, this),\n                                extractNewData(log.data) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4 border border-gray-200 rounded-lg overflow-hidden\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowNewDataTab(!showNewDataTab),\n                                            className: \"w-full px-4 py-3 bg-gray-50 hover:bg-gray-100 flex items-center justify-between text-left transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        showNewDataTab ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCalendar_FiChevronDown_FiChevronRight_FiDatabase_FiFileText_FiTag_FiUser_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiChevronDown, {\n                                                            className: \"w-4 h-4 text-gray-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                            lineNumber: 422,\n                                                            columnNumber: 25\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCalendar_FiChevronDown_FiChevronRight_FiDatabase_FiFileText_FiTag_FiUser_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiChevronRight, {\n                                                            className: \"w-4 h-4 text-gray-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                            lineNumber: 424,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-gray-900\",\n                                                            children: \"Raw New Data (After Changes)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                            lineNumber: 426,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                    lineNumber: 420,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-gray-500 bg-green-100 px-2 py-1 rounded\",\n                                                    children: \"Updated Values\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                    lineNumber: 428,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                            lineNumber: 416,\n                                            columnNumber: 19\n                                        }, this),\n                                        showNewDataTab && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-4 bg-gray-900 text-green-400 overflow-x-auto\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                className: \"text-sm font-mono whitespace-pre-wrap\",\n                                                children: formatJsonData(extractNewData(log.data) || {})\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                lineNumber: 434,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                            lineNumber: 433,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 415,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                            lineNumber: 381,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"pt-6 border-t border-gray-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"Log ID\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 446,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                    className: \"text-xs text-gray-600 bg-gray-100 px-2 py-1 rounded\",\n                                    children: log.id\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 449,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                            lineNumber: 445,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                    lineNumber: 258,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 border-t border-gray-200 flex-shrink-0 flex justify-end\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onClose,\n                        className: \"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-400 transition-colors\",\n                        children: \"Close\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                        lineNumber: 457,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                    lineNumber: 456,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n            lineNumber: 236,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n        lineNumber: 235,\n        columnNumber: 5\n    }, this);\n}\n_s(LogDetailsModal, \"3gNnT4s7QjbDbG3nIYQIGUhCzM8=\");\n_c = LogDetailsModal;\nvar _c;\n$RefreshReg$(_c, \"LogDetailsModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/logs/components/LogDetailsModal.tsx\n"));

/***/ })

});