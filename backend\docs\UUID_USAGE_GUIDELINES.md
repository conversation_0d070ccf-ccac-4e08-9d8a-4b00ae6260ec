# UUID Usage Guidelines (UUIDv7)

> **Note:** This application now uses YugabyteDB (PostgreSQL) with TypeORM. UUIDs are stored as PostgreSQL UUID type and exposed as strings in APIs.

## Quick Checklist
- All primary and foreign keys are UUIDv7, stored as PostgreSQL UUID type.
- Every UUID field is exposed as a string in API responses and DTOs.
- **ALWAYS validate UUIDs in DTOs** using `@IsUUID("7")` decorator (see note below about custom validator).
- Never use names or plain strings as foreign keys.
- DTOs and API always expose UUIDs as strings.
- Swagger docs (`@ApiProperty`) document UUIDs as strings with proper examples (see below).
- Use `@IsUUID("7")` for DTO validation, and `ParseUUIDPipe` in controllers for path/query params.
- Always generate a new UUIDv7 for every new primary key.
- Use `Uuid7.generate()` for generating new UUIDs (see below).
- **TypeORM automatically handles UUID indexing** for primary keys.

## Why UUIDv7?
- Globally unique, time-ordered for efficient indexing.
- Does not reveal record count or order.

## Utility Functions
The `src/utils/uuid7.ts` file provides several utility functions:

- `Uuid7.generate()` - Static method to generate new UUIDs (**use this everywhere**)
- `new Uuid7(value)` - Main class for UUID conversion and generation
- `toString()` - Convert UUID to string representation

## Canonical Patterns

### TypeORM Entity
```typescript
// ✅ CORRECT - TypeORM automatically handles UUID primary keys
@PrimaryColumn("uuid")
id: string;

// ✅ CORRECT - Foreign key UUIDs
@Column({ type: "uuid", nullable: false })
warehouseUuid: string;

// ✅ CORRECT - Generate UUID in entity
static generateId(): string {
  return Uuid7.generate();
}
```

For API layer code patterns (DTO, Controller, Service), see [docs/API_LAYER.md](./API_LAYER.md).

## Common Mistakes
- Never store UUIDs as plain strings without validation.
- Never expose raw UUID objects in API responses.
- Never use names or plain strings as foreign keys.
- Always validate UUIDs in DTOs using `@IsUUID("7")` (see note below).
- Always use `Uuid7.generate()` for new UUID generation.

## ⚠️ CRITICAL: UUID Validation

**Always validate UUIDs in DTOs and controllers.**

### ✅ CORRECT - Proper UUID Validation
```typescript
// In DTO
@IsUUID("7") // Requires custom validator, see note below
warehouseUuid: string;

// In Controller
@Get(':warehouseUuid')
findOne(@Param('warehouseUuid', ParseUUIDPipe) warehouseUuid: string) {
  return this.service.findOne(warehouseUuid);
}
```

### Why This Matters
- **Data Integrity**: Ensures only valid UUIDs are accepted
- **Security**: Prevents injection attacks through malformed UUIDs
- **API Consistency**: Maintains consistent UUID format across all endpoints

### ⚠️ Note on @IsUUID("7")
- The standard `class-validator` package only supports versions "3", "4", and "5" for `@IsUUID`. To use `@IsUUID("7")`, you **must implement and register a custom validator** for UUIDv7. Ensure this is present in your codebase, or validation will not work as intended.

## Swagger Documentation Example

Document UUID fields in DTOs using Swagger's `@ApiProperty` decorator:

```typescript
import { ApiProperty } from '@nestjs/swagger';

export class ExampleDto {
  @ApiProperty({
    example: '01890b6e-7b8c-7e6b-8e2e-7b8c7e6b8e2e',
    description: 'UUIDv7 string',
    type: 'string',
    format: 'uuid',
  })
  warehouseUuid: string;
}
```

## Database Enforcement Note
- **PostgreSQL/YugabyteDB stores UUIDs as type `uuid`, but does not enforce the UUID version.**
- **All UUIDv7 validation is performed at the application layer (DTOs, controllers, services).**

## Summary
- Always use `Uuid7.generate()` for UUID generation.
- Store as PostgreSQL UUID type, expose as string in APIs.
- **Always validate UUIDs** using `@IsUUID("7")` decorator (with custom validator).
- Use `ParseUUIDPipe` in controllers for path parameters.
- Validate and document UUIDs in all DTOs and API endpoints.
- Use `warehouseUuid` (not `warehouseId`) for foreign key fields.
- This ensures robust, future-proof, and type-safe UUID handling across the backend.

For more information, see `src/users/user.entity.ts`, `src/users/users.service.ts`, and other entity files.

---

**All contributors must follow these guidelines. If in doubt, reference this section or ask for a code review.**
