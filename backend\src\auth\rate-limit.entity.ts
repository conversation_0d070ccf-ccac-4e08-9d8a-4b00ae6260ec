import { <PERSON><PERSON>ty, PrimaryColumn, Column, CreateDateColumn, UpdateDateColumn, Index } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';

@Entity('rate_limits')
export class RateLimit {
  @ApiProperty({
    example: "<EMAIL>",
    description: "Identifier for rate limiting (usually email or IP)",
  })
  @PrimaryColumn()
  @Index()
  identifier: string;

  @ApiProperty({
    example: 3,
    description: "Number of failed attempts",
  })
  @Column({ default: 0 })
  attempts: number;

  @ApiProperty({
    example: "2025-01-15T10:30:00.000Z",
    description: "Timestamp of first attempt",
  })
  @CreateDateColumn()
  firstAttempt: Date;

  @ApiProperty({
    example: "2025-01-15T10:35:00.000Z",
    description: "Timestamp of last attempt",
  })
  @UpdateDateColumn()
  lastAttempt: Date;

  @ApiProperty({
    example: "2025-01-15T11:30:00.000Z",
    description: "Timestamp until which the account is locked",
    required: false,
  })
  @Column({ nullable: true })
  lockedUntil?: Date;

  @ApiProperty({
    example: "Too many failed login attempts",
    description: "Reason for lockout",
    required: false,
  })
  @Column({ nullable: true })
  lockoutReason?: string;
} 