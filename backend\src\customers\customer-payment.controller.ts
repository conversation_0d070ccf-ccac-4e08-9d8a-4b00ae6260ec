import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Patch,
  Delete,
  Query,
  ParseUUIDPipe,
} from "@nestjs/common";
import { CustomerPaymentService } from "./customer-payment.service.typeorm";
import { CreateCustomerPaymentDto } from "./dto/create-customer-payment.dto";
import { UpdateCustomerPaymentDto } from "./dto/update-customer-payment.dto";
import { FilterCustomerPaymentDto } from "./dto/filter-customer-payment.dto";
import { CustomerPaymentResponseDto } from "./dto/customer-payment-response.dto";
import {
  PaginationQueryDto,
  PaginatedResponseDto,
} from "../dto/pagination.dto";
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBody,
} from "@nestjs/swagger";

@ApiTags("customer-payments")
@Controller("customer-payments")
export class CustomerPaymentController {
  constructor(
    private readonly customerPaymentService: CustomerPaymentService,
  ) {}

  @Post()
  @ApiOperation({ 
    summary: "Create a new customer payment",
    description: "Creates a new customer payment and automatically updates customer credit balance using atomic transactions."
  })
  @ApiBody({ 
    type: CreateCustomerPaymentDto, 
    description: "Customer payment data to create",
    examples: {
      basic: {
        summary: "Basic cash payment",
        value: {
          customerUuid: "uuid-v7-string",
          userUuid: "uuid-v7-string", 
          warehouseUuid: "uuid-v7-string",
          paymentMethod: "cash",
          amount: 100.0,
          description: "Payment for invoice #INV-001"
        }
      },
      withSale: {
        summary: "Payment for specific sale",
        value: {
          customerUuid: "uuid-v7-string",
          userUuid: "uuid-v7-string",
          warehouseUuid: "uuid-v7-string",
          saleUuid: "uuid-v7-string",
          paymentMethod: "credit_card",
          amount: 250.0,
          description: "Payment for sale #SALE-123",
          referenceNumber: "CC-TXN-456789"
        }
      }
    }
  })
  @ApiResponse({
    status: 201,
    description: "Payment created successfully",
    type: CustomerPaymentResponseDto,
  })
  @ApiResponse({ status: 400, description: "Invalid input data" })
  @ApiResponse({ status: 404, description: "Customer not found" })
  create(
    @Body() createDto: CreateCustomerPaymentDto,
  ): Promise<CustomerPaymentResponseDto> {
    return this.customerPaymentService.create(createDto);
  }

  @Post("filter")
  @ApiOperation({
    summary: "Filter customer payments with advanced criteria",
    description: "Advanced filtering for customer payments using POST body. Supports filtering by customer, warehouse, payment method, status, amounts, and date ranges. Returns paginated results with metadata including totalPages and totalEntries.",
  })
  @ApiBody({
    type: FilterCustomerPaymentDto,
    description: "Advanced payment filtering payload",
    examples: {
      byCustomer: {
        summary: "Payments for specific customer",
        value: {
          customerUuid: "uuid-v7-string",
          status: "completed"
        }
      },
      byDateRange: {
        summary: "Payments in date range",
        value: {
          fromDate: "2024-01-01T00:00:00.000Z",
          toDate: "2024-01-31T23:59:59.999Z",
          minAmount: 50.0,
          maxAmount: 1000.0
        }
      },
      byMethod: {
        summary: "Payments by method",
        value: {
          paymentMethod: "credit_card",
          warehouseUuid: "uuid-v7-string"
        }
      }
    }
  })
  @ApiResponse({
    status: 200,
    description: "Filtered payments retrieved successfully with pagination metadata",
    type: PaginatedResponseDto,
  })
  async filter(
    @Body() filterDto: FilterCustomerPaymentDto,
  ): Promise<PaginatedResponseDto<CustomerPaymentResponseDto>> {
    // Use pagination from DTO or reasonable defaults
    const page = filterDto.page || 1;
    const limit = filterDto.limit || 50;
    return this.customerPaymentService.findAll(filterDto, page, limit);
  }

  @Get()
  @ApiOperation({ 
    summary: "Get all customer payments with pagination and filtering",
    description: "Retrieve customer payments with support for pagination, sorting, and basic filtering via query parameters."
  })
  @ApiQuery({
    name: "page",
    required: false,
    type: Number,
    description: "Page number (default: 1)",
  })
  @ApiQuery({
    name: "limit",
    required: false,
    type: Number,
    description: "Items per page (default: 10)",
  })
  @ApiQuery({
    name: "customerUuid",
    required: false,
    type: String,
    description: "Filter by customer UUID",
  })
  @ApiQuery({
    name: "userUuid",
    required: false,
    type: String,
    description: "Filter by user UUID",
  })
  @ApiQuery({
    name: "warehouseUuid",
    required: false,
    type: String,
    description: "Filter by warehouse UUID",
  })
  @ApiQuery({
    name: "saleUuid",
    required: false,
    type: String,
    description: "Filter by sale UUID",
  })
  @ApiQuery({
    name: "paymentMethod",
    required: false,
    enum: ["cash", "credit_card", "debit_card", "bank_transfer", "check", "mobile_payment"],
    description: "Filter by payment method",
  })
  @ApiQuery({
    name: "status",
    required: false,
    enum: ["pending", "completed", "failed", "cancelled", "refunded"],
    description: "Filter by payment status",
  })
  @ApiQuery({
    name: "minAmount",
    required: false,
    type: Number,
    description: "Filter by minimum amount (inclusive)",
  })
  @ApiQuery({
    name: "maxAmount",
    required: false,
    type: Number,
    description: "Filter by maximum amount (inclusive)",
  })
  @ApiQuery({
    name: "description",
    required: false,
    type: String,
    description: "Search in description (partial match)",
  })
  @ApiQuery({
    name: "referenceNumber",
    required: false,
    type: String,
    description: "Search in reference number (partial match)",
  })
  @ApiQuery({
    name: "sortBy",
    required: false,
    enum: ["amount", "status", "paymentMethod", "description", "referenceNumber", "processedAt", "createdAt", "updatedAt"],
    description: "Sort by field",
  })
  @ApiQuery({
    name: "sortOrder",
    required: false,
    enum: ["asc", "desc"],
    description: "Sort order (default: desc)",
  })
  @ApiResponse({
    status: 200,
    description: "Payments retrieved successfully",
    type: PaginatedResponseDto<CustomerPaymentResponseDto>,
  })
  async findAll(
    @Query() paginationQuery: PaginationQueryDto,
    @Query() filter: FilterCustomerPaymentDto,
  ): Promise<PaginatedResponseDto<CustomerPaymentResponseDto>> {
    const { page = 1, limit = 10 } = paginationQuery;
    return this.customerPaymentService.findAll(filter, page, limit);
  }

  @Get("customer/:customerUuid")
  @ApiOperation({ 
    summary: "Get payments for a specific customer",
    description: "Retrieve all payments made by a specific customer with pagination support."
  })
  @ApiParam({
    name: "customerUuid",
    type: String,
    example: "uuid-v7-string",
    description: "Customer UUID",
  })
  @ApiQuery({
    name: "page",
    required: false,
    type: Number,
    description: "Page number (default: 1)",
  })
  @ApiQuery({
    name: "limit",
    required: false,
    type: Number,
    description: "Items per page (default: 10)",
  })
  @ApiResponse({
    status: 200,
    description: "Customer payments retrieved successfully",
    type: PaginatedResponseDto<CustomerPaymentResponseDto>,
  })
  @ApiResponse({ status: 404, description: "Customer not found" })
  async findByCustomer(
    @Param("customerUuid", ParseUUIDPipe) customerUuid: string,
    @Query("page") page?: number,
    @Query("limit") limit?: number,
  ): Promise<PaginatedResponseDto<CustomerPaymentResponseDto>> {
    return this.customerPaymentService.findByCustomer(
      customerUuid,
      page || 1,
      limit || 10,
    );
  }

  @Get(":uuid")
  @ApiOperation({ 
    summary: "Get a customer payment by UUID",
    description: "Retrieve detailed information about a specific customer payment."
  })
  @ApiParam({
    name: "uuid",
    type: String,
    example: "uuid-v7-string",
    description: "Payment UUID",
  })
  @ApiResponse({
    status: 200,
    description: "Payment retrieved successfully",
    type: CustomerPaymentResponseDto,
  })
  @ApiResponse({ status: 404, description: "Payment not found" })
  findOne(
    @Param("uuid", ParseUUIDPipe) uuid: string,
  ): Promise<CustomerPaymentResponseDto> {
    return this.customerPaymentService.findOne(uuid);
  }

  @Patch(":uuid")
  @ApiOperation({
    summary: "Update a customer payment by UUID",
    description: "Update payment information. Amount changes are only allowed for non-completed payments and will automatically adjust customer credit.",
  })
  @ApiParam({
    name: "uuid",
    type: String,
    example: "uuid-v7-string",
    description: "Payment UUID",
  })
  @ApiBody({
    type: UpdateCustomerPaymentDto,
    description: "Payment data to update",
    examples: {
      updateStatus: {
        summary: "Update payment status",
        value: {
          status: "completed",
          processedAt: "2024-01-15T10:30:00.000Z"
        }
      },
      updateDescription: {
        summary: "Update payment description",
        value: {
          description: "Updated payment description",
          referenceNumber: "NEW-REF-123"
        }
      },
      updateAmount: {
        summary: "Update payment amount (pending payments only)",
        value: {
          amount: 150.0,
          userUuid: "uuid-v7-string"
        }
      }
    }
  })
  @ApiResponse({
    status: 200,
    description: "Payment updated successfully",
    type: CustomerPaymentResponseDto,
  })
  @ApiResponse({ status: 404, description: "Payment not found" })
  @ApiResponse({
    status: 400,
    description: "Invalid input data or operation not allowed",
  })
  update(
    @Param("uuid", ParseUUIDPipe) uuid: string,
    @Body() updateDto: UpdateCustomerPaymentDto,
  ): Promise<CustomerPaymentResponseDto> {
    return this.customerPaymentService.update(uuid, updateDto);
  }

  @Post(":uuid/refund")
  @ApiOperation({
    summary: "Refund a completed payment",
    description: "Refund a completed payment, automatically adjusting customer credit and creating audit trail.",
  })
  @ApiParam({
    name: "uuid",
    type: String,
    example: "uuid-v7-string",
    description: "Payment UUID",
  })
  @ApiBody({
    schema: {
      type: "object",
      properties: {
        userUuid: {
          type: "string",
          example: "uuid-v7-string",
          description: "UUID of the user processing the refund"
        },
        reason: {
          type: "string",
          example: "Customer requested refund",
          description: "Reason for the refund"
        }
      },
      required: ["userUuid", "reason"]
    },
    examples: {
      basicRefund: {
        summary: "Process payment refund",
        value: {
          userUuid: "uuid-v7-string",
          reason: "Customer requested refund due to product defect"
        }
      }
    }
  })
  @ApiResponse({
    status: 200,
    description: "Payment refunded successfully",
    schema: {
      type: "object",
      properties: {
        payment: { $ref: "#/components/schemas/CustomerPaymentResponseDto" },
        creditAdjustment: { $ref: "#/components/schemas/CreditAdjustmentResponseDto" }
      }
    }
  })
  @ApiResponse({ status: 404, description: "Payment not found" })
  @ApiResponse({ status: 400, description: "Payment cannot be refunded" })
  async refundPayment(
    @Param("uuid", ParseUUIDPipe) uuid: string,
    @Body() body: { userUuid: string; reason: string; refundAmount: number },
  ) {
    return this.customerPaymentService.refundPayment(
      uuid,
      body.refundAmount,
      body.reason,
      body.userUuid,
    );
  }

  @Post(":uuid/cancel")
  @ApiOperation({
    summary: "Cancel a pending payment",
    description: "Cancel a pending payment. Only payments with 'pending' status can be cancelled.",
  })
  @ApiParam({
    name: "uuid",
    type: String,
    example: "uuid-v7-string",
    description: "Payment UUID",
  })
  @ApiBody({
    schema: {
      type: "object",
      properties: {
        userUuid: {
          type: "string",
          example: "uuid-v7-string",
          description: "UUID of the user cancelling the payment"
        }
      },
      required: ["userUuid"]
    },
    examples: {
      cancelPayment: {
        summary: "Cancel pending payment",
        value: {
          userUuid: "uuid-v7-string"
        }
      }
    }
  })
  @ApiResponse({
    status: 200,
    description: "Payment cancelled successfully",
    type: CustomerPaymentResponseDto,
  })
  @ApiResponse({ status: 404, description: "Payment not found" })
  @ApiResponse({ status: 400, description: "Payment cannot be cancelled" })
  async cancelPayment(
    @Param("uuid", ParseUUIDPipe) uuid: string,
    @Body() body: { userUuid: string },
  ): Promise<CustomerPaymentResponseDto> {
    return this.customerPaymentService.cancelPayment(uuid, body.userUuid);
  }

  @Delete(":uuid")
  @ApiOperation({
    summary: "Delete a customer payment",
    description: "Soft delete a payment (only pending payments can be deleted). Completed payments must be refunded instead.",
  })
  @ApiParam({
    name: "uuid",
    type: String,
    example: "uuid-v7-string",
    description: "Payment UUID",
  })
  @ApiResponse({ 
    status: 200, 
    description: "Payment deleted successfully (status changed to cancelled)" 
  })
  @ApiResponse({ status: 404, description: "Payment not found" })
  @ApiResponse({ 
    status: 400, 
    description: "Payment cannot be deleted (use refund for completed payments)" 
  })
  remove(@Param("uuid", ParseUUIDPipe) uuid: string): Promise<void> {
    return this.customerPaymentService.remove(uuid);
  }
} 