import { useState } from 'react';
import { toast } from 'sonner';
import { Customer, CreateCustomerDto, UpdateCustomerDto } from '../customersApi';
import { useCreateCustomer, useUpdateCustomer, useDeleteCustomer } from '../useCustomers';
import { buildPOSUrl } from '../../sales/pos/config/posConfig';

/**
 * Hook for managing customer actions (create, update, delete, view details)
 */
export const useCustomerActions = () => {
  // Modal states
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
  const [customerToDelete, setCustomerToDelete] = useState<Customer | null>(null);

  // Mutations
  const createCustomerMutation = useCreateCustomer();
  const updateCustomerMutation = useUpdateCustomer();
  const deleteCustomerMutation = useDeleteCustomer();

  // Loading states
  const isLoading = createCustomerMutation.isPending || updateCustomerMutation.isPending || deleteCustomerMutation.isPending;

  // Add customer
  const handleAddCustomer = () => {
    setSelectedCustomer(null);
    setIsAddModalOpen(true);
  };

  // Edit customer
  const handleEditCustomer = (customer: Customer) => {
    setSelectedCustomer(customer);
    setIsEditModalOpen(true);
  };

  // View customer details
  const handleViewCustomerDetails = (customer: Customer) => {
    setSelectedCustomer(customer);
    setIsDetailsModalOpen(true);
  };

  // Delete customer
  const handleDeleteCustomer = (customer: Customer) => {
    setCustomerToDelete(customer);
    setIsDeleteModalOpen(true);
  };

  // Create sale for customer
  const handleCreateSale = (customer: Customer) => {
    const posUrl = buildPOSUrl({ customerUuid: customer.uuid });
    window.open(posUrl, '_blank');
  };

  // Submit customer form (create or update)
  const handleSubmitCustomer = async (data: CreateCustomerDto | UpdateCustomerDto) => {
    try {
      if (selectedCustomer) {
        // Update existing customer
        await updateCustomerMutation.mutateAsync({
          uuid: selectedCustomer.uuid,
          data: data as UpdateCustomerDto
        });
        toast.success('Customer updated successfully');
      } else {
        // Create new customer
        await createCustomerMutation.mutateAsync(data as CreateCustomerDto);
        toast.success('Customer created successfully');
      }
      
      // Close appropriate modal
      if (selectedCustomer) {
        setIsEditModalOpen(false);
      } else {
        setIsAddModalOpen(false);
      }
      setSelectedCustomer(null);
    } catch (error) {
      console.error('Error saving customer:', error);
      toast.error('Failed to save customer');
      throw error; // Re-throw to let the modal handle the error
    }
  };

  // Confirm delete customer
  const handleConfirmDelete = async () => {
    if (!customerToDelete) return;

    try {
      await deleteCustomerMutation.mutateAsync(customerToDelete.uuid);
      toast.success('Customer deleted successfully');
      setIsDeleteModalOpen(false);
      setCustomerToDelete(null);
    } catch (error) {
      console.error('Error deleting customer:', error);
      toast.error('Failed to delete customer');
    }
  };

  // Close modals
  const closeAddModal = () => {
    setIsAddModalOpen(false);
    setSelectedCustomer(null);
  };

  const closeEditModal = () => {
    setIsEditModalOpen(false);
    setSelectedCustomer(null);
  };

  const closeDetailsModal = () => {
    setIsDetailsModalOpen(false);
    setSelectedCustomer(null);
  };

  const closeDeleteModal = () => {
    setIsDeleteModalOpen(false);
    setCustomerToDelete(null);
  };

  return {
    // Modal states
    isAddModalOpen,
    isEditModalOpen,
    isDetailsModalOpen,
    isDeleteModalOpen,
    selectedCustomer,
    customerToDelete,
    
    // Loading state
    isLoading,
    
    // Actions
    handleAddCustomer,
    handleEditCustomer,
    handleViewCustomerDetails,
    handleDeleteCustomer,
    handleCreateSale,
    handleSubmitCustomer,
    handleConfirmDelete,
    
    // Modal close handlers
    closeAddModal,
    closeEditModal,
    closeDetailsModal,
    closeDeleteModal,
  };
}; 