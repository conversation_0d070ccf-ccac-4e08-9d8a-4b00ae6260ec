import { En<PERSON>ty, PrimaryColumn, Column, CreateDateColumn, UpdateDateColumn, Index } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Uuid7 } from '../utils/uuid7';

@Entity('suppliers')
export class Supplier {
  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the supplier (primary key)",
  })
  @PrimaryColumn('uuid')
  id: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the warehouse",
    required: false,
  })
  @Column('uuid', { nullable: true })
  @Index()
  warehouseUuid?: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the user who created/owns this supplier",
  })
  @Column('uuid')
  @Index()
  userUuid: string;

  @ApiProperty({ example: "Acme Supplies Ltd." })
  @Column()
  name: string;

  @ApiProperty({
    example: "12345678901",
    description: "Fiscal identification number (tax ID)",
    required: false,
  })
  @Column({ nullable: true })
  fiscalId?: string;

  @ApiProperty({
    example: "RC123456789",
    description: "Registration Certificate number",
    required: false,
  })
  @Column({ nullable: true })
  rc?: string;

  @ApiProperty({
    example: "SUP-001",
    description: "Supplier code",
    required: false,
  })
  @Column({ nullable: true })
  code?: string;

  @ApiProperty({
    example: "ART-001-2024",
    description: "Article Number assigned by the supplier",
    required: false,
  })
  @Column({ nullable: true })
  articleNumber?: string;

  @ApiProperty({
    example: "Premium supplier for electronics",
    description: "Supplier description",
    required: false,
  })
  @Column({ nullable: true })
  description?: string;

  @ApiProperty({ example: "<EMAIL>", required: false })
  @Column({ nullable: true })
  email?: string;

  @ApiProperty({ example: "+1234567890", required: false })
  @Column({ nullable: true })
  phone?: string;

  @ApiProperty({ example: "123 Main St, City, Country", required: false })
  @Column({ nullable: true })
  address?: string;

  @ApiProperty({
    example: 40.7128,
    description: "Latitude coordinate for supplier location",
    required: false,
  })
  @Column('decimal', { precision: 10, scale: 8, nullable: true })
  latitude?: number;

  @ApiProperty({
    example: -74.006,
    description: "Longitude coordinate for supplier location",
    required: false,
  })
  @Column('decimal', { precision: 11, scale: 8, nullable: true })
  longitude?: number;

  @ApiProperty({ example: "Supplier notes or remarks.", required: false })
  @Column({ nullable: true })
  notes?: string;

  @ApiProperty({ example: false, description: "Soft delete flag" })
  @Column({ default: false })
  isDeleted: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Helper method to generate UUID
  static generateId(): string {
    return new Uuid7().toString();
  }

  // Helper method to generate UUID
  static fromBinary(binary: any): string {
    if (!binary) return null;
    try {
      return new Uuid7(binary).toString();
    } catch {
      return null;
    }
  }
} 