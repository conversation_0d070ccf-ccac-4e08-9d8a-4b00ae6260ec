/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/auth/page";
exports.ids = ["app/auth/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fauth%2Fpage&page=%2Fauth%2Fpage&appPaths=%2Fauth%2Fpage&pagePath=private-next-app-dir%2Fauth%2Fpage.tsx&appDir=C%3A%5CUsers%5Cmahll%5CDocuments%5Cworkspace%5Cprojects%5Cdido-distribution%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmahll%5CDocuments%5Cworkspace%5Cprojects%5Cdido-distribution%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fauth%2Fpage&page=%2Fauth%2Fpage&appPaths=%2Fauth%2Fpage&pagePath=private-next-app-dir%2Fauth%2Fpage.tsx&appDir=C%3A%5CUsers%5Cmahll%5CDocuments%5Cworkspace%5Cprojects%5Cdido-distribution%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmahll%5CDocuments%5Cworkspace%5Cprojects%5Cdido-distribution%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'auth',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/auth/page.tsx */ \"(rsc)/./app/auth/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/auth/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/auth/page\",\n        pathname: \"/auth\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fauth%2Fpage&page=%2Fauth%2Fpage&appPaths=%2Fauth%2Fpage&pagePath=private-next-app-dir%2Fauth%2Fpage.tsx&appDir=C%3A%5CUsers%5Cmahll%5CDocuments%5Cworkspace%5Cprojects%5Cdido-distribution%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmahll%5CDocuments%5Cworkspace%5Cprojects%5Cdido-distribution%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Capp%5C%5Cauth%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Capp%5C%5Cauth%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/auth/page.tsx */ \"(ssr)/./app/auth/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q21haGxsJTVDJTVDRG9jdW1lbnRzJTVDJTVDd29ya3NwYWNlJTVDJTVDcHJvamVjdHMlNUMlNUNkaWRvLWRpc3RyaWJ1dGlvbiU1QyU1Q2Zyb250ZW5kJTVDJTVDYXBwJTVDJTVDYXV0aCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxrSkFBdUkiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9kaWRvLWRpc3RyaWJ1dGlvbi1mcm9udGVuZC8/ZDRjMCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXG1haGxsXFxcXERvY3VtZW50c1xcXFx3b3Jrc3BhY2VcXFxccHJvamVjdHNcXFxcZGlkby1kaXN0cmlidXRpb25cXFxcZnJvbnRlbmRcXFxcYXBwXFxcXGF1dGhcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Capp%5C%5Cauth%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/providers.tsx */ \"(ssr)/./app/providers.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/sonner/dist/index.mjs */ \"(ssr)/./node_modules/sonner/dist/index.mjs\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/auth/authApi.ts":
/*!*****************************!*\
  !*** ./app/auth/authApi.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   enhancedLogin: () => (/* binding */ enhancedLogin),\n/* harmony export */   getSecurityStatus: () => (/* binding */ getSecurityStatus),\n/* harmony export */   googleTokenAuth: () => (/* binding */ googleTokenAuth),\n/* harmony export */   legacyLogin: () => (/* binding */ legacyLogin),\n/* harmony export */   logout: () => (/* binding */ logout),\n/* harmony export */   refreshToken: () => (/* binding */ refreshToken),\n/* harmony export */   validateToken: () => (/* binding */ validateToken)\n/* harmony export */ });\n// Auth API service for centralized authentication calls\n// Uses the new enhanced backend endpoints with security features\n// Enhanced login with security features\nasync function enhancedLogin(request) {\n    const response = await fetch(\"/api/auth/login\", {\n        method: \"POST\",\n        headers: {\n            \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({\n            ...request,\n            clientIp: request.clientIp || \"frontend\",\n            userAgent: request.userAgent || navigator.userAgent\n        })\n    });\n    if (!response.ok) {\n        const errorData = await response.json().catch(()=>({}));\n        throw new Error(errorData.message || `Login failed: ${response.status}`);\n    }\n    return response.json();\n}\n// Legacy login for backward compatibility\nasync function legacyLogin(request) {\n    const response = await fetch(\"/api/auth/login/legacy\", {\n        method: \"POST\",\n        headers: {\n            \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify(request)\n    });\n    if (!response.ok) {\n        const errorData = await response.json().catch(()=>({}));\n        throw new Error(errorData.message || `Login failed: ${response.status}`);\n    }\n    return response.json();\n}\n// Refresh access token\nasync function refreshToken(request) {\n    const response = await fetch(\"/api/auth/refresh\", {\n        method: \"POST\",\n        headers: {\n            \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({\n            ...request,\n            clientIp: request.clientIp || \"frontend\",\n            userAgent: request.userAgent || navigator.userAgent\n        })\n    });\n    if (!response.ok) {\n        const errorData = await response.json().catch(()=>({}));\n        throw new Error(errorData.message || `Token refresh failed: ${response.status}`);\n    }\n    return response.json();\n}\n// Logout with token revocation\nasync function logout(request, accessToken) {\n    const response = await fetch(\"/api/auth/logout\", {\n        method: \"POST\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            \"Authorization\": `Bearer ${accessToken}`\n        },\n        body: JSON.stringify(request)\n    });\n    if (!response.ok) {\n        const errorData = await response.json().catch(()=>({}));\n        throw new Error(errorData.message || `Logout failed: ${response.status}`);\n    }\n    return response.json();\n}\n// Get security status\nasync function getSecurityStatus(accessToken) {\n    const response = await fetch(\"/api/auth/security-status\", {\n        headers: {\n            \"Authorization\": `Bearer ${accessToken}`\n        }\n    });\n    if (!response.ok) {\n        const errorData = await response.json().catch(()=>({}));\n        throw new Error(errorData.message || `Failed to get security status: ${response.status}`);\n    }\n    return response.json();\n}\n// Validate token (for session checking)\nasync function validateToken(accessToken) {\n    try {\n        const response = await fetch(\"/api/auth/validate\", {\n            headers: {\n                \"Authorization\": `Bearer ${accessToken}`\n            }\n        });\n        return response.ok;\n    } catch  {\n        return false;\n    }\n}\n// Google OAuth token authentication\nasync function googleTokenAuth(googleData) {\n    const response = await fetch(\"/api/auth/google/token\", {\n        method: \"POST\",\n        headers: {\n            \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify(googleData)\n    });\n    if (!response.ok) {\n        const errorData = await response.json().catch(()=>({}));\n        throw new Error(errorData.message || `Google authentication failed: ${response.status}`);\n    }\n    return response.json();\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/auth/authApi.ts\n");

/***/ }),

/***/ "(ssr)/./app/auth/page.tsx":
/*!***************************!*\
  !*** ./app/auth/page.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AuthPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./contexts/AuthContext.tsx\");\n/* harmony import */ var _authApi__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./authApi */ \"(ssr)/./app/auth/authApi.ts\");\n/* harmony import */ var _components_BackendStatusChecker__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/BackendStatusChecker */ \"(ssr)/./components/BackendStatusChecker.tsx\");\n/* harmony import */ var _components_NetworkStatusChecker__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/NetworkStatusChecker */ \"(ssr)/./components/NetworkStatusChecker.tsx\");\n/* harmony import */ var _utils_networkUtils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/utils/networkUtils */ \"(ssr)/./utils/networkUtils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nconst BACKEND_URL = process.env.NEXT_PUBLIC_API_BASE_URL || process.env.NEXT_PUBLIC_BACKEND_URL || \"http://localhost:8000\";\n// NOTE: The backend must redirect to /auth/callback with token and user as query params after Google login.\n// Example: res.redirect(`https://your-frontend-domain.com/auth/callback?token=JWT_TOKEN&user=${encodeURIComponent(JSON.stringify(user))}`);\nfunction AuthPage() {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { user, loading, logout, checkUserExists } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const [checkingAuth, setCheckingAuth] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [googleLoading, setGoogleLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // State for custom login (moved to top)\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [password, setPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [loginError, setLoginError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoggingIn, setIsLoggingIn] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [backendStatus, setBackendStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"checking\");\n    const [networkStatus, setNetworkStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"checking\");\n    const { login } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    // Check for error parameters in URL\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const params = new URLSearchParams(window.location.search);\n        const error = params.get(\"error\");\n        if (error) {\n            switch(error){\n                case \"login_failed\":\n                    setLoginError(\"Google sign-in failed. Please try again.\");\n                    break;\n                case \"callback_failed\":\n                    setLoginError(\"Authentication callback failed. This could be due to:\\n\\n1. Backend server not running\\n2. Google OAuth configuration issues\\n3. Network connectivity problems\\n\\nPlease check that:\\n• Backend server is running on localhost:8000\\n• Google OAuth credentials are properly configured\\n• You have a stable internet connection\");\n                    break;\n                case \"timeout\":\n                    setLoginError(\"Authentication timed out. This could be due to:\\n\\n1. Slow or unstable internet connection\\n2. Backend server not responding\\n3. Google OAuth configuration issues\\n\\nPlease try again when your connection is more stable.\");\n                    break;\n                case \"account_locked\":\n                    setLoginError(\"Account is temporarily locked due to too many failed attempts. Please try again later.\");\n                    break;\n                case \"rate_limited\":\n                    setLoginError(\"Too many login attempts. Please wait before trying again.\");\n                    break;\n                default:\n                    setLoginError(\"An authentication error occurred. Please try again.\");\n            }\n            // Clear the error from URL\n            window.history.replaceState({}, document.title, window.location.pathname);\n        }\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const verifyUser = async ()=>{\n            if (!loading) {\n                if (user) {\n                    // Check with backend if user still exists\n                    const exists = await checkUserExists(user.uuid || \"\");\n                    if (exists) {\n                        router.replace(\"/dashboard\");\n                    } else {\n                        // Clear session and show error\n                        logout();\n                        setLoginError(\"Your session has expired. Please sign in again.\");\n                    }\n                } else {\n                    setCheckingAuth(false);\n                }\n            }\n        };\n        verifyUser();\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [\n        router,\n        user,\n        loading\n    ]);\n    // Redirect to backend Google OAuth endpoint\n    const handleGoogleLogin = async ()=>{\n        setGoogleLoading(true);\n        setLoginError(\"\");\n        // First check if backend is accessible\n        if (backendStatus === \"offline\") {\n            setLoginError(\"Cannot connect to backend server. Please ensure the backend is running on localhost:8000 before trying Google sign-in.\\n\\nIf you just started the backend, wait a few seconds and try again.\");\n            setGoogleLoading(false);\n            return;\n        }\n        // Check if network connection is unstable\n        if ((0,_utils_networkUtils__WEBPACK_IMPORTED_MODULE_7__.isConnectionUnstable)()) {\n            const advice = (0,_utils_networkUtils__WEBPACK_IMPORTED_MODULE_7__.getConnectionAdvice)();\n            setLoginError(`Network connection issue detected. ${advice || \"Please check your internet connection and try again.\"}`);\n            setGoogleLoading(false);\n            return;\n        }\n        console.log(\"Initiating Google OAuth flow...\");\n        console.log(\"Backend URL:\", BACKEND_URL);\n        console.log(\"Google OAuth endpoint:\", `${BACKEND_URL}/auth/google`);\n        // Set a timeout for the Google OAuth redirect\n        const redirectTimeout = setTimeout(()=>{\n            if (googleLoading) {\n                setGoogleLoading(false);\n                setLoginError(\"Google sign-in is taking longer than expected. This could be due to:\\n\\n1. Slow or unstable internet connection\\n2. Backend server not responding\\n3. Google OAuth configuration issues\\n\\nPlease check that:\\n• You have a stable internet connection\\n• Backend server is running on localhost:8000\\n• Google OAuth credentials are properly configured\\n\\nIf your internet is unstable, try:\\n• Using a wired connection instead of WiFi\\n• Moving closer to your router\\n• Trying again when your connection is more stable\");\n            }\n        }, 10000); // Reduced to 10 seconds for faster feedback\n        try {\n            // Double-check backend connectivity before redirecting\n            try {\n                const healthCheck = await fetch(\"/api/auth/health\", {\n                    method: \"GET\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    signal: AbortSignal.timeout(5000) // 5 second timeout\n                });\n                if (!healthCheck.ok) {\n                    throw new Error(`Backend health check failed: ${healthCheck.status}`);\n                }\n            } catch (healthError) {\n                clearTimeout(redirectTimeout);\n                setGoogleLoading(false);\n                setLoginError(\"Backend server is not accessible. Please ensure:\\n\\n1. Backend server is running on localhost:8000\\n2. No firewall is blocking the connection\\n3. Check backend logs for any startup errors\\n\\nIf you just started the backend, wait a few seconds and try again.\");\n                return;\n            }\n            // Clear the timeout since we're about to redirect\n            clearTimeout(redirectTimeout);\n            // Redirect to Google OAuth\n            window.location.href = `${BACKEND_URL}/auth/google`;\n        } catch (error) {\n            clearTimeout(redirectTimeout);\n            console.error(\"Error redirecting to Google OAuth:\", error);\n            setGoogleLoading(false);\n            setLoginError(\"Failed to redirect to Google sign-in. This might be due to network connectivity issues. Please try again when your connection is more stable.\");\n        }\n    };\n    if (checkingAuth || loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"loader mb-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n                    lineNumber: 147,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-lg font-medium\",\n                    children: \"Checking authentication...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n                    lineNumber: 148,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n            lineNumber: 146,\n            columnNumber: 7\n        }, this);\n    }\n    // Enhanced login handler with better error handling\n    const handleCustomLogin = async (e)=>{\n        e.preventDefault();\n        setLoginError(\"\");\n        setIsLoggingIn(true);\n        try {\n            // Try enhanced login first\n            const response = await (0,_authApi__WEBPACK_IMPORTED_MODULE_4__.enhancedLogin)({\n                identifier: email,\n                password,\n                clientIp: \"frontend\",\n                userAgent: navigator.userAgent\n            });\n            // Enhanced login successful\n            login(response.user, response.accessToken, response.refreshToken);\n            router.replace(\"/dashboard\");\n        } catch (error) {\n            console.error(\"Enhanced login failed:\", error);\n            // Check if it's a rate limiting or account lockout error\n            if (error instanceof Error) {\n                if (error.message.includes(\"Too many login attempts\") || error.message.includes(\"Account locked\") || error.message.includes(\"429\") || error.message.includes(\"423\")) {\n                    setLoginError(\"Too many failed login attempts. Your account is temporarily locked. Please try again later.\");\n                    return;\n                }\n                if (error.message.includes(\"Invalid credentials\") || error.message.includes(\"401\")) {\n                    setLoginError(\"Invalid email or password. Please check your credentials and try again.\");\n                    return;\n                }\n                // For other errors, try legacy login as fallback\n                try {\n                    console.log(\"Trying legacy login as fallback...\");\n                    const legacyResponse = await (0,_authApi__WEBPACK_IMPORTED_MODULE_4__.legacyLogin)({\n                        identifier: email,\n                        password\n                    });\n                    // Legacy login successful\n                    login(legacyResponse.user, legacyResponse.accessToken, legacyResponse.refreshToken || \"\");\n                    router.replace(\"/dashboard\");\n                    return;\n                } catch (legacyError) {\n                    console.error(\"Legacy login also failed:\", legacyError);\n                    if (legacyError instanceof Error) {\n                        setLoginError(legacyError.message || \"Login failed. Please check your credentials and try again.\");\n                    } else {\n                        setLoginError(\"Login failed. Please try again.\");\n                    }\n                }\n            } else {\n                setLoginError(\"An unexpected error occurred. Please try again.\");\n            }\n        } finally{\n            setIsLoggingIn(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex flex-col items-center justify-center bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white shadow-lg rounded-lg p-8 max-w-md w-full\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-2xl font-bold mb-6 text-center\",\n                    children: \"Sign in to Dido Distribution\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n                    lineNumber: 223,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-4 space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-3 rounded-lg text-sm bg-gray-50\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_BackendStatusChecker__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                onStatusChange: setBackendStatus,\n                                showDetails: true\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n                            lineNumber: 227,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-3 rounded-lg text-sm bg-gray-50\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NetworkStatusChecker__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                onStatusChange: setNetworkStatus,\n                                showDetails: true\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n                                lineNumber: 234,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n                            lineNumber: 233,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n                    lineNumber: 226,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleCustomLogin,\n                    className: \"space-y-4 mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"email\",\n                            value: email,\n                            onChange: (e)=>setEmail(e.target.value),\n                            placeholder: \"Email\",\n                            className: \"w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-200\",\n                            required: true,\n                            disabled: isLoggingIn\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n                            lineNumber: 242,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"password\",\n                            value: password,\n                            onChange: (e)=>setPassword(e.target.value),\n                            placeholder: \"Password\",\n                            className: \"w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-200\",\n                            required: true,\n                            disabled: isLoggingIn\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n                            lineNumber: 251,\n                            columnNumber: 11\n                        }, this),\n                        loginError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-red-600 text-sm text-center p-3 bg-red-50 rounded-lg border border-red-200 whitespace-pre-line\",\n                            children: loginError\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n                            lineNumber: 261,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"submit\",\n                            disabled: isLoggingIn || backendStatus === \"offline\",\n                            className: \"w-full py-3 px-4 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center\",\n                            children: isLoggingIn ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n                                        lineNumber: 272,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"Signing in...\"\n                                ]\n                            }, void 0, true) : \"Sign in\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n                            lineNumber: 265,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n                    lineNumber: 241,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center my-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-grow border-t border-gray-200\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n                            lineNumber: 281,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"mx-2 text-gray-400 text-xs\",\n                            children: \"OR\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n                            lineNumber: 282,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-grow border-t border-gray-200\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n                            lineNumber: 283,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n                    lineNumber: 280,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handleGoogleLogin,\n                    disabled: googleLoading || isLoggingIn || backendStatus === \"offline\" || networkStatus === \"offline\",\n                    className: \"w-full flex items-center justify-center gap-2 py-3 px-4 border border-gray-300 rounded-lg bg-white hover:bg-gray-100 transition text-gray-700 font-medium shadow-sm disabled:opacity-50 disabled:cursor-not-allowed\",\n                    children: [\n                        googleLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n                            lineNumber: 291,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-5 h-5\",\n                            viewBox: \"0 0 48 48\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"g\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        fill: \"#4285F4\",\n                                        d: \"M24 9.5c3.54 0 6.29 1.52 7.74 2.79l5.67-5.67C33.54 3.22 29.3 1 24 1 14.82 1 6.99 6.98 3.69 15.14l6.97 5.41C12.22 14.05 17.61 9.5 24 9.5z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n                                        lineNumber: 295,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        fill: \"#34A853\",\n                                        d: \"M46.1 24.55c0-1.64-.15-3.22-.42-4.74H24v9.01h12.42c-.54 2.85-2.17 5.27-4.63 6.91l7.11 5.53C43.98 37.22 46.1 31.37 46.1 24.55z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n                                        lineNumber: 296,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        fill: \"#FBBC05\",\n                                        d: \"M10.66 28.55a14.7 14.7 0 010-9.1l-6.97-5.41A23.99 23.99 0 001 24c0 3.87.92 7.53 2.55 10.76l7.10-6.21z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n                                        lineNumber: 297,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        fill: \"#EA4335\",\n                                        d: \"M24 47c6.48 0 11.93-2.15 15.9-5.85l-7.11-5.53c-2.01 1.36-4.6 2.16-8.79 2.16-6.39 0-11.78-4.55-13.64-10.66l-7.10 6.21C6.99 41.02 14.82 47 24 47z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n                                        lineNumber: 298,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        fill: \"none\",\n                                        d: \"M1 1h46v46H1z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n                                        lineNumber: 299,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n                                lineNumber: 294,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n                            lineNumber: 293,\n                            columnNumber: 13\n                        }, this),\n                        googleLoading ? \"Redirecting to Google...\" : \"Sign in with Google\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n                    lineNumber: 285,\n                    columnNumber: 9\n                }, this),\n                backendStatus === \"offline\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-sm font-medium text-yellow-800 mb-2\",\n                            children: \"Backend Server Offline\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n                            lineNumber: 309,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-yellow-700\",\n                            children: \"The backend server is not accessible. Please ensure:\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n                            lineNumber: 310,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            className: \"text-xs text-yellow-700 mt-1 list-disc list-inside\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"Backend server is running on localhost:8000\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n                                    lineNumber: 314,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"No firewall is blocking the connection\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n                                    lineNumber: 315,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"Check backend logs for any startup errors\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n                                    lineNumber: 316,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n                            lineNumber: 313,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n                    lineNumber: 308,\n                    columnNumber: 11\n                }, this),\n                networkStatus === \"unstable\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-4 p-3 bg-orange-50 border border-orange-200 rounded-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-sm font-medium text-orange-800 mb-2\",\n                            children: \"Unstable Network Connection\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n                            lineNumber: 323,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-orange-700\",\n                            children: \"Your internet connection appears to be unstable. This may cause authentication issues:\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n                            lineNumber: 324,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            className: \"text-xs text-orange-700 mt-1 list-disc list-inside\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"Try using a wired connection instead of WiFi\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n                                    lineNumber: 328,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"Move closer to your router\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n                                    lineNumber: 329,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"Wait a few minutes and try again\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n                                    lineNumber: 330,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"Authentication may take longer than usual\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n                                    lineNumber: 331,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n                            lineNumber: 327,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n                    lineNumber: 322,\n                    columnNumber: 11\n                }, this),\n                networkStatus === \"slow\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-sm font-medium text-blue-800 mb-2\",\n                            children: \"Slow Network Connection\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n                            lineNumber: 338,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-blue-700\",\n                            children: \"Your internet connection is slow. Authentication may take longer than usual.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n                            lineNumber: 339,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n                    lineNumber: 337,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"mt-6 text-center text-sm text-gray-500\",\n                    children: [\n                        \"By signing in, you agree to our \",\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                            href: \"#\",\n                            className: \"underline\",\n                            children: \"Terms\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n                            lineNumber: 346,\n                            columnNumber: 43\n                        }, this),\n                        \" and \",\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                            href: \"#\",\n                            className: \"underline\",\n                            children: \"Privacy Policy\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n                            lineNumber: 346,\n                            columnNumber: 91\n                        }, this),\n                        \".\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n                    lineNumber: 345,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n            lineNumber: 222,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n        lineNumber: 221,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/auth/page.tsx\n");

/***/ }),

/***/ "(ssr)/./app/providers.tsx":
/*!***************************!*\
  !*** ./app/providers.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: () => (/* binding */ Providers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/contexts/ThemeContext */ \"(ssr)/./contexts/ThemeContext.tsx\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./contexts/AuthContext.tsx\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/queryClient.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* __next_internal_client_entry_do_not_use__ Providers auto */ \n\n\n\nconst queryClient = new _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.QueryClient({\n    defaultOptions: {\n        queries: {\n            retry: (failureCount, error)=>{\n                // Don't retry on 401 errors\n                if (error?.response?.status === 401) {\n                    return false;\n                }\n                // Retry up to 2 times for other errors\n                return failureCount < 2;\n            },\n            // Global error handler for queries\n            onError: (error)=>{\n                if (error?.response?.status === 401) {\n                    console.log(\"Query failed with 401, clearing tokens\");\n                    // Clear tokens on 401 errors\n                    localStorage.removeItem(\"dido_token\");\n                    localStorage.removeItem(\"dido_refresh_token\");\n                    localStorage.removeItem(\"dido_user\");\n                    // Dispatch event to notify components\n                    if (false) {}\n                }\n            }\n        },\n        mutations: {\n            retry: (failureCount, error)=>{\n                // Don't retry on 401 errors\n                if (error?.response?.status === 401) {\n                    return false;\n                }\n                // Retry up to 1 time for other errors\n                return failureCount < 1;\n            },\n            // Global error handler for mutations\n            onError: (error)=>{\n                if (error?.response?.status === 401) {\n                    console.log(\"Mutation failed with 401, clearing tokens\");\n                    // Clear tokens on 401 errors\n                    localStorage.removeItem(\"dido_token\");\n                    localStorage.removeItem(\"dido_refresh_token\");\n                    localStorage.removeItem(\"dido_user\");\n                    // Dispatch event to notify components\n                    if (false) {}\n                }\n            }\n        }\n    }\n});\nfunction Providers({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__.QueryClientProvider, {\n        client: queryClient,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_1__.ThemeProvider, {\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\providers.tsx\",\n                lineNumber: 67,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\providers.tsx\",\n            lineNumber: 66,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\providers.tsx\",\n        lineNumber: 65,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/providers.tsx\n");

/***/ }),

/***/ "(ssr)/./components/BackendStatusChecker.tsx":
/*!*********************************************!*\
  !*** ./components/BackendStatusChecker.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BackendStatusChecker)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction BackendStatusChecker({ onStatusChange, showDetails = false, className = \"\" }) {\n    const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"checking\");\n    const [details, setDetails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [lastCheck, setLastCheck] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [checkTimeout, setCheckTimeout] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const checkBackendStatus = async ()=>{\n        setStatus(\"checking\");\n        setDetails(\"Checking backend connectivity...\");\n        // Clear any existing timeout\n        if (checkTimeout) {\n            clearTimeout(checkTimeout);\n        }\n        // Set a timeout for the check\n        const timeout = setTimeout(()=>{\n            setStatus(\"offline\");\n            setDetails(\"Backend check timed out after 10 seconds. Server may be offline or unreachable.\");\n            onStatusChange?.(\"offline\");\n        }, 10000);\n        setCheckTimeout(timeout);\n        try {\n            const startTime = Date.now();\n            const response = await fetch(\"/api/auth/health\", {\n                method: \"GET\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                signal: AbortSignal.timeout(8000)\n            });\n            const endTime = Date.now();\n            const responseTime = endTime - startTime;\n            // Clear the timeout since we got a response\n            clearTimeout(timeout);\n            setCheckTimeout(null);\n            setLastCheck(new Date());\n            if (response.ok) {\n                const data = await response.json();\n                setStatus(\"online\");\n                setDetails(`Backend is online (${responseTime}ms) - Uptime: ${Math.floor(data.uptime)}s`);\n                onStatusChange?.(\"online\");\n            } else {\n                setStatus(\"error\");\n                setDetails(`Backend returned error: ${response.status} ${response.statusText} (${responseTime}ms)`);\n                onStatusChange?.(\"error\");\n            }\n        } catch (error) {\n            // Clear the timeout since we got an error\n            clearTimeout(timeout);\n            setCheckTimeout(null);\n            setLastCheck(new Date());\n            setStatus(\"offline\");\n            setDetails(`Cannot connect to backend: ${error instanceof Error ? error.message : \"Unknown error\"}`);\n            onStatusChange?.(\"offline\");\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        checkBackendStatus();\n        // Cleanup timeout on unmount\n        return ()=>{\n            if (checkTimeout) {\n                clearTimeout(checkTimeout);\n            }\n        };\n    }, []);\n    const getStatusIcon = ()=>{\n        switch(status){\n            case \"checking\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\BackendStatusChecker.tsx\",\n                    lineNumber: 91,\n                    columnNumber: 11\n                }, this);\n            case \"online\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-4 h-4 text-green-600\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 20 20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n                        clipRule: \"evenodd\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\BackendStatusChecker.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\BackendStatusChecker.tsx\",\n                    lineNumber: 95,\n                    columnNumber: 11\n                }, this);\n            case \"offline\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-4 h-4 text-red-600\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 20 20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\",\n                        clipRule: \"evenodd\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\BackendStatusChecker.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\BackendStatusChecker.tsx\",\n                    lineNumber: 101,\n                    columnNumber: 11\n                }, this);\n            case \"error\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-4 h-4 text-yellow-600\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 20 20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\",\n                        clipRule: \"evenodd\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\BackendStatusChecker.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\BackendStatusChecker.tsx\",\n                    lineNumber: 107,\n                    columnNumber: 11\n                }, this);\n        }\n    };\n    const getStatusText = ()=>{\n        switch(status){\n            case \"checking\":\n                return \"Checking backend connection...\";\n            case \"online\":\n                return \"Backend server is online\";\n            case \"offline\":\n                return \"Backend server is offline\";\n            case \"error\":\n                return \"Backend server error\";\n        }\n    };\n    const getStatusColor = ()=>{\n        switch(status){\n            case \"checking\":\n                return \"text-blue-600\";\n            case \"online\":\n                return \"text-green-600\";\n            case \"offline\":\n                return \"text-red-600\";\n            case \"error\":\n                return \"text-yellow-600\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `flex items-center ${className}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mr-2\",\n                children: getStatusIcon()\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\BackendStatusChecker.tsx\",\n                lineNumber: 142,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `text-sm font-medium ${getStatusColor()}`,\n                        children: getStatusText()\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\BackendStatusChecker.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 9\n                    }, this),\n                    showDetails && details && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-500 mt-1\",\n                        children: details\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\BackendStatusChecker.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 11\n                    }, this),\n                    showDetails && lastCheck && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-400 mt-1\",\n                        children: [\n                            \"Last checked: \",\n                            lastCheck.toLocaleTimeString()\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\BackendStatusChecker.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\BackendStatusChecker.tsx\",\n                lineNumber: 145,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: checkBackendStatus,\n                disabled: status === \"checking\",\n                className: \"ml-2 px-2 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded transition disabled:opacity-50\",\n                title: \"Refresh backend status\",\n                children: status === \"checking\" ? \"Checking...\" : \"Refresh\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\BackendStatusChecker.tsx\",\n                lineNumber: 160,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\BackendStatusChecker.tsx\",\n        lineNumber: 141,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/BackendStatusChecker.tsx\n");

/***/ }),

/***/ "(ssr)/./components/NetworkStatusChecker.tsx":
/*!*********************************************!*\
  !*** ./components/NetworkStatusChecker.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NetworkStatusChecker)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction NetworkStatusChecker({ onStatusChange, showDetails = false, className = \"\" }) {\n    const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"checking\");\n    const [details, setDetails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [lastCheck, setLastCheck] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [connectionType, setConnectionType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [checkTimeout, setCheckTimeout] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const checkNetworkStatus = async ()=>{\n        setStatus(\"checking\");\n        setDetails(\"Checking network connectivity...\");\n        // Clear any existing timeout\n        if (checkTimeout) {\n            clearTimeout(checkTimeout);\n        }\n        // Set a timeout for the check\n        const timeout = setTimeout(()=>{\n            setStatus(\"offline\");\n            setDetails(\"Network check timed out after 15 seconds. No internet connection detected.\");\n            onStatusChange?.(\"offline\");\n        }, 15000);\n        setCheckTimeout(timeout);\n        try {\n            // Check connection type\n            if (\"connection\" in navigator) {\n                const conn = navigator.connection;\n                if (conn) {\n                    setConnectionType(`${conn.effectiveType || \"unknown\"} (${conn.type || \"unknown\"})`);\n                }\n            }\n            // Test connection speed by making multiple requests with shorter timeouts\n            const testUrls = [\n                \"https://www.google.com/favicon.ico\",\n                \"https://www.cloudflare.com/favicon.ico\",\n                \"https://www.github.com/favicon.ico\"\n            ];\n            const results = await Promise.allSettled(testUrls.map((url)=>fetch(url, {\n                    method: \"HEAD\",\n                    mode: \"no-cors\",\n                    cache: \"no-cache\",\n                    signal: AbortSignal.timeout(5000) // 5 second timeout per request\n                })));\n            const successfulRequests = results.filter((result)=>result.status === \"fulfilled\").length;\n            const totalRequests = results.length;\n            // Clear the timeout since we got results\n            clearTimeout(timeout);\n            setCheckTimeout(null);\n            setLastCheck(new Date());\n            if (successfulRequests === 0) {\n                setStatus(\"offline\");\n                setDetails(\"No internet connection detected. Please check your network connection.\");\n                onStatusChange?.(\"offline\");\n            } else if (successfulRequests < totalRequests) {\n                setStatus(\"unstable\");\n                setDetails(`Unstable connection: ${successfulRequests}/${totalRequests} requests successful. Some requests failed.`);\n                onStatusChange?.(\"unstable\");\n            } else {\n                // All requests successful, now test speed\n                const startTime = Date.now();\n                try {\n                    await fetch(\"https://www.google.com/favicon.ico\", {\n                        method: \"HEAD\",\n                        mode: \"no-cors\",\n                        cache: \"no-cache\",\n                        signal: AbortSignal.timeout(3000) // 3 second timeout for speed test\n                    });\n                    const endTime = Date.now();\n                    const responseTime = endTime - startTime;\n                    if (responseTime < 1000) {\n                        setStatus(\"good\");\n                        setDetails(`Good connection: ${responseTime}ms response time`);\n                        onStatusChange?.(\"good\");\n                    } else if (responseTime < 3000) {\n                        setStatus(\"slow\");\n                        setDetails(`Slow connection: ${responseTime}ms response time`);\n                        onStatusChange?.(\"slow\");\n                    } else {\n                        setStatus(\"unstable\");\n                        setDetails(`Very slow connection: ${responseTime}ms response time`);\n                        onStatusChange?.(\"unstable\");\n                    }\n                } catch (error) {\n                    setStatus(\"unstable\");\n                    setDetails(\"Connection test failed\");\n                    onStatusChange?.(\"unstable\");\n                }\n            }\n        } catch (error) {\n            // Clear the timeout since we got an error\n            clearTimeout(timeout);\n            setCheckTimeout(null);\n            setLastCheck(new Date());\n            setStatus(\"offline\");\n            setDetails(`Network check failed: ${error instanceof Error ? error.message : \"Unknown error\"}`);\n            onStatusChange?.(\"offline\");\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        checkNetworkStatus();\n        // Cleanup timeout on unmount\n        return ()=>{\n            if (checkTimeout) {\n                clearTimeout(checkTimeout);\n            }\n        };\n    }, []);\n    const getStatusIcon = ()=>{\n        switch(status){\n            case \"checking\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\NetworkStatusChecker.tsx\",\n                    lineNumber: 142,\n                    columnNumber: 11\n                }, this);\n            case \"good\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-4 h-4 text-green-600\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 20 20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n                        clipRule: \"evenodd\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\NetworkStatusChecker.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\NetworkStatusChecker.tsx\",\n                    lineNumber: 146,\n                    columnNumber: 11\n                }, this);\n            case \"slow\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-4 h-4 text-yellow-600\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 20 20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\",\n                        clipRule: \"evenodd\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\NetworkStatusChecker.tsx\",\n                        lineNumber: 153,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\NetworkStatusChecker.tsx\",\n                    lineNumber: 152,\n                    columnNumber: 11\n                }, this);\n            case \"unstable\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-4 h-4 text-orange-600\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 20 20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\",\n                        clipRule: \"evenodd\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\NetworkStatusChecker.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\NetworkStatusChecker.tsx\",\n                    lineNumber: 158,\n                    columnNumber: 11\n                }, this);\n            case \"offline\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-4 h-4 text-red-600\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 20 20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\",\n                        clipRule: \"evenodd\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\NetworkStatusChecker.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\NetworkStatusChecker.tsx\",\n                    lineNumber: 164,\n                    columnNumber: 11\n                }, this);\n        }\n    };\n    const getStatusText = ()=>{\n        switch(status){\n            case \"checking\":\n                return \"Checking network connection...\";\n            case \"good\":\n                return \"Network connection is good\";\n            case \"slow\":\n                return \"Network connection is slow\";\n            case \"unstable\":\n                return \"Network connection is unstable\";\n            case \"offline\":\n                return \"No network connection\";\n        }\n    };\n    const getStatusColor = ()=>{\n        switch(status){\n            case \"checking\":\n                return \"text-blue-600\";\n            case \"good\":\n                return \"text-green-600\";\n            case \"slow\":\n                return \"text-yellow-600\";\n            case \"unstable\":\n                return \"text-orange-600\";\n            case \"offline\":\n                return \"text-red-600\";\n        }\n    };\n    const getConnectionAdvice = ()=>{\n        switch(status){\n            case \"good\":\n                return null;\n            case \"slow\":\n                return \"Your connection is slow. Authentication may take longer than usual.\";\n            case \"unstable\":\n                return \"Your connection is unstable. Consider using a wired connection or moving closer to your router.\";\n            case \"offline\":\n                return \"No internet connection detected. Please check your network settings.\";\n            default:\n                return null;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `flex items-center ${className}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mr-2\",\n                children: getStatusIcon()\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\NetworkStatusChecker.tsx\",\n                lineNumber: 218,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `text-sm font-medium ${getStatusColor()}`,\n                        children: getStatusText()\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\NetworkStatusChecker.tsx\",\n                        lineNumber: 222,\n                        columnNumber: 9\n                    }, this),\n                    showDetails && details && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-500 mt-1\",\n                        children: details\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\NetworkStatusChecker.tsx\",\n                        lineNumber: 226,\n                        columnNumber: 11\n                    }, this),\n                    showDetails && connectionType && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-400 mt-1\",\n                        children: [\n                            \"Connection: \",\n                            connectionType\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\NetworkStatusChecker.tsx\",\n                        lineNumber: 231,\n                        columnNumber: 11\n                    }, this),\n                    showDetails && lastCheck && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-400 mt-1\",\n                        children: [\n                            \"Last checked: \",\n                            lastCheck.toLocaleTimeString()\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\NetworkStatusChecker.tsx\",\n                        lineNumber: 236,\n                        columnNumber: 11\n                    }, this),\n                    showDetails && getConnectionAdvice() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-orange-600 mt-1 font-medium\",\n                        children: getConnectionAdvice()\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\NetworkStatusChecker.tsx\",\n                        lineNumber: 241,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\NetworkStatusChecker.tsx\",\n                lineNumber: 221,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: checkNetworkStatus,\n                disabled: status === \"checking\",\n                className: \"ml-2 px-2 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded transition disabled:opacity-50\",\n                title: \"Refresh network status\",\n                children: status === \"checking\" ? \"Checking...\" : \"Refresh\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\NetworkStatusChecker.tsx\",\n                lineNumber: 246,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\NetworkStatusChecker.tsx\",\n        lineNumber: 217,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/NetworkStatusChecker.tsx\n");

/***/ }),

/***/ "(ssr)/./contexts/AuthContext.tsx":
/*!**********************************!*\
  !*** ./contexts/AuthContext.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [token, setToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [refreshToken, setRefreshToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [fetchingWarehouse, setFetchingWarehouse] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [refreshingToken, setRefreshingToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    // Use refs to avoid dependency issues\n    const fetchingWarehouseRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const tokenRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const userRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Update refs when state changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchingWarehouseRef.current = fetchingWarehouse;\n    }, [\n        fetchingWarehouse\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        tokenRef.current = token;\n    }, [\n        token\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        userRef.current = user;\n    }, [\n        user\n    ]);\n    // Helper to fetch warehouse info in background\n    const fetchAndPersistWarehouseInfo = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (userObjParam, tokenParam)=>{\n        const tokenToUse = tokenParam !== undefined ? tokenParam : tokenRef.current;\n        const userToFetch = userObjParam || userRef.current;\n        console.log(\"fetchAndPersistWarehouseInfo called with:\", {\n            tokenToUse: !!tokenToUse,\n            userToFetch: userToFetch?.uuid,\n            userToFetchWarehouseUuid: userToFetch?.warehouseUuidString || userToFetch?.warehouseUuid,\n            fetchingWarehouse: fetchingWarehouseRef.current,\n            currentUser: userRef.current?.uuid,\n            currentUserWarehouseUuid: userRef.current?.warehouseUuidString || userRef.current?.warehouseUuid\n        });\n        if (!tokenToUse || !userToFetch?.uuid || fetchingWarehouseRef.current) {\n            console.log(\"fetchAndPersistWarehouseInfo early return:\", {\n                noToken: !tokenToUse,\n                noUserUuid: !userToFetch?.uuid,\n                alreadyFetching: fetchingWarehouseRef.current\n            });\n            return;\n        }\n        // Additional safety check: ensure UUID is a valid string\n        if (typeof userToFetch.uuid !== \"string\" || userToFetch.uuid.trim() === \"\") {\n            console.log(\"fetchAndPersistWarehouseInfo early return: invalid UUID:\", userToFetch.uuid);\n            return;\n        }\n        // Additional check: if we don't have a valid token in localStorage, don't proceed\n        const storedToken = localStorage.getItem(\"dido_token\");\n        if (!storedToken) {\n            console.log(\"fetchAndPersistWarehouseInfo early return: no stored token\");\n            return;\n        }\n        setFetchingWarehouse(true);\n        try {\n            console.log(\"Fetching latest user info for UUID:\", userToFetch.uuid);\n            // Always fetch the latest user info from backend to ensure we have current data\n            const userRes = await fetch(`/api/users/${userToFetch.uuid}`, {\n                headers: {\n                    Authorization: `Bearer ${tokenToUse}`\n                }\n            });\n            if (!userRes.ok) {\n                console.error(\"Failed to fetch user info:\", userRes.status, userRes.statusText);\n                return;\n            }\n            const latestUser = await userRes.json();\n            console.log(\"Latest user info from API:\", latestUser);\n            console.log(\"Warehouse UUID from backend:\", {\n                warehouseUuidString: latestUser.warehouseUuidString,\n                warehouseUuid: latestUser.warehouseUuid,\n                finalWarehouseUuid: latestUser.warehouseUuidString || latestUser.warehouseUuid\n            });\n            // Check if user has a warehouse assigned\n            const warehouseUuid = latestUser.warehouseUuidString || latestUser.warehouseUuid;\n            if (!warehouseUuid) {\n                console.log(\"No warehouse assigned to user, setting user without warehouse info\");\n                const updatedUser = {\n                    ...latestUser,\n                    warehouseUuid: null,\n                    warehouseName: null\n                };\n                setUser(updatedUser);\n                localStorage.setItem(\"dido_user\", JSON.stringify(updatedUser));\n                return;\n            }\n            console.log(\"Fetching warehouse info for UUID:\", warehouseUuid);\n            const warehouseRes = await fetch(`/api/warehouses/${warehouseUuid}`, {\n                headers: {\n                    Authorization: `Bearer ${tokenToUse}`\n                }\n            });\n            if (!warehouseRes.ok) {\n                console.error(\"Failed to fetch warehouse info:\", warehouseRes.status, warehouseRes.statusText);\n                // If warehouse fetch fails, still update user with current data but without warehouse name\n                const updatedUser = {\n                    ...latestUser,\n                    warehouseUuid: warehouseUuid,\n                    warehouseName: null\n                };\n                setUser(updatedUser);\n                localStorage.setItem(\"dido_user\", JSON.stringify(updatedUser));\n                return;\n            }\n            const warehouse = await warehouseRes.json();\n            console.log(\"Warehouse info from API:\", warehouse);\n            const updatedUser = {\n                ...latestUser,\n                warehouseUuid: warehouseUuid,\n                warehouseName: warehouse.name\n            };\n            console.log(\"Setting updated user with warehouse info:\", updatedUser);\n            setUser(updatedUser);\n            localStorage.setItem(\"dido_user\", JSON.stringify(updatedUser));\n        } catch (err) {\n            console.error(\"Error fetching warehouse info:\", err);\n            // On error, still try to update user with current data from backend\n            try {\n                const userRes = await fetch(`/api/users/${userToFetch.uuid}`, {\n                    headers: {\n                        Authorization: `Bearer ${tokenToUse}`\n                    }\n                });\n                if (userRes.ok) {\n                    const latestUser = await userRes.json();\n                    const updatedUser = {\n                        ...latestUser,\n                        warehouseUuid: latestUser.warehouseUuidString || latestUser.warehouseUuid,\n                        warehouseName: null\n                    };\n                    setUser(updatedUser);\n                    localStorage.setItem(\"dido_user\", JSON.stringify(updatedUser));\n                }\n            } catch (fallbackErr) {\n                console.error(\"Error in fallback user update:\", fallbackErr);\n            }\n        } finally{\n            setFetchingWarehouse(false);\n        }\n    }, []); // Remove all dependencies since we're using refs\n    // Refresh access token using refresh token\n    const refreshAccessToken = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        if (!refreshToken || refreshingToken) return false;\n        setRefreshingToken(true);\n        try {\n            const response = await fetch(\"/api/auth/refresh\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    refreshToken,\n                    clientIp: \"frontend\",\n                    userAgent: navigator.userAgent\n                })\n            });\n            if (!response.ok) {\n                console.error(\"Token refresh failed:\", response.status);\n                return false;\n            }\n            const data = await response.json();\n            setToken(data.accessToken);\n            setRefreshToken(data.refreshToken);\n            localStorage.setItem(\"dido_token\", data.accessToken);\n            localStorage.setItem(\"dido_refresh_token\", data.refreshToken);\n            console.log(\"Token refreshed successfully\");\n            return true;\n        } catch (error) {\n            console.error(\"Error refreshing token:\", error);\n            return false;\n        } finally{\n            setRefreshingToken(false);\n        }\n    }, [\n        refreshToken,\n        refreshingToken\n    ]);\n    // Get security status for the current user\n    const getSecurityStatus = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        if (!token) return null;\n        try {\n            const response = await fetch(\"/api/auth/security-status\", {\n                headers: {\n                    Authorization: `Bearer ${token}`\n                }\n            });\n            if (!response.ok) {\n                console.error(\"Failed to get security status:\", response.status);\n                return null;\n            }\n            return await response.json();\n        } catch (error) {\n            console.error(\"Error getting security status:\", error);\n            return null;\n        }\n    }, [\n        token\n    ]);\n    // On initial mount: restore session from localStorage\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.log(\"AuthContext initial mount - checking localStorage\");\n        const storedUser = localStorage.getItem(\"dido_user\");\n        const storedToken = localStorage.getItem(\"dido_token\");\n        const storedRefreshToken = localStorage.getItem(\"dido_refresh_token\");\n        console.log(\"Stored data from localStorage:\", {\n            hasStoredUser: !!storedUser,\n            hasStoredToken: !!storedToken,\n            hasStoredRefreshToken: !!storedRefreshToken\n        });\n        if (storedUser && storedToken) {\n            const userObj = JSON.parse(storedUser);\n            console.log(\"Restored user from localStorage:\", {\n                userUuid: userObj.uuid,\n                userEmail: userObj.email,\n                userWarehouseUuid: userObj.warehouseUuidString || userObj.warehouseUuid,\n                userWarehouseName: userObj.warehouseName\n            });\n            // Validate that the user UUID is still valid by fetching from backend\n            const validateUserExists = async ()=>{\n                try {\n                    console.log(\"Validating user UUID from localStorage:\", userObj.uuid);\n                    const response = await fetch(`/api/users/${userObj.uuid}`, {\n                        headers: {\n                            Authorization: `Bearer ${storedToken}`\n                        }\n                    });\n                    if (response.status === 404) {\n                        console.log(\"User UUID not found in backend (404), clearing localStorage and redirecting to login\");\n                        // User was deleted/recreated, clear localStorage and redirect to login\n                        localStorage.removeItem(\"dido_token\");\n                        localStorage.removeItem(\"dido_refresh_token\");\n                        localStorage.removeItem(\"dido_user\");\n                        router.replace(\"/auth\");\n                        return;\n                    }\n                    if (!response.ok) {\n                        console.log(\"Failed to validate user UUID:\", response.status, response.statusText);\n                        // If we can't validate the user, clear localStorage and redirect to login\n                        localStorage.removeItem(\"dido_token\");\n                        localStorage.removeItem(\"dido_refresh_token\");\n                        localStorage.removeItem(\"dido_user\");\n                        router.replace(\"/auth\");\n                        return;\n                    }\n                    // User exists, proceed with normal flow\n                    console.log(\"User UUID validated successfully\");\n                    setUser(userObj);\n                    setToken(storedToken);\n                    if (storedRefreshToken) {\n                        setRefreshToken(storedRefreshToken);\n                    }\n                    // Always trigger background fetch to ensure we have the latest warehouse info\n                    // This is especially important after database clearing when warehouse UUIDs may have changed\n                    console.log(\"Triggering background warehouse info fetch to ensure latest data from localStorage\");\n                    setTimeout(()=>{\n                        fetchAndPersistWarehouseInfo(userObj, storedToken);\n                    }, 0);\n                } catch (error) {\n                    console.error(\"Error validating user UUID:\", error);\n                    // If there's a network error, clear localStorage and redirect to login\n                    localStorage.removeItem(\"dido_token\");\n                    localStorage.removeItem(\"dido_refresh_token\");\n                    localStorage.removeItem(\"dido_user\");\n                    router.replace(\"/auth\");\n                }\n            };\n            // Validate user exists before setting state\n            validateUserExists();\n        } else {\n            console.log(\"No stored user/token found in localStorage\");\n        }\n        // If we have no valid authentication and we're not on the auth page, redirect\n        if (!storedToken && !window.location.pathname.includes(\"/auth\")) {\n            console.log(\"No valid authentication found, redirecting to login\");\n            router.replace(\"/auth\");\n        }\n        setLoading(false);\n    }, []); // Remove fetchAndPersistWarehouseInfo from dependencies to prevent infinite loop\n    // Periodically validate session and refresh token if needed\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!token || !refreshToken) {\n            console.log(\"No token or refresh token available, skipping periodic validation\");\n            return;\n        }\n        const interval = setInterval(async ()=>{\n            try {\n                console.log(\"Periodic token validation - checking token validity\");\n                // Try to validate current token\n                const res = await fetch(\"/api/auth/validate\", {\n                    headers: {\n                        Authorization: `Bearer ${token}`\n                    }\n                });\n                if (!res.ok) {\n                    console.log(\"Token validation failed, attempting refresh\");\n                    // Token is invalid, try to refresh\n                    const refreshSuccess = await refreshAccessToken();\n                    if (!refreshSuccess) {\n                        console.log(\"Token refresh failed, logging out user\");\n                        localStorage.removeItem(\"dido_token\");\n                        localStorage.removeItem(\"dido_refresh_token\");\n                        localStorage.removeItem(\"dido_user\");\n                        router.replace(\"/auth\");\n                    } else {\n                        console.log(\"Token refresh successful\");\n                    }\n                } else {\n                    console.log(\"Token validation successful\");\n                }\n            } catch (error) {\n                console.log(\"Network error during token validation, attempting refresh\");\n                // Network error, try to refresh token\n                const refreshSuccess = await refreshAccessToken();\n                if (!refreshSuccess) {\n                    console.log(\"Token refresh failed after network error, logging out user\");\n                    localStorage.removeItem(\"dido_token\");\n                    localStorage.removeItem(\"dido_refresh_token\");\n                    localStorage.removeItem(\"dido_user\");\n                    router.replace(\"/auth\");\n                } else {\n                    console.log(\"Token refresh successful after network error\");\n                }\n            }\n        }, 30 * 60 * 1000); // 30 minutes\n        return ()=>clearInterval(interval);\n    }, [\n        token,\n        refreshToken,\n        refreshAccessToken,\n        router\n    ]);\n    const login = (user, token, refreshTokenValue)=>{\n        console.log(\"AuthContext login called with:\", {\n            userUuid: user.uuid,\n            userEmail: user.email,\n            userWarehouseUuid: user.warehouseUuidString || user.warehouseUuid,\n            userWarehouseUuidString: user.warehouseUuidString,\n            userWarehouseUuidLegacy: user.warehouseUuid,\n            userWarehouseName: user.warehouseName,\n            hasToken: !!token,\n            hasRefreshToken: !!refreshTokenValue\n        });\n        setUser(user);\n        setToken(token);\n        setRefreshToken(refreshTokenValue);\n        localStorage.setItem(\"dido_user\", JSON.stringify(user));\n        localStorage.setItem(\"dido_token\", token);\n        localStorage.setItem(\"dido_refresh_token\", refreshTokenValue);\n        // Always trigger background fetch to ensure we have the latest warehouse info\n        // This is especially important after database clearing when warehouse UUIDs may have changed\n        console.log(\"Triggering background warehouse info fetch to ensure latest data\");\n        setTimeout(()=>{\n            fetchAndPersistWarehouseInfo(user, token);\n        }, 0);\n    };\n    const logout = async ()=>{\n        console.log(\"AuthContext logout called - clearing session and redirecting to /auth\");\n        // Revoke tokens on backend if we have them\n        if (token && refreshToken) {\n            try {\n                await fetch(\"/api/auth/logout\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\",\n                        \"Authorization\": `Bearer ${token}`\n                    },\n                    body: JSON.stringify({\n                        refreshToken,\n                        revokeAll: false\n                    })\n                });\n            } catch (error) {\n                console.error(\"Error during logout:\", error);\n            // Continue with logout even if backend call fails\n            }\n        }\n        setUser(null);\n        setToken(null);\n        setRefreshToken(null);\n        localStorage.removeItem(\"dido_token\");\n        localStorage.removeItem(\"dido_refresh_token\");\n        localStorage.removeItem(\"dido_user\");\n        console.log(\"Session cleared, redirecting to /auth\");\n        router.replace(\"/auth\");\n    };\n    const checkUserExists = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (uuid)=>{\n        if (!token) return false;\n        // Safety check: ensure UUID is valid\n        if (!uuid || typeof uuid !== \"string\" || uuid.trim() === \"\") {\n            console.log(\"checkUserExists: invalid UUID provided:\", uuid);\n            return false;\n        }\n        try {\n            const response = await fetch(`/api/users/${uuid}`, {\n                headers: {\n                    Authorization: `Bearer ${token}`\n                }\n            });\n            if (response.status === 404) return false;\n            if (!response.ok) throw new Error(\"Failed to check user existence\");\n            return true;\n        } catch (error) {\n            console.error(\"Error checking user existence:\", error);\n            return false;\n        }\n    }, [\n        token\n    ]);\n    const switchWarehouse = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (warehouseUuid, warehouseName)=>{\n        if (!user?.uuid || !token) {\n            throw new Error(\"User not authenticated\");\n        }\n        // Safety check: ensure user UUID is valid\n        if (typeof user.uuid !== \"string\" || user.uuid.trim() === \"\") {\n            throw new Error(\"Invalid user UUID\");\n        }\n        try {\n            // Update user's warehouse on backend\n            const response = await fetch(`/api/users/${user.uuid}/warehouse`, {\n                method: \"PATCH\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    Authorization: `Bearer ${token}`\n                },\n                body: JSON.stringify({\n                    warehouseUuid\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to update warehouse on backend\");\n            }\n            // Update user context with new warehouse info\n            const updatedUser = {\n                ...user,\n                warehouseUuid: warehouseUuid,\n                warehouseName: warehouseName\n            };\n            // Update localStorage\n            localStorage.setItem(\"dido_user\", JSON.stringify(updatedUser));\n            // Update context state\n            setUser(updatedUser);\n            console.log(\"Warehouse switched successfully:\", {\n                warehouseUuid,\n                warehouseName\n            });\n        } catch (error) {\n            console.error(\"Error switching warehouse:\", error);\n            throw error;\n        }\n    }, [\n        user,\n        token\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: {\n            user,\n            token,\n            refreshToken,\n            loading,\n            login,\n            logout,\n            refreshAccessToken,\n            getSecurityStatus,\n            fetchAndPersistWarehouseInfo,\n            checkUserExists,\n            switchWarehouse\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 550,\n        columnNumber: 5\n    }, this);\n}\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (!context) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./contexts/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./contexts/ThemeContext.tsx":
/*!***********************************!*\
  !*** ./contexts/ThemeContext.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider),\n/* harmony export */   useTheme: () => (/* binding */ useTheme)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _styles_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/styles/themes */ \"(ssr)/./styles/themes.ts\");\n/* __next_internal_client_entry_do_not_use__ ThemeProvider,useTheme auto */ \n\n\nconst ThemeContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst ThemeProvider = ({ children, initialTheme = \"white\" })=>{\n    const [themeName, setThemeName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialTheme);\n    const [theme, setTheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((0,_styles_themes__WEBPACK_IMPORTED_MODULE_2__.getThemeByName)(initialTheme));\n    // Load saved theme from localStorage on initial render (client-side only)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (false) {}\n    }, []);\n    // Update CSS variables when theme changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Only run on client side\n        if (true) return;\n        const root = document.documentElement;\n        // Set color variables\n        Object.entries(theme.colors).forEach(([key, value])=>{\n            root.style.setProperty(`--color-${key}`, value);\n        });\n        // Set shadow variables\n        Object.entries(theme.shadows).forEach(([key, value])=>{\n            root.style.setProperty(`--shadow-${key}`, value);\n        });\n        // Set border radius variables\n        Object.entries(theme.borderRadius).forEach(([key, value])=>{\n            root.style.setProperty(`--radius-${key}`, value);\n        });\n        // Update the data-theme attribute for potential CSS selectors\n        root.setAttribute(\"data-theme\", theme.name);\n    }, [\n        theme\n    ]);\n    const changeTheme = (newThemeName)=>{\n        const newTheme = (0,_styles_themes__WEBPACK_IMPORTED_MODULE_2__.getThemeByName)(newThemeName);\n        setThemeName(newTheme.name);\n        setTheme(newTheme);\n        // Save to localStorage\n        localStorage.setItem(\"theme\", newTheme.name);\n    };\n    const availableThemes = _styles_themes__WEBPACK_IMPORTED_MODULE_2__.allThemes.map((t)=>({\n            name: t.name,\n            label: t.name.charAt(0).toUpperCase() + t.name.slice(1)\n        }));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ThemeContext.Provider, {\n        value: {\n            theme,\n            themeName,\n            setTheme: changeTheme,\n            availableThemes\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\contexts\\\\ThemeContext.tsx\",\n        lineNumber: 79,\n        columnNumber: 5\n    }, undefined);\n};\nconst useTheme = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ThemeContext);\n    if (context === undefined) {\n        throw new Error(\"useTheme must be used within a ThemeProvider\");\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./contexts/ThemeContext.tsx\n");

/***/ }),

/***/ "(ssr)/./styles/themes.ts":
/*!**************************!*\
  !*** ./styles/themes.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   allThemes: () => (/* binding */ allThemes),\n/* harmony export */   blueTheme: () => (/* binding */ blueTheme),\n/* harmony export */   darkTheme: () => (/* binding */ darkTheme),\n/* harmony export */   defaultTheme: () => (/* binding */ defaultTheme),\n/* harmony export */   getThemeByName: () => (/* binding */ getThemeByName),\n/* harmony export */   lightTheme: () => (/* binding */ lightTheme)\n/* harmony export */ });\nconst lightTheme = {\n    name: \"light\",\n    colors: {\n        primary: \"#2563eb\",\n        primaryLight: \"#3b82f6\",\n        primaryDark: \"#1d4ed8\",\n        background: \"#f9fafb\",\n        surface: \"#ffffff\",\n        textPrimary: \"#111827\",\n        textSecondary: \"#4b5563\",\n        success: \"#10b981\",\n        warning: \"#f59e0b\",\n        error: \"#ef4444\",\n        info: \"#3b82f6\",\n        border: \"#e5e7eb\",\n        hover: \"#f3f4f6\",\n        active: \"#e5e7eb\"\n    },\n    shadows: {\n        small: \"0 1px 2px 0 rgb(0 0 0 / 0.05)\",\n        medium: \"0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)\",\n        large: \"0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)\"\n    },\n    borderRadius: {\n        small: \"0.25rem\",\n        medium: \"0.5rem\",\n        large: \"1rem\"\n    }\n};\nconst darkTheme = {\n    name: \"dark\",\n    colors: {\n        primary: \"#3b82f6\",\n        primaryLight: \"#60a5fa\",\n        primaryDark: \"#2563eb\",\n        background: \"#111827\",\n        surface: \"#1f2937\",\n        textPrimary: \"#f9fafb\",\n        textSecondary: \"#d1d5db\",\n        success: \"#10b981\",\n        warning: \"#f59e0b\",\n        error: \"#ef4444\",\n        info: \"#3b82f6\",\n        border: \"#374151\",\n        hover: \"#1f2937\",\n        active: \"#374151\"\n    },\n    shadows: {\n        small: \"0 1px 2px 0 rgb(0 0 0 / 0.25)\",\n        medium: \"0 4px 6px -1px rgb(0 0 0 / 0.3), 0 2px 4px -2px rgb(0 0 0 / 0.3)\",\n        large: \"0 10px 15px -3px rgb(0 0 0 / 0.3), 0 4px 6px -4px rgb(0 0 0 / 0.3)\"\n    },\n    borderRadius: {\n        small: \"0.25rem\",\n        medium: \"0.5rem\",\n        large: \"1rem\"\n    }\n};\nconst blueTheme = {\n    name: \"blue\",\n    colors: {\n        primary: \"#1d4ed8\",\n        primaryLight: \"#2563eb\",\n        primaryDark: \"#1e40af\",\n        background: \"#eff6ff\",\n        surface: \"#ffffff\",\n        textPrimary: \"#1e3a8a\",\n        textSecondary: \"#1e40af\",\n        success: \"#047857\",\n        warning: \"#b45309\",\n        error: \"#b91c1c\",\n        info: \"#1d4ed8\",\n        border: \"#bfdbfe\",\n        hover: \"#dbeafe\",\n        active: \"#bfdbfe\"\n    },\n    shadows: {\n        small: \"0 1px 3px 0 rgb(29 78 216 / 0.1), 0 1px 2px -1px rgb(29 78 216 / 0.1)\",\n        medium: \"0 4px 6px -1px rgb(29 78 216 / 0.1), 0 2px 4px -2px rgb(29 78 216 / 0.1)\",\n        large: \"0 10px 15px -3px rgb(29 78 216 / 0.1), 0 4px 6px -4px rgb(29 78 216 / 0.1)\"\n    },\n    borderRadius: {\n        small: \"0.25rem\",\n        medium: \"0.5rem\",\n        large: \"1rem\"\n    }\n};\n// Export a default theme (can be changed based on user preference)\nconst defaultTheme = lightTheme;\n// Export all themes as an array\nconst allThemes = [\n    lightTheme,\n    darkTheme,\n    blueTheme\n];\n// Helper function to get a theme by name\nconst getThemeByName = (name)=>{\n    return allThemes.find((theme)=>theme.name === name) || defaultTheme;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./styles/themes.ts\n");

/***/ }),

/***/ "(ssr)/./utils/networkUtils.ts":
/*!*******************************!*\
  !*** ./utils/networkUtils.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addNetworkListeners: () => (/* binding */ addNetworkListeners),\n/* harmony export */   getConnectionAdvice: () => (/* binding */ getConnectionAdvice),\n/* harmony export */   getNetworkInfo: () => (/* binding */ getNetworkInfo),\n/* harmony export */   isConnectionSlow: () => (/* binding */ isConnectionSlow),\n/* harmony export */   isConnectionUnstable: () => (/* binding */ isConnectionUnstable),\n/* harmony export */   retryWithBackoff: () => (/* binding */ retryWithBackoff),\n/* harmony export */   testConnectionSpeed: () => (/* binding */ testConnectionSpeed)\n/* harmony export */ });\n// Network utility functions for handling connection issues\nfunction getNetworkInfo() {\n    const info = {\n        online: navigator.onLine\n    };\n    // Check for Network Information API\n    if (\"connection\" in navigator) {\n        const conn = navigator.connection;\n        if (conn) {\n            info.connectionType = conn.type;\n            info.effectiveType = conn.effectiveType;\n            info.downlink = conn.downlink;\n            info.rtt = conn.rtt;\n        }\n    }\n    return info;\n}\nfunction isConnectionSlow() {\n    const info = getNetworkInfo();\n    // Check if connection is slow based on effective type\n    if (info.effectiveType) {\n        return [\n            \"slow-2g\",\n            \"2g\",\n            \"3g\"\n        ].includes(info.effectiveType);\n    }\n    // Check if downlink is very slow (less than 1 Mbps)\n    if (info.downlink && info.downlink < 1) {\n        return true;\n    }\n    // Check if RTT is very high (more than 200ms)\n    if (info.rtt && info.rtt > 200) {\n        return true;\n    }\n    return false;\n}\nfunction isConnectionUnstable() {\n    return !navigator.onLine || isConnectionSlow();\n}\nfunction getConnectionAdvice() {\n    if (!navigator.onLine) {\n        return \"No internet connection detected. Please check your network settings.\";\n    }\n    if (isConnectionSlow()) {\n        return \"Your connection is slow. Authentication may take longer than usual.\";\n    }\n    return null;\n}\n// Exponential backoff retry function\nasync function retryWithBackoff(fn, maxRetries = 3, baseDelay = 1000) {\n    let lastError;\n    for(let attempt = 1; attempt <= maxRetries; attempt++){\n        try {\n            return await fn();\n        } catch (error) {\n            lastError = error;\n            if (attempt === maxRetries) {\n                throw lastError;\n            }\n            // Exponential backoff: 1s, 2s, 4s, etc.\n            const delay = baseDelay * Math.pow(2, attempt - 1);\n            await new Promise((resolve)=>setTimeout(resolve, delay));\n        }\n    }\n    throw lastError;\n}\n// Test connection speed by making a simple request\nasync function testConnectionSpeed() {\n    const startTime = Date.now();\n    try {\n        const response = await fetch(\"https://www.google.com/favicon.ico\", {\n            method: \"HEAD\",\n            mode: \"no-cors\",\n            cache: \"no-cache\"\n        });\n        const endTime = Date.now();\n        const responseTime = endTime - startTime;\n        if (responseTime < 1000) {\n            return {\n                responseTime,\n                status: \"good\"\n            };\n        } else if (responseTime < 3000) {\n            return {\n                responseTime,\n                status: \"slow\"\n            };\n        } else {\n            return {\n                responseTime,\n                status: \"unstable\"\n            };\n        }\n    } catch (error) {\n        return {\n            responseTime: Infinity,\n            status: \"unstable\"\n        };\n    }\n}\n// Listen for online/offline events\nfunction addNetworkListeners(onOnline, onOffline) {\n    window.addEventListener(\"online\", onOnline);\n    window.addEventListener(\"offline\", onOffline);\n    // Return cleanup function\n    return ()=>{\n        window.removeEventListener(\"online\", onOnline);\n        window.removeEventListener(\"offline\", onOffline);\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./utils/networkUtils.ts\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"f63b265f9b07\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9kaWRvLWRpc3RyaWJ1dGlvbi1mcm9udGVuZC8uL2FwcC9nbG9iYWxzLmNzcz8xZDIzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiZjYzYjI2NWY5YjA3XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/auth/page.tsx":
/*!***************************!*\
  !*** ./app/auth/page.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\workspace\projects\dido-distribution\frontend\app\auth\page.tsx#default`));


/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _providers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./providers */ \"(rsc)/./app/providers.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! sonner */ \"(rsc)/./node_modules/sonner/dist/index.mjs\");\n\n\n\n\n\nconst metadata = {\n    title: \"Dido Distribution\",\n    description: \"Distribution management system\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers__WEBPACK_IMPORTED_MODULE_2__.Providers, {\n                children: [\n                    children,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(sonner__WEBPACK_IMPORTED_MODULE_3__.Toaster, {\n                        richColors: true,\n                        closeButton: true,\n                        duration: 4000,\n                        position: \"top-center\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\layout.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\layout.tsx\",\n                lineNumber: 22,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\layout.tsx\",\n            lineNumber: 21,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\layout.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7O0FBTU1BO0FBSmlCO0FBQ2lCO0FBQ1A7QUFJMUIsTUFBTUcsV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtBQUNmLEVBQUU7QUFFYSxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1Q7SUFDQyxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztrQkFDVCw0RUFBQ0M7WUFBS0MsV0FBV1gsMkpBQWU7c0JBQzlCLDRFQUFDQyxpREFBU0E7O29CQUNQTTtrQ0FDRCw4REFBQ0wsMkNBQU9BO3dCQUFDVSxVQUFVO3dCQUFDQyxXQUFXO3dCQUFDQyxVQUFVO3dCQUFNQyxVQUFTOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBS25FIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZGlkby1kaXN0cmlidXRpb24tZnJvbnRlbmQvLi9hcHAvbGF5b3V0LnRzeD85OTg4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgTWV0YWRhdGEgfSBmcm9tIFwibmV4dFwiO1xyXG5pbXBvcnQgeyBJbnRlciB9IGZyb20gXCJuZXh0L2ZvbnQvZ29vZ2xlXCI7XHJcbmltcG9ydCBcIi4vZ2xvYmFscy5jc3NcIjtcclxuaW1wb3J0IHsgUHJvdmlkZXJzIH0gZnJvbSAnLi9wcm92aWRlcnMnO1xyXG5pbXBvcnQgeyBUb2FzdGVyIH0gZnJvbSAnc29ubmVyJztcclxuXHJcbmNvbnN0IGludGVyID0gSW50ZXIoeyBzdWJzZXRzOiBbXCJsYXRpblwiXSB9KTtcclxuXHJcbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XHJcbiAgdGl0bGU6ICdEaWRvIERpc3RyaWJ1dGlvbicsXHJcbiAgZGVzY3JpcHRpb246ICdEaXN0cmlidXRpb24gbWFuYWdlbWVudCBzeXN0ZW0nLFxyXG59O1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XHJcbiAgY2hpbGRyZW4sXHJcbn06IHtcclxuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlO1xyXG59KSB7XHJcbiAgcmV0dXJuIChcclxuICAgIDxodG1sIGxhbmc9XCJlblwiPlxyXG4gICAgICA8Ym9keSBjbGFzc05hbWU9e2ludGVyLmNsYXNzTmFtZX0+XHJcbiAgICAgICAgPFByb3ZpZGVycz5cclxuICAgICAgICAgIHtjaGlsZHJlbn1cclxuICAgICAgICAgIDxUb2FzdGVyIHJpY2hDb2xvcnMgY2xvc2VCdXR0b24gZHVyYXRpb249ezQwMDB9IHBvc2l0aW9uPVwidG9wLWNlbnRlclwiIC8+XHJcbiAgICAgICAgPC9Qcm92aWRlcnM+XHJcbiAgICAgIDwvYm9keT5cclxuICAgIDwvaHRtbD5cclxuICApO1xyXG59Il0sIm5hbWVzIjpbImludGVyIiwiUHJvdmlkZXJzIiwiVG9hc3RlciIsIm1ldGFkYXRhIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwiYm9keSIsImNsYXNzTmFtZSIsInJpY2hDb2xvcnMiLCJjbG9zZUJ1dHRvbiIsImR1cmF0aW9uIiwicG9zaXRpb24iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/providers.tsx":
/*!***************************!*\
  !*** ./app/providers.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Providers: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\workspace\projects\dido-distribution\frontend\app\providers.tsx#Providers`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/sonner","vendor-chunks/@tanstack"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fauth%2Fpage&page=%2Fauth%2Fpage&appPaths=%2Fauth%2Fpage&pagePath=private-next-app-dir%2Fauth%2Fpage.tsx&appDir=C%3A%5CUsers%5Cmahll%5CDocuments%5Cworkspace%5Cprojects%5Cdido-distribution%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmahll%5CDocuments%5Cworkspace%5Cprojects%5Cdido-distribution%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();