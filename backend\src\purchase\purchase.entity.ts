import { Entity, PrimaryColumn, Column, CreateDateColumn, UpdateDateColumn, Index } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Uuid7 } from '../utils/uuid7';

export enum PurchaseStatus {
  PAID = "paid",
  PARTIALLY_PAID = "partially_paid",
  UNPAID = "unpaid",
  CANCELLED = "cancelled",
}

export enum PaymentMethods {
  CASH = "cash",
  CREDIT_CARD = "credit_card",
  BANK_TRANSFER = "bank_transfer",
  MOBILE_PAYMENT = "mobile_payment",
  CHEQUE = "cheque",
  OTHER = "other",
}

export class PurchaseItemSnapshot {
  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the product",
  })
  productUuid: string;

  @ApiProperty({ example: "Product Name", description: "Name of the product" })
  name: string;

  @ApiProperty({ example: 5, description: "Quantity purchased" })
  quantity: number;

  @ApiProperty({ example: 9.99, description: "Unit price of the product" })
  unitPrice: number;

  @ApiProperty({
    example: 49.95,
    description: "Line total (quantity * unit price)",
  })
  lineTotal: number;
}

@Entity('purchases')
export class Purchase {
  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the purchase (primary key)",
  })
  @PrimaryColumn('uuid')
  id: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the user creating the purchase",
  })
  @Column('uuid')
  @Index()
  userUuid: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the warehouse",
  })
  @Column('uuid')
  @Index()
  warehouseUuid: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the supplier",
    required: false,
  })
  @Column('uuid', { nullable: true })
  @Index()
  supplierUuid?: string;

  @ApiProperty({
    type: [PurchaseItemSnapshot],
    description: "Array of items in the purchase",
  })
  @Column('jsonb', { default: [] })
  itemsSnapshot: PurchaseItemSnapshot[];

  @ApiProperty({ example: 100.0, description: "Total amount of the purchase" })
  @Column('decimal', { precision: 10, scale: 2, default: 0 })
  totalAmount: number;

  @ApiProperty({ example: 50.0, description: "Amount already paid" })
  @Column('decimal', { precision: 10, scale: 2, default: 0 })
  amountPaid: number;

  @ApiProperty({
    example: 50.0,
    description: "Balance due (totalAmount - amountPaid)",
  })
  @Column('decimal', { precision: 10, scale: 2, default: 0 })
  balanceDue: number;

  @ApiProperty({
    example: PaymentMethods.CASH,
    description: "Payment method used",
    enum: PaymentMethods,
  })
  @Column({
    type: 'enum',
    enum: PaymentMethods,
    default: PaymentMethods.CASH
  })
  paymentMethod: PaymentMethods;

  @ApiProperty({
    example: "2024-01-15T10:30:00.000Z",
    description: "Date when payment was made",
  })
  @Column({ default: () => 'CURRENT_TIMESTAMP' })
  paymentDate: Date;

  @ApiProperty({
    example: "2024-01-15T10:30:00.000Z",
    description: "Invoice date",
  })
  @Column({ default: () => 'CURRENT_TIMESTAMP' })
  invoiceDate: Date;

  @ApiProperty({
    example: "2024-02-14T10:30:00.000Z",
    description: "Due date for payment",
  })
  @Column({ default: () => 'CURRENT_TIMESTAMP' })
  dueDate: Date;

  @ApiProperty({
    example: PurchaseStatus.UNPAID,
    description: "Current status of the purchase",
    enum: PurchaseStatus,
  })
  @Column({
    type: 'enum',
    enum: PurchaseStatus,
    default: PurchaseStatus.UNPAID,
  })
  status: PurchaseStatus;

  @ApiProperty({ example: false, description: "Soft delete flag" })
  @Column({ default: false })
  isDeleted: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Helper method to generate UUID
  static generateId(): string {
    return new Uuid7().toString();
  }

  // Helper method to generate UUID
  static fromBinary(binary: any): string {
    if (!binary) return null;
    try {
      return new Uuid7(binary).toString();
    } catch {
      return null;
    }
  }
} 