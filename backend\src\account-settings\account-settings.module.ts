import { Module } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { AccountSettingsController } from "./account-settings.controller";
import { AccountSettingsService } from "./account-settings.service";
import { AccountSettings } from "./account-settings.entity";
import { UsersModule } from "../users/users.module";

@Module({
  imports: [
    TypeOrmModule.forFeature([AccountSettings]),
    UsersModule,
  ],
  controllers: [AccountSettingsController],
  providers: [AccountSettingsService],
  exports: [AccountSettingsService],
})
export class AccountSettingsModule {}
