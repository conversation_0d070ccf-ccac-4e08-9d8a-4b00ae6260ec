"use client";
import React, { useEffect } from 'react';
import { Fi<PERSON>rinter, FiX, FiDownload } from 'react-icons/fi';
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import { Order, OrderItemSnapshot } from './orders/types';

interface OrderPrintProps {
  order: Order;
  isOpen: boolean;
  onClose: () => void;
}

const OrderPrint: React.FC<OrderPrintProps> = ({
  order,
  isOpen,
  onClose
}) => {

  useEffect(() => {
    console.log('OrderPrint component rendered with:', { 
      isOpen, 
      orderNumber: order?.orderNumber,
    });
  }, [isOpen, order]);

  useEffect(() => {
    if (isOpen) {
      // Auto-focus first field when modal opens
      const handleKeyDown = (e: KeyboardEvent) => {
        if (e.key === "Escape") onClose();
      };
      window.addEventListener("keydown", handleKeyDown);
      return () => window.removeEventListener("keydown", handleKeyDown);
    }
  }, [isOpen, onClose]);

  const formatDate = (dateString: string | Date) => {
    return new Date(dateString).toLocaleDateString('fr-DZ', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('fr-DZ', {
      style: 'currency',
      currency: 'DZD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(amount);
  };

  const generateOrderHTML = () => {
    // Use actual order items from backend
    const orderItems = order.itemsSnapshot || [];

    // Mock company info - in real app, this would come from API
    const companyInfo = {
      name: 'Dido Distribution SARL',
      nif: '123456789012345',
      rc: '23/00-1234567B23',
      articleNumber: 'ART-2023-001',
      address: '123 Rue de la République, Alger 16000, Algérie',
      phoneNumber: '+213 21 123 456',
      website: 'www.dido-distribution.dz'
    };

    return `
      <!DOCTYPE html>
      <html>
        <head>
          <title>Bon de Commande - ${order.orderNumber}</title>
          <style>
            @media print {
              body { margin: 0; padding: 20px; font-family: Arial, sans-serif; }
              .no-print { display: none !important; }
              .page-break { page-break-before: always; }
            }
            body { font-family: Arial, sans-serif; margin: 0; padding: 20px; }
            .order-container { max-width: 800px; margin: 0 auto; }
            .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #333; padding-bottom: 20px; }
            .order-title { font-size: 24px; font-weight: bold; margin-bottom: 10px; color: #2563eb; }
            .order-number { font-size: 18px; margin-bottom: 5px; }
            .order-date { font-size: 14px; color: #666; }
            .not-invoice-notice { font-size: 12px; color: #dc2626; font-style: italic; margin-top: 10px; }
            .parties { display: flex; justify-content: space-between; margin-bottom: 30px; }
            .party { flex: 1; }
            .party-title { font-weight: bold; margin-bottom: 10px; font-size: 16px; }
            .party-info { font-size: 14px; line-height: 1.4; }
            .items-table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
            .items-table th, .items-table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
            .items-table th { background-color: #f8f9fa; font-weight: bold; }
            .items-table tr:nth-child(even) { background-color: #f9f9f9; }
            .totals { margin-left: auto; width: 300px; }
            .total-row { display: flex; justify-content: space-between; margin-bottom: 5px; }
            .total-label { font-weight: bold; }
            .total-amount { font-weight: bold; }
            .order-status { display: inline-block; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: bold; }
            .status-draft { background-color: #f3f4f6; color: #374151; }
            .status-pending { background-color: #fef3c7; color: #92400e; }
            .status-confirmed { background-color: #dbeafe; color: #1e40af; }
            .status-processing { background-color: #e0e7ff; color: #5b21b6; }
            .status-shipped { background-color: #e0f2fe; color: #0f4c75; }
            .status-delivered { background-color: #d1fae5; color: #065f46; }
            .status-cancelled { background-color: #fee2e2; color: #991b1b; }
            .status-returned { background-color: #fed7aa; color: #9a3412; }
            .notes-section { margin-top: 20px; padding: 15px; background-color: #f9f9f9; border-left: 4px solid #2563eb; }
            .notes-title { font-weight: bold; margin-bottom: 8px; }
            .footer-notice { margin-top: 30px; padding: 15px; background-color: #fffbeb; border: 1px solid #fbbf24; font-size: 12px; }
          </style>
        </head>
        <body>
          <div class="order-container">
            <!-- Header - Order Identification -->
            <div class="header">
              <div class="order-title">BON DE COMMANDE</div>
              <div class="order-number">N° ${order.orderNumber}</div>
              <div class="order-date">Date: ${formatDate(order.orderDate)}${order.requestedDeliveryDate ? ` | Livraison demandée: ${formatDate(order.requestedDeliveryDate)}` : ''}</div>
              <div class="not-invoice-notice">Ce document n'est pas une facture - ${order.useTax ? `TVA ${(order.taxRate * 100).toFixed(1)}%` : 'Aucune TVA déclarée'}</div>
              <div class="order-status status-${order.status}">
                Statut: ${order.status.charAt(0).toUpperCase() + order.status.slice(1)} | Priorité: ${order.priority.charAt(0).toUpperCase() + order.priority.slice(1)}
              </div>
            </div>

            <!-- Parties Information -->
            <div class="parties">
              <div class="party" style="margin-right: 20px;">
                <div class="party-title">Vendeur:</div>
                <div class="party-info">
                  <p><strong>${companyInfo.name}</strong></p>
                  <p>NIF: ${companyInfo.nif}</p>
                  <p>RC: ${companyInfo.rc}</p>
                  <p>Art: ${companyInfo.articleNumber}</p>
                  <p>${companyInfo.address}</p>
                  <p>Tél: ${companyInfo.phoneNumber}</p>
                  ${companyInfo.website ? `<p>Site: ${companyInfo.website}</p>` : ''}
                </div>
              </div>
              
              <div class="party" style="margin-left: 20px;">
                <div class="party-title">Client:</div>
                <div class="party-info">
                  <p><strong>${order.customerName || 'Client non spécifié'}</strong></p>
                  <p>NIF: ${order.customerFiscalId || 'Non spécifié'}</p>
                  ${order.customerRc ? `<p>RC: ${order.customerRc}</p>` : ''}
                  ${order.customerArticleNumber ? `<p>Art: ${order.customerArticleNumber}</p>` : ''}
                </div>
              </div>
            </div>

            <!-- Items Table -->
            <table class="items-table">
              <thead>
                <tr>
                  <th style="width: 5%;">N°</th>
                  <th style="width: 50%;">Désignation</th>
                  <th style="width: 15%;">Qté</th>
                  <th style="width: 15%;">Prix Unit.</th>
                  <th style="width: 15%;">Montant</th>
                </tr>
              </thead>
              <tbody>
                ${orderItems.map((item, index) => `
                  <tr>
                    <td>${index + 1}</td>
                    <td>${item.name}${item.notes ? ` <br><small style="color: #666;">${item.notes}</small>` : ''}</td>
                    <td>${item.quantity}</td>
                    <td>${formatCurrency(item.unitPrice)}</td>
                    <td>${formatCurrency(item.lineTotal)}</td>
                  </tr>
                `).join('')}
              </tbody>
            </table>

            <!-- Totals -->
            <div class="totals">
              <div class="total-row">
                <span class="total-label">Sous-total:</span>
                <span class="total-amount">${formatCurrency(order.subtotal)}</span>
              </div>
              ${order.useTax ? `
                <div class="total-row">
                  <span class="total-label">TVA (${(order.taxRate * 100).toFixed(1)}%):</span>
                  <span class="total-amount">${formatCurrency(order.taxAmount)}</span>
                </div>
              ` : ''}
              <div class="total-row" style="border-top: 2px solid #333; padding-top: 10px; margin-top: 10px;">
                <span class="total-label">Total de la Commande:</span>
                <span class="total-amount">${formatCurrency(order.totalAmount)}</span>
              </div>
            </div>

            ${order.notes ? `
              <div class="notes-section">
                <div class="notes-title">Notes:</div>
                <div>${order.notes}</div>
              </div>
            ` : ''}

            <!-- Footer Notice -->
            <div class="footer-notice">
              <strong>Important:</strong> Ce bon de commande n'est pas une facture. 
              ${order.useTax ? `TVA ${(order.taxRate * 100).toFixed(1)}% incluse.` : 'Aucune TVA n\'est déclarée sur ce document.'} 
              Une facture sera émise lors de la livraison.
            </div>
          </div>
        </body>
      </html>
    `;
  };

  const handlePrint = () => {
    try {
      console.log('Print button clicked');
      const printWindow = window.open('', '_blank');
      if (printWindow) {
        console.log('Print window opened successfully');
        const orderHTML = generateOrderHTML();
        console.log('Order HTML generated');
        
        printWindow.document.write(orderHTML);
        printWindow.document.close();
        
        // Wait for content to load before printing
        printWindow.onload = () => {
          console.log('Print window loaded, initiating print');
          printWindow.print();
          printWindow.close();
        };
      } else {
        console.error('Failed to open print window');
        alert('Impossible d\'ouvrir la fenêtre d\'impression. Veuillez vérifier les paramètres de votre navigateur.');
      }
    } catch (error) {
      console.error('Error during print:', error);
      alert('Erreur lors de l\'impression. Veuillez réessayer.');
    }
  };

  const handleDownloadPDF = async () => {
    try {
      console.log('Download PDF button clicked');
      
      // Create a temporary div to render the HTML
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = generateOrderHTML();
      tempDiv.style.position = 'absolute';
      tempDiv.style.left = '-9999px';
      tempDiv.style.top = '-9999px';
      tempDiv.style.width = '800px';
      document.body.appendChild(tempDiv);

      // Convert to canvas
      const canvas = await html2canvas(tempDiv, {
        scale: 2,
        useCORS: true,
        allowTaint: true
      });

      // Remove temporary div
      document.body.removeChild(tempDiv);

      // Create PDF
      const imgData = canvas.toDataURL('image/png');
      const pdf = new jsPDF('p', 'mm', 'a4');
      const imgWidth = 210;
      const pageHeight = 295;
      const imgHeight = (canvas.height * imgWidth) / canvas.width;
      let heightLeft = imgHeight;

      let position = 0;

      pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
      heightLeft -= pageHeight;

      while (heightLeft >= 0) {
        position = heightLeft - imgHeight;
        pdf.addPage();
        pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
        heightLeft -= pageHeight;
      }

      pdf.save(`Bon_Commande_${order.orderNumber}.pdf`);
      console.log('PDF generated and downloaded successfully');
    } catch (error) {
      console.error('Error generating PDF:', error);
      alert('Erreur lors de la génération du PDF. Veuillez réessayer.');
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto m-4">
        <div className="p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-bold">Aperçu du Bon de Commande</h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 p-2 rounded-full hover:bg-gray-100"
            >
              <FiX className="w-6 h-6" />
            </button>
          </div>

          {/* Order Preview */}
          <div className="border border-gray-200 rounded-lg p-6 mb-6 bg-gray-50">
            <div className="text-center mb-4">
              <h3 className="text-lg font-bold text-blue-600">BON DE COMMANDE</h3>
              <p className="text-sm text-gray-600">N° {order.orderNumber}</p>
              <p className="text-xs text-gray-500">
                Date: {formatDate(order.orderDate)}
                {order.requestedDeliveryDate && ` | Livraison demandée: ${formatDate(order.requestedDeliveryDate)}`}
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <h4 className="font-semibold text-gray-800 mb-2">Informations Client</h4>
                <div className="text-sm text-gray-600">
                  <p><strong>{order.customerName || 'Client non spécifié'}</strong></p>
                  <p>NIF: {order.customerFiscalId || 'Non spécifié'}</p>
                  {order.customerRc && <p>RC: {order.customerRc}</p>}
                  {order.customerArticleNumber && <p>Art: {order.customerArticleNumber}</p>}
                </div>
              </div>
              
              <div>
                <h4 className="font-semibold text-gray-800 mb-2">Détails Commande</h4>
                <div className="text-sm text-gray-600">
                  <p>Statut: <span className={`px-2 py-1 rounded-full text-xs ${
                    order.status === 'delivered' ? 'bg-green-100 text-green-800' :
                    order.status === 'cancelled' ? 'bg-red-100 text-red-800' :
                    order.status === 'processing' ? 'bg-blue-100 text-blue-800' :
                    'bg-yellow-100 text-yellow-800'
                  }`}>{order.status}</span></p>
                  <p>Priorité: <span className="capitalize">{order.priority}</span></p>
                  <p>Articles: {order.itemsSnapshot.length}</p>
                  <p>Total: <strong>{formatCurrency(order.totalAmount)}</strong></p>
                </div>
              </div>
            </div>

            {order.notes && (
              <div className="bg-blue-50 p-3 rounded-md">
                <h4 className="font-semibold text-gray-800 mb-1">Notes</h4>
                <p className="text-sm text-gray-600">{order.notes}</p>
              </div>
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-3">
            <button
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
            >
              Fermer
            </button>
            <button
              onClick={handleDownloadPDF}
              className="px-4 py-2 text-sm font-medium text-white bg-green-600 rounded-md hover:bg-green-700 flex items-center"
            >
              <FiDownload className="w-4 h-4 mr-2" />
              Télécharger PDF
            </button>
            <button
              onClick={handlePrint}
              className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 flex items-center"
            >
              <FiPrinter className="w-4 h-4 mr-2" />
              Imprimer
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OrderPrint; 