import { Injectable, NotFoundException } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";
import { Route } from "./route.entity";
import { CreateRouteDto } from "./dto/create-route.dto";
import { UpdateRouteDto } from "./dto/update-route.dto";
import { FilterRouteDto } from "./dto/filter-route.dto";
import { RouteResponseDto } from "./dto/route-response.dto";
import { ComputeRouteDto } from "./dto/compute-route.dto";
import { PaginationQueryDto } from "../dto/pagination.dto";
import { PaginatedResponseDto } from "../dto/pagination.dto";

@Injectable()
export class RoutesService {
  constructor(
    @InjectRepository(Route)
    private routeRepository: Repository<Route>,
  ) {}

  async create(createRouteDto: CreateRouteDto): Promise<RouteResponseDto> {
    const route = this.routeRepository.create({
      id: Route.generateId(),
      ...createRouteDto,
      warehouseId: createRouteDto.warehouseUuid,
    });

    const savedRoute = await this.routeRepository.save(route);
    return this.mapToResponseDto(savedRoute);
  }

  async findAll(
    paginationQuery?: PaginationQueryDto,
    filter?: FilterRouteDto,
  ): Promise<PaginatedResponseDto<RouteResponseDto>> {
    const queryBuilder = this.routeRepository
      .createQueryBuilder("route")
      .leftJoinAndSelect("route.warehouse", "warehouse")
      .where("route.isDeleted = :isDeleted", { isDeleted: false });

    if (filter?.name) {
      queryBuilder.andWhere("route.name ILIKE :name", {
        name: `%${filter.name}%`,
      });
    }

    if (filter?.warehouseUuid) {
      queryBuilder.andWhere("route.warehouseId = :warehouseId", {
        warehouseId: filter.warehouseUuid,
      });
    }

    const routes = await queryBuilder.getMany();
    const routeDtos = routes.map((route) => this.mapToResponseDto(route));
    
    const page = paginationQuery?.page || 1;
    const limit = paginationQuery?.limit || 10;
    const total = routeDtos.length;
    
    return new PaginatedResponseDto(routeDtos, total, page, limit);
  }

  async findOne(id: string): Promise<RouteResponseDto> {
    const route = await this.routeRepository.findOne({
      where: { id, isDeleted: false },
      relations: ["warehouse"],
    });

    if (!route) {
      throw new NotFoundException(`Route with ID ${id} not found`);
    }

    return this.mapToResponseDto(route);
  }

  async update(id: string, updateRouteDto: UpdateRouteDto): Promise<RouteResponseDto> {
    const route = await this.routeRepository.findOne({
      where: { id, isDeleted: false },
    });

    if (!route) {
      throw new NotFoundException(`Route with ID ${id} not found`);
    }

    Object.assign(route, updateRouteDto);
    const updatedRoute = await this.routeRepository.save(route);
    
    return this.mapToResponseDto(updatedRoute);
  }

  async remove(id: string): Promise<void> {
    const route = await this.routeRepository.findOne({
      where: { id, isDeleted: false },
    });

    if (!route) {
      throw new NotFoundException(`Route with ID ${id} not found`);
    }

    route.isDeleted = true;
    await this.routeRepository.save(route);
  }

  async computeRoute(computeRouteDto: ComputeRouteDto): Promise<RouteResponseDto> {
    // This is a placeholder implementation
    // In a real application, you would implement route optimization logic here
    const route = this.routeRepository.create({
      id: Route.generateId(),
      name: computeRouteDto.name || "Computed Route",
      description: computeRouteDto.description,
      customerLocations: computeRouteDto.customerLocations,
      optimizedRoute: computeRouteDto.customerLocations.map((loc, index) => ({
        ...loc,
        order: index + 1,
      })),
      totalDistance: 0, // Calculate actual distance
      warehouseId: computeRouteDto.warehouseUuid,
    });

    const savedRoute = await this.routeRepository.save(route);
    return this.mapToResponseDto(savedRoute);
  }

  async hardDeleteAll(): Promise<{ success: boolean; message: string; deletedCount: number }> {
    const result = await this.routeRepository.delete({});
    return {
      success: true,
      message: "All routes have been permanently deleted",
      deletedCount: result.affected || 0,
    };
  }

  private mapToResponseDto(route: Route): RouteResponseDto {
    return {
      uuid: route.id,
      name: route.name,
      description: route.description,
      customerLocations: route.customerLocations,
      optimizedRoute: route.optimizedRoute,
      totalDistance: route.totalDistance,
      warehouseUuidString: route.warehouseId,
      createdAt: route.createdAt,
      updatedAt: route.updatedAt,
    };
  }
} 