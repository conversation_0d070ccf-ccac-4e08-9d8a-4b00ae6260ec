import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Put,
  Query,
  HttpStatus,
  HttpException,
  ParseU<PERSON><PERSON>ipe,
  BadRequestException,
} from "@nestjs/common";
import { Uuid7 } from "../utils/uuid7";
import { CompaniesService } from "./companies.service";
import {
  ApiTags,
  ApiResponse,
  ApiBody,
  ApiOperation,
  ApiParam,
} from "@nestjs/swagger";
import { CreateCompanyDto } from "./dto/create-company.dto";
import { UpdateCompanyDto } from "./dto/update-company.dto";
import { CompanyResponseDto } from "./dto/company-response.dto";
@ApiTags("companies")
@Controller("companies")
export class CompaniesController {
  constructor(private readonly companiesService: CompaniesService) {}

  @Post()
  @ApiOperation({ summary: "Create a new company" })
  @ApiBody({ type: CreateCompanyDto })
  @ApiResponse({
    status: 201,
    description: "Company created successfully",
    type: CompanyResponseDto,
  })
  @ApiResponse({ status: 400, description: "Invalid input or duplicate NIF" })
  async create(
    @Body() createCompanyDto: CreateCompanyDto,
  ): Promise<CompanyResponseDto> {
    return this.companiesService.create(createCompanyDto);
  }

  @Get("list")
  @ApiOperation({ summary: "List all companies (excluding deleted)" })
  @ApiResponse({
    status: 200,
    description: "List of all companies",
    type: [CompanyResponseDto],
  })
  async findAll(): Promise<CompanyResponseDto[]> {
    return this.companiesService.findAll();
  }

  @Get("by-user/:userUuid")
  @ApiOperation({
    summary:
      "Get or create company for a specific user (auto-creates default company for super users if none exists)",
  })
  @ApiParam({
    name: "userUuid",
    description: "User UUID to get/create company for",
    type: String,
    example: "018ea2bb-6b8d-7e9a-8a2a-7e9a8a2a7e9a",
  })
  @ApiResponse({
    status: 200,
    description: "Company for the specified user (created if none existed)",
    type: [CompanyResponseDto],
  })
  @ApiResponse({
    status: 400,
    description: "Invalid user UUID or user is not super type",
  })
  @ApiResponse({ status: 404, description: "User not found" })
  async findByUserUuid(
    @Param("userUuid", ParseUUIDPipe) userUuid: string,
  ): Promise<CompanyResponseDto[]> {
    try {
      new Uuid7(userUuid);
    } catch {
      throw new BadRequestException("Invalid user UUID");
    }
    return this.companiesService.findByUserUuid(userUuid);
  }

  @Get(":uuid")
  @ApiOperation({ summary: "Get company by UUID" })
  @ApiParam({
    name: "uuid",
    description: "Company UUID",
    type: String,
    example: "018ea2bb-6b8d-7e9a-8a2a-7e9a8a2a7e9a",
  })
  @ApiResponse({
    status: 200,
    description: "Company found",
    type: CompanyResponseDto,
  })
  @ApiResponse({ status: 404, description: "Company not found" })
  @ApiResponse({ status: 400, description: "Invalid company UUID" })
  async findOne(@Param("uuid", ParseUUIDPipe) uuid: string): Promise<CompanyResponseDto> {
    try {
      new Uuid7(uuid);
    } catch {
      throw new BadRequestException("Invalid company UUID");
    }
    return this.companiesService.findOne(uuid);
  }

  @Put(":uuid")
  @ApiOperation({ summary: "Update company by UUID" })
  @ApiParam({
    name: "uuid",
    description: "Company UUID",
    type: String,
    example: "018ea2bb-6b8d-7e9a-8a2a-7e9a8a2a7e9a",
  })
  @ApiBody({ type: UpdateCompanyDto })
  @ApiResponse({
    status: 200,
    description: "Company updated successfully",
    type: CompanyResponseDto,
  })
  @ApiResponse({ status: 404, description: "Company not found" })
  @ApiResponse({ status: 400, description: "Invalid input or duplicate NIF" })
  async update(
    @Param("uuid", ParseUUIDPipe) uuid: string,
    @Body() updateCompanyDto: UpdateCompanyDto,
  ): Promise<CompanyResponseDto> {
    try {
      new Uuid7(uuid);
    } catch {
      throw new BadRequestException("Invalid company UUID");
    }
    return this.companiesService.update(uuid, updateCompanyDto);
  }

  @Delete(":uuid")
  @ApiOperation({ summary: "Delete company by UUID (soft delete)" })
  @ApiParam({
    name: "uuid",
    description: "Company UUID",
    type: String,
    example: "018ea2bb-6b8d-7e9a-8a2a-7e9a8a2a7e9a",
  })
  @ApiResponse({
    status: 200,
    description: "Company soft deleted successfully",
    schema: {
      example: {
        message: "Company soft deleted successfully",
      },
    },
  })
  @ApiResponse({ status: 404, description: "Company not found" })
  @ApiResponse({ status: 400, description: "Invalid company UUID" })
  async remove(@Param("uuid", ParseUUIDPipe) uuid: string): Promise<{ message: string }> {
    try {
      new Uuid7(uuid);
    } catch {
      throw new BadRequestException("Invalid company UUID");
    }
    return this.companiesService.remove(uuid);
  }

  @Delete("all")
  @ApiOperation({ summary: "Hard delete all companies (irreversible)" })
  @ApiResponse({
    status: 200,
    description: "All companies hard deleted successfully",
    schema: {
      example: {
        message: "All companies hard deleted successfully",
        deletedCount: 5,
      },
    },
  })
  async deleteAllCompanies(): Promise<{
    message: string;
    deletedCount: number;
  }> {
    return this.companiesService.deleteAllCompanies();
  }
}
