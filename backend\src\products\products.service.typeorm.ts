import { Injectable, NotFoundException } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository, Like, Between } from "typeorm";
import { Product } from "./product.entity";
import { Uuid7 } from "../utils/uuid7";

interface AdvancedFilterOptions {
  name?: string;
  productCategoryUuid?: string;
  sku?: string;
  barcode?: string;
  search?: string;
  minRetailPrice?: number;
  maxRetailPrice?: number;
  minWholesalePrice?: number;
  maxWholesalePrice?: number;
  minMidWholesalePrice?: number;
  maxMidWholesalePrice?: number;
  minInstitutionalPrice?: number;
  maxInstitutionalPrice?: number;
}

@Injectable()
export class ProductsService {
  constructor(
    @InjectRepository(Product)
    private productRepository: Repository<Product>,
  ) {}

  async create(data: any) {
    // Validate required fields before processing
    if (!data.name || data.name.trim() === "") {
      throw new Error("Product name is required and cannot be empty");
    }

    if (!data.warehouseUuid || data.warehouseUuid.trim() === "") {
      throw new Error("Warehouse UUID is required and cannot be empty");
    }

    // Create new product with generated UUID
    const product = this.productRepository.create({
      id: Product.generateId(),
      ...data,
    });

    return this.productRepository.save(product);
  }

  async findAll(page: number = 1, limit: number = 10) {
    const skip = (page - 1) * limit;

    const [docs, total] = await Promise.all([
      this.productRepository.find({
        where: { isDeleted: false },
        skip,
        take: limit,
        order: { createdAt: 'DESC' },
      }),
      this.productRepository.count({
        where: { isDeleted: false },
      }),
    ]);

    const totalPages = Math.ceil(total / limit);

    return {
      data: docs,
      total,
      page,
      limit,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    };
  }

  async findAllByWarehouseUuid(warehouseUuid: string) {
    const docs = await this.productRepository.find({
      where: {
        warehouseUuid,
        isDeleted: false,
      },
    });
    return docs;
  }

  async findOne(uuid: string) {
    const product = await this.productRepository.findOne({
      where: { id: uuid, isDeleted: false },
    });

    if (!product) {
      throw new NotFoundException(`Product with UUID ${uuid} not found`);
    }

    return product;
  }

  async filterAdvanced(
    warehouseUuid: string,
    filters: AdvancedFilterOptions,
    page: number = 1,
    limit: number = 10,
  ) {
    const skip = (page - 1) * limit;
    const whereConditions: any = {
      warehouseUuid,
      isDeleted: false,
    };

    // Add filter conditions
    if (filters.name) {
      whereConditions.name = Like(`%${filters.name}%`);
    }

    if (filters.productCategoryUuid) {
      whereConditions.productCategoryUuid = filters.productCategoryUuid;
    }

    if (filters.sku) {
      whereConditions.sku = Like(`%${filters.sku}%`);
    }

    if (filters.barcode) {
      whereConditions.barcode = Like(`%${filters.barcode}%`);
    }

    // Price range filters
    if (filters.minRetailPrice !== undefined || filters.maxRetailPrice !== undefined) {
      whereConditions.retailPrice = Between(
        filters.minRetailPrice || 0,
        filters.maxRetailPrice || 999999999,
      );
    }

    if (filters.minWholesalePrice !== undefined || filters.maxWholesalePrice !== undefined) {
      whereConditions.wholesalePrice = Between(
        filters.minWholesalePrice || 0,
        filters.maxWholesalePrice || 999999999,
      );
    }

    if (filters.minMidWholesalePrice !== undefined || filters.maxMidWholesalePrice !== undefined) {
      whereConditions.midWholesalePrice = Between(
        filters.minMidWholesalePrice || 0,
        filters.maxMidWholesalePrice || 999999999,
      );
    }

    if (filters.minInstitutionalPrice !== undefined || filters.maxInstitutionalPrice !== undefined) {
      whereConditions.institutionalPrice = Between(
        filters.minInstitutionalPrice || 0,
        filters.maxInstitutionalPrice || 999999999,
      );
    }

    // Search across multiple fields
    if (filters.search) {
      const searchQuery = `%${filters.search}%`;
      const [docs, total] = await Promise.all([
        this.productRepository
          .createQueryBuilder('product')
          .where('product.warehouseUuid = :warehouseUuid', { warehouseUuid })
          .andWhere('product.isDeleted = :isDeleted', { isDeleted: false })
          .andWhere(
            '(product.name ILIKE :search OR product.description ILIKE :search OR product.sku ILIKE :search OR product.barcode ILIKE :search)',
            { search: searchQuery }
          )
          .skip(skip)
          .take(limit)
          .orderBy('product.createdAt', 'DESC')
          .getMany(),
        this.productRepository
          .createQueryBuilder('product')
          .where('product.warehouseUuid = :warehouseUuid', { warehouseUuid })
          .andWhere('product.isDeleted = :isDeleted', { isDeleted: false })
          .andWhere(
            '(product.name ILIKE :search OR product.description ILIKE :search OR product.sku ILIKE :search OR product.barcode ILIKE :search)',
            { search: searchQuery }
          )
          .getCount(),
      ]);

      const totalPages = Math.ceil(total / limit);

      return {
        data: docs,
        total,
        page,
        limit,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      };
    }

    const [docs, total] = await Promise.all([
      this.productRepository.find({
        where: whereConditions,
        skip,
        take: limit,
        order: { createdAt: 'DESC' },
      }),
      this.productRepository.count({
        where: whereConditions,
      }),
    ]);

    const totalPages = Math.ceil(total / limit);

    return {
      data: docs,
      total,
      page,
      limit,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    };
  }

  async searchProducts(
    warehouseUuid: string,
    searchQuery: string,
    page: number = 1,
    limit: number = 10,
  ) {
    const skip = (page - 1) * limit;
    const searchPattern = `%${searchQuery}%`;

    const [docs, total] = await Promise.all([
      this.productRepository
        .createQueryBuilder('product')
        .where('product.warehouseUuid = :warehouseUuid', { warehouseUuid })
        .andWhere('product.isDeleted = :isDeleted', { isDeleted: false })
        .andWhere(
          '(product.name ILIKE :search OR product.description ILIKE :search OR product.sku ILIKE :search OR product.barcode ILIKE :search)',
          { search: searchPattern }
        )
        .skip(skip)
        .take(limit)
        .orderBy('product.createdAt', 'DESC')
        .getMany(),
      this.productRepository
        .createQueryBuilder('product')
        .where('product.warehouseUuid = :warehouseUuid', { warehouseUuid })
        .andWhere('product.isDeleted = :isDeleted', { isDeleted: false })
        .andWhere(
          '(product.name ILIKE :search OR product.description ILIKE :search OR product.sku ILIKE :search OR product.barcode ILIKE :search)',
          { search: searchPattern }
        )
        .getCount(),
    ]);

    const totalPages = Math.ceil(total / limit);

    return {
      data: docs,
      total,
      page,
      limit,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    };
  }

  async getCategoriesByWarehouse(warehouseUuid: string): Promise<string[]> {
    const products = await this.productRepository.find({
      where: {
        warehouseUuid,
        isDeleted: false,
      },
      select: ['productCategoryUuid'],
    });

    const categoryUuids = products
      .map((product) => product.productCategoryUuid)
      .filter((uuid) => uuid !== null && uuid !== undefined);

    return [...new Set(categoryUuids)];
  }

  async update(uuid: string, data: any) {
    console.log('=== PRODUCT SERVICE UPDATE START ===');
    console.log('[Products Service] Update request for UUID:', uuid);
    console.log('[Products Service] Update data received:', JSON.stringify(data, null, 2));
    console.log('[Products Service] Update data types:', {
      retailPrice: typeof data.retailPrice,
      wholesalePrice: typeof data.wholesalePrice,
      midWholesalePrice: typeof data.midWholesalePrice,
      institutionalPrice: typeof data.institutionalPrice,
      cost: typeof data.cost
    });
    
    const product = await this.findOne(uuid);
    console.log('[Products Service] Found product before update:', {
      id: product.id,
      name: product.name,
      retailPrice: product.retailPrice,
      wholesalePrice: product.wholesalePrice,
      midWholesalePrice: product.midWholesalePrice,
      institutionalPrice: product.institutionalPrice,
      cost: product.cost
    });

    // Convert string price values to numbers and handle simple pricing
    const processedData = { ...data };
    
    // Handle simple pricing: if price is provided, set it for all price fields
    if (processedData.price !== undefined && processedData.price !== null && processedData.price !== '') {
      const priceValue = typeof processedData.price === 'string' ? parseFloat(processedData.price) : processedData.price;
      if (!isNaN(priceValue)) {
        processedData.retailPrice = priceValue;
        processedData.wholesalePrice = priceValue;
        processedData.midWholesalePrice = priceValue;
        processedData.institutionalPrice = priceValue;
        console.log('[Products Service] Converted simple price to all price fields:', priceValue);
      }
    }
    
    // Convert all price fields from strings to numbers
    const priceFields = ['retailPrice', 'wholesalePrice', 'midWholesalePrice', 'institutionalPrice', 'cost'];
    priceFields.forEach(field => {
      if (processedData[field] !== undefined && processedData[field] !== null && processedData[field] !== '') {
        const numValue = typeof processedData[field] === 'string' ? parseFloat(processedData[field]) : processedData[field];
        if (!isNaN(numValue)) {
          processedData[field] = numValue;
          console.log(`[Products Service] Converted ${field} from string to number:`, numValue);
        } else {
          // If conversion fails, set to undefined to avoid saving invalid data
          processedData[field] = undefined;
          console.log(`[Products Service] Failed to convert ${field}, setting to undefined`);
        }
      } else if (processedData[field] === '' || processedData[field] === '0.00') {
        // Handle empty strings or "0.00" strings
        processedData[field] = undefined;
        console.log(`[Products Service] Setting ${field} to undefined (was empty or "0.00")`);
      }
    });
    
    console.log('[Products Service] Processed data after conversion:', processedData);
    console.log('[Products Service] Processed data types:', {
      retailPrice: typeof processedData.retailPrice,
      wholesalePrice: typeof processedData.wholesalePrice,
      midWholesalePrice: typeof processedData.midWholesalePrice,
      institutionalPrice: typeof processedData.institutionalPrice,
      cost: typeof processedData.cost
    });

    // Update the product
    console.log('[Products Service] About to apply Object.assign with processed data:', processedData);
    Object.assign(product, processedData);
    
    console.log('[Products Service] Product after Object.assign:', {
      id: product.id,
      name: product.name,
      retailPrice: product.retailPrice,
      wholesalePrice: product.wholesalePrice,
      midWholesalePrice: product.midWholesalePrice,
      institutionalPrice: product.institutionalPrice,
      cost: product.cost
    });
    
    console.log('[Products Service] About to save product to database...');
    const savedProduct = await this.productRepository.save(product);
    console.log('[Products Service] Product saved successfully to database');
    console.log('[Products Service] Saved product from database:', {
      id: savedProduct.id,
      name: savedProduct.name,
      retailPrice: savedProduct.retailPrice,
      wholesalePrice: savedProduct.wholesalePrice,
      midWholesalePrice: savedProduct.midWholesalePrice,
      institutionalPrice: savedProduct.institutionalPrice,
      cost: savedProduct.cost
    });
    console.log('=== PRODUCT SERVICE UPDATE END ===');
    return savedProduct;
  }

  async softDelete(uuid: string) {
    const product = await this.findOne(uuid);
    product.isDeleted = true;
    return this.productRepository.save(product);
  }

  async deleteAll(): Promise<{ deletedCount: number }> {
    const result = await this.productRepository.update(
      { isDeleted: false },
      { isDeleted: true }
    );
    return { deletedCount: result.affected || 0 };
  }

  async count() {
    return this.productRepository.count({
      where: { isDeleted: false },
    });
  }

  getPriceForCustomerType(
    product: any,
    customerType: "retail" | "wholesale" | "mid-wholesale" | "institutional",
  ): number {
    switch (customerType) {
      case "retail":
        return product.retailPrice || 0;
      case "wholesale":
        return product.wholesalePrice || 0;
      case "mid-wholesale":
        return product.midWholesalePrice || 0;
      case "institutional":
        return product.institutionalPrice || 0;
      default:
        return product.retailPrice || 0;
    }
  }
} 