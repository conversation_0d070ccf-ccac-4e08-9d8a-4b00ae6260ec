import { useAuth } from '@/contexts/AuthContext';
import { useMemo } from 'react';
import { 
  usePOSState,
  usePOSOperations,
  usePOSProducts
} from '../pos/hooks';

/**
 * Centralized POS state management hook for the sales page level.
 * This prevents POS state from unmounting when switching between list and POS views.
 */
export function useSalesPOSState() {
  const { user } = useAuth();
  const warehouseUuid = user?.warehouseUuid;

  // Initialize all POS hooks
  const posState = usePOSState();
  const posOperations = usePOSOperations();
  const posProducts = usePOSProducts();

  // Memoize the return object to prevent unnecessary re-renders
  return useMemo(() => ({
    // POS State
    saleState: posState.saleState,
    startNewSale: posState.startNewSale,
    loadSaleForEdit: posState.loadSaleForEdit,
    loadSaleForContinue: posState.loadSaleForContinue,
    clearSale: posState.clearSale,
    setCustomer: posState.setCustomer,
    addItem: posState.addItem,
    removeItem: posState.removeItem,
    updateItemQuantity: posState.updateItemQuantity,
    updateItemPrice: posState.updateItemPrice,
    clearItems: posState.clearItems,
    setTaxEnabled: posState.setTaxEnabled,
    setTaxRate: posState.setTaxRate,
    recalculateTotals: posState.recalculateTotals,
    setPaymentMethod: posState.setPaymentMethod,
    setAmountPaid: posState.setAmountPaid,
    setNotes: posState.setNotes,
    setNotesEnabled: posState.setNotesEnabled,
    setError: posState.setError,
    setSubmitting: posState.setSubmitting,
    setLoading: posState.setLoading,
    markClean: posState.markClean,
    validateSale: posState.validateSale,

    // POS Operations
    saveSale: posOperations.saveSale,
    loadSale: posOperations.loadSale,
    createNewCustomer: posOperations.createNewCustomer,
    getDefaultCustomer: posOperations.getDefaultCustomer,
    searchCustomers: posOperations.searchCustomers,
    validateStock: posOperations.validateStock,

    // POS Products
    products: posProducts.products,
    isLoading: posProducts.isLoading,
    currentPage: posProducts.pagination.page,
    totalPages: Math.ceil(posProducts.pagination.total / posProducts.pagination.limit),
    hasNext: posProducts.pagination.hasNext,
    hasPrev: posProducts.pagination.hasPrev,
    loadPage: posProducts.loadPage,
    searchProducts: posProducts.loadProducts,
    updateCustomerType: posProducts.updateCustomerType,
    currentCustomerType: posProducts.currentCustomerType,
    updateCategory: posProducts.updateCategory,
    currentCategory: posProducts.currentCategory,

    // Warehouse context
    warehouseUuid
  }), [
    // Dependencies for the memoized object
    posState.saleState,
    posState.startNewSale,
    posState.loadSaleForEdit,
    posState.loadSaleForContinue,
    posState.clearSale,
    posState.setCustomer,
    posState.addItem,
    posState.removeItem,
    posState.updateItemQuantity,
    posState.updateItemPrice,
    posState.clearItems,
    posState.setTaxEnabled,
    posState.setTaxRate,
    posState.recalculateTotals,
    posState.setPaymentMethod,
    posState.setAmountPaid,
    posState.setNotes,
    posState.setNotesEnabled,
    posState.setError,
    posState.setSubmitting,
    posState.setLoading,
    posState.markClean,
    posState.validateSale,
    posOperations.saveSale,
    posOperations.loadSale,
    posOperations.createNewCustomer,
    posOperations.getDefaultCustomer,
    posOperations.searchCustomers,
    posOperations.validateStock,
    posProducts.products,
    posProducts.isLoading,
    posProducts.pagination.page,
    posProducts.pagination.total,
    posProducts.pagination.limit,
    posProducts.pagination.hasNext,
    posProducts.pagination.hasPrev,
    posProducts.loadPage,
    posProducts.loadProducts,
    posProducts.updateCustomerType,
    posProducts.currentCustomerType,
    posProducts.updateCategory,
    posProducts.currentCategory,
    warehouseUuid
  ]);
} 