import { Controller, Get, HttpStatus } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { DatabaseTestService, DatabaseTestResult } from './database-test.service';

@ApiTags('Database Connectivity')
@Controller('database-test')
export class DatabaseTestController {
  constructor(private readonly databaseTestService: DatabaseTestService) {}

  @Get()
  @ApiOperation({
    summary: 'Test database connectivity',
    description: 'Tests connectivity to YugabyteDB database using environment variables'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Database connectivity test results',
    schema: {
      type: 'object',
      properties: {
        yugabyte: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' },
            details: { type: 'object' },
            timestamp: { type: 'string' }
          }
        },
        overall: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' },
            timestamp: { type: 'string' }
          }
        }
      }
    }
  })
  @ApiResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description: 'Error occurred during database connectivity test'
  })
  async testDatabaseConnections(): Promise<DatabaseTestResult> {
    return this.databaseTestService.testDatabaseConnections();
  }
} 