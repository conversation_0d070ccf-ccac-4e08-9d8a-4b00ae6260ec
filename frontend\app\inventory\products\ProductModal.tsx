// ProductModal.tsx - Modal for create/edit product
//
// [Modal UX Convention]
// - <PERSON><PERSON> must close on Escape key.
// - When modal opens, the first input (e.g. Name) must be focused automatically.
// - These conventions are required for all modals in this project (see CODE_GENERATION.md).

import React, { useEffect, useRef } from "react";

interface ProductModalProps {
  open: boolean;
  title: string;
  children: React.ReactNode;
  onClose: () => void;
}

export default function ProductModal({ open, title, children, onClose }: ProductModalProps) {
  const modalRef = useRef<HTMLDivElement>(null);
  const firstInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (!open) return;
    // Focus the first input
    const input = modalRef.current?.querySelector('input, textarea, select') as HTMLElement | null;
    if (input) {
      input.focus();
    }
    // Escape key handler
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === "Escape") {
        onClose();
      }
    };
    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [open, onClose]);

  if (!open) return null;
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40">
      <div ref={modalRef} className="bg-white rounded-lg shadow-lg w-full max-w-lg relative p-6">
        <button
          className="absolute top-2 right-2 text-gray-400 hover:text-gray-700"
          onClick={onClose}
          aria-label="Close"
        >
          &times;
        </button>
        <h2 className="text-xl font-bold mb-4">{title}</h2>
        {children}
      </div>
    </div>
  );
}
