import { Modu<PERSON> } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { Customer } from "./customer.entity";
import { CustomerPayment } from "./customer-payment.entity";
import { CreditAdjustment } from "./credit-adjustment.entity";
import { CustomerController } from "./customer.controller";
import { CustomerPaymentController } from "./customer-payment.controller";
import { CustomerService } from "./customer.service.typeorm";
import { CustomerCreditService } from "./customer-credit.service.typeorm";
import { CustomerPaymentService } from "./customer-payment.service.typeorm";

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Customer,
      CustomerPayment,
      CreditAdjustment,
    ]),
  ],
  controllers: [CustomerController, CustomerPaymentController],
  providers: [CustomerService, CustomerCreditService, CustomerPaymentService],
  exports: [TypeOrmModule, CustomerCreditService, CustomerPaymentService],
})
export class CustomersModule {}
