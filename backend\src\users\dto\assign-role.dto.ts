import { ApiProperty } from "@nestjs/swagger";
import { IsUUID } from "class-validator";

export class AssignRoleDto {
  @ApiProperty({
    example: "018ea2bb-6b8d-7e9a-8a2a-7e9a8a2a7e9a",
    description: "UUID of the role to assign",
  })
  @IsUUID("all")
  roleUuid: string;

  @ApiProperty({
    example: "018ea2bb-aaaa-bbbb-cccc-ddddeeeeffff",
    description: "UUID of the user performing the assignment (assigner)",
  })
  @IsUUID("all")
  assignerUuid: string;
}
