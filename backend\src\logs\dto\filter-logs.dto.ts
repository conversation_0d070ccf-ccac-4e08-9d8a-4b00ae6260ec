import { IsOptional, IsString, IsUUID } from "class-validator";
import { ApiProperty } from "@nestjs/swagger";
import { PaginationQueryDto } from "../../dto/pagination.dto";

export class FilterLogsDto extends PaginationQueryDto {
  @ApiProperty({
    description: "Filter by user UUID",
    example: "01890b6e-7b8c-7e6b-8e2e-7b8c7e6b8e2e",
    required: false,
  })
  @IsOptional()
  @IsString()
  @IsUUID('all', { message: "userUuid must be a valid UUID" })
  userUuid?: string;

  @ApiProperty({
    description: "Filter by operation (e.g., 'created', 'updated', 'deleted', 'cancelled')",
    example: "deleted",
    required: false,
  })
  @IsOptional()
  @IsString()
  operation?: string;

  @ApiProperty({
    description: "Filter by entity (e.g., 'sale123', 'product456', 'customer789')",
    example: "sale123",
    required: false,
  })
  @IsOptional()
  @IsString()
  entity?: string;

  @ApiProperty({
    description: "Filter by description text (partial match)",
    example: "deleted sale order",
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: "Filter by JSON data field (exact match on key-value pairs)",
    example: { "orderId": "12345" },
    required: false,
  })
  @IsOptional()
  data?: Record<string, any>;
} 