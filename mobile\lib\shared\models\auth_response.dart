import 'package:json_annotation/json_annotation.dart';
import 'user.dart';

part 'auth_response.g.dart';

@JsonSerializable()
class AuthResponse {

  AuthResponse({
    required this.accessToken,
    this.refreshToken,
    required this.user,
    required this.expiresIn,
  });

  factory AuthResponse.fromJson(Map<String, dynamic> json) => _$AuthResponseFromJson(json);
  final String accessToken;
  final String? refreshToken;
  final User user;
  final int expiresIn;
  Map<String, dynamic> toJson() => _$AuthResponseToJson(this);
} 