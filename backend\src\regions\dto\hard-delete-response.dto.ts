import { ApiProperty } from "@nestjs/swagger";

export class HardDeleteResponseDto {
  @ApiProperty({
    example: true,
    description: "Whether the operation was successful",
  })
  success: boolean;

  @ApiProperty({ example: 42, description: "Number of regions deleted" })
  deletedCount: number;

  @ApiProperty({
    example: "Successfully deleted 42 regions",
    description: "Message describing the operation result",
  })
  message: string;
}
