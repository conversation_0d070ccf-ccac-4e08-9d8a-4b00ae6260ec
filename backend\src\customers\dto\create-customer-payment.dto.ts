import { ApiProperty } from "@nestjs/swagger";
import { IsUUID, IsPositive, IsEnum, IsString, IsOptional } from "class-validator";
import { PaymentMethod } from "../customer-payment.entity";

export class CreateCustomerPaymentDto {
  @ApiProperty({
    example: "uuid-v7-string",
    description: "UUID of the customer making the payment",
  })
  @IsUUID("all")
  customerUuid: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "UUID of the user processing the payment",
  })
  @IsUUID("all")
  userUuid: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "UUID of the warehouse",
  })
  @IsUUID("all")
  warehouseUuid: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "UUID of the related sale (optional)",
    required: false,
  })
  @IsOptional()
  @IsUUID("all")
  saleUuid?: string;

  @ApiProperty({
    example: "cash",
    description: "Payment method used",
    enum: Object.values(PaymentMethod),
  })
  @IsEnum(PaymentMethod)
  paymentMethod: PaymentMethod;

  @ApiProperty({
    example: 100.0,
    description: "Payment amount (must be positive)",
  })
  @IsPositive()
  amount: number;

  @ApiProperty({
    example: "Payment for invoice #INV-001",
    description: "Description or note for the payment",
  })
  @IsString()
  description: string;

  @ApiProperty({
    example: "REF-123456",
    description: "External reference number (e.g., transaction ID)",
    required: false,
  })
  @IsOptional()
  @IsString()
  referenceNumber?: string;
} 