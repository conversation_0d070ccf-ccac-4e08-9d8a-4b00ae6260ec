import { ApiProperty } from "@nestjs/swagger";

export class CompanyResponseDto {
  @ApiProperty({
    example: "018ea2bb-6b8d-7e9a-8a2a-7e9a8a2a7e9a",
    description: "Unique identifier for the company (UUIDv7)",
  })
  uuid: string;

  @ApiProperty({
    example: "018ea2bb-6b8d-7e9a-8a2a-7e9a8a2a7e9a",
    description: "UUIDv7 of the user who owns this company",
  })
  userUuidString: string;

  @ApiProperty({
    example: "Acme Corporation",
    description: "Company name",
  })
  name: string;

  @ApiProperty({
    example: "*********",
    description: "Tax identification number (NIF)",
  })
  nif: string;

  @ApiProperty({
    example: "RC123456",
    description: "Register number (RC)",
  })
  rc: string;

  @ApiProperty({
    example: "ART001",
    description: "Article number",
  })
  articleNumber: string;

  @ApiProperty({
    example: "123 Main Street, City, Country",
    description: "Company address",
  })
  address: string;

  @ApiProperty({
    example: "https://www.acme.com",
    description: "Company website URL",
    required: false,
  })
  website?: string;

  @ApiProperty({
    example: "+*********0",
    description: "Company phone number",
  })
  phoneNumber: string;

  @ApiProperty({
    example: false,
    description: "Soft delete flag",
  })
  isDeleted: boolean;

  @ApiProperty({
    example: "2024-01-01T00:00:00.000Z",
    description: "Creation timestamp",
  })
  createdAt: Date;

  @ApiProperty({
    example: "2024-01-01T00:00:00.000Z",
    description: "Last update timestamp",
  })
  updatedAt: Date;
}
