import React, { useEffect, useRef } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Dialog } from '@headlessui/react';
import { ExclamationCircleIcon } from '@heroicons/react/20/solid';
import { Role } from '../usersApi';

const addUserSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  email: z.string().email('Invalid email address'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
  roleUuid: z.string().min(1, 'Role is required'),
});

export type AddUserForm = z.infer<typeof addUserSchema>;
import { VanDto } from '@/app/logistics/vans/vansApi';

interface AddUserModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: AddUserForm & { userType: 'user' }) => void;
  roles: Role[];
  vans: VanDto[];
  isLoading: boolean;
}

const AddUserModal: React.FC<AddUserModalProps> = ({ isOpen, onClose, onSubmit, roles, vans, isLoading }) => {
  const { register, handleSubmit, reset, watch, formState: { errors } } = useForm<AddUserForm>({
    resolver: zodResolver(addUserSchema),
    defaultValues: { name: '', email: '', password: '', roleUuid: '' }
  });

  const selectedRoleUuid = watch('roleUuid');
  const selectedRole = roles.find(r => r.uuid === selectedRoleUuid);

  const handleClose = () => {
    reset();
    onClose();
  };

  const handleFormSubmit = (data: AddUserForm) => {
    // Always set userType to 'user' since we don't allow creating 'super' users through the UI
    const formDataWithUserType = { ...data, userType: 'user' as const };
    console.log('Submitted data:', formDataWithUserType); // DEBUG
    onSubmit(formDataWithUserType);
  };

  return (
    <Dialog open={isOpen} onClose={handleClose} className="relative z-50">
      <div className="fixed inset-0 bg-black/30" aria-hidden="true" />
      <div className="fixed inset-0 flex items-center justify-center p-4">
        <Dialog.Panel className="w-full max-w-sm rounded-lg bg-white p-6 shadow-xl">
          <Dialog.Title className="text-lg font-bold text-gray-900">Add New User</Dialog.Title>
          <Dialog.Description className="mt-2 text-sm text-gray-500">
            Fill in the details below to create a new user account. All users created through this interface will be standard users.
          </Dialog.Description>
          <form onSubmit={handleSubmit(handleFormSubmit)} className="mt-4 space-y-4">
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700">Name <span className="text-red-500">*</span></label>
              <input id="name" {...register('name')} placeholder="e.g. John Doe" className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm" />
              {errors.name && <p className="mt-1 flex items-center text-xs text-red-500"><ExclamationCircleIcon className="mr-1 h-4 w-4" />{errors.name.message}</p>}
            </div>
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700">Email <span className="text-red-500">*</span></label>
              <input id="email" {...register('email')} placeholder="e.g. <EMAIL>" className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm" />
              {errors.email && <p className="mt-1 flex items-center text-xs text-red-500"><ExclamationCircleIcon className="mr-1 h-4 w-4" />{errors.email.message}</p>}
            </div>
            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700">Password <span className="text-red-500">*</span></label>
              <input id="password" type="password" {...register('password')} placeholder="Min. 6 characters" className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm" />
              {errors.password && <p className="mt-1 flex items-center text-xs text-red-500"><ExclamationCircleIcon className="mr-1 h-4 w-4" />{errors.password.message}</p>}
            </div>
            <div>
              <label htmlFor="roleUuid" className="block text-sm font-medium text-gray-700">Role <span className="text-red-500">*</span></label>
              <select id="roleUuid" {...register('roleUuid')} className="mt-1 block w-full rounded-lg border-2 border-gray-200 py-1.5 pl-4 pr-10 text-gray-700 bg-white shadow-sm hover:border-blue-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 focus:outline-none transition-all duration-200 sm:text-sm text-left cursor-pointer">
                <option value="">Select a role</option>
                {roles.map(role => (
                  <option key={role.uuid} value={role.uuid}>{role.name}</option>
                ))}
              </select>
              {errors.roleUuid && <p className="mt-1 flex items-center text-xs text-red-500"><ExclamationCircleIcon className="mr-1 h-4 w-4" />{errors.roleUuid.message}</p>}
            </div>
            <div className="mt-6 flex flex-col space-y-2">
              <button type="submit" disabled={isLoading} className="inline-flex w-full justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:bg-blue-300">
                {isLoading ? 'Adding...' : 'Add User'}
              </button>
              <button type="button" onClick={handleClose} className="inline-flex w-full justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2">
                Cancel
              </button>
            </div>
          </form>
        </Dialog.Panel>
      </div>
    </Dialog>
  );
};

export default AddUserModal;
