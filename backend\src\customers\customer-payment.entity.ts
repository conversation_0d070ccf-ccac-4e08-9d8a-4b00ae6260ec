import { Entity, PrimaryColumn, Column, CreateDateColumn, UpdateDateColumn, Index } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Uuid7 } from '../utils/uuid7';

export enum PaymentMethod {
  CASH = "cash",
  CREDIT_CARD = "credit_card",
  DEBIT_CARD = "debit_card",
  BANK_TRANSFER = "bank_transfer",
  CHECK = "check",
  MOBILE_PAYMENT = "mobile_payment",
}

export enum PaymentStatus {
  PENDING = "pending",
  COMPLETED = "completed",
  FAILED = "failed",
  CANCELLED = "cancelled",
  REFUNDED = "refunded",
}

@Entity('customer_payments')
export class CustomerPayment {
  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the customer payment (primary key)",
  })
  @PrimaryColumn('uuid')
  id: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the customer",
  })
  @Column('uuid')
  @Index()
  customerUuid: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the user who processed the payment",
  })
  @Column('uuid')
  @Index()
  userUuid: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the warehouse",
  })
  @Column('uuid')
  @Index()
  warehouseUuid: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the related sale (optional)",
    required: false,
  })
  @Column('uuid', { nullable: true })
  @Index()
  saleUuid?: string;

  @ApiProperty({
    example: "cash",
    description: "Payment method used",
    enum: Object.values(PaymentMethod),
  })
  @Column({
    type: 'enum',
    enum: PaymentMethod,
  })
  paymentMethod: PaymentMethod;

  @ApiProperty({
    example: 100.0,
    description: "Payment amount (must be positive)",
  })
  @Column('decimal', { precision: 10, scale: 2 })
  amount: number;

  @ApiProperty({
    example: "completed",
    description: "Current status of the payment",
    enum: Object.values(PaymentStatus),
  })
  @Column({
    type: 'enum',
    enum: PaymentStatus,
    default: PaymentStatus.PENDING,
  })
  status: PaymentStatus;

  @ApiProperty({
    example: "Payment for invoice #INV-001",
    description: "Description or note for the payment",
  })
  @Column()
  description: string;

  @ApiProperty({
    example: "REF-123456",
    description: "External reference number (e.g., transaction ID)",
    required: false,
  })
  @Column({ nullable: true })
  referenceNumber?: string;

  @ApiProperty({
    example: "2024-01-15T10:30:00.000Z",
    description: "Date and time when the payment was processed",
    required: false,
  })
  @Column({ nullable: true })
  processedAt?: Date;

  @ApiProperty({
    example: 50.0,
    description: "Customer credit balance before this payment",
  })
  @Column('decimal', { precision: 10, scale: 2 })
  previousCreditBalance: number;

  @ApiProperty({
    example: 150.0,
    description: "Customer credit balance after this payment",
  })
  @Column('decimal', { precision: 10, scale: 2 })
  newCreditBalance: number;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Helper method to generate UUID
  static generateId(): string {
    return new Uuid7().toString();
  }

  // Helper method to generate UUID
  static fromBinary(binary: any): string {
    if (!binary) return null;
    try {
      return new Uuid7(binary).toString();
    } catch {
      return null;
    }
  }
} 