import React, { useState } from 'react';

interface SupplierSearchBarProps {
  onSearch: (query: string) => void;
  onClear: () => void;
  loading: boolean;
}

export default function SupplierSearchBar({ onSearch, onClear, loading }: SupplierSearchBarProps) {
  const [query, setQuery] = useState('');

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setQuery(e.target.value);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSearch(query);
  };

  const handleClear = () => {
    setQuery('');
    onClear();
  };

  return (
    <div className="mb-4">
      <form onSubmit={handleSubmit} className="flex gap-2">
        <input
          type="text"
          className="border rounded px-3 py-2 flex-1"
          placeholder="Search suppliers by name..."
          value={query}
          onChange={handleChange}
          disabled={loading}
          aria-label="Search suppliers"
        />
        <button
          type="submit"
          className="bg-blue-600 text-white rounded px-4 py-2 font-semibold hover:bg-blue-700 disabled:opacity-50"
          disabled={loading || !query.trim()}
        >
          Search
        </button>
        {query && (
          <button
            type="button"
            onClick={handleClear}
            className="bg-gray-200 text-gray-700 rounded px-4 py-2 font-semibold hover:bg-gray-300 disabled:opacity-50"
            disabled={loading}
          >
            Clear
          </button>
        )}
      </form>
    </div>
  );
}
