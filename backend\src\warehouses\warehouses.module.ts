import { Module } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { WarehousesController } from "./warehouses.controller";
import { WarehousesService } from "./warehouses.service";
import { Warehouse } from "./warehouse.entity";
import { Role } from "../users/role.entity";
import { InventoryModule } from "../inventory/inventory.module";

@Module({
  imports: [
    TypeOrmModule.forFeature([Warehouse, Role]),
    InventoryModule,
  ],
  controllers: [WarehousesController],
  providers: [WarehousesService],
  exports: [WarehousesService, TypeOrmModule],
})
export class WarehousesModule {}
