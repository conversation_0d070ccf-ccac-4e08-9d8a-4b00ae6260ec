import { <PERSON><PERSON><PERSON>, PrimaryColumn, <PERSON>umn, CreateDateColumn, UpdateDateColumn, Index } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Uuid7 } from '../utils/uuid7';
import { AccountPlanIdentifier } from './enums/account-plan.enum';

@Entity('account_plans')
export class AccountPlan {
  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the account plan (primary key)",
  })
  @PrimaryColumn('uuid')
  id: string;

  @ApiProperty({
    example: "FREE",
    description: "The identifier of the account plan",
    enum: AccountPlanIdentifier,
  })
  @Column({ unique: true })
  @Index()
  identifier: AccountPlanIdentifier;

  @ApiProperty({
    example: "Free Plan",
    description: "The display name of the account plan",
  })
  @Column()
  name: string;

  @ApiProperty({
    example: ["INVENTORY_TRACKING", "SALES_ORDERS", "CUSTOMER_MANAGEMENT"],
    description: "List of feature identifiers included in this plan",
    isArray: true,
  })
  @Column('text', { array: true })
  features: string[];

  @ApiProperty({ example: false, description: "Soft delete flag" })
  @Column({ default: false })
  isDeleted: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Helper method to generate UUID
  static generateId(): string {
    return new Uuid7().toString();
  }

  // Helper method to generate UUID
  static fromBinary(binary: any): string {
    if (!binary) return null;
    try {
      return new Uuid7(binary).toString();
    } catch {
      return null;
    }
  }
} 