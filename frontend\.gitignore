# === Node Modules ===
node_modules/

# === Logs ===
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# === Build Output ===
.next/
out/
dist/

# === OS Files ===
.DS_Store
Thumbs.db

# === Environment Variables ===
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# === Vercel Deployment Folder ===
.vercel/

# === IDE Specific ===
.vscode/
.idea/

# === Misc ===
*.local
coverage/
*.log
*.swp

# === Optional Cache ===
.cache/
.next/cache/

# === Testing ===
jest.config.js
test-results/

# === TypeScript ===
*.tsbuildinfo
