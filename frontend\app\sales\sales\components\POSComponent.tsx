import React from 'react';
import { POSView } from './POSView';
import type { Sale } from '../salesApi';

interface POSComponentProps {
  onSaleComplete?: () => void;
  onNewSale?: () => void;
  editSaleData?: Sale | null;
  posState?: any; // Will be properly typed when we update POSView
}

export const POSComponent: React.FC<POSComponentProps> = ({ 
  onSaleComplete, 
  onNewSale,
  editSaleData,
  posState
}) => {
  return (
    <div className="h-full flex flex-col overflow-hidden">
      {/* POS View - Full remaining height */}
      <div className="flex-1">
        <POSView 
          onSaleComplete={onSaleComplete} 
          onNewSale={onNewSale}
          editSaleData={editSaleData}
          posState={posState}
        />
      </div>
    </div>
  );
}; 