import { ApiProperty } from "@nestjs/swagger";
import {
  IsUUID,
  IsOptional,
  IsEnum,
  IsString,
  IsNumber,
  IsDateString,
  Min,
  IsInt,
} from "class-validator";
import { Type } from "class-transformer";
import { PaymentMethod, PaymentStatus } from "../customer-payment.entity";

export class FilterCustomerPaymentDto {
  @ApiProperty({
    example: "uuid-v7-string",
    description: "Filter by customer UUID",
    required: false,
  })
  @IsOptional()
  @IsUUID("all")
  customerUuid?: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "Filter by user UUID",
    required: false,
  })
  @IsOptional()
  @IsUUID("all")
  userUuid?: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "Filter by warehouse UUID",
    required: false,
  })
  @IsOptional()
  @IsUUID("all")
  warehouseUuid?: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "Filter by sale UUID",
    required: false,
  })
  @IsOptional()
  @IsUUID("all")
  saleUuid?: string;

  @ApiProperty({
    example: "cash",
    description: "Filter by payment method",
    enum: Object.values(PaymentMethod),
    required: false,
  })
  @IsOptional()
  @IsEnum(PaymentMethod)
  paymentMethod?: PaymentMethod;

  @ApiProperty({
    example: "completed",
    description: "Filter by payment status",
    enum: Object.values(PaymentStatus),
    required: false,
  })
  @IsOptional()
  @IsEnum(PaymentStatus)
  status?: PaymentStatus;

  @ApiProperty({
    example: 100.0,
    description: "Filter by exact amount",
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  amount?: number;

  @ApiProperty({
    example: 50.0,
    description: "Filter by minimum amount (inclusive)",
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  minAmount?: number;

  @ApiProperty({
    example: 500.0,
    description: "Filter by maximum amount (inclusive)",
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  maxAmount?: number;

  @ApiProperty({
    example: "Payment for invoice",
    description: "Search in description (partial match)",
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    example: "REF-123",
    description: "Search in reference number (partial match)",
    required: false,
  })
  @IsOptional()
  @IsString()
  referenceNumber?: string;

  @ApiProperty({
    example: "2024-01-15T00:00:00.000Z",
    description: "Filter by payments from this date (inclusive)",
    required: false,
  })
  @IsOptional()
  @IsDateString()
  fromDate?: string;

  @ApiProperty({
    example: "2024-01-31T23:59:59.999Z",
    description: "Filter by payments to this date (inclusive)",
    required: false,
  })
  @IsOptional()
  @IsDateString()
  toDate?: string;

  @ApiProperty({
    example: "2024-01-15T00:00:00.000Z",
    description: "Filter by payments processed from this date (inclusive)",
    required: false,
  })
  @IsOptional()
  @IsDateString()
  processedFromDate?: string;

  @ApiProperty({
    example: "2024-01-31T23:59:59.999Z",
    description: "Filter by payments processed to this date (inclusive)",
    required: false,
  })
  @IsOptional()
  @IsDateString()
  processedToDate?: string;

  @ApiProperty({
    example: "amount",
    description: "Sort by field",
    enum: [
      "amount",
      "status",
      "paymentMethod",
      "description",
      "referenceNumber",
      "processedAt",
      "createdAt",
      "updatedAt",
    ],
    required: false,
  })
  @IsOptional()
  @IsString()
  sortBy?: string;

  @ApiProperty({
    example: "desc",
    description: "Sort order (default: desc)",
    enum: ["asc", "desc"],
    required: false,
  })
  @IsOptional()
  @IsString()
  sortOrder?: "asc" | "desc";

  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @ApiProperty({
    example: 1,
    description: "Page number (1-based)",
    required: false,
    minimum: 1,
    default: 1,
  })
  page?: number;

  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @ApiProperty({
    example: 50,
    description: "Number of items per page",
    required: false,
    minimum: 1,
    default: 50,
  })
  limit?: number;
} 