// Auth API service for centralized authentication calls
// Uses the new enhanced backend endpoints with security features

export interface EnhancedLoginRequest {
  identifier: string;
  password?: string;
  clientIp?: string;
  userAgent?: string;
}

export interface EnhancedLoginResponse {
  accessToken: string;
  refreshToken: string;
  accessTokenExpiresIn: number;
  refreshTokenExpiresIn: number;
  user: {
    uuid: string;
    email: string;
    firstName: string | null;
    lastName: string | null;
    phone: string | null;
    isActive: boolean;
    roles: Array<{
      uuid: string;
      name: string;
      permissions: string[];
    }>;
    warehouseUuid: string | null;
    vanUuid: string | null;
    createdAt: string;
    updatedAt: string;
  };
  sessionStats: {
    activeTokens: number;
    totalTokens: number;
    lastUsed: string | null;
  };
}

export interface RefreshTokenRequest {
  refreshToken: string;
  clientIp?: string;
  userAgent?: string;
}

export interface RefreshTokenResponse {
  accessToken: string;
  refreshToken: string;
  accessTokenExpiresIn: number;
  refreshTokenExpiresIn: number;
}

export interface LogoutRequest {
  refreshToken?: string;
  revokeAll?: boolean;
}

export interface SecurityStatusResponse {
  isLocked: boolean;
  lockedUntil: string | null;
  remainingAttempts: number;
  suspiciousActivityDetected: boolean;
  recentEvents: Array<{
    eventType: string;
    timestamp: string;
    ipAddress?: string;
    success: boolean;
  }>;
}

export interface LegacyLoginRequest {
  identifier: string;
  password?: string;
}

export interface LegacyLoginResponse {
  accessToken: string;
  refreshToken: string | null;
  user: {
    uuid: string;
    email: string;
    firstName: string | null;
    lastName: string | null;
    phone: string | null;
    isActive: boolean;
    roles: any[];
    warehouseUuid: string | null;
    vanUuid: string | null;
    createdAt: string;
    updatedAt: string;
  };
  expiresIn: number;
}

// Enhanced login with security features
export async function enhancedLogin(request: EnhancedLoginRequest): Promise<EnhancedLoginResponse> {
  const response = await fetch('/api/auth/login', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      ...request,
      clientIp: request.clientIp || 'frontend',
      userAgent: request.userAgent || navigator.userAgent,
    }),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.message || `Login failed: ${response.status}`);
  }

  return response.json();
}

// Legacy login for backward compatibility
export async function legacyLogin(request: LegacyLoginRequest): Promise<LegacyLoginResponse> {
  const response = await fetch('/api/auth/login/legacy', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(request),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.message || `Login failed: ${response.status}`);
  }

  return response.json();
}

// Refresh access token
export async function refreshToken(request: RefreshTokenRequest): Promise<RefreshTokenResponse> {
  const response = await fetch('/api/auth/refresh', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      ...request,
      clientIp: request.clientIp || 'frontend',
      userAgent: request.userAgent || navigator.userAgent,
    }),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.message || `Token refresh failed: ${response.status}`);
  }

  return response.json();
}

// Logout with token revocation
export async function logout(request: LogoutRequest, accessToken: string): Promise<{ message: string }> {
  const response = await fetch('/api/auth/logout', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${accessToken}`,
    },
    body: JSON.stringify(request),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.message || `Logout failed: ${response.status}`);
  }

  return response.json();
}

// Get security status
export async function getSecurityStatus(accessToken: string): Promise<SecurityStatusResponse> {
  const response = await fetch('/api/auth/security-status', {
    headers: {
      'Authorization': `Bearer ${accessToken}`,
    },
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.message || `Failed to get security status: ${response.status}`);
  }

  return response.json();
}

// Validate token (for session checking)
export async function validateToken(accessToken: string): Promise<boolean> {
  try {
    const response = await fetch('/api/auth/validate', {
      headers: {
        'Authorization': `Bearer ${accessToken}`,
      },
    });
    return response.ok;
  } catch {
    return false;
  }
}

// Google OAuth token authentication
export async function googleTokenAuth(googleData: {
  accessToken: string;
  idToken: string;
  email: string;
  name: string;
  photoUrl?: string;
}): Promise<{
  access_token: string;
  refresh_token: string | null;
  user: {
    uuid: string;
    email: string;
    name: string;
    roleUuidString: string;
    userType: string;
    warehouseUuidString: string | null;
  };
  expiresIn: number;
}> {
  const response = await fetch('/api/auth/google/token', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(googleData),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.message || `Google authentication failed: ${response.status}`);
  }

  return response.json();
} 