import axios from "axios";
import { getAxiosAuthHeaders } from '@/utils/authHeaders';

export interface VanDto {
  uuid: string;
  name: string;
  warehouseUuid: string;
  storageUuid: string;
  licensePlate?: string;
  model?: string;
  year?: number;
  createdAt: string;
  updatedAt: string;
  isDeleted: boolean;
}

export interface CreateVanDto {
  name: string;
  warehouseUuid: string;
  licensePlate?: string;
  model?: string;
  year?: number;
}

export interface UpdateVanDto {
  name?: string;
  licensePlate?: string;
  model?: string;
  year?: number;
}

export interface FilterVanDto {
  warehouseUuid?: string;
  name?: string;
  licensePlate?: string;
  model?: string;
}

const VANS_API = "/api/vans";

export async function listVans(filter?: FilterVanDto): Promise<VanDto[]> {
  const res = await axios.get(VANS_API, { 
    params: filter,
    headers: getAxiosAuthHeaders(),
  });
  return res.data;
}

export async function getVanByUuid(uuid: string): Promise<VanDto> {
  const res = await axios.get(`${VANS_API}/${uuid}`, {
    headers: getAxiosAuthHeaders(),
  });
  return res.data;
}

export async function createVan(data: CreateVanDto): Promise<VanDto> {
  const res = await axios.post(VANS_API, data, {
    headers: getAxiosAuthHeaders(),
  });
  return res.data;
}

export async function updateVan(uuid: string, data: UpdateVanDto): Promise<VanDto> {
  const res = await axios.patch(`${VANS_API}/${uuid}`, data, {
    headers: getAxiosAuthHeaders(),
  });
  return res.data;
}

export async function softDeleteVan(uuid: string): Promise<VanDto> {
  const res = await axios.delete(`${VANS_API}/${uuid}`, {
    headers: getAxiosAuthHeaders(),
  });
  return res.data;
}

export async function listAllVans(): Promise<VanDto[]> {
  const res = await axios.get(`${VANS_API}/raw-list`, {
    headers: getAxiosAuthHeaders(),
  });
  return res.data;
} 