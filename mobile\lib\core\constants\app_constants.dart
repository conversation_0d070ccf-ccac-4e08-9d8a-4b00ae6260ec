import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';

/// Application-wide constants
/// 
/// This class contains all the constant values used throughout
/// the Dido Distribution mobile application.
class AppConstants {
  // Private constructor to prevent instantiation
  AppConstants._();

  // =============================================================================
  // App Information
  // =============================================================================
  
  /// Application name
  static const String appName = 'Dido Distribution Mobile';
  
  /// Application version
  static const String appVersion = '1.0.0';
  
  /// Application build number
  static const String buildNumber = '1';
  
  /// Company name
  static const String companyName = 'Dido Distribution Inc.';

  // =============================================================================
  // API Configuration
  // =============================================================================
  
  /// Get API base URL from environment variables
  /// For Android emulator: http://********:8000 (maps to localhost:8000)
  /// For iOS simulator: http://localhost:8000 or http://127.0.0.1:8000
  /// For physical device: http://YOUR_COMPUTER_IP:8000
  static String get apiBaseUrl => dotenv.env['API_BASE_URL']!;
  
  /// API version
  static const String apiVersion = 'v1';
  
  /// Default API timeout fallback - used when .env is not available
  static const int _defaultApiTimeout = 15000;
  
  /// Get API timeout from environment variables or use default
  static int get apiTimeout => int.tryParse(dotenv.env['API_TIMEOUT'] ?? '') ?? _defaultApiTimeout;
  
  /// API endpoints
  static const String authEndpoint = '/auth';
  static const String usersEndpoint = '/users';
  static const String productsEndpoint = '/products';
  static const String inventoryEndpoint = '/inventory';
  static const String salesEndpoint = '/sales';
  static const String purchaseEndpoint = '/purchase';
  static const String warehousesEndpoint = '/warehouses';
  static const String vansEndpoint = '/vans';
  static const String customersEndpoint = '/customers';
  static const String suppliersEndpoint = '/suppliers';

  // =============================================================================
  // Storage Keys
  // =============================================================================
  
  /// SharedPreferences keys
  static const String accessTokenKey = 'access_token';
  static const String refreshTokenKey = 'refresh_token';
  static const String userDataKey = 'user_data';
  static const String settingsKey = 'app_settings';
  
  /// Hive box names
  static const String userBoxName = 'users';
  static const String productsBoxName = 'products';
  static const String inventoryBoxName = 'inventory';
  static const String salesBoxName = 'sales';
  static const String cacheBoxName = 'cache';
  static const String settingsBoxName = 'settings';

  // =============================================================================
  // UI Constants
  // =============================================================================
  
  /// Default padding values
  static const double paddingXS = 4;
  static const double paddingSM = 8;
  static const double paddingMD = 16;
  static const double paddingLG = 24;
  static const double paddingXL = 32;
  
  /// Default margin values
  static const double marginXS = 4;
  static const double marginSM = 8;
  static const double marginMD = 16;
  static const double marginLG = 24;
  static const double marginXL = 32;
  
  /// Border radius values
  static const double radiusXS = 4;
  static const double radiusSM = 8;
  static const double radiusMD = 12;
  static const double radiusLG = 16;
  static const double radiusXL = 24;
  static const double radiusFull = 999;
  
  /// Icon sizes
  static const double iconSizeXS = 16;
  static const double iconSizeSM = 20;
  static const double iconSizeMD = 24;
  static const double iconSizeLG = 32;
  static const double iconSizeXL = 48;

  // =============================================================================
  // Animation Durations
  // =============================================================================
  
  /// Animation duration constants
  static const Duration animationFast = Duration(milliseconds: 150);
  static const Duration animationNormal = Duration(milliseconds: 300);
  static const Duration animationSlow = Duration(milliseconds: 500);
  
  /// Transition durations
  static const Duration transitionDuration = Duration(milliseconds: 300);
  static const Duration splashDuration = Duration(seconds: 2);

  // =============================================================================
  // Validation Constants
  // =============================================================================
  
  /// Minimum password length
  static const int minPasswordLength = 8;
  
  /// Maximum password length
  static const int maxPasswordLength = 128;
  
  /// Username minimum length
  static const int minUsernameLength = 3;
  
  /// Username maximum length
  static const int maxUsernameLength = 50;
  
  /// Email regex pattern
  static const String emailPattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$';
  
  /// Phone number regex pattern
  static const String phonePattern = r'^\+?[1-9]\d{1,14}$';

  // =============================================================================
  // Business Logic Constants
  // =============================================================================
  
  /// Default page size for pagination
  static const int defaultPageSize = 20;
  
  /// Maximum page size
  static const int maxPageSize = 100;
  
  /// Default currency
  static const String defaultCurrency = 'USD';
  
  /// Default currency symbol
  static const String defaultCurrencySymbol = '\$';
  
  /// Tax rate (as decimal, e.g., 0.08 for 8%)
  static const double defaultTaxRate = 0.08;
  
  /// Low stock threshold
  static const int lowStockThreshold = 10;
  
  /// Critical stock threshold
  static const int criticalStockThreshold = 5;

  // =============================================================================
  // Localization
  // =============================================================================
  
  /// Supported locales
  static const List<Locale> supportedLocales = [
    Locale('en', 'US'), // English (United States)
    Locale('es', 'ES'), // Spanish (Spain)
    Locale('fr', 'FR'), // French (France)
  ];
  
  /// Default locale
  static const Locale defaultLocale = Locale('en', 'US');

  // =============================================================================
  // File and Media Constants
  // =============================================================================
  
  /// Maximum file size for uploads (in bytes)
  static const int maxFileSize = 10 * 1024 * 1024; // 10 MB
  
  /// Allowed image extensions
  static const List<String> allowedImageExtensions = [
    'jpg',
    'jpeg',
    'png',
    'gif',
    'webp',
  ];
  
  /// Allowed document extensions
  static const List<String> allowedDocumentExtensions = [
    'pdf',
    'doc',
    'docx',
    'xls',
    'xlsx',
    'txt',
  ];

  // =============================================================================
  // Sync and Offline Constants
  // =============================================================================
  
  /// Sync interval in minutes
  static const int syncIntervalMinutes = 15;
  
  /// Maximum offline queue size
  static const int maxOfflineQueueSize = 1000;
  
  /// Retry attempts for failed operations
  static const int maxRetryAttempts = 3;
  
  /// Retry delay in milliseconds
  static const int retryDelayMs = 2000;

  // =============================================================================
  // Security Constants
  // =============================================================================
  
  /// Session timeout in minutes
  static const int sessionTimeoutMinutes = 30;
  
  /// Maximum login attempts
  static const int maxLoginAttempts = 5;
  
  /// Account lockout duration in minutes
  static const int lockoutDurationMinutes = 15;

  // =============================================================================
  // Feature Flags
  // =============================================================================
  
  /// Default feature flag values
  static const bool defaultBiometricAuth = true;
  static const bool defaultOfflineMode = true;
  static const bool defaultPushNotifications = true;
  static const bool defaultBarcodeScanning = true;
  static const bool defaultGpsTracking = true;
  static const bool defaultBluetoothPrinting = true;
  static const bool defaultCameraFeatures = true;
  static const bool defaultAnalytics = true;
  static const bool defaultCrashlytics = false;

  // =============================================================================
  // Error Messages
  // =============================================================================
  
  /// Common error messages
  static const String errorGeneral = 'An unexpected error occurred. Please try again.';
  static const String errorNetwork = 'Network error. Please check your connection.';
  static const String errorTimeout = 'Request timeout. Please try again.';
  static const String errorUnauthorized = 'Unauthorized access. Please login again.';
  static const String errorForbidden = 'Access forbidden. You don\'t have permission.';
  static const String errorNotFound = 'Resource not found.';
  static const String errorServerError = 'Server error. Please try again later.';
  static const String errorValidation = 'Please check your input and try again.';
  static const String errorOffline = 'You are offline. Some features may not be available.';

  // =============================================================================
  // Success Messages
  // =============================================================================
  
  /// Common success messages
  static const String successLogin = 'Login successful!';
  static const String successLogout = 'Logout successful!';
  static const String successSave = 'Saved successfully!';
  static const String successUpdate = 'Updated successfully!';
  static const String successDelete = 'Deleted successfully!';
  static const String successSync = 'Sync completed successfully!';

  // =============================================================================
  // Routes
  // =============================================================================
  
  /// Route names
  static const String routeSplash = '/splash';
  static const String routeOnboarding = '/onboarding';
  static const String routeLogin = '/login';
  static const String routeRegister = '/register';
  static const String routeForgotPassword = '/forgot-password';
  static const String routeDashboard = '/dashboard';
  static const String routeProfile = '/profile';
  static const String routeSettings = '/settings';
  static const String routeInventory = '/inventory';
  static const String routeProducts = '/products';
  static const String routeSales = '/sales';
  static const String routePOS = '/pos';
  static const String routePurchase = '/purchase';
  static const String routeCustomers = '/customers';
  static const String routeSuppliers = '/suppliers';
  static const String routeReports = '/reports';
  static const String routeWarehouses = '/warehouses';
  static const String routeVans = '/vans';

  // =============================================================================
  // Asset Paths
  // =============================================================================
  
  /// Image asset paths
  static const String logoPath = 'assets/images/logo.png';
  static const String splashLogoPath = 'assets/images/splash_logo.png';
  static const String onboardingPath = 'assets/images/onboarding/';
  static const String placeholderImagePath = 'assets/images/placeholder.png';
  
  /// Icon asset paths
  static const String iconPath = 'assets/icons/';
  
  /// Animation asset paths
  static const String animationPath = 'assets/animations/';
  static const String loadingAnimationPath = 'assets/animations/loading.json';
  static const String successAnimationPath = 'assets/animations/success.json';
  static const String errorAnimationPath = 'assets/animations/error.json';
}

class ApiEndpoints {
  // Authentication
  static const String login = '/auth/login';
  static const String register = '/auth/register';

  static const String refreshToken = '/auth/refresh';
  
  // Users
  static const String users = '/users';
  static const String userProfile = '/users/profile';
  static const String changePassword = '/users/change-password';
  
  // Products
  static const String products = '/products';
  static const String productSearch = '/products/search';
  static const String productFilter = '/products/filter';
  
  // Inventory
  static const String inventory = '/inventory';
  static const String storage = '/inventory/storage';
  static const String stockAdjustment = '/inventory/stock-adjustment';
  static const String stockComputation = '/stock-computation';
  
  // Sales
  static const String sales = '/sales';
  static const String quotes = '/quotes';
  static const String customers = '/customers';
  
  // Purchasing
  static const String purchases = '/purchases';
  static const String suppliers = '/suppliers';
  
  // Logistics
  static const String vans = '/vans';
  static const String warehouses = '/companies';
  
  // Reports
  static const String reports = '/reports';
}

class AppRoutes {
  // Authentication
  static const String login = '/login';
  static const String register = '/register';
  static const String forgotPassword = '/forgot-password';
  
  // Main Navigation
  static const String dashboard = '/dashboard';
  static const String inventory = '/inventory';
  static const String sales = '/sales';
  static const String logistics = '/logistics';
  static const String purchasing = '/purchasing';
  static const String reports = '/reports';
  static const String settings = '/settings';
  
  // Inventory
  static const String products = '/inventory/products';
  static const String stockLevels = '/inventory/stock-levels';
  static const String stockAdjustments = '/inventory/adjustments';
  static const String stockTransfers = '/inventory/transfers';
  static const String storage = '/inventory/storage';
  
  // Sales
  static const String pos = '/sales/pos';
  static const String quotes = '/sales/quotes';
  static const String customers = '/sales/customers';
  static const String orders = '/sales/orders';
  
  // Logistics
  static const String vans = '/logistics/vans';
  static const String warehouses = '/logistics/warehouses';
  static const String routes = '/logistics/routes';
  static const String vanLoading = '/logistics/van-loading';
  
  // Purchasing
  static const String suppliers = '/purchasing/suppliers';
  static const String purchaseOrders = '/purchasing/orders';
  static const String goodsReceipt = '/purchasing/goods-receipt';
  
  // Reports
  static const String salesReports = '/reports/sales';
  static const String inventoryReports = '/reports/inventory';
  static const String financialReports = '/reports/financial';
  
  // Settings
  static const String userSettings = '/settings/user';
  static const String systemSettings = '/settings/system';
  static const String dataSettings = '/settings/data';
}

class AppStrings {
  // Common
  static const String loading = 'Loading...';
  static const String error = 'Error';
  static const String success = 'Success';
  static const String cancel = 'Cancel';
  static const String save = 'Save';
  static const String delete = 'Delete';
  static const String edit = 'Edit';
  static const String add = 'Add';
  static const String search = 'Search';
  static const String filter = 'Filter';
  static const String refresh = 'Refresh';
  static const String retry = 'Retry';
  static const String ok = 'OK';
  static const String yes = 'Yes';
  static const String no = 'No';
  
  // Authentication
  static const String login = 'Login';
  static const String logout = 'Logout';
  static const String register = 'Register';
  static const String email = 'Email';
  static const String password = 'Password';
  static const String confirmPassword = 'Confirm Password';
  static const String forgotPassword = 'Forgot Password?';

  static const String biometricAuth = 'Use Biometric Authentication';
  
  // Navigation
  static const String dashboard = 'Dashboard';
  static const String inventory = 'Inventory';
  static const String sales = 'Sales';
  static const String logistics = 'Logistics';
  static const String purchasing = 'Purchasing';
  static const String reports = 'Reports';
  static const String settings = 'Settings';
  
  // Inventory
  static const String products = 'Products';
  static const String stockLevels = 'Stock Levels';
  static const String stockAdjustments = 'Stock Adjustments';
  static const String stockTransfers = 'Stock Transfers';
  static const String storage = 'Storage';
  static const String lowStockAlert = 'Low Stock Alert';
  
  // Sales
  static const String pos = 'Point of Sale';
  static const String quotes = 'Quotes';
  static const String customers = 'Customers';
  static const String orders = 'Orders';
  static const String cart = 'Cart';
  static const String checkout = 'Checkout';
  
  // Error Messages
  static const String networkError = 'Network connection error';
  static const String serverError = 'Server error occurred';
  static const String invalidCredentials = 'Invalid email or password';
  static const String sessionExpired = 'Session expired, please login again';
  static const String permissionDenied = 'Permission denied';
  static const String dataNotFound = 'Data not found';
  static const String validationError = 'Please check your input';
  
  // Success Messages
  static const String loginSuccess = 'Login successful';
  static const String dataUpdated = 'Data updated successfully';
  static const String dataDeleted = 'Data deleted successfully';
  static const String dataSaved = 'Data saved successfully';
} 