const { Client } = require('pg');
require('dotenv').config();

const client = new Client({
  host: process.env.YUGABYTE_HOST,
  port: process.env.YUGABYTE_PORT,
  database: process.env.YUGABYTE_DATABASE,
  user: process.env.YUGABYTE_USER,
  password: process.env.YUGABYTE_PASSWORD,
});

async function testUserAccountPlans() {
  try {
    await client.connect();
    console.log('Connected to database');

    // Test if tables exist
    const tablesResult = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name IN ('account_plans', 'features')
    `);
    
    console.log('Available tables:', tablesResult.rows.map(row => row.table_name));

    // Test if we can query the tables
    const featuresCount = await client.query('SELECT COUNT(*) FROM features WHERE "isDeleted" = false');
    const plansCount = await client.query('SELECT COUNT(*) FROM account_plans WHERE "isDeleted" = false');
    
    console.log(`Features count: ${featuresCount.rows[0].count}`);
    console.log(`Account plans count: ${plansCount.rows[0].count}`);

    // Test a specific query
    const freePlan = await client.query('SELECT * FROM account_plans WHERE identifier = $1', ['FREE']);
    if (freePlan.rows.length > 0) {
      console.log('Free plan found:', freePlan.rows[0]);
    } else {
      console.log('Free plan not found');
    }

    console.log('Test completed successfully');
  } catch (error) {
    console.error('Error testing user account plans:', error);
  } finally {
    await client.end();
  }
}

testUserAccountPlans(); 