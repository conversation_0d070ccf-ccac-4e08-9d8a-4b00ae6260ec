import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  ParseUUI<PERSON>ipe,
  BadRequestException,
  UseGuards,
} from "@nestjs/common";
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
} from "@nestjs/swagger";
import { ProductCategoriesService } from "./product-categories.service";
import { CreateProductCategoryDto } from "./dto/create-product-category.dto";
import { UpdateProductCategoryDto } from "./dto/update-product-category.dto";
import { ProductCategoryDto } from "./dto/product-category.dto";
import {
  ProductCategoryPaginationDto,
  ProductCategoryListResponseDto,
} from "./dto/pagination.dto";
@ApiTags("Product Categories")
@Controller("product-categories")
export class ProductCategoriesController {
  constructor(
    private readonly productCategoriesService: ProductCategoriesService,
  ) {}

  @Post()
  @ApiOperation({ summary: "Create a new product category" })
  @ApiResponse({
    status: 201,
    description: "Product category created successfully",
    type: ProductCategoryDto,
  })
  @ApiResponse({
    status: 400,
    description: "Bad request - validation failed or missing userUuid",
  })
  @ApiResponse({
    status: 409,
    description: "Conflict - category name already exists",
  })
  create(
    @Body() createProductCategoryDto: CreateProductCategoryDto,
  ): Promise<ProductCategoryDto> {
    if (!createProductCategoryDto.userUuid) {
      throw new BadRequestException(
        "userUuid is required to create a product category",
      );
    }
    return this.productCategoriesService.create(
      createProductCategoryDto,
      createProductCategoryDto.userUuid,
    );
  }

  @Get()
  @ApiOperation({ summary: "Get all product categories with pagination" })
  @ApiQuery({
    name: "warehouseUuid",
    type: String,
    required: false,
    description: "Filter by warehouse UUID",
  })
  @ApiResponse({
    status: 200,
    description: "List of product categories",
    type: ProductCategoryListResponseDto,
  })
  findAll(
    @Query() paginationDto: ProductCategoryPaginationDto,
    @Query("warehouseUuid") warehouseUuid?: string,
  ): Promise<ProductCategoryListResponseDto> {
    return this.productCategoriesService.findAll(paginationDto, warehouseUuid);
  }

  @Get("search")
  @ApiOperation({ summary: "Search product categories by name" })
  @ApiQuery({
    name: "name",
    type: String,
    required: true,
    description: "Category name to search for (case-insensitive)",
  })
  @ApiQuery({
    name: "warehouseUuid",
    type: String,
    required: false,
    description: "Filter by warehouse UUID",
  })
  @ApiResponse({
    status: 200,
    description: "List of matching product categories",
    type: ProductCategoryListResponseDto,
  })
  findByName(
    @Query("name") name: string,
    @Query() paginationDto: ProductCategoryPaginationDto,
    @Query("warehouseUuid") warehouseUuid?: string,
  ): Promise<ProductCategoryListResponseDto> {
    return this.productCategoriesService.findByName(
      name,
      paginationDto,
      warehouseUuid,
    );
  }

  @Get(":uuid")
  @ApiOperation({ summary: "Get a product category by UUID" })
  @ApiParam({
    name: "uuid",
    type: String,
    description: "UUID of the product category",
  })
  @ApiResponse({
    status: 200,
    description: "Product category found",
    type: ProductCategoryDto,
  })
  @ApiResponse({ status: 404, description: "Product category not found" })
  findOne(
    @Param("uuid", ParseUUIDPipe) uuid: string,
  ): Promise<ProductCategoryDto> {
    return this.productCategoriesService.findOne(uuid);
  }

  @Patch(":uuid")
  @ApiOperation({ summary: "Update a product category" })
  @ApiParam({
    name: "uuid",
    type: String,
    description: "UUID of the product category to update",
  })
  @ApiResponse({
    status: 200,
    description: "Product category updated successfully",
    type: ProductCategoryDto,
  })
  @ApiResponse({
    status: 400,
    description: "Bad request - validation failed or missing userUuid",
  })
  @ApiResponse({ status: 404, description: "Product category not found" })
  @ApiResponse({
    status: 409,
    description: "Conflict - category name already exists",
  })
  update(
    @Param("uuid", ParseUUIDPipe) uuid: string,
    @Body() updateProductCategoryDto: UpdateProductCategoryDto,
  ): Promise<ProductCategoryDto> {
    if (!updateProductCategoryDto.userUuid) {
      throw new BadRequestException(
        "userUuid is required to update a product category",
      );
    }
    return this.productCategoriesService.update(
      uuid,
      updateProductCategoryDto,
      updateProductCategoryDto.userUuid,
    );
  }

  @Delete(":uuid")
  @ApiOperation({ summary: "Delete a product category (soft delete)" })
  @ApiParam({
    name: "uuid",
    type: String,
    description: "UUID of the product category to delete",
  })
  @ApiResponse({
    status: 200,
    description: "Product category deleted successfully",
  })
  @ApiResponse({ status: 404, description: "Product category not found" })
  remove(@Param("uuid", ParseUUIDPipe) uuid: string): Promise<void> {
    return this.productCategoriesService.remove(uuid);
  }
}
