// [uuid]/edit.tsx - Edit product page
import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import ProductForm, { ProductFormValues } from '../ProductForm';
import { useAuth } from '@/contexts/AuthContext';
import { getProductById, updateProduct } from '../productsApi';

export default function EditProductPage() {
  const { user } = useAuth();
  const warehouseUuid = user?.warehouseUuid || "";
  const router = useRouter();
  const params = useParams();
  const uuid = params?.uuid as string;
  const [initialValues, setInitialValues] = useState<ProductFormValues | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchProduct() {
      setLoading(true);
      try {
        const prod = await getProductById(uuid);
        setInitialValues({
          ...prod,
          price: prod.price ?? 0,
          cost: prod.cost ?? 0,
          // Convert number price fields to strings for form
          retailPrice: prod.retailPrice?.toString() ?? '',
          wholesalePrice: prod.wholesalePrice?.toString() ?? '',
          midWholesalePrice: prod.midWholesalePrice?.toString() ?? '',
          institutionalPrice: prod.institutionalPrice?.toString() ?? '',
        });
      } catch (e) {
        setError('Failed to load product');
      } finally {
        setLoading(false);
      }
    }
    fetchProduct();
  }, [uuid]);

  const handleSubmit = async (values: ProductFormValues) => {
    setSaving(true);
    setError(null);
    try {
      console.log('[Edit Product] Form values received:', values);
      
      // Determine if we're using simple or custom pricing
      const isUsingSimplePricing = !values.retailPrice || values.retailPrice === '' || values.retailPrice === '0';
      
      let updateData: any = {
        ...values,
        cost: values.cost && values.cost !== '' ? Number(values.cost) : undefined,
      };
      
      if (isUsingSimplePricing) {
        // Simple pricing: send the same price value for all price fields
        console.log('[Edit Product] Using simple pricing');
        const simplePrice = values.price && values.price !== '' ? Number(values.price) : undefined;
        updateData = {
          ...updateData,
          price: simplePrice,
          // Set all price fields to the same value for simple pricing
          retailPrice: simplePrice,
          wholesalePrice: simplePrice,
          midWholesalePrice: simplePrice,
          institutionalPrice: simplePrice,
        };
      } else {
        // Custom pricing: send all individual price fields
        console.log('[Edit Product] Using custom pricing');
        updateData = {
          ...updateData,
          retailPrice: values.retailPrice && values.retailPrice !== '' ? Number(values.retailPrice) : undefined,
          wholesalePrice: values.wholesalePrice && values.wholesalePrice !== '' ? Number(values.wholesalePrice) : undefined,
          midWholesalePrice: values.midWholesalePrice && values.midWholesalePrice !== '' ? Number(values.midWholesalePrice) : undefined,
          institutionalPrice: values.institutionalPrice && values.institutionalPrice !== '' ? Number(values.institutionalPrice) : undefined,
        };
      }
      
      console.log('[Edit Product] Processed update data:', updateData);
      console.log('[Edit Product] Price fields in update data:', {
        price: updateData.price,
        retailPrice: updateData.retailPrice,
        wholesalePrice: updateData.wholesalePrice,
        midWholesalePrice: updateData.midWholesalePrice,
        institutionalPrice: updateData.institutionalPrice,
        cost: updateData.cost
      });
      
      console.log('[Edit Product] About to call updateProduct with UUID:', uuid);
      await updateProduct(uuid, updateData);
      console.log('[Edit Product] Product updated successfully');
      router.push('/inventory/products');
    } catch (err: any) {
      console.error('[Edit Product] Update error:', err);
      setError(err?.response?.data?.message || 'Failed to update product');
    } finally {
      setSaving(false);
    }
  };


  if (loading) return <div className="p-6">Loading...</div>;
  if (error) return <div className="p-6 text-red-600">{error}</div>;
  if (!initialValues) return null;

  return (
    <div className="max-w-xl mx-auto p-6">
      <h1 className="text-2xl font-bold mb-6">Edit Product</h1>
      {error && <div className="mb-4 text-red-600">{error}</div>}
      <ProductForm initialValues={initialValues} onSubmit={handleSubmit} submitLabel="Update" loading={saving} warehouseUuid={warehouseUuid} />
    </div>
  );
}
