/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/auth/page"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Capp%5C%5Cauth%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Capp%5C%5Cauth%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/auth/page.tsx */ \"(app-pages-browser)/./app/auth/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDbWFobGwlNUMlNUNEb2N1bWVudHMlNUMlNUN3b3Jrc3BhY2UlNUMlNUNwcm9qZWN0cyU1QyU1Q2RpZG8tZGlzdHJpYnV0aW9uJTVDJTVDZnJvbnRlbmQlNUMlNUNhcHAlNUMlNUNhdXRoJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPWZhbHNlISIsIm1hcHBpbmdzIjoiQUFBQSxnS0FBdUkiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLz9iMWU5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcbWFobGxcXFxcRG9jdW1lbnRzXFxcXHdvcmtzcGFjZVxcXFxwcm9qZWN0c1xcXFxkaWRvLWRpc3RyaWJ1dGlvblxcXFxmcm9udGVuZFxcXFxhcHBcXFxcYXV0aFxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Capp%5C%5Cauth%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/api/navigation.js":
/*!**************************************************!*\
  !*** ./node_modules/next/dist/api/navigation.js ***!
  \**************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../client/components/navigation */ \"(app-pages-browser)/./node_modules/next/dist/client/components/navigation.js\");\n/* harmony import */ var _client_components_navigation__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_client_components_navigation__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = function(key) { return _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__[key]; }.bind(0, __WEBPACK_IMPORT_KEY__)\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n//# sourceMappingURL=navigation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYXBpL25hdmlnYXRpb24uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQWdEOztBQUVoRCIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2FwaS9uYXZpZ2F0aW9uLmpzPzQyOGYiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSBcIi4uL2NsaWVudC9jb21wb25lbnRzL25hdmlnYXRpb25cIjtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9bmF2aWdhdGlvbi5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/api/navigation.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js":
/*!***********************************************************!*\
  !*** ./node_modules/next/dist/build/polyfills/process.js ***!
  \***********************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nvar _global_process, _global_process1;\nmodule.exports = ((_global_process = __webpack_require__.g.process) == null ? void 0 : _global_process.env) && typeof ((_global_process1 = __webpack_require__.g.process) == null ? void 0 : _global_process1.env) === \"object\" ? __webpack_require__.g.process : __webpack_require__(/*! next/dist/compiled/process */ \"(app-pages-browser)/./node_modules/next/dist/compiled/process/browser.js\");\n\n//# sourceMappingURL=process.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvcG9seWZpbGxzL3Byb2Nlc3MuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYjtBQUNBLHFDQUFxQyxxQkFBTSxpRkFBaUYscUJBQU0sa0VBQWtFLHFCQUFNLFdBQVcsbUJBQU8sQ0FBQyw0R0FBNEI7O0FBRXpQIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvcG9seWZpbGxzL3Byb2Nlc3MuanM/NTNlZSJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbnZhciBfZ2xvYmFsX3Byb2Nlc3MsIF9nbG9iYWxfcHJvY2VzczE7XG5tb2R1bGUuZXhwb3J0cyA9ICgoX2dsb2JhbF9wcm9jZXNzID0gZ2xvYmFsLnByb2Nlc3MpID09IG51bGwgPyB2b2lkIDAgOiBfZ2xvYmFsX3Byb2Nlc3MuZW52KSAmJiB0eXBlb2YgKChfZ2xvYmFsX3Byb2Nlc3MxID0gZ2xvYmFsLnByb2Nlc3MpID09IG51bGwgPyB2b2lkIDAgOiBfZ2xvYmFsX3Byb2Nlc3MxLmVudikgPT09IFwib2JqZWN0XCIgPyBnbG9iYWwucHJvY2VzcyA6IHJlcXVpcmUoXCJuZXh0L2Rpc3QvY29tcGlsZWQvcHJvY2Vzc1wiKTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9cHJvY2Vzcy5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/process/browser.js":
/*!************************************************************!*\
  !*** ./node_modules/next/dist/compiled/process/browser.js ***!
  \************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("var __dirname = \"/\";\n(function(){var e={229:function(e){var t=e.exports={};var r;var n;function defaultSetTimout(){throw new Error(\"setTimeout has not been defined\")}function defaultClearTimeout(){throw new Error(\"clearTimeout has not been defined\")}(function(){try{if(typeof setTimeout===\"function\"){r=setTimeout}else{r=defaultSetTimout}}catch(e){r=defaultSetTimout}try{if(typeof clearTimeout===\"function\"){n=clearTimeout}else{n=defaultClearTimeout}}catch(e){n=defaultClearTimeout}})();function runTimeout(e){if(r===setTimeout){return setTimeout(e,0)}if((r===defaultSetTimout||!r)&&setTimeout){r=setTimeout;return setTimeout(e,0)}try{return r(e,0)}catch(t){try{return r.call(null,e,0)}catch(t){return r.call(this,e,0)}}}function runClearTimeout(e){if(n===clearTimeout){return clearTimeout(e)}if((n===defaultClearTimeout||!n)&&clearTimeout){n=clearTimeout;return clearTimeout(e)}try{return n(e)}catch(t){try{return n.call(null,e)}catch(t){return n.call(this,e)}}}var i=[];var o=false;var u;var a=-1;function cleanUpNextTick(){if(!o||!u){return}o=false;if(u.length){i=u.concat(i)}else{a=-1}if(i.length){drainQueue()}}function drainQueue(){if(o){return}var e=runTimeout(cleanUpNextTick);o=true;var t=i.length;while(t){u=i;i=[];while(++a<t){if(u){u[a].run()}}a=-1;t=i.length}u=null;o=false;runClearTimeout(e)}t.nextTick=function(e){var t=new Array(arguments.length-1);if(arguments.length>1){for(var r=1;r<arguments.length;r++){t[r-1]=arguments[r]}}i.push(new Item(e,t));if(i.length===1&&!o){runTimeout(drainQueue)}};function Item(e,t){this.fun=e;this.array=t}Item.prototype.run=function(){this.fun.apply(null,this.array)};t.title=\"browser\";t.browser=true;t.env={};t.argv=[];t.version=\"\";t.versions={};function noop(){}t.on=noop;t.addListener=noop;t.once=noop;t.off=noop;t.removeListener=noop;t.removeAllListeners=noop;t.emit=noop;t.prependListener=noop;t.prependOnceListener=noop;t.listeners=function(e){return[]};t.binding=function(e){throw new Error(\"process.binding is not supported\")};t.cwd=function(){return\"/\"};t.chdir=function(e){throw new Error(\"process.chdir is not supported\")};t.umask=function(){return 0}}};var t={};function __nccwpck_require__(r){var n=t[r];if(n!==undefined){return n.exports}var i=t[r]={exports:{}};var o=true;try{e[r](i,i.exports,__nccwpck_require__);o=false}finally{if(o)delete t[r]}return i.exports}if(typeof __nccwpck_require__!==\"undefined\")__nccwpck_require__.ab=__dirname+\"/\";var r=__nccwpck_require__(229);module.exports=r})();//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/process/browser.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/auth/authApi.ts":
/*!*****************************!*\
  !*** ./app/auth/authApi.ts ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   enhancedLogin: function() { return /* binding */ enhancedLogin; },\n/* harmony export */   getSecurityStatus: function() { return /* binding */ getSecurityStatus; },\n/* harmony export */   googleTokenAuth: function() { return /* binding */ googleTokenAuth; },\n/* harmony export */   legacyLogin: function() { return /* binding */ legacyLogin; },\n/* harmony export */   logout: function() { return /* binding */ logout; },\n/* harmony export */   refreshToken: function() { return /* binding */ refreshToken; },\n/* harmony export */   validateToken: function() { return /* binding */ validateToken; }\n/* harmony export */ });\n// Auth API service for centralized authentication calls\n// Uses the new enhanced backend endpoints with security features\n// Enhanced login with security features\nasync function enhancedLogin(request) {\n    const response = await fetch(\"/api/auth/login\", {\n        method: \"POST\",\n        headers: {\n            \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({\n            ...request,\n            clientIp: request.clientIp || \"frontend\",\n            userAgent: request.userAgent || navigator.userAgent\n        })\n    });\n    if (!response.ok) {\n        const errorData = await response.json().catch(()=>({}));\n        throw new Error(errorData.message || \"Login failed: \".concat(response.status));\n    }\n    return response.json();\n}\n// Legacy login for backward compatibility\nasync function legacyLogin(request) {\n    const response = await fetch(\"/api/auth/login/legacy\", {\n        method: \"POST\",\n        headers: {\n            \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify(request)\n    });\n    if (!response.ok) {\n        const errorData = await response.json().catch(()=>({}));\n        throw new Error(errorData.message || \"Login failed: \".concat(response.status));\n    }\n    return response.json();\n}\n// Refresh access token\nasync function refreshToken(request) {\n    const response = await fetch(\"/api/auth/refresh\", {\n        method: \"POST\",\n        headers: {\n            \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({\n            ...request,\n            clientIp: request.clientIp || \"frontend\",\n            userAgent: request.userAgent || navigator.userAgent\n        })\n    });\n    if (!response.ok) {\n        const errorData = await response.json().catch(()=>({}));\n        throw new Error(errorData.message || \"Token refresh failed: \".concat(response.status));\n    }\n    return response.json();\n}\n// Logout with token revocation\nasync function logout(request, accessToken) {\n    const response = await fetch(\"/api/auth/logout\", {\n        method: \"POST\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n            \"Authorization\": \"Bearer \".concat(accessToken)\n        },\n        body: JSON.stringify(request)\n    });\n    if (!response.ok) {\n        const errorData = await response.json().catch(()=>({}));\n        throw new Error(errorData.message || \"Logout failed: \".concat(response.status));\n    }\n    return response.json();\n}\n// Get security status\nasync function getSecurityStatus(accessToken) {\n    const response = await fetch(\"/api/auth/security-status\", {\n        headers: {\n            \"Authorization\": \"Bearer \".concat(accessToken)\n        }\n    });\n    if (!response.ok) {\n        const errorData = await response.json().catch(()=>({}));\n        throw new Error(errorData.message || \"Failed to get security status: \".concat(response.status));\n    }\n    return response.json();\n}\n// Validate token (for session checking)\nasync function validateToken(accessToken) {\n    try {\n        const response = await fetch(\"/api/auth/validate\", {\n            headers: {\n                \"Authorization\": \"Bearer \".concat(accessToken)\n            }\n        });\n        return response.ok;\n    } catch (e) {\n        return false;\n    }\n}\n// Google OAuth token authentication\nasync function googleTokenAuth(googleData) {\n    const response = await fetch(\"/api/auth/google/token\", {\n        method: \"POST\",\n        headers: {\n            \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify(googleData)\n    });\n    if (!response.ok) {\n        const errorData = await response.json().catch(()=>({}));\n        throw new Error(errorData.message || \"Google authentication failed: \".concat(response.status));\n    }\n    return response.json();\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9hdXRoL2F1dGhBcGkudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUFBLHdEQUF3RDtBQUN4RCxpRUFBaUU7QUE2RmpFLHdDQUF3QztBQUNqQyxlQUFlQSxjQUFjQyxPQUE2QjtJQUMvRCxNQUFNQyxXQUFXLE1BQU1DLE1BQU0sbUJBQW1CO1FBQzlDQyxRQUFRO1FBQ1JDLFNBQVM7WUFDUCxnQkFBZ0I7UUFDbEI7UUFDQUMsTUFBTUMsS0FBS0MsU0FBUyxDQUFDO1lBQ25CLEdBQUdQLE9BQU87WUFDVlEsVUFBVVIsUUFBUVEsUUFBUSxJQUFJO1lBQzlCQyxXQUFXVCxRQUFRUyxTQUFTLElBQUlDLFVBQVVELFNBQVM7UUFDckQ7SUFDRjtJQUVBLElBQUksQ0FBQ1IsU0FBU1UsRUFBRSxFQUFFO1FBQ2hCLE1BQU1DLFlBQVksTUFBTVgsU0FBU1ksSUFBSSxHQUFHQyxLQUFLLENBQUMsSUFBTyxFQUFDO1FBQ3RELE1BQU0sSUFBSUMsTUFBTUgsVUFBVUksT0FBTyxJQUFJLGlCQUFpQyxPQUFoQmYsU0FBU2dCLE1BQU07SUFDdkU7SUFFQSxPQUFPaEIsU0FBU1ksSUFBSTtBQUN0QjtBQUVBLDBDQUEwQztBQUNuQyxlQUFlSyxZQUFZbEIsT0FBMkI7SUFDM0QsTUFBTUMsV0FBVyxNQUFNQyxNQUFNLDBCQUEwQjtRQUNyREMsUUFBUTtRQUNSQyxTQUFTO1lBQ1AsZ0JBQWdCO1FBQ2xCO1FBQ0FDLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQ1A7SUFDdkI7SUFFQSxJQUFJLENBQUNDLFNBQVNVLEVBQUUsRUFBRTtRQUNoQixNQUFNQyxZQUFZLE1BQU1YLFNBQVNZLElBQUksR0FBR0MsS0FBSyxDQUFDLElBQU8sRUFBQztRQUN0RCxNQUFNLElBQUlDLE1BQU1ILFVBQVVJLE9BQU8sSUFBSSxpQkFBaUMsT0FBaEJmLFNBQVNnQixNQUFNO0lBQ3ZFO0lBRUEsT0FBT2hCLFNBQVNZLElBQUk7QUFDdEI7QUFFQSx1QkFBdUI7QUFDaEIsZUFBZU0sYUFBYW5CLE9BQTRCO0lBQzdELE1BQU1DLFdBQVcsTUFBTUMsTUFBTSxxQkFBcUI7UUFDaERDLFFBQVE7UUFDUkMsU0FBUztZQUNQLGdCQUFnQjtRQUNsQjtRQUNBQyxNQUFNQyxLQUFLQyxTQUFTLENBQUM7WUFDbkIsR0FBR1AsT0FBTztZQUNWUSxVQUFVUixRQUFRUSxRQUFRLElBQUk7WUFDOUJDLFdBQVdULFFBQVFTLFNBQVMsSUFBSUMsVUFBVUQsU0FBUztRQUNyRDtJQUNGO0lBRUEsSUFBSSxDQUFDUixTQUFTVSxFQUFFLEVBQUU7UUFDaEIsTUFBTUMsWUFBWSxNQUFNWCxTQUFTWSxJQUFJLEdBQUdDLEtBQUssQ0FBQyxJQUFPLEVBQUM7UUFDdEQsTUFBTSxJQUFJQyxNQUFNSCxVQUFVSSxPQUFPLElBQUkseUJBQXlDLE9BQWhCZixTQUFTZ0IsTUFBTTtJQUMvRTtJQUVBLE9BQU9oQixTQUFTWSxJQUFJO0FBQ3RCO0FBRUEsK0JBQStCO0FBQ3hCLGVBQWVPLE9BQU9wQixPQUFzQixFQUFFcUIsV0FBbUI7SUFDdEUsTUFBTXBCLFdBQVcsTUFBTUMsTUFBTSxvQkFBb0I7UUFDL0NDLFFBQVE7UUFDUkMsU0FBUztZQUNQLGdCQUFnQjtZQUNoQixpQkFBaUIsVUFBc0IsT0FBWmlCO1FBQzdCO1FBQ0FoQixNQUFNQyxLQUFLQyxTQUFTLENBQUNQO0lBQ3ZCO0lBRUEsSUFBSSxDQUFDQyxTQUFTVSxFQUFFLEVBQUU7UUFDaEIsTUFBTUMsWUFBWSxNQUFNWCxTQUFTWSxJQUFJLEdBQUdDLEtBQUssQ0FBQyxJQUFPLEVBQUM7UUFDdEQsTUFBTSxJQUFJQyxNQUFNSCxVQUFVSSxPQUFPLElBQUksa0JBQWtDLE9BQWhCZixTQUFTZ0IsTUFBTTtJQUN4RTtJQUVBLE9BQU9oQixTQUFTWSxJQUFJO0FBQ3RCO0FBRUEsc0JBQXNCO0FBQ2YsZUFBZVMsa0JBQWtCRCxXQUFtQjtJQUN6RCxNQUFNcEIsV0FBVyxNQUFNQyxNQUFNLDZCQUE2QjtRQUN4REUsU0FBUztZQUNQLGlCQUFpQixVQUFzQixPQUFaaUI7UUFDN0I7SUFDRjtJQUVBLElBQUksQ0FBQ3BCLFNBQVNVLEVBQUUsRUFBRTtRQUNoQixNQUFNQyxZQUFZLE1BQU1YLFNBQVNZLElBQUksR0FBR0MsS0FBSyxDQUFDLElBQU8sRUFBQztRQUN0RCxNQUFNLElBQUlDLE1BQU1ILFVBQVVJLE9BQU8sSUFBSSxrQ0FBa0QsT0FBaEJmLFNBQVNnQixNQUFNO0lBQ3hGO0lBRUEsT0FBT2hCLFNBQVNZLElBQUk7QUFDdEI7QUFFQSx3Q0FBd0M7QUFDakMsZUFBZVUsY0FBY0YsV0FBbUI7SUFDckQsSUFBSTtRQUNGLE1BQU1wQixXQUFXLE1BQU1DLE1BQU0sc0JBQXNCO1lBQ2pERSxTQUFTO2dCQUNQLGlCQUFpQixVQUFzQixPQUFaaUI7WUFDN0I7UUFDRjtRQUNBLE9BQU9wQixTQUFTVSxFQUFFO0lBQ3BCLEVBQUUsVUFBTTtRQUNOLE9BQU87SUFDVDtBQUNGO0FBRUEsb0NBQW9DO0FBQzdCLGVBQWVhLGdCQUFnQkMsVUFNckM7SUFhQyxNQUFNeEIsV0FBVyxNQUFNQyxNQUFNLDBCQUEwQjtRQUNyREMsUUFBUTtRQUNSQyxTQUFTO1lBQ1AsZ0JBQWdCO1FBQ2xCO1FBQ0FDLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQ2tCO0lBQ3ZCO0lBRUEsSUFBSSxDQUFDeEIsU0FBU1UsRUFBRSxFQUFFO1FBQ2hCLE1BQU1DLFlBQVksTUFBTVgsU0FBU1ksSUFBSSxHQUFHQyxLQUFLLENBQUMsSUFBTyxFQUFDO1FBQ3RELE1BQU0sSUFBSUMsTUFBTUgsVUFBVUksT0FBTyxJQUFJLGlDQUFpRCxPQUFoQmYsU0FBU2dCLE1BQU07SUFDdkY7SUFFQSxPQUFPaEIsU0FBU1ksSUFBSTtBQUN0QiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9hcHAvYXV0aC9hdXRoQXBpLnRzPzFjMTkiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gQXV0aCBBUEkgc2VydmljZSBmb3IgY2VudHJhbGl6ZWQgYXV0aGVudGljYXRpb24gY2FsbHNcclxuLy8gVXNlcyB0aGUgbmV3IGVuaGFuY2VkIGJhY2tlbmQgZW5kcG9pbnRzIHdpdGggc2VjdXJpdHkgZmVhdHVyZXNcclxuXHJcbmV4cG9ydCBpbnRlcmZhY2UgRW5oYW5jZWRMb2dpblJlcXVlc3Qge1xyXG4gIGlkZW50aWZpZXI6IHN0cmluZztcclxuICBwYXNzd29yZD86IHN0cmluZztcclxuICBjbGllbnRJcD86IHN0cmluZztcclxuICB1c2VyQWdlbnQ/OiBzdHJpbmc7XHJcbn1cclxuXHJcbmV4cG9ydCBpbnRlcmZhY2UgRW5oYW5jZWRMb2dpblJlc3BvbnNlIHtcclxuICBhY2Nlc3NUb2tlbjogc3RyaW5nO1xyXG4gIHJlZnJlc2hUb2tlbjogc3RyaW5nO1xyXG4gIGFjY2Vzc1Rva2VuRXhwaXJlc0luOiBudW1iZXI7XHJcbiAgcmVmcmVzaFRva2VuRXhwaXJlc0luOiBudW1iZXI7XHJcbiAgdXNlcjoge1xyXG4gICAgdXVpZDogc3RyaW5nO1xyXG4gICAgZW1haWw6IHN0cmluZztcclxuICAgIGZpcnN0TmFtZTogc3RyaW5nIHwgbnVsbDtcclxuICAgIGxhc3ROYW1lOiBzdHJpbmcgfCBudWxsO1xyXG4gICAgcGhvbmU6IHN0cmluZyB8IG51bGw7XHJcbiAgICBpc0FjdGl2ZTogYm9vbGVhbjtcclxuICAgIHJvbGVzOiBBcnJheTx7XHJcbiAgICAgIHV1aWQ6IHN0cmluZztcclxuICAgICAgbmFtZTogc3RyaW5nO1xyXG4gICAgICBwZXJtaXNzaW9uczogc3RyaW5nW107XHJcbiAgICB9PjtcclxuICAgIHdhcmVob3VzZVV1aWQ6IHN0cmluZyB8IG51bGw7XHJcbiAgICB2YW5VdWlkOiBzdHJpbmcgfCBudWxsO1xyXG4gICAgY3JlYXRlZEF0OiBzdHJpbmc7XHJcbiAgICB1cGRhdGVkQXQ6IHN0cmluZztcclxuICB9O1xyXG4gIHNlc3Npb25TdGF0czoge1xyXG4gICAgYWN0aXZlVG9rZW5zOiBudW1iZXI7XHJcbiAgICB0b3RhbFRva2VuczogbnVtYmVyO1xyXG4gICAgbGFzdFVzZWQ6IHN0cmluZyB8IG51bGw7XHJcbiAgfTtcclxufVxyXG5cclxuZXhwb3J0IGludGVyZmFjZSBSZWZyZXNoVG9rZW5SZXF1ZXN0IHtcclxuICByZWZyZXNoVG9rZW46IHN0cmluZztcclxuICBjbGllbnRJcD86IHN0cmluZztcclxuICB1c2VyQWdlbnQ/OiBzdHJpbmc7XHJcbn1cclxuXHJcbmV4cG9ydCBpbnRlcmZhY2UgUmVmcmVzaFRva2VuUmVzcG9uc2Uge1xyXG4gIGFjY2Vzc1Rva2VuOiBzdHJpbmc7XHJcbiAgcmVmcmVzaFRva2VuOiBzdHJpbmc7XHJcbiAgYWNjZXNzVG9rZW5FeHBpcmVzSW46IG51bWJlcjtcclxuICByZWZyZXNoVG9rZW5FeHBpcmVzSW46IG51bWJlcjtcclxufVxyXG5cclxuZXhwb3J0IGludGVyZmFjZSBMb2dvdXRSZXF1ZXN0IHtcclxuICByZWZyZXNoVG9rZW4/OiBzdHJpbmc7XHJcbiAgcmV2b2tlQWxsPzogYm9vbGVhbjtcclxufVxyXG5cclxuZXhwb3J0IGludGVyZmFjZSBTZWN1cml0eVN0YXR1c1Jlc3BvbnNlIHtcclxuICBpc0xvY2tlZDogYm9vbGVhbjtcclxuICBsb2NrZWRVbnRpbDogc3RyaW5nIHwgbnVsbDtcclxuICByZW1haW5pbmdBdHRlbXB0czogbnVtYmVyO1xyXG4gIHN1c3BpY2lvdXNBY3Rpdml0eURldGVjdGVkOiBib29sZWFuO1xyXG4gIHJlY2VudEV2ZW50czogQXJyYXk8e1xyXG4gICAgZXZlbnRUeXBlOiBzdHJpbmc7XHJcbiAgICB0aW1lc3RhbXA6IHN0cmluZztcclxuICAgIGlwQWRkcmVzcz86IHN0cmluZztcclxuICAgIHN1Y2Nlc3M6IGJvb2xlYW47XHJcbiAgfT47XHJcbn1cclxuXHJcbmV4cG9ydCBpbnRlcmZhY2UgTGVnYWN5TG9naW5SZXF1ZXN0IHtcclxuICBpZGVudGlmaWVyOiBzdHJpbmc7XHJcbiAgcGFzc3dvcmQ/OiBzdHJpbmc7XHJcbn1cclxuXHJcbmV4cG9ydCBpbnRlcmZhY2UgTGVnYWN5TG9naW5SZXNwb25zZSB7XHJcbiAgYWNjZXNzVG9rZW46IHN0cmluZztcclxuICByZWZyZXNoVG9rZW46IHN0cmluZyB8IG51bGw7XHJcbiAgdXNlcjoge1xyXG4gICAgdXVpZDogc3RyaW5nO1xyXG4gICAgZW1haWw6IHN0cmluZztcclxuICAgIGZpcnN0TmFtZTogc3RyaW5nIHwgbnVsbDtcclxuICAgIGxhc3ROYW1lOiBzdHJpbmcgfCBudWxsO1xyXG4gICAgcGhvbmU6IHN0cmluZyB8IG51bGw7XHJcbiAgICBpc0FjdGl2ZTogYm9vbGVhbjtcclxuICAgIHJvbGVzOiBhbnlbXTtcclxuICAgIHdhcmVob3VzZVV1aWQ6IHN0cmluZyB8IG51bGw7XHJcbiAgICB2YW5VdWlkOiBzdHJpbmcgfCBudWxsO1xyXG4gICAgY3JlYXRlZEF0OiBzdHJpbmc7XHJcbiAgICB1cGRhdGVkQXQ6IHN0cmluZztcclxuICB9O1xyXG4gIGV4cGlyZXNJbjogbnVtYmVyO1xyXG59XHJcblxyXG4vLyBFbmhhbmNlZCBsb2dpbiB3aXRoIHNlY3VyaXR5IGZlYXR1cmVzXHJcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBlbmhhbmNlZExvZ2luKHJlcXVlc3Q6IEVuaGFuY2VkTG9naW5SZXF1ZXN0KTogUHJvbWlzZTxFbmhhbmNlZExvZ2luUmVzcG9uc2U+IHtcclxuICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKCcvYXBpL2F1dGgvbG9naW4nLCB7XHJcbiAgICBtZXRob2Q6ICdQT1NUJyxcclxuICAgIGhlYWRlcnM6IHtcclxuICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcclxuICAgIH0sXHJcbiAgICBib2R5OiBKU09OLnN0cmluZ2lmeSh7XHJcbiAgICAgIC4uLnJlcXVlc3QsXHJcbiAgICAgIGNsaWVudElwOiByZXF1ZXN0LmNsaWVudElwIHx8ICdmcm9udGVuZCcsXHJcbiAgICAgIHVzZXJBZ2VudDogcmVxdWVzdC51c2VyQWdlbnQgfHwgbmF2aWdhdG9yLnVzZXJBZ2VudCxcclxuICAgIH0pLFxyXG4gIH0pO1xyXG5cclxuICBpZiAoIXJlc3BvbnNlLm9rKSB7XHJcbiAgICBjb25zdCBlcnJvckRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCkuY2F0Y2goKCkgPT4gKHt9KSk7XHJcbiAgICB0aHJvdyBuZXcgRXJyb3IoZXJyb3JEYXRhLm1lc3NhZ2UgfHwgYExvZ2luIGZhaWxlZDogJHtyZXNwb25zZS5zdGF0dXN9YCk7XHJcbiAgfVxyXG5cclxuICByZXR1cm4gcmVzcG9uc2UuanNvbigpO1xyXG59XHJcblxyXG4vLyBMZWdhY3kgbG9naW4gZm9yIGJhY2t3YXJkIGNvbXBhdGliaWxpdHlcclxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGxlZ2FjeUxvZ2luKHJlcXVlc3Q6IExlZ2FjeUxvZ2luUmVxdWVzdCk6IFByb21pc2U8TGVnYWN5TG9naW5SZXNwb25zZT4ge1xyXG4gIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goJy9hcGkvYXV0aC9sb2dpbi9sZWdhY3knLCB7XHJcbiAgICBtZXRob2Q6ICdQT1NUJyxcclxuICAgIGhlYWRlcnM6IHtcclxuICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcclxuICAgIH0sXHJcbiAgICBib2R5OiBKU09OLnN0cmluZ2lmeShyZXF1ZXN0KSxcclxuICB9KTtcclxuXHJcbiAgaWYgKCFyZXNwb25zZS5vaykge1xyXG4gICAgY29uc3QgZXJyb3JEYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpLmNhdGNoKCgpID0+ICh7fSkpO1xyXG4gICAgdGhyb3cgbmV3IEVycm9yKGVycm9yRGF0YS5tZXNzYWdlIHx8IGBMb2dpbiBmYWlsZWQ6ICR7cmVzcG9uc2Uuc3RhdHVzfWApO1xyXG4gIH1cclxuXHJcbiAgcmV0dXJuIHJlc3BvbnNlLmpzb24oKTtcclxufVxyXG5cclxuLy8gUmVmcmVzaCBhY2Nlc3MgdG9rZW5cclxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIHJlZnJlc2hUb2tlbihyZXF1ZXN0OiBSZWZyZXNoVG9rZW5SZXF1ZXN0KTogUHJvbWlzZTxSZWZyZXNoVG9rZW5SZXNwb25zZT4ge1xyXG4gIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goJy9hcGkvYXV0aC9yZWZyZXNoJywge1xyXG4gICAgbWV0aG9kOiAnUE9TVCcsXHJcbiAgICBoZWFkZXJzOiB7XHJcbiAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXHJcbiAgICB9LFxyXG4gICAgYm9keTogSlNPTi5zdHJpbmdpZnkoe1xyXG4gICAgICAuLi5yZXF1ZXN0LFxyXG4gICAgICBjbGllbnRJcDogcmVxdWVzdC5jbGllbnRJcCB8fCAnZnJvbnRlbmQnLFxyXG4gICAgICB1c2VyQWdlbnQ6IHJlcXVlc3QudXNlckFnZW50IHx8IG5hdmlnYXRvci51c2VyQWdlbnQsXHJcbiAgICB9KSxcclxuICB9KTtcclxuXHJcbiAgaWYgKCFyZXNwb25zZS5vaykge1xyXG4gICAgY29uc3QgZXJyb3JEYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpLmNhdGNoKCgpID0+ICh7fSkpO1xyXG4gICAgdGhyb3cgbmV3IEVycm9yKGVycm9yRGF0YS5tZXNzYWdlIHx8IGBUb2tlbiByZWZyZXNoIGZhaWxlZDogJHtyZXNwb25zZS5zdGF0dXN9YCk7XHJcbiAgfVxyXG5cclxuICByZXR1cm4gcmVzcG9uc2UuanNvbigpO1xyXG59XHJcblxyXG4vLyBMb2dvdXQgd2l0aCB0b2tlbiByZXZvY2F0aW9uXHJcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBsb2dvdXQocmVxdWVzdDogTG9nb3V0UmVxdWVzdCwgYWNjZXNzVG9rZW46IHN0cmluZyk6IFByb21pc2U8eyBtZXNzYWdlOiBzdHJpbmcgfT4ge1xyXG4gIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goJy9hcGkvYXV0aC9sb2dvdXQnLCB7XHJcbiAgICBtZXRob2Q6ICdQT1NUJyxcclxuICAgIGhlYWRlcnM6IHtcclxuICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcclxuICAgICAgJ0F1dGhvcml6YXRpb24nOiBgQmVhcmVyICR7YWNjZXNzVG9rZW59YCxcclxuICAgIH0sXHJcbiAgICBib2R5OiBKU09OLnN0cmluZ2lmeShyZXF1ZXN0KSxcclxuICB9KTtcclxuXHJcbiAgaWYgKCFyZXNwb25zZS5vaykge1xyXG4gICAgY29uc3QgZXJyb3JEYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpLmNhdGNoKCgpID0+ICh7fSkpO1xyXG4gICAgdGhyb3cgbmV3IEVycm9yKGVycm9yRGF0YS5tZXNzYWdlIHx8IGBMb2dvdXQgZmFpbGVkOiAke3Jlc3BvbnNlLnN0YXR1c31gKTtcclxuICB9XHJcblxyXG4gIHJldHVybiByZXNwb25zZS5qc29uKCk7XHJcbn1cclxuXHJcbi8vIEdldCBzZWN1cml0eSBzdGF0dXNcclxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGdldFNlY3VyaXR5U3RhdHVzKGFjY2Vzc1Rva2VuOiBzdHJpbmcpOiBQcm9taXNlPFNlY3VyaXR5U3RhdHVzUmVzcG9uc2U+IHtcclxuICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKCcvYXBpL2F1dGgvc2VjdXJpdHktc3RhdHVzJywge1xyXG4gICAgaGVhZGVyczoge1xyXG4gICAgICAnQXV0aG9yaXphdGlvbic6IGBCZWFyZXIgJHthY2Nlc3NUb2tlbn1gLFxyXG4gICAgfSxcclxuICB9KTtcclxuXHJcbiAgaWYgKCFyZXNwb25zZS5vaykge1xyXG4gICAgY29uc3QgZXJyb3JEYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpLmNhdGNoKCgpID0+ICh7fSkpO1xyXG4gICAgdGhyb3cgbmV3IEVycm9yKGVycm9yRGF0YS5tZXNzYWdlIHx8IGBGYWlsZWQgdG8gZ2V0IHNlY3VyaXR5IHN0YXR1czogJHtyZXNwb25zZS5zdGF0dXN9YCk7XHJcbiAgfVxyXG5cclxuICByZXR1cm4gcmVzcG9uc2UuanNvbigpO1xyXG59XHJcblxyXG4vLyBWYWxpZGF0ZSB0b2tlbiAoZm9yIHNlc3Npb24gY2hlY2tpbmcpXHJcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiB2YWxpZGF0ZVRva2VuKGFjY2Vzc1Rva2VuOiBzdHJpbmcpOiBQcm9taXNlPGJvb2xlYW4+IHtcclxuICB0cnkge1xyXG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaCgnL2FwaS9hdXRoL3ZhbGlkYXRlJywge1xyXG4gICAgICBoZWFkZXJzOiB7XHJcbiAgICAgICAgJ0F1dGhvcml6YXRpb24nOiBgQmVhcmVyICR7YWNjZXNzVG9rZW59YCxcclxuICAgICAgfSxcclxuICAgIH0pO1xyXG4gICAgcmV0dXJuIHJlc3BvbnNlLm9rO1xyXG4gIH0gY2F0Y2gge1xyXG4gICAgcmV0dXJuIGZhbHNlO1xyXG4gIH1cclxufVxyXG5cclxuLy8gR29vZ2xlIE9BdXRoIHRva2VuIGF1dGhlbnRpY2F0aW9uXHJcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBnb29nbGVUb2tlbkF1dGgoZ29vZ2xlRGF0YToge1xyXG4gIGFjY2Vzc1Rva2VuOiBzdHJpbmc7XHJcbiAgaWRUb2tlbjogc3RyaW5nO1xyXG4gIGVtYWlsOiBzdHJpbmc7XHJcbiAgbmFtZTogc3RyaW5nO1xyXG4gIHBob3RvVXJsPzogc3RyaW5nO1xyXG59KTogUHJvbWlzZTx7XHJcbiAgYWNjZXNzX3Rva2VuOiBzdHJpbmc7XHJcbiAgcmVmcmVzaF90b2tlbjogc3RyaW5nIHwgbnVsbDtcclxuICB1c2VyOiB7XHJcbiAgICB1dWlkOiBzdHJpbmc7XHJcbiAgICBlbWFpbDogc3RyaW5nO1xyXG4gICAgbmFtZTogc3RyaW5nO1xyXG4gICAgcm9sZVV1aWRTdHJpbmc6IHN0cmluZztcclxuICAgIHVzZXJUeXBlOiBzdHJpbmc7XHJcbiAgICB3YXJlaG91c2VVdWlkU3RyaW5nOiBzdHJpbmcgfCBudWxsO1xyXG4gIH07XHJcbiAgZXhwaXJlc0luOiBudW1iZXI7XHJcbn0+IHtcclxuICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKCcvYXBpL2F1dGgvZ29vZ2xlL3Rva2VuJywge1xyXG4gICAgbWV0aG9kOiAnUE9TVCcsXHJcbiAgICBoZWFkZXJzOiB7XHJcbiAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXHJcbiAgICB9LFxyXG4gICAgYm9keTogSlNPTi5zdHJpbmdpZnkoZ29vZ2xlRGF0YSksXHJcbiAgfSk7XHJcblxyXG4gIGlmICghcmVzcG9uc2Uub2spIHtcclxuICAgIGNvbnN0IGVycm9yRGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKS5jYXRjaCgoKSA9PiAoe30pKTtcclxuICAgIHRocm93IG5ldyBFcnJvcihlcnJvckRhdGEubWVzc2FnZSB8fCBgR29vZ2xlIGF1dGhlbnRpY2F0aW9uIGZhaWxlZDogJHtyZXNwb25zZS5zdGF0dXN9YCk7XHJcbiAgfVxyXG5cclxuICByZXR1cm4gcmVzcG9uc2UuanNvbigpO1xyXG59ICJdLCJuYW1lcyI6WyJlbmhhbmNlZExvZ2luIiwicmVxdWVzdCIsInJlc3BvbnNlIiwiZmV0Y2giLCJtZXRob2QiLCJoZWFkZXJzIiwiYm9keSIsIkpTT04iLCJzdHJpbmdpZnkiLCJjbGllbnRJcCIsInVzZXJBZ2VudCIsIm5hdmlnYXRvciIsIm9rIiwiZXJyb3JEYXRhIiwianNvbiIsImNhdGNoIiwiRXJyb3IiLCJtZXNzYWdlIiwic3RhdHVzIiwibGVnYWN5TG9naW4iLCJyZWZyZXNoVG9rZW4iLCJsb2dvdXQiLCJhY2Nlc3NUb2tlbiIsImdldFNlY3VyaXR5U3RhdHVzIiwidmFsaWRhdGVUb2tlbiIsImdvb2dsZVRva2VuQXV0aCIsImdvb2dsZURhdGEiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/auth/authApi.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/auth/page.tsx":
/*!***************************!*\
  !*** ./app/auth/page.tsx ***!
  \***************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AuthPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./contexts/AuthContext.tsx\");\n/* harmony import */ var _authApi__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./authApi */ \"(app-pages-browser)/./app/auth/authApi.ts\");\n/* harmony import */ var _components_BackendStatusChecker__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/BackendStatusChecker */ \"(app-pages-browser)/./components/BackendStatusChecker.tsx\");\n/* harmony import */ var _components_NetworkStatusChecker__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/NetworkStatusChecker */ \"(app-pages-browser)/./components/NetworkStatusChecker.tsx\");\n/* harmony import */ var _utils_networkUtils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/utils/networkUtils */ \"(app-pages-browser)/./utils/networkUtils.ts\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst BACKEND_URL = process.env.NEXT_PUBLIC_API_BASE_URL || process.env.NEXT_PUBLIC_BACKEND_URL || \"http://localhost:8000\";\n// NOTE: The backend must redirect to /auth/callback with token and user as query params after Google login.\n// Example: res.redirect(`https://your-frontend-domain.com/auth/callback?token=JWT_TOKEN&user=${encodeURIComponent(JSON.stringify(user))}`);\nfunction AuthPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { user, loading, logout, checkUserExists } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const [checkingAuth, setCheckingAuth] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [googleLoading, setGoogleLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // State for custom login (moved to top)\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [password, setPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [loginError, setLoginError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoggingIn, setIsLoggingIn] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [backendStatus, setBackendStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"checking\");\n    const [networkStatus, setNetworkStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"checking\");\n    const { login } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    // Check for error parameters in URL\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const params = new URLSearchParams(window.location.search);\n        const error = params.get(\"error\");\n        if (error) {\n            switch(error){\n                case \"login_failed\":\n                    setLoginError(\"Google sign-in failed. Please try again.\");\n                    break;\n                case \"callback_failed\":\n                    setLoginError(\"Authentication callback failed. This could be due to:\\n\\n1. Backend server not running\\n2. Google OAuth configuration issues\\n3. Network connectivity problems\\n\\nPlease check that:\\n• Backend server is running on localhost:8000\\n• Google OAuth credentials are properly configured\\n• You have a stable internet connection\");\n                    break;\n                case \"timeout\":\n                    setLoginError(\"Authentication timed out. This could be due to:\\n\\n1. Slow or unstable internet connection\\n2. Backend server not responding\\n3. Google OAuth configuration issues\\n\\nPlease try again when your connection is more stable.\");\n                    break;\n                case \"account_locked\":\n                    setLoginError(\"Account is temporarily locked due to too many failed attempts. Please try again later.\");\n                    break;\n                case \"rate_limited\":\n                    setLoginError(\"Too many login attempts. Please wait before trying again.\");\n                    break;\n                default:\n                    setLoginError(\"An authentication error occurred. Please try again.\");\n            }\n            // Clear the error from URL\n            window.history.replaceState({}, document.title, window.location.pathname);\n        }\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const verifyUser = async ()=>{\n            if (!loading) {\n                if (user) {\n                    // Check with backend if user still exists\n                    const exists = await checkUserExists(user.uuid || \"\");\n                    if (exists) {\n                        router.replace(\"/dashboard\");\n                    } else {\n                        // Clear session and show error\n                        logout();\n                        setLoginError(\"Your session has expired. Please sign in again.\");\n                    }\n                } else {\n                    setCheckingAuth(false);\n                }\n            }\n        };\n        verifyUser();\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [\n        router,\n        user,\n        loading\n    ]);\n    // Redirect to backend Google OAuth endpoint\n    const handleGoogleLogin = async ()=>{\n        setGoogleLoading(true);\n        setLoginError(\"\");\n        // First check if backend is accessible\n        if (backendStatus === \"offline\") {\n            setLoginError(\"Cannot connect to backend server. Please ensure the backend is running on localhost:8000 before trying Google sign-in.\\n\\nIf you just started the backend, wait a few seconds and try again.\");\n            setGoogleLoading(false);\n            return;\n        }\n        // Check if network connection is unstable\n        if ((0,_utils_networkUtils__WEBPACK_IMPORTED_MODULE_7__.isConnectionUnstable)()) {\n            const advice = (0,_utils_networkUtils__WEBPACK_IMPORTED_MODULE_7__.getConnectionAdvice)();\n            setLoginError(\"Network connection issue detected. \".concat(advice || \"Please check your internet connection and try again.\"));\n            setGoogleLoading(false);\n            return;\n        }\n        console.log(\"Initiating Google OAuth flow...\");\n        console.log(\"Backend URL:\", BACKEND_URL);\n        console.log(\"Google OAuth endpoint:\", \"\".concat(BACKEND_URL, \"/auth/google\"));\n        // Set a timeout for the Google OAuth redirect\n        const redirectTimeout = setTimeout(()=>{\n            if (googleLoading) {\n                setGoogleLoading(false);\n                setLoginError(\"Google sign-in is taking longer than expected. This could be due to:\\n\\n1. Slow or unstable internet connection\\n2. Backend server not responding\\n3. Google OAuth configuration issues\\n\\nPlease check that:\\n• You have a stable internet connection\\n• Backend server is running on localhost:8000\\n• Google OAuth credentials are properly configured\\n\\nIf your internet is unstable, try:\\n• Using a wired connection instead of WiFi\\n• Moving closer to your router\\n• Trying again when your connection is more stable\");\n            }\n        }, 10000); // Reduced to 10 seconds for faster feedback\n        try {\n            // Double-check backend connectivity before redirecting\n            try {\n                const healthCheck = await fetch(\"/api/auth/health\", {\n                    method: \"GET\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    signal: AbortSignal.timeout(5000) // 5 second timeout\n                });\n                if (!healthCheck.ok) {\n                    throw new Error(\"Backend health check failed: \".concat(healthCheck.status));\n                }\n            } catch (healthError) {\n                clearTimeout(redirectTimeout);\n                setGoogleLoading(false);\n                setLoginError(\"Backend server is not accessible. Please ensure:\\n\\n1. Backend server is running on localhost:8000\\n2. No firewall is blocking the connection\\n3. Check backend logs for any startup errors\\n\\nIf you just started the backend, wait a few seconds and try again.\");\n                return;\n            }\n            // Clear the timeout since we're about to redirect\n            clearTimeout(redirectTimeout);\n            // Redirect to Google OAuth\n            window.location.href = \"\".concat(BACKEND_URL, \"/auth/google\");\n        } catch (error) {\n            clearTimeout(redirectTimeout);\n            console.error(\"Error redirecting to Google OAuth:\", error);\n            setGoogleLoading(false);\n            setLoginError(\"Failed to redirect to Google sign-in. This might be due to network connectivity issues. Please try again when your connection is more stable.\");\n        }\n    };\n    if (checkingAuth || loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"loader mb-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n                    lineNumber: 147,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-lg font-medium\",\n                    children: \"Checking authentication...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n                    lineNumber: 148,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n            lineNumber: 146,\n            columnNumber: 7\n        }, this);\n    }\n    // Enhanced login handler with better error handling\n    const handleCustomLogin = async (e)=>{\n        e.preventDefault();\n        setLoginError(\"\");\n        setIsLoggingIn(true);\n        try {\n            // Try enhanced login first\n            const response = await (0,_authApi__WEBPACK_IMPORTED_MODULE_4__.enhancedLogin)({\n                identifier: email,\n                password,\n                clientIp: \"frontend\",\n                userAgent: navigator.userAgent\n            });\n            // Enhanced login successful\n            login(response.user, response.accessToken, response.refreshToken);\n            router.replace(\"/dashboard\");\n        } catch (error) {\n            console.error(\"Enhanced login failed:\", error);\n            // Check if it's a rate limiting or account lockout error\n            if (error instanceof Error) {\n                if (error.message.includes(\"Too many login attempts\") || error.message.includes(\"Account locked\") || error.message.includes(\"429\") || error.message.includes(\"423\")) {\n                    setLoginError(\"Too many failed login attempts. Your account is temporarily locked. Please try again later.\");\n                    return;\n                }\n                if (error.message.includes(\"Invalid credentials\") || error.message.includes(\"401\")) {\n                    setLoginError(\"Invalid email or password. Please check your credentials and try again.\");\n                    return;\n                }\n                // For other errors, try legacy login as fallback\n                try {\n                    console.log(\"Trying legacy login as fallback...\");\n                    const legacyResponse = await (0,_authApi__WEBPACK_IMPORTED_MODULE_4__.legacyLogin)({\n                        identifier: email,\n                        password\n                    });\n                    // Legacy login successful\n                    login(legacyResponse.user, legacyResponse.accessToken, legacyResponse.refreshToken || \"\");\n                    router.replace(\"/dashboard\");\n                    return;\n                } catch (legacyError) {\n                    console.error(\"Legacy login also failed:\", legacyError);\n                    if (legacyError instanceof Error) {\n                        setLoginError(legacyError.message || \"Login failed. Please check your credentials and try again.\");\n                    } else {\n                        setLoginError(\"Login failed. Please try again.\");\n                    }\n                }\n            } else {\n                setLoginError(\"An unexpected error occurred. Please try again.\");\n            }\n        } finally{\n            setIsLoggingIn(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex flex-col items-center justify-center bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white shadow-lg rounded-lg p-8 max-w-md w-full\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-2xl font-bold mb-6 text-center\",\n                    children: \"Sign in to Dido Distribution\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n                    lineNumber: 223,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-4 space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-3 rounded-lg text-sm bg-gray-50\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_BackendStatusChecker__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                onStatusChange: setBackendStatus,\n                                showDetails: true\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n                            lineNumber: 227,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-3 rounded-lg text-sm bg-gray-50\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NetworkStatusChecker__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                onStatusChange: setNetworkStatus,\n                                showDetails: true\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n                                lineNumber: 234,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n                            lineNumber: 233,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n                    lineNumber: 226,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleCustomLogin,\n                    className: \"space-y-4 mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"email\",\n                            value: email,\n                            onChange: (e)=>setEmail(e.target.value),\n                            placeholder: \"Email\",\n                            className: \"w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-200\",\n                            required: true,\n                            disabled: isLoggingIn\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n                            lineNumber: 242,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"password\",\n                            value: password,\n                            onChange: (e)=>setPassword(e.target.value),\n                            placeholder: \"Password\",\n                            className: \"w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-200\",\n                            required: true,\n                            disabled: isLoggingIn\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n                            lineNumber: 251,\n                            columnNumber: 11\n                        }, this),\n                        loginError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-red-600 text-sm text-center p-3 bg-red-50 rounded-lg border border-red-200 whitespace-pre-line\",\n                            children: loginError\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n                            lineNumber: 261,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"submit\",\n                            disabled: isLoggingIn || backendStatus === \"offline\",\n                            className: \"w-full py-3 px-4 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center\",\n                            children: isLoggingIn ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n                                        lineNumber: 272,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"Signing in...\"\n                                ]\n                            }, void 0, true) : \"Sign in\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n                            lineNumber: 265,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n                    lineNumber: 241,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center my-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-grow border-t border-gray-200\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n                            lineNumber: 281,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"mx-2 text-gray-400 text-xs\",\n                            children: \"OR\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n                            lineNumber: 282,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-grow border-t border-gray-200\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n                            lineNumber: 283,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n                    lineNumber: 280,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handleGoogleLogin,\n                    disabled: googleLoading || isLoggingIn || backendStatus === \"offline\" || networkStatus === \"offline\",\n                    className: \"w-full flex items-center justify-center gap-2 py-3 px-4 border border-gray-300 rounded-lg bg-white hover:bg-gray-100 transition text-gray-700 font-medium shadow-sm disabled:opacity-50 disabled:cursor-not-allowed\",\n                    children: [\n                        googleLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n                            lineNumber: 291,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-5 h-5\",\n                            viewBox: \"0 0 48 48\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"g\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        fill: \"#4285F4\",\n                                        d: \"M24 9.5c3.54 0 6.29 1.52 7.74 2.79l5.67-5.67C33.54 3.22 29.3 1 24 1 14.82 1 6.99 6.98 3.69 15.14l6.97 5.41C12.22 14.05 17.61 9.5 24 9.5z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n                                        lineNumber: 295,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        fill: \"#34A853\",\n                                        d: \"M46.1 24.55c0-1.64-.15-3.22-.42-4.74H24v9.01h12.42c-.54 2.85-2.17 5.27-4.63 6.91l7.11 5.53C43.98 37.22 46.1 31.37 46.1 24.55z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n                                        lineNumber: 296,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        fill: \"#FBBC05\",\n                                        d: \"M10.66 28.55a14.7 14.7 0 010-9.1l-6.97-5.41A23.99 23.99 0 001 24c0 3.87.92 7.53 2.55 10.76l7.10-6.21z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n                                        lineNumber: 297,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        fill: \"#EA4335\",\n                                        d: \"M24 47c6.48 0 11.93-2.15 15.9-5.85l-7.11-5.53c-2.01 1.36-4.6 2.16-8.79 2.16-6.39 0-11.78-4.55-13.64-10.66l-7.10 6.21C6.99 41.02 14.82 47 24 47z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n                                        lineNumber: 298,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        fill: \"none\",\n                                        d: \"M1 1h46v46H1z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n                                        lineNumber: 299,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n                                lineNumber: 294,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n                            lineNumber: 293,\n                            columnNumber: 13\n                        }, this),\n                        googleLoading ? \"Redirecting to Google...\" : \"Sign in with Google\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n                    lineNumber: 285,\n                    columnNumber: 9\n                }, this),\n                backendStatus === \"offline\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-sm font-medium text-yellow-800 mb-2\",\n                            children: \"Backend Server Offline\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n                            lineNumber: 309,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-yellow-700\",\n                            children: \"The backend server is not accessible. Please ensure:\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n                            lineNumber: 310,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            className: \"text-xs text-yellow-700 mt-1 list-disc list-inside\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"Backend server is running on localhost:8000\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n                                    lineNumber: 314,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"No firewall is blocking the connection\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n                                    lineNumber: 315,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"Check backend logs for any startup errors\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n                                    lineNumber: 316,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n                            lineNumber: 313,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n                    lineNumber: 308,\n                    columnNumber: 11\n                }, this),\n                networkStatus === \"unstable\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-4 p-3 bg-orange-50 border border-orange-200 rounded-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-sm font-medium text-orange-800 mb-2\",\n                            children: \"Unstable Network Connection\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n                            lineNumber: 323,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-orange-700\",\n                            children: \"Your internet connection appears to be unstable. This may cause authentication issues:\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n                            lineNumber: 324,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            className: \"text-xs text-orange-700 mt-1 list-disc list-inside\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"Try using a wired connection instead of WiFi\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n                                    lineNumber: 328,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"Move closer to your router\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n                                    lineNumber: 329,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"Wait a few minutes and try again\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n                                    lineNumber: 330,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"Authentication may take longer than usual\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n                                    lineNumber: 331,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n                            lineNumber: 327,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n                    lineNumber: 322,\n                    columnNumber: 11\n                }, this),\n                networkStatus === \"slow\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-sm font-medium text-blue-800 mb-2\",\n                            children: \"Slow Network Connection\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n                            lineNumber: 338,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-blue-700\",\n                            children: \"Your internet connection is slow. Authentication may take longer than usual.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n                            lineNumber: 339,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n                    lineNumber: 337,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"mt-6 text-center text-sm text-gray-500\",\n                    children: [\n                        \"By signing in, you agree to our \",\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                            href: \"#\",\n                            className: \"underline\",\n                            children: \"Terms\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n                            lineNumber: 346,\n                            columnNumber: 43\n                        }, this),\n                        \" and \",\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                            href: \"#\",\n                            className: \"underline\",\n                            children: \"Privacy Policy\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n                            lineNumber: 346,\n                            columnNumber: 91\n                        }, this),\n                        \".\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n                    lineNumber: 345,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n            lineNumber: 222,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\auth\\\\page.tsx\",\n        lineNumber: 221,\n        columnNumber: 5\n    }, this);\n}\n_s(AuthPage, \"M39mNhpRlzjyZHYmoghMYmgCXBc=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth,\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth\n    ];\n});\n_c = AuthPage;\nvar _c;\n$RefreshReg$(_c, \"AuthPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/auth/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/BackendStatusChecker.tsx":
/*!*********************************************!*\
  !*** ./components/BackendStatusChecker.tsx ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ BackendStatusChecker; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction BackendStatusChecker(param) {\n    let { onStatusChange, showDetails = false, className = \"\" } = param;\n    _s();\n    const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"checking\");\n    const [details, setDetails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [lastCheck, setLastCheck] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [checkTimeout, setCheckTimeout] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const checkBackendStatus = async ()=>{\n        setStatus(\"checking\");\n        setDetails(\"Checking backend connectivity...\");\n        // Clear any existing timeout\n        if (checkTimeout) {\n            clearTimeout(checkTimeout);\n        }\n        // Set a timeout for the check\n        const timeout = setTimeout(()=>{\n            setStatus(\"offline\");\n            setDetails(\"Backend check timed out after 10 seconds. Server may be offline or unreachable.\");\n            onStatusChange === null || onStatusChange === void 0 ? void 0 : onStatusChange(\"offline\");\n        }, 10000);\n        setCheckTimeout(timeout);\n        try {\n            const startTime = Date.now();\n            const response = await fetch(\"/api/auth/health\", {\n                method: \"GET\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                signal: AbortSignal.timeout(8000)\n            });\n            const endTime = Date.now();\n            const responseTime = endTime - startTime;\n            // Clear the timeout since we got a response\n            clearTimeout(timeout);\n            setCheckTimeout(null);\n            setLastCheck(new Date());\n            if (response.ok) {\n                const data = await response.json();\n                setStatus(\"online\");\n                setDetails(\"Backend is online (\".concat(responseTime, \"ms) - Uptime: \").concat(Math.floor(data.uptime), \"s\"));\n                onStatusChange === null || onStatusChange === void 0 ? void 0 : onStatusChange(\"online\");\n            } else {\n                setStatus(\"error\");\n                setDetails(\"Backend returned error: \".concat(response.status, \" \").concat(response.statusText, \" (\").concat(responseTime, \"ms)\"));\n                onStatusChange === null || onStatusChange === void 0 ? void 0 : onStatusChange(\"error\");\n            }\n        } catch (error) {\n            // Clear the timeout since we got an error\n            clearTimeout(timeout);\n            setCheckTimeout(null);\n            setLastCheck(new Date());\n            setStatus(\"offline\");\n            setDetails(\"Cannot connect to backend: \".concat(error instanceof Error ? error.message : \"Unknown error\"));\n            onStatusChange === null || onStatusChange === void 0 ? void 0 : onStatusChange(\"offline\");\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        checkBackendStatus();\n        // Cleanup timeout on unmount\n        return ()=>{\n            if (checkTimeout) {\n                clearTimeout(checkTimeout);\n            }\n        };\n    }, []);\n    const getStatusIcon = ()=>{\n        switch(status){\n            case \"checking\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\BackendStatusChecker.tsx\",\n                    lineNumber: 91,\n                    columnNumber: 11\n                }, this);\n            case \"online\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-4 h-4 text-green-600\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 20 20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n                        clipRule: \"evenodd\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\BackendStatusChecker.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\BackendStatusChecker.tsx\",\n                    lineNumber: 95,\n                    columnNumber: 11\n                }, this);\n            case \"offline\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-4 h-4 text-red-600\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 20 20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\",\n                        clipRule: \"evenodd\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\BackendStatusChecker.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\BackendStatusChecker.tsx\",\n                    lineNumber: 101,\n                    columnNumber: 11\n                }, this);\n            case \"error\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-4 h-4 text-yellow-600\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 20 20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\",\n                        clipRule: \"evenodd\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\BackendStatusChecker.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\BackendStatusChecker.tsx\",\n                    lineNumber: 107,\n                    columnNumber: 11\n                }, this);\n        }\n    };\n    const getStatusText = ()=>{\n        switch(status){\n            case \"checking\":\n                return \"Checking backend connection...\";\n            case \"online\":\n                return \"Backend server is online\";\n            case \"offline\":\n                return \"Backend server is offline\";\n            case \"error\":\n                return \"Backend server error\";\n        }\n    };\n    const getStatusColor = ()=>{\n        switch(status){\n            case \"checking\":\n                return \"text-blue-600\";\n            case \"online\":\n                return \"text-green-600\";\n            case \"offline\":\n                return \"text-red-600\";\n            case \"error\":\n                return \"text-yellow-600\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mr-2\",\n                children: getStatusIcon()\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\BackendStatusChecker.tsx\",\n                lineNumber: 142,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm font-medium \".concat(getStatusColor()),\n                        children: getStatusText()\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\BackendStatusChecker.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 9\n                    }, this),\n                    showDetails && details && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-500 mt-1\",\n                        children: details\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\BackendStatusChecker.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 11\n                    }, this),\n                    showDetails && lastCheck && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-400 mt-1\",\n                        children: [\n                            \"Last checked: \",\n                            lastCheck.toLocaleTimeString()\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\BackendStatusChecker.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\BackendStatusChecker.tsx\",\n                lineNumber: 145,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: checkBackendStatus,\n                disabled: status === \"checking\",\n                className: \"ml-2 px-2 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded transition disabled:opacity-50\",\n                title: \"Refresh backend status\",\n                children: status === \"checking\" ? \"Checking...\" : \"Refresh\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\BackendStatusChecker.tsx\",\n                lineNumber: 160,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\BackendStatusChecker.tsx\",\n        lineNumber: 141,\n        columnNumber: 5\n    }, this);\n}\n_s(BackendStatusChecker, \"gcuBQwD+VEnB9/YWymv0lF1zNd0=\");\n_c = BackendStatusChecker;\nvar _c;\n$RefreshReg$(_c, \"BackendStatusChecker\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/BackendStatusChecker.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/NetworkStatusChecker.tsx":
/*!*********************************************!*\
  !*** ./components/NetworkStatusChecker.tsx ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ NetworkStatusChecker; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction NetworkStatusChecker(param) {\n    let { onStatusChange, showDetails = false, className = \"\" } = param;\n    _s();\n    const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"checking\");\n    const [details, setDetails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [lastCheck, setLastCheck] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [connectionType, setConnectionType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [checkTimeout, setCheckTimeout] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const checkNetworkStatus = async ()=>{\n        setStatus(\"checking\");\n        setDetails(\"Checking network connectivity...\");\n        // Clear any existing timeout\n        if (checkTimeout) {\n            clearTimeout(checkTimeout);\n        }\n        // Set a timeout for the check\n        const timeout = setTimeout(()=>{\n            setStatus(\"offline\");\n            setDetails(\"Network check timed out after 15 seconds. No internet connection detected.\");\n            onStatusChange === null || onStatusChange === void 0 ? void 0 : onStatusChange(\"offline\");\n        }, 15000);\n        setCheckTimeout(timeout);\n        try {\n            // Check connection type\n            if (\"connection\" in navigator) {\n                const conn = navigator.connection;\n                if (conn) {\n                    setConnectionType(\"\".concat(conn.effectiveType || \"unknown\", \" (\").concat(conn.type || \"unknown\", \")\"));\n                }\n            }\n            // Test connection speed by making multiple requests with shorter timeouts\n            const testUrls = [\n                \"https://www.google.com/favicon.ico\",\n                \"https://www.cloudflare.com/favicon.ico\",\n                \"https://www.github.com/favicon.ico\"\n            ];\n            const results = await Promise.allSettled(testUrls.map((url)=>fetch(url, {\n                    method: \"HEAD\",\n                    mode: \"no-cors\",\n                    cache: \"no-cache\",\n                    signal: AbortSignal.timeout(5000) // 5 second timeout per request\n                })));\n            const successfulRequests = results.filter((result)=>result.status === \"fulfilled\").length;\n            const totalRequests = results.length;\n            // Clear the timeout since we got results\n            clearTimeout(timeout);\n            setCheckTimeout(null);\n            setLastCheck(new Date());\n            if (successfulRequests === 0) {\n                setStatus(\"offline\");\n                setDetails(\"No internet connection detected. Please check your network connection.\");\n                onStatusChange === null || onStatusChange === void 0 ? void 0 : onStatusChange(\"offline\");\n            } else if (successfulRequests < totalRequests) {\n                setStatus(\"unstable\");\n                setDetails(\"Unstable connection: \".concat(successfulRequests, \"/\").concat(totalRequests, \" requests successful. Some requests failed.\"));\n                onStatusChange === null || onStatusChange === void 0 ? void 0 : onStatusChange(\"unstable\");\n            } else {\n                // All requests successful, now test speed\n                const startTime = Date.now();\n                try {\n                    await fetch(\"https://www.google.com/favicon.ico\", {\n                        method: \"HEAD\",\n                        mode: \"no-cors\",\n                        cache: \"no-cache\",\n                        signal: AbortSignal.timeout(3000) // 3 second timeout for speed test\n                    });\n                    const endTime = Date.now();\n                    const responseTime = endTime - startTime;\n                    if (responseTime < 1000) {\n                        setStatus(\"good\");\n                        setDetails(\"Good connection: \".concat(responseTime, \"ms response time\"));\n                        onStatusChange === null || onStatusChange === void 0 ? void 0 : onStatusChange(\"good\");\n                    } else if (responseTime < 3000) {\n                        setStatus(\"slow\");\n                        setDetails(\"Slow connection: \".concat(responseTime, \"ms response time\"));\n                        onStatusChange === null || onStatusChange === void 0 ? void 0 : onStatusChange(\"slow\");\n                    } else {\n                        setStatus(\"unstable\");\n                        setDetails(\"Very slow connection: \".concat(responseTime, \"ms response time\"));\n                        onStatusChange === null || onStatusChange === void 0 ? void 0 : onStatusChange(\"unstable\");\n                    }\n                } catch (error) {\n                    setStatus(\"unstable\");\n                    setDetails(\"Connection test failed\");\n                    onStatusChange === null || onStatusChange === void 0 ? void 0 : onStatusChange(\"unstable\");\n                }\n            }\n        } catch (error) {\n            // Clear the timeout since we got an error\n            clearTimeout(timeout);\n            setCheckTimeout(null);\n            setLastCheck(new Date());\n            setStatus(\"offline\");\n            setDetails(\"Network check failed: \".concat(error instanceof Error ? error.message : \"Unknown error\"));\n            onStatusChange === null || onStatusChange === void 0 ? void 0 : onStatusChange(\"offline\");\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        checkNetworkStatus();\n        // Cleanup timeout on unmount\n        return ()=>{\n            if (checkTimeout) {\n                clearTimeout(checkTimeout);\n            }\n        };\n    }, []);\n    const getStatusIcon = ()=>{\n        switch(status){\n            case \"checking\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\NetworkStatusChecker.tsx\",\n                    lineNumber: 142,\n                    columnNumber: 11\n                }, this);\n            case \"good\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-4 h-4 text-green-600\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 20 20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n                        clipRule: \"evenodd\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\NetworkStatusChecker.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\NetworkStatusChecker.tsx\",\n                    lineNumber: 146,\n                    columnNumber: 11\n                }, this);\n            case \"slow\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-4 h-4 text-yellow-600\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 20 20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\",\n                        clipRule: \"evenodd\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\NetworkStatusChecker.tsx\",\n                        lineNumber: 153,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\NetworkStatusChecker.tsx\",\n                    lineNumber: 152,\n                    columnNumber: 11\n                }, this);\n            case \"unstable\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-4 h-4 text-orange-600\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 20 20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\",\n                        clipRule: \"evenodd\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\NetworkStatusChecker.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\NetworkStatusChecker.tsx\",\n                    lineNumber: 158,\n                    columnNumber: 11\n                }, this);\n            case \"offline\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-4 h-4 text-red-600\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 20 20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\",\n                        clipRule: \"evenodd\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\NetworkStatusChecker.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\NetworkStatusChecker.tsx\",\n                    lineNumber: 164,\n                    columnNumber: 11\n                }, this);\n        }\n    };\n    const getStatusText = ()=>{\n        switch(status){\n            case \"checking\":\n                return \"Checking network connection...\";\n            case \"good\":\n                return \"Network connection is good\";\n            case \"slow\":\n                return \"Network connection is slow\";\n            case \"unstable\":\n                return \"Network connection is unstable\";\n            case \"offline\":\n                return \"No network connection\";\n        }\n    };\n    const getStatusColor = ()=>{\n        switch(status){\n            case \"checking\":\n                return \"text-blue-600\";\n            case \"good\":\n                return \"text-green-600\";\n            case \"slow\":\n                return \"text-yellow-600\";\n            case \"unstable\":\n                return \"text-orange-600\";\n            case \"offline\":\n                return \"text-red-600\";\n        }\n    };\n    const getConnectionAdvice = ()=>{\n        switch(status){\n            case \"good\":\n                return null;\n            case \"slow\":\n                return \"Your connection is slow. Authentication may take longer than usual.\";\n            case \"unstable\":\n                return \"Your connection is unstable. Consider using a wired connection or moving closer to your router.\";\n            case \"offline\":\n                return \"No internet connection detected. Please check your network settings.\";\n            default:\n                return null;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mr-2\",\n                children: getStatusIcon()\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\NetworkStatusChecker.tsx\",\n                lineNumber: 218,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm font-medium \".concat(getStatusColor()),\n                        children: getStatusText()\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\NetworkStatusChecker.tsx\",\n                        lineNumber: 222,\n                        columnNumber: 9\n                    }, this),\n                    showDetails && details && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-500 mt-1\",\n                        children: details\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\NetworkStatusChecker.tsx\",\n                        lineNumber: 226,\n                        columnNumber: 11\n                    }, this),\n                    showDetails && connectionType && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-400 mt-1\",\n                        children: [\n                            \"Connection: \",\n                            connectionType\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\NetworkStatusChecker.tsx\",\n                        lineNumber: 231,\n                        columnNumber: 11\n                    }, this),\n                    showDetails && lastCheck && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-400 mt-1\",\n                        children: [\n                            \"Last checked: \",\n                            lastCheck.toLocaleTimeString()\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\NetworkStatusChecker.tsx\",\n                        lineNumber: 236,\n                        columnNumber: 11\n                    }, this),\n                    showDetails && getConnectionAdvice() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-orange-600 mt-1 font-medium\",\n                        children: getConnectionAdvice()\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\NetworkStatusChecker.tsx\",\n                        lineNumber: 241,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\NetworkStatusChecker.tsx\",\n                lineNumber: 221,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: checkNetworkStatus,\n                disabled: status === \"checking\",\n                className: \"ml-2 px-2 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded transition disabled:opacity-50\",\n                title: \"Refresh network status\",\n                children: status === \"checking\" ? \"Checking...\" : \"Refresh\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\NetworkStatusChecker.tsx\",\n                lineNumber: 246,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\components\\\\NetworkStatusChecker.tsx\",\n        lineNumber: 217,\n        columnNumber: 5\n    }, this);\n}\n_s(NetworkStatusChecker, \"rJfFZcVEoHlkGb1CxhQEz6NKNts=\");\n_c = NetworkStatusChecker;\nvar _c;\n$RefreshReg$(_c, \"NetworkStatusChecker\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/NetworkStatusChecker.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./contexts/AuthContext.tsx":
/*!**********************************!*\
  !*** ./contexts/AuthContext.tsx ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: function() { return /* binding */ AuthProvider; },\n/* harmony export */   useAuth: function() { return /* binding */ useAuth; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider(param) {\n    let { children } = param;\n    _s();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [token, setToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [refreshToken, setRefreshToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [fetchingWarehouse, setFetchingWarehouse] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [refreshingToken, setRefreshingToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    // Use refs to avoid dependency issues\n    const fetchingWarehouseRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const tokenRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const userRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Update refs when state changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchingWarehouseRef.current = fetchingWarehouse;\n    }, [\n        fetchingWarehouse\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        tokenRef.current = token;\n    }, [\n        token\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        userRef.current = user;\n    }, [\n        user\n    ]);\n    // Helper to fetch warehouse info in background\n    const fetchAndPersistWarehouseInfo = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (userObjParam, tokenParam)=>{\n        var _userRef_current, _userRef_current1, _userRef_current2;\n        const tokenToUse = tokenParam !== undefined ? tokenParam : tokenRef.current;\n        const userToFetch = userObjParam || userRef.current;\n        console.log(\"fetchAndPersistWarehouseInfo called with:\", {\n            tokenToUse: !!tokenToUse,\n            userToFetch: userToFetch === null || userToFetch === void 0 ? void 0 : userToFetch.uuid,\n            userToFetchWarehouseUuid: (userToFetch === null || userToFetch === void 0 ? void 0 : userToFetch.warehouseUuidString) || (userToFetch === null || userToFetch === void 0 ? void 0 : userToFetch.warehouseUuid),\n            fetchingWarehouse: fetchingWarehouseRef.current,\n            currentUser: (_userRef_current = userRef.current) === null || _userRef_current === void 0 ? void 0 : _userRef_current.uuid,\n            currentUserWarehouseUuid: ((_userRef_current1 = userRef.current) === null || _userRef_current1 === void 0 ? void 0 : _userRef_current1.warehouseUuidString) || ((_userRef_current2 = userRef.current) === null || _userRef_current2 === void 0 ? void 0 : _userRef_current2.warehouseUuid)\n        });\n        if (!tokenToUse || !(userToFetch === null || userToFetch === void 0 ? void 0 : userToFetch.uuid) || fetchingWarehouseRef.current) {\n            console.log(\"fetchAndPersistWarehouseInfo early return:\", {\n                noToken: !tokenToUse,\n                noUserUuid: !(userToFetch === null || userToFetch === void 0 ? void 0 : userToFetch.uuid),\n                alreadyFetching: fetchingWarehouseRef.current\n            });\n            return;\n        }\n        // Additional safety check: ensure UUID is a valid string\n        if (typeof userToFetch.uuid !== \"string\" || userToFetch.uuid.trim() === \"\") {\n            console.log(\"fetchAndPersistWarehouseInfo early return: invalid UUID:\", userToFetch.uuid);\n            return;\n        }\n        // Additional check: if we don't have a valid token in localStorage, don't proceed\n        const storedToken = localStorage.getItem(\"dido_token\");\n        if (!storedToken) {\n            console.log(\"fetchAndPersistWarehouseInfo early return: no stored token\");\n            return;\n        }\n        setFetchingWarehouse(true);\n        try {\n            console.log(\"Fetching latest user info for UUID:\", userToFetch.uuid);\n            // Always fetch the latest user info from backend to ensure we have current data\n            const userRes = await fetch(\"/api/users/\".concat(userToFetch.uuid), {\n                headers: {\n                    Authorization: \"Bearer \".concat(tokenToUse)\n                }\n            });\n            if (!userRes.ok) {\n                console.error(\"Failed to fetch user info:\", userRes.status, userRes.statusText);\n                return;\n            }\n            const latestUser = await userRes.json();\n            console.log(\"Latest user info from API:\", latestUser);\n            console.log(\"Warehouse UUID from backend:\", {\n                warehouseUuidString: latestUser.warehouseUuidString,\n                warehouseUuid: latestUser.warehouseUuid,\n                finalWarehouseUuid: latestUser.warehouseUuidString || latestUser.warehouseUuid\n            });\n            // Check if user has a warehouse assigned\n            const warehouseUuid = latestUser.warehouseUuidString || latestUser.warehouseUuid;\n            if (!warehouseUuid) {\n                console.log(\"No warehouse assigned to user, setting user without warehouse info\");\n                const updatedUser = {\n                    ...latestUser,\n                    warehouseUuid: null,\n                    warehouseName: null\n                };\n                setUser(updatedUser);\n                localStorage.setItem(\"dido_user\", JSON.stringify(updatedUser));\n                return;\n            }\n            console.log(\"Fetching warehouse info for UUID:\", warehouseUuid);\n            const warehouseRes = await fetch(\"/api/warehouses/\".concat(warehouseUuid), {\n                headers: {\n                    Authorization: \"Bearer \".concat(tokenToUse)\n                }\n            });\n            if (!warehouseRes.ok) {\n                console.error(\"Failed to fetch warehouse info:\", warehouseRes.status, warehouseRes.statusText);\n                // If warehouse fetch fails, still update user with current data but without warehouse name\n                const updatedUser = {\n                    ...latestUser,\n                    warehouseUuid: warehouseUuid,\n                    warehouseName: null\n                };\n                setUser(updatedUser);\n                localStorage.setItem(\"dido_user\", JSON.stringify(updatedUser));\n                return;\n            }\n            const warehouse = await warehouseRes.json();\n            console.log(\"Warehouse info from API:\", warehouse);\n            const updatedUser = {\n                ...latestUser,\n                warehouseUuid: warehouseUuid,\n                warehouseName: warehouse.name\n            };\n            console.log(\"Setting updated user with warehouse info:\", updatedUser);\n            setUser(updatedUser);\n            localStorage.setItem(\"dido_user\", JSON.stringify(updatedUser));\n        } catch (err) {\n            console.error(\"Error fetching warehouse info:\", err);\n            // On error, still try to update user with current data from backend\n            try {\n                const userRes = await fetch(\"/api/users/\".concat(userToFetch.uuid), {\n                    headers: {\n                        Authorization: \"Bearer \".concat(tokenToUse)\n                    }\n                });\n                if (userRes.ok) {\n                    const latestUser = await userRes.json();\n                    const updatedUser = {\n                        ...latestUser,\n                        warehouseUuid: latestUser.warehouseUuidString || latestUser.warehouseUuid,\n                        warehouseName: null\n                    };\n                    setUser(updatedUser);\n                    localStorage.setItem(\"dido_user\", JSON.stringify(updatedUser));\n                }\n            } catch (fallbackErr) {\n                console.error(\"Error in fallback user update:\", fallbackErr);\n            }\n        } finally{\n            setFetchingWarehouse(false);\n        }\n    }, []); // Remove all dependencies since we're using refs\n    // Refresh access token using refresh token\n    const refreshAccessToken = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        if (!refreshToken || refreshingToken) return false;\n        setRefreshingToken(true);\n        try {\n            const response = await fetch(\"/api/auth/refresh\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    refreshToken,\n                    clientIp: \"frontend\",\n                    userAgent: navigator.userAgent\n                })\n            });\n            if (!response.ok) {\n                console.error(\"Token refresh failed:\", response.status);\n                return false;\n            }\n            const data = await response.json();\n            setToken(data.accessToken);\n            setRefreshToken(data.refreshToken);\n            localStorage.setItem(\"dido_token\", data.accessToken);\n            localStorage.setItem(\"dido_refresh_token\", data.refreshToken);\n            console.log(\"Token refreshed successfully\");\n            return true;\n        } catch (error) {\n            console.error(\"Error refreshing token:\", error);\n            return false;\n        } finally{\n            setRefreshingToken(false);\n        }\n    }, [\n        refreshToken,\n        refreshingToken\n    ]);\n    // Get security status for the current user\n    const getSecurityStatus = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        if (!token) return null;\n        try {\n            const response = await fetch(\"/api/auth/security-status\", {\n                headers: {\n                    Authorization: \"Bearer \".concat(token)\n                }\n            });\n            if (!response.ok) {\n                console.error(\"Failed to get security status:\", response.status);\n                return null;\n            }\n            return await response.json();\n        } catch (error) {\n            console.error(\"Error getting security status:\", error);\n            return null;\n        }\n    }, [\n        token\n    ]);\n    // On initial mount: restore session from localStorage\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.log(\"AuthContext initial mount - checking localStorage\");\n        const storedUser = localStorage.getItem(\"dido_user\");\n        const storedToken = localStorage.getItem(\"dido_token\");\n        const storedRefreshToken = localStorage.getItem(\"dido_refresh_token\");\n        console.log(\"Stored data from localStorage:\", {\n            hasStoredUser: !!storedUser,\n            hasStoredToken: !!storedToken,\n            hasStoredRefreshToken: !!storedRefreshToken\n        });\n        if (storedUser && storedToken) {\n            const userObj = JSON.parse(storedUser);\n            console.log(\"Restored user from localStorage:\", {\n                userUuid: userObj.uuid,\n                userEmail: userObj.email,\n                userWarehouseUuid: userObj.warehouseUuidString || userObj.warehouseUuid,\n                userWarehouseName: userObj.warehouseName\n            });\n            // Validate that the user UUID is still valid by fetching from backend\n            const validateUserExists = async ()=>{\n                try {\n                    console.log(\"Validating user UUID from localStorage:\", userObj.uuid);\n                    const response = await fetch(\"/api/users/\".concat(userObj.uuid), {\n                        headers: {\n                            Authorization: \"Bearer \".concat(storedToken)\n                        }\n                    });\n                    if (response.status === 404) {\n                        console.log(\"User UUID not found in backend (404), clearing localStorage and redirecting to login\");\n                        // User was deleted/recreated, clear localStorage and redirect to login\n                        localStorage.removeItem(\"dido_token\");\n                        localStorage.removeItem(\"dido_refresh_token\");\n                        localStorage.removeItem(\"dido_user\");\n                        router.replace(\"/auth\");\n                        return;\n                    }\n                    if (!response.ok) {\n                        console.log(\"Failed to validate user UUID:\", response.status, response.statusText);\n                        // If we can't validate the user, clear localStorage and redirect to login\n                        localStorage.removeItem(\"dido_token\");\n                        localStorage.removeItem(\"dido_refresh_token\");\n                        localStorage.removeItem(\"dido_user\");\n                        router.replace(\"/auth\");\n                        return;\n                    }\n                    // User exists, proceed with normal flow\n                    console.log(\"User UUID validated successfully\");\n                    setUser(userObj);\n                    setToken(storedToken);\n                    if (storedRefreshToken) {\n                        setRefreshToken(storedRefreshToken);\n                    }\n                    // Always trigger background fetch to ensure we have the latest warehouse info\n                    // This is especially important after database clearing when warehouse UUIDs may have changed\n                    console.log(\"Triggering background warehouse info fetch to ensure latest data from localStorage\");\n                    setTimeout(()=>{\n                        fetchAndPersistWarehouseInfo(userObj, storedToken);\n                    }, 0);\n                } catch (error) {\n                    console.error(\"Error validating user UUID:\", error);\n                    // If there's a network error, clear localStorage and redirect to login\n                    localStorage.removeItem(\"dido_token\");\n                    localStorage.removeItem(\"dido_refresh_token\");\n                    localStorage.removeItem(\"dido_user\");\n                    router.replace(\"/auth\");\n                }\n            };\n            // Validate user exists before setting state\n            validateUserExists();\n        } else {\n            console.log(\"No stored user/token found in localStorage\");\n        }\n        // If we have no valid authentication and we're not on the auth page, redirect\n        if (!storedToken && !window.location.pathname.includes(\"/auth\")) {\n            console.log(\"No valid authentication found, redirecting to login\");\n            router.replace(\"/auth\");\n        }\n        setLoading(false);\n    }, []); // Remove fetchAndPersistWarehouseInfo from dependencies to prevent infinite loop\n    // Periodically validate session and refresh token if needed\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!token || !refreshToken) {\n            console.log(\"No token or refresh token available, skipping periodic validation\");\n            return;\n        }\n        const interval = setInterval(async ()=>{\n            try {\n                console.log(\"Periodic token validation - checking token validity\");\n                // Try to validate current token\n                const res = await fetch(\"/api/auth/validate\", {\n                    headers: {\n                        Authorization: \"Bearer \".concat(token)\n                    }\n                });\n                if (!res.ok) {\n                    console.log(\"Token validation failed, attempting refresh\");\n                    // Token is invalid, try to refresh\n                    const refreshSuccess = await refreshAccessToken();\n                    if (!refreshSuccess) {\n                        console.log(\"Token refresh failed, logging out user\");\n                        localStorage.removeItem(\"dido_token\");\n                        localStorage.removeItem(\"dido_refresh_token\");\n                        localStorage.removeItem(\"dido_user\");\n                        router.replace(\"/auth\");\n                    } else {\n                        console.log(\"Token refresh successful\");\n                    }\n                } else {\n                    console.log(\"Token validation successful\");\n                }\n            } catch (error) {\n                console.log(\"Network error during token validation, attempting refresh\");\n                // Network error, try to refresh token\n                const refreshSuccess = await refreshAccessToken();\n                if (!refreshSuccess) {\n                    console.log(\"Token refresh failed after network error, logging out user\");\n                    localStorage.removeItem(\"dido_token\");\n                    localStorage.removeItem(\"dido_refresh_token\");\n                    localStorage.removeItem(\"dido_user\");\n                    router.replace(\"/auth\");\n                } else {\n                    console.log(\"Token refresh successful after network error\");\n                }\n            }\n        }, 30 * 60 * 1000); // 30 minutes\n        return ()=>clearInterval(interval);\n    }, [\n        token,\n        refreshToken,\n        refreshAccessToken,\n        router\n    ]);\n    const login = (user, token, refreshTokenValue)=>{\n        console.log(\"AuthContext login called with:\", {\n            userUuid: user.uuid,\n            userEmail: user.email,\n            userWarehouseUuid: user.warehouseUuidString || user.warehouseUuid,\n            userWarehouseUuidString: user.warehouseUuidString,\n            userWarehouseUuidLegacy: user.warehouseUuid,\n            userWarehouseName: user.warehouseName,\n            hasToken: !!token,\n            hasRefreshToken: !!refreshTokenValue\n        });\n        setUser(user);\n        setToken(token);\n        setRefreshToken(refreshTokenValue);\n        localStorage.setItem(\"dido_user\", JSON.stringify(user));\n        localStorage.setItem(\"dido_token\", token);\n        localStorage.setItem(\"dido_refresh_token\", refreshTokenValue);\n        // Always trigger background fetch to ensure we have the latest warehouse info\n        // This is especially important after database clearing when warehouse UUIDs may have changed\n        console.log(\"Triggering background warehouse info fetch to ensure latest data\");\n        setTimeout(()=>{\n            fetchAndPersistWarehouseInfo(user, token);\n        }, 0);\n    };\n    const logout = async ()=>{\n        console.log(\"AuthContext logout called - clearing session and redirecting to /auth\");\n        // Revoke tokens on backend if we have them\n        if (token && refreshToken) {\n            try {\n                await fetch(\"/api/auth/logout\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\",\n                        \"Authorization\": \"Bearer \".concat(token)\n                    },\n                    body: JSON.stringify({\n                        refreshToken,\n                        revokeAll: false\n                    })\n                });\n            } catch (error) {\n                console.error(\"Error during logout:\", error);\n            // Continue with logout even if backend call fails\n            }\n        }\n        setUser(null);\n        setToken(null);\n        setRefreshToken(null);\n        localStorage.removeItem(\"dido_token\");\n        localStorage.removeItem(\"dido_refresh_token\");\n        localStorage.removeItem(\"dido_user\");\n        console.log(\"Session cleared, redirecting to /auth\");\n        router.replace(\"/auth\");\n    };\n    const checkUserExists = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (uuid)=>{\n        if (!token) return false;\n        // Safety check: ensure UUID is valid\n        if (!uuid || typeof uuid !== \"string\" || uuid.trim() === \"\") {\n            console.log(\"checkUserExists: invalid UUID provided:\", uuid);\n            return false;\n        }\n        try {\n            const response = await fetch(\"/api/users/\".concat(uuid), {\n                headers: {\n                    Authorization: \"Bearer \".concat(token)\n                }\n            });\n            if (response.status === 404) return false;\n            if (!response.ok) throw new Error(\"Failed to check user existence\");\n            return true;\n        } catch (error) {\n            console.error(\"Error checking user existence:\", error);\n            return false;\n        }\n    }, [\n        token\n    ]);\n    const switchWarehouse = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (warehouseUuid, warehouseName)=>{\n        if (!(user === null || user === void 0 ? void 0 : user.uuid) || !token) {\n            throw new Error(\"User not authenticated\");\n        }\n        // Safety check: ensure user UUID is valid\n        if (typeof user.uuid !== \"string\" || user.uuid.trim() === \"\") {\n            throw new Error(\"Invalid user UUID\");\n        }\n        try {\n            // Update user's warehouse on backend\n            const response = await fetch(\"/api/users/\".concat(user.uuid, \"/warehouse\"), {\n                method: \"PATCH\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    Authorization: \"Bearer \".concat(token)\n                },\n                body: JSON.stringify({\n                    warehouseUuid\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to update warehouse on backend\");\n            }\n            // Update user context with new warehouse info\n            const updatedUser = {\n                ...user,\n                warehouseUuid: warehouseUuid,\n                warehouseName: warehouseName\n            };\n            // Update localStorage\n            localStorage.setItem(\"dido_user\", JSON.stringify(updatedUser));\n            // Update context state\n            setUser(updatedUser);\n            console.log(\"Warehouse switched successfully:\", {\n                warehouseUuid,\n                warehouseName\n            });\n        } catch (error) {\n            console.error(\"Error switching warehouse:\", error);\n            throw error;\n        }\n    }, [\n        user,\n        token\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: {\n            user,\n            token,\n            refreshToken,\n            loading,\n            login,\n            logout,\n            refreshAccessToken,\n            getSecurityStatus,\n            fetchAndPersistWarehouseInfo,\n            checkUserExists,\n            switchWarehouse\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 550,\n        columnNumber: 5\n    }, this);\n}\n_s(AuthProvider, \"OjTPxP8iICzioNwr/F7TdAy6Vi8=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = AuthProvider;\nfunction useAuth() {\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (!context) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n_s1(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./contexts/AuthContext.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./utils/networkUtils.ts":
/*!*******************************!*\
  !*** ./utils/networkUtils.ts ***!
  \*******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addNetworkListeners: function() { return /* binding */ addNetworkListeners; },\n/* harmony export */   getConnectionAdvice: function() { return /* binding */ getConnectionAdvice; },\n/* harmony export */   getNetworkInfo: function() { return /* binding */ getNetworkInfo; },\n/* harmony export */   isConnectionSlow: function() { return /* binding */ isConnectionSlow; },\n/* harmony export */   isConnectionUnstable: function() { return /* binding */ isConnectionUnstable; },\n/* harmony export */   retryWithBackoff: function() { return /* binding */ retryWithBackoff; },\n/* harmony export */   testConnectionSpeed: function() { return /* binding */ testConnectionSpeed; }\n/* harmony export */ });\n// Network utility functions for handling connection issues\nfunction getNetworkInfo() {\n    const info = {\n        online: navigator.onLine\n    };\n    // Check for Network Information API\n    if (\"connection\" in navigator) {\n        const conn = navigator.connection;\n        if (conn) {\n            info.connectionType = conn.type;\n            info.effectiveType = conn.effectiveType;\n            info.downlink = conn.downlink;\n            info.rtt = conn.rtt;\n        }\n    }\n    return info;\n}\nfunction isConnectionSlow() {\n    const info = getNetworkInfo();\n    // Check if connection is slow based on effective type\n    if (info.effectiveType) {\n        return [\n            \"slow-2g\",\n            \"2g\",\n            \"3g\"\n        ].includes(info.effectiveType);\n    }\n    // Check if downlink is very slow (less than 1 Mbps)\n    if (info.downlink && info.downlink < 1) {\n        return true;\n    }\n    // Check if RTT is very high (more than 200ms)\n    if (info.rtt && info.rtt > 200) {\n        return true;\n    }\n    return false;\n}\nfunction isConnectionUnstable() {\n    return !navigator.onLine || isConnectionSlow();\n}\nfunction getConnectionAdvice() {\n    if (!navigator.onLine) {\n        return \"No internet connection detected. Please check your network settings.\";\n    }\n    if (isConnectionSlow()) {\n        return \"Your connection is slow. Authentication may take longer than usual.\";\n    }\n    return null;\n}\n// Exponential backoff retry function\nasync function retryWithBackoff(fn) {\n    let maxRetries = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 3, baseDelay = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 1000;\n    let lastError;\n    for(let attempt = 1; attempt <= maxRetries; attempt++){\n        try {\n            return await fn();\n        } catch (error) {\n            lastError = error;\n            if (attempt === maxRetries) {\n                throw lastError;\n            }\n            // Exponential backoff: 1s, 2s, 4s, etc.\n            const delay = baseDelay * Math.pow(2, attempt - 1);\n            await new Promise((resolve)=>setTimeout(resolve, delay));\n        }\n    }\n    throw lastError;\n}\n// Test connection speed by making a simple request\nasync function testConnectionSpeed() {\n    const startTime = Date.now();\n    try {\n        const response = await fetch(\"https://www.google.com/favicon.ico\", {\n            method: \"HEAD\",\n            mode: \"no-cors\",\n            cache: \"no-cache\"\n        });\n        const endTime = Date.now();\n        const responseTime = endTime - startTime;\n        if (responseTime < 1000) {\n            return {\n                responseTime,\n                status: \"good\"\n            };\n        } else if (responseTime < 3000) {\n            return {\n                responseTime,\n                status: \"slow\"\n            };\n        } else {\n            return {\n                responseTime,\n                status: \"unstable\"\n            };\n        }\n    } catch (error) {\n        return {\n            responseTime: Infinity,\n            status: \"unstable\"\n        };\n    }\n}\n// Listen for online/offline events\nfunction addNetworkListeners(onOnline, onOffline) {\n    window.addEventListener(\"online\", onOnline);\n    window.addEventListener(\"offline\", onOffline);\n    // Return cleanup function\n    return ()=>{\n        window.removeEventListener(\"online\", onOnline);\n        window.removeEventListener(\"offline\", onOffline);\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./utils/networkUtils.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \****************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n\nif (true) {\n  (function() {\n'use strict';\n\nvar React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\n// ATTENTION\n// When adding new symbols to this file,\n// Please consider also adding to 'react-devtools-shared/src/backend/ReactSymbols'\n// The Symbol used to tag the ReactElement-like types.\nvar REACT_ELEMENT_TYPE = Symbol.for('react.element');\nvar REACT_PORTAL_TYPE = Symbol.for('react.portal');\nvar REACT_FRAGMENT_TYPE = Symbol.for('react.fragment');\nvar REACT_STRICT_MODE_TYPE = Symbol.for('react.strict_mode');\nvar REACT_PROFILER_TYPE = Symbol.for('react.profiler');\nvar REACT_PROVIDER_TYPE = Symbol.for('react.provider'); // TODO: Delete with enableRenderableContext\n\nvar REACT_CONSUMER_TYPE = Symbol.for('react.consumer');\nvar REACT_CONTEXT_TYPE = Symbol.for('react.context');\nvar REACT_FORWARD_REF_TYPE = Symbol.for('react.forward_ref');\nvar REACT_SUSPENSE_TYPE = Symbol.for('react.suspense');\nvar REACT_SUSPENSE_LIST_TYPE = Symbol.for('react.suspense_list');\nvar REACT_MEMO_TYPE = Symbol.for('react.memo');\nvar REACT_LAZY_TYPE = Symbol.for('react.lazy');\nvar REACT_OFFSCREEN_TYPE = Symbol.for('react.offscreen');\nvar REACT_CACHE_TYPE = Symbol.for('react.cache');\nvar MAYBE_ITERATOR_SYMBOL = Symbol.iterator;\nvar FAUX_ITERATOR_SYMBOL = '@@iterator';\nfunction getIteratorFn(maybeIterable) {\n  if (maybeIterable === null || typeof maybeIterable !== 'object') {\n    return null;\n  }\n\n  var maybeIterator = MAYBE_ITERATOR_SYMBOL && maybeIterable[MAYBE_ITERATOR_SYMBOL] || maybeIterable[FAUX_ITERATOR_SYMBOL];\n\n  if (typeof maybeIterator === 'function') {\n    return maybeIterator;\n  }\n\n  return null;\n}\n\nvar ReactSharedInternals = React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;\n\nfunction error(format) {\n  {\n    {\n      for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n        args[_key2 - 1] = arguments[_key2];\n      }\n\n      printWarning('error', format, args);\n    }\n  }\n}\n\nfunction printWarning(level, format, args) {\n  // When changing this logic, you might want to also\n  // update consoleWithStackDev.www.js as well.\n  {\n    var ReactDebugCurrentFrame = ReactSharedInternals.ReactDebugCurrentFrame;\n    var stack = ReactDebugCurrentFrame.getStackAddendum();\n\n    if (stack !== '') {\n      format += '%s';\n      args = args.concat([stack]);\n    } // eslint-disable-next-line react-internal/safe-string-coercion\n\n\n    var argsWithFormat = args.map(function (item) {\n      return String(item);\n    }); // Careful: RN currently depends on this prefix\n\n    argsWithFormat.unshift('Warning: ' + format); // We intentionally don't use spread (or .apply) directly because it\n    // breaks IE9: https://github.com/facebook/react/issues/13610\n    // eslint-disable-next-line react-internal/no-production-logging\n\n    Function.prototype.apply.call(console[level], console, argsWithFormat);\n  }\n}\n\n// -----------------------------------------------------------------------------\n\nvar enableScopeAPI = false; // Experimental Create Event Handle API.\nvar enableCacheElement = false;\nvar enableTransitionTracing = false; // No known bugs, but needs performance testing\n\nvar enableLegacyHidden = false; // Enables unstable_avoidThisFallback feature in Fiber\nvar enableRenderableContext = false;\n// stuff. Intended to enable React core members to more easily debug scheduling\n// issues in DEV builds.\n\nvar enableDebugTracing = false;\n\nfunction getWrappedName(outerType, innerType, wrapperName) {\n  var displayName = outerType.displayName;\n\n  if (displayName) {\n    return displayName;\n  }\n\n  var functionName = innerType.displayName || innerType.name || '';\n  return functionName !== '' ? wrapperName + \"(\" + functionName + \")\" : wrapperName;\n} // Keep in sync with react-reconciler/getComponentNameFromFiber\n\n\nfunction getContextName(type) {\n  return type.displayName || 'Context';\n}\n\nvar REACT_CLIENT_REFERENCE$2 = Symbol.for('react.client.reference'); // Note that the reconciler package should generally prefer to use getComponentNameFromFiber() instead.\n\nfunction getComponentNameFromType(type) {\n  if (type == null) {\n    // Host root, text node or just invalid type.\n    return null;\n  }\n\n  if (typeof type === 'function') {\n    if (type.$$typeof === REACT_CLIENT_REFERENCE$2) {\n      // TODO: Create a convention for naming client references with debug info.\n      return null;\n    }\n\n    return type.displayName || type.name || null;\n  }\n\n  if (typeof type === 'string') {\n    return type;\n  }\n\n  switch (type) {\n    case REACT_FRAGMENT_TYPE:\n      return 'Fragment';\n\n    case REACT_PORTAL_TYPE:\n      return 'Portal';\n\n    case REACT_PROFILER_TYPE:\n      return 'Profiler';\n\n    case REACT_STRICT_MODE_TYPE:\n      return 'StrictMode';\n\n    case REACT_SUSPENSE_TYPE:\n      return 'Suspense';\n\n    case REACT_SUSPENSE_LIST_TYPE:\n      return 'SuspenseList';\n\n    case REACT_CACHE_TYPE:\n      {\n        return 'Cache';\n      }\n\n  }\n\n  if (typeof type === 'object') {\n    {\n      if (typeof type.tag === 'number') {\n        error('Received an unexpected object in getComponentNameFromType(). ' + 'This is likely a bug in React. Please file an issue.');\n      }\n    }\n\n    switch (type.$$typeof) {\n      case REACT_PROVIDER_TYPE:\n        {\n          var provider = type;\n          return getContextName(provider._context) + '.Provider';\n        }\n\n      case REACT_CONTEXT_TYPE:\n        var context = type;\n\n        {\n          return getContextName(context) + '.Consumer';\n        }\n\n      case REACT_CONSUMER_TYPE:\n        {\n          return null;\n        }\n\n      case REACT_FORWARD_REF_TYPE:\n        return getWrappedName(type, type.render, 'ForwardRef');\n\n      case REACT_MEMO_TYPE:\n        var outerName = type.displayName || null;\n\n        if (outerName !== null) {\n          return outerName;\n        }\n\n        return getComponentNameFromType(type.type) || 'Memo';\n\n      case REACT_LAZY_TYPE:\n        {\n          var lazyComponent = type;\n          var payload = lazyComponent._payload;\n          var init = lazyComponent._init;\n\n          try {\n            return getComponentNameFromType(init(payload));\n          } catch (x) {\n            return null;\n          }\n        }\n    }\n  }\n\n  return null;\n}\n\n// $FlowFixMe[method-unbinding]\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\n\nvar assign = Object.assign;\n\n/*\n * The `'' + value` pattern (used in perf-sensitive code) throws for Symbol\n * and Temporal.* types. See https://github.com/facebook/react/pull/22064.\n *\n * The functions in this module will throw an easier-to-understand,\n * easier-to-debug exception with a clear errors message message explaining the\n * problem. (Instead of a confusing exception thrown inside the implementation\n * of the `value` object).\n */\n// $FlowFixMe[incompatible-return] only called in DEV, so void return is not possible.\nfunction typeName(value) {\n  {\n    // toStringTag is needed for namespaced types like Temporal.Instant\n    var hasToStringTag = typeof Symbol === 'function' && Symbol.toStringTag;\n    var type = hasToStringTag && value[Symbol.toStringTag] || value.constructor.name || 'Object'; // $FlowFixMe[incompatible-return]\n\n    return type;\n  }\n} // $FlowFixMe[incompatible-return] only called in DEV, so void return is not possible.\n\n\nfunction willCoercionThrow(value) {\n  {\n    try {\n      testStringCoercion(value);\n      return false;\n    } catch (e) {\n      return true;\n    }\n  }\n}\n\nfunction testStringCoercion(value) {\n  // If you ended up here by following an exception call stack, here's what's\n  // happened: you supplied an object or symbol value to React (as a prop, key,\n  // DOM attribute, CSS property, string ref, etc.) and when React tried to\n  // coerce it to a string using `'' + value`, an exception was thrown.\n  //\n  // The most common types that will cause this exception are `Symbol` instances\n  // and Temporal objects like `Temporal.Instant`. But any object that has a\n  // `valueOf` or `[Symbol.toPrimitive]` method that throws will also cause this\n  // exception. (Library authors do this to prevent users from using built-in\n  // numeric operators like `+` or comparison operators like `>=` because custom\n  // methods are needed to perform accurate arithmetic or comparison.)\n  //\n  // To fix the problem, coerce this object or symbol value to a string before\n  // passing it to React. The most reliable way is usually `String(value)`.\n  //\n  // To find which value is throwing, check the browser or debugger console.\n  // Before this exception was thrown, there should be `console.error` output\n  // that shows the type (Symbol, Temporal.PlainDate, etc.) that caused the\n  // problem and how that type was used: key, atrribute, input value prop, etc.\n  // In most cases, this console output also shows the component and its\n  // ancestor components where the exception happened.\n  //\n  // eslint-disable-next-line react-internal/safe-string-coercion\n  return '' + value;\n}\nfunction checkKeyStringCoercion(value) {\n  {\n    if (willCoercionThrow(value)) {\n      error('The provided key is an unsupported type %s.' + ' This value must be coerced to a string before using it here.', typeName(value));\n\n      return testStringCoercion(value); // throw (to help callers find troubleshooting comments)\n    }\n  }\n}\n\nvar REACT_CLIENT_REFERENCE$1 = Symbol.for('react.client.reference');\nfunction isValidElementType(type) {\n  if (typeof type === 'string' || typeof type === 'function') {\n    return true;\n  } // Note: typeof might be other than 'symbol' or 'number' (e.g. if it's a polyfill).\n\n\n  if (type === REACT_FRAGMENT_TYPE || type === REACT_PROFILER_TYPE || enableDebugTracing  || type === REACT_STRICT_MODE_TYPE || type === REACT_SUSPENSE_TYPE || type === REACT_SUSPENSE_LIST_TYPE || enableLegacyHidden  || type === REACT_OFFSCREEN_TYPE || enableScopeAPI  || enableCacheElement  || enableTransitionTracing ) {\n    return true;\n  }\n\n  if (typeof type === 'object' && type !== null) {\n    if (type.$$typeof === REACT_LAZY_TYPE || type.$$typeof === REACT_MEMO_TYPE || type.$$typeof === REACT_CONTEXT_TYPE || type.$$typeof === REACT_PROVIDER_TYPE || enableRenderableContext  || type.$$typeof === REACT_FORWARD_REF_TYPE || // This needs to include all possible module reference object\n    // types supported by any Flight configuration anywhere since\n    // we don't know which Flight build this will end up being used\n    // with.\n    type.$$typeof === REACT_CLIENT_REFERENCE$1 || type.getModuleId !== undefined) {\n      return true;\n    }\n  }\n\n  return false;\n}\n\nvar isArrayImpl = Array.isArray; // eslint-disable-next-line no-redeclare\n\nfunction isArray(a) {\n  return isArrayImpl(a);\n}\n\n// Helpers to patch console.logs to avoid logging during side-effect free\n// replaying on render function. This currently only patches the object\n// lazily which won't cover if the log function was extracted eagerly.\n// We could also eagerly patch the method.\nvar disabledDepth = 0;\nvar prevLog;\nvar prevInfo;\nvar prevWarn;\nvar prevError;\nvar prevGroup;\nvar prevGroupCollapsed;\nvar prevGroupEnd;\n\nfunction disabledLog() {}\n\ndisabledLog.__reactDisabledLog = true;\nfunction disableLogs() {\n  {\n    if (disabledDepth === 0) {\n      /* eslint-disable react-internal/no-production-logging */\n      prevLog = console.log;\n      prevInfo = console.info;\n      prevWarn = console.warn;\n      prevError = console.error;\n      prevGroup = console.group;\n      prevGroupCollapsed = console.groupCollapsed;\n      prevGroupEnd = console.groupEnd; // https://github.com/facebook/react/issues/19099\n\n      var props = {\n        configurable: true,\n        enumerable: true,\n        value: disabledLog,\n        writable: true\n      }; // $FlowFixMe[cannot-write] Flow thinks console is immutable.\n\n      Object.defineProperties(console, {\n        info: props,\n        log: props,\n        warn: props,\n        error: props,\n        group: props,\n        groupCollapsed: props,\n        groupEnd: props\n      });\n      /* eslint-enable react-internal/no-production-logging */\n    }\n\n    disabledDepth++;\n  }\n}\nfunction reenableLogs() {\n  {\n    disabledDepth--;\n\n    if (disabledDepth === 0) {\n      /* eslint-disable react-internal/no-production-logging */\n      var props = {\n        configurable: true,\n        enumerable: true,\n        writable: true\n      }; // $FlowFixMe[cannot-write] Flow thinks console is immutable.\n\n      Object.defineProperties(console, {\n        log: assign({}, props, {\n          value: prevLog\n        }),\n        info: assign({}, props, {\n          value: prevInfo\n        }),\n        warn: assign({}, props, {\n          value: prevWarn\n        }),\n        error: assign({}, props, {\n          value: prevError\n        }),\n        group: assign({}, props, {\n          value: prevGroup\n        }),\n        groupCollapsed: assign({}, props, {\n          value: prevGroupCollapsed\n        }),\n        groupEnd: assign({}, props, {\n          value: prevGroupEnd\n        })\n      });\n      /* eslint-enable react-internal/no-production-logging */\n    }\n\n    if (disabledDepth < 0) {\n      error('disabledDepth fell below zero. ' + 'This is a bug in React. Please file an issue.');\n    }\n  }\n}\n\nvar ReactCurrentDispatcher = ReactSharedInternals.ReactCurrentDispatcher;\nvar prefix;\nfunction describeBuiltInComponentFrame(name, ownerFn) {\n  {\n    if (prefix === undefined) {\n      // Extract the VM specific prefix used by each line.\n      try {\n        throw Error();\n      } catch (x) {\n        var match = x.stack.trim().match(/\\n( *(at )?)/);\n        prefix = match && match[1] || '';\n      }\n    } // We use the prefix to ensure our stacks line up with native stack frames.\n\n\n    return '\\n' + prefix + name;\n  }\n}\nvar reentry = false;\nvar componentFrameCache;\n\n{\n  var PossiblyWeakMap = typeof WeakMap === 'function' ? WeakMap : Map;\n  componentFrameCache = new PossiblyWeakMap();\n}\n/**\n * Leverages native browser/VM stack frames to get proper details (e.g.\n * filename, line + col number) for a single component in a component stack. We\n * do this by:\n *   (1) throwing and catching an error in the function - this will be our\n *       control error.\n *   (2) calling the component which will eventually throw an error that we'll\n *       catch - this will be our sample error.\n *   (3) diffing the control and sample error stacks to find the stack frame\n *       which represents our component.\n */\n\n\nfunction describeNativeComponentFrame(fn, construct) {\n  // If something asked for a stack inside a fake render, it should get ignored.\n  if (!fn || reentry) {\n    return '';\n  }\n\n  {\n    var frame = componentFrameCache.get(fn);\n\n    if (frame !== undefined) {\n      return frame;\n    }\n  }\n\n  reentry = true;\n  var previousPrepareStackTrace = Error.prepareStackTrace; // $FlowFixMe[incompatible-type] It does accept undefined.\n\n  Error.prepareStackTrace = undefined;\n  var previousDispatcher;\n\n  {\n    previousDispatcher = ReactCurrentDispatcher.current; // Set the dispatcher in DEV because this might be call in the render function\n    // for warnings.\n\n    ReactCurrentDispatcher.current = null;\n    disableLogs();\n  }\n  /**\n   * Finding a common stack frame between sample and control errors can be\n   * tricky given the different types and levels of stack trace truncation from\n   * different JS VMs. So instead we'll attempt to control what that common\n   * frame should be through this object method:\n   * Having both the sample and control errors be in the function under the\n   * `DescribeNativeComponentFrameRoot` property, + setting the `name` and\n   * `displayName` properties of the function ensures that a stack\n   * frame exists that has the method name `DescribeNativeComponentFrameRoot` in\n   * it for both control and sample stacks.\n   */\n\n\n  var RunInRootFrame = {\n    DetermineComponentFrameRoot: function () {\n      var control;\n\n      try {\n        // This should throw.\n        if (construct) {\n          // Something should be setting the props in the constructor.\n          var Fake = function () {\n            throw Error();\n          }; // $FlowFixMe[prop-missing]\n\n\n          Object.defineProperty(Fake.prototype, 'props', {\n            set: function () {\n              // We use a throwing setter instead of frozen or non-writable props\n              // because that won't throw in a non-strict mode function.\n              throw Error();\n            }\n          });\n\n          if (typeof Reflect === 'object' && Reflect.construct) {\n            // We construct a different control for this case to include any extra\n            // frames added by the construct call.\n            try {\n              Reflect.construct(Fake, []);\n            } catch (x) {\n              control = x;\n            }\n\n            Reflect.construct(fn, [], Fake);\n          } else {\n            try {\n              Fake.call();\n            } catch (x) {\n              control = x;\n            } // $FlowFixMe[prop-missing] found when upgrading Flow\n\n\n            fn.call(Fake.prototype);\n          }\n        } else {\n          try {\n            throw Error();\n          } catch (x) {\n            control = x;\n          } // TODO(luna): This will currently only throw if the function component\n          // tries to access React/ReactDOM/props. We should probably make this throw\n          // in simple components too\n\n\n          var maybePromise = fn(); // If the function component returns a promise, it's likely an async\n          // component, which we don't yet support. Attach a noop catch handler to\n          // silence the error.\n          // TODO: Implement component stacks for async client components?\n\n          if (maybePromise && typeof maybePromise.catch === 'function') {\n            maybePromise.catch(function () {});\n          }\n        }\n      } catch (sample) {\n        // This is inlined manually because closure doesn't do it for us.\n        if (sample && control && typeof sample.stack === 'string') {\n          return [sample.stack, control.stack];\n        }\n      }\n\n      return [null, null];\n    }\n  }; // $FlowFixMe[prop-missing]\n\n  RunInRootFrame.DetermineComponentFrameRoot.displayName = 'DetermineComponentFrameRoot';\n  var namePropDescriptor = Object.getOwnPropertyDescriptor(RunInRootFrame.DetermineComponentFrameRoot, 'name'); // Before ES6, the `name` property was not configurable.\n\n  if (namePropDescriptor && namePropDescriptor.configurable) {\n    // V8 utilizes a function's `name` property when generating a stack trace.\n    Object.defineProperty(RunInRootFrame.DetermineComponentFrameRoot, // Configurable properties can be updated even if its writable descriptor\n    // is set to `false`.\n    // $FlowFixMe[cannot-write]\n    'name', {\n      value: 'DetermineComponentFrameRoot'\n    });\n  }\n\n  try {\n    var _RunInRootFrame$Deter = RunInRootFrame.DetermineComponentFrameRoot(),\n        sampleStack = _RunInRootFrame$Deter[0],\n        controlStack = _RunInRootFrame$Deter[1];\n\n    if (sampleStack && controlStack) {\n      // This extracts the first frame from the sample that isn't also in the control.\n      // Skipping one frame that we assume is the frame that calls the two.\n      var sampleLines = sampleStack.split('\\n');\n      var controlLines = controlStack.split('\\n');\n      var s = 0;\n      var c = 0;\n\n      while (s < sampleLines.length && !sampleLines[s].includes('DetermineComponentFrameRoot')) {\n        s++;\n      }\n\n      while (c < controlLines.length && !controlLines[c].includes('DetermineComponentFrameRoot')) {\n        c++;\n      } // We couldn't find our intentionally injected common root frame, attempt\n      // to find another common root frame by search from the bottom of the\n      // control stack...\n\n\n      if (s === sampleLines.length || c === controlLines.length) {\n        s = sampleLines.length - 1;\n        c = controlLines.length - 1;\n\n        while (s >= 1 && c >= 0 && sampleLines[s] !== controlLines[c]) {\n          // We expect at least one stack frame to be shared.\n          // Typically this will be the root most one. However, stack frames may be\n          // cut off due to maximum stack limits. In this case, one maybe cut off\n          // earlier than the other. We assume that the sample is longer or the same\n          // and there for cut off earlier. So we should find the root most frame in\n          // the sample somewhere in the control.\n          c--;\n        }\n      }\n\n      for (; s >= 1 && c >= 0; s--, c--) {\n        // Next we find the first one that isn't the same which should be the\n        // frame that called our sample function and the control.\n        if (sampleLines[s] !== controlLines[c]) {\n          // In V8, the first line is describing the message but other VMs don't.\n          // If we're about to return the first line, and the control is also on the same\n          // line, that's a pretty good indicator that our sample threw at same line as\n          // the control. I.e. before we entered the sample frame. So we ignore this result.\n          // This can happen if you passed a class to function component, or non-function.\n          if (s !== 1 || c !== 1) {\n            do {\n              s--;\n              c--; // We may still have similar intermediate frames from the construct call.\n              // The next one that isn't the same should be our match though.\n\n              if (c < 0 || sampleLines[s] !== controlLines[c]) {\n                // V8 adds a \"new\" prefix for native classes. Let's remove it to make it prettier.\n                var _frame = '\\n' + sampleLines[s].replace(' at new ', ' at '); // If our component frame is labeled \"<anonymous>\"\n                // but we have a user-provided \"displayName\"\n                // splice it in to make the stack more readable.\n\n\n                if (fn.displayName && _frame.includes('<anonymous>')) {\n                  _frame = _frame.replace('<anonymous>', fn.displayName);\n                }\n\n                if (true) {\n                  if (typeof fn === 'function') {\n                    componentFrameCache.set(fn, _frame);\n                  }\n                } // Return the line we found.\n\n\n                return _frame;\n              }\n            } while (s >= 1 && c >= 0);\n          }\n\n          break;\n        }\n      }\n    }\n  } finally {\n    reentry = false;\n\n    {\n      ReactCurrentDispatcher.current = previousDispatcher;\n      reenableLogs();\n    }\n\n    Error.prepareStackTrace = previousPrepareStackTrace;\n  } // Fallback to just using the name if we couldn't make it throw.\n\n\n  var name = fn ? fn.displayName || fn.name : '';\n  var syntheticFrame = name ? describeBuiltInComponentFrame(name) : '';\n\n  {\n    if (typeof fn === 'function') {\n      componentFrameCache.set(fn, syntheticFrame);\n    }\n  }\n\n  return syntheticFrame;\n}\nfunction describeFunctionComponentFrame(fn, ownerFn) {\n  {\n    return describeNativeComponentFrame(fn, false);\n  }\n}\n\nfunction shouldConstruct(Component) {\n  var prototype = Component.prototype;\n  return !!(prototype && prototype.isReactComponent);\n}\n\nfunction describeUnknownElementTypeFrameInDEV(type, ownerFn) {\n\n  if (type == null) {\n    return '';\n  }\n\n  if (typeof type === 'function') {\n    {\n      return describeNativeComponentFrame(type, shouldConstruct(type));\n    }\n  }\n\n  if (typeof type === 'string') {\n    return describeBuiltInComponentFrame(type);\n  }\n\n  switch (type) {\n    case REACT_SUSPENSE_TYPE:\n      return describeBuiltInComponentFrame('Suspense');\n\n    case REACT_SUSPENSE_LIST_TYPE:\n      return describeBuiltInComponentFrame('SuspenseList');\n  }\n\n  if (typeof type === 'object') {\n    switch (type.$$typeof) {\n      case REACT_FORWARD_REF_TYPE:\n        return describeFunctionComponentFrame(type.render);\n\n      case REACT_MEMO_TYPE:\n        // Memo may contain any component type so we recursively resolve it.\n        return describeUnknownElementTypeFrameInDEV(type.type, ownerFn);\n\n      case REACT_LAZY_TYPE:\n        {\n          var lazyComponent = type;\n          var payload = lazyComponent._payload;\n          var init = lazyComponent._init;\n\n          try {\n            // Lazy may contain any component type so we recursively resolve it.\n            return describeUnknownElementTypeFrameInDEV(init(payload), ownerFn);\n          } catch (x) {}\n        }\n    }\n  }\n\n  return '';\n}\n\nvar ReactCurrentOwner = ReactSharedInternals.ReactCurrentOwner;\nvar ReactDebugCurrentFrame = ReactSharedInternals.ReactDebugCurrentFrame;\nvar REACT_CLIENT_REFERENCE = Symbol.for('react.client.reference');\nvar specialPropKeyWarningShown;\nvar specialPropRefWarningShown;\nvar didWarnAboutStringRefs;\n\n{\n  didWarnAboutStringRefs = {};\n}\n\nfunction hasValidRef(config) {\n  {\n    if (hasOwnProperty.call(config, 'ref')) {\n      var getter = Object.getOwnPropertyDescriptor(config, 'ref').get;\n\n      if (getter && getter.isReactWarning) {\n        return false;\n      }\n    }\n  }\n\n  return config.ref !== undefined;\n}\n\nfunction hasValidKey(config) {\n  {\n    if (hasOwnProperty.call(config, 'key')) {\n      var getter = Object.getOwnPropertyDescriptor(config, 'key').get;\n\n      if (getter && getter.isReactWarning) {\n        return false;\n      }\n    }\n  }\n\n  return config.key !== undefined;\n}\n\nfunction warnIfStringRefCannotBeAutoConverted(config, self) {\n  {\n    if (typeof config.ref === 'string' && ReactCurrentOwner.current && self && ReactCurrentOwner.current.stateNode !== self) {\n      var componentName = getComponentNameFromType(ReactCurrentOwner.current.type);\n\n      if (!didWarnAboutStringRefs[componentName]) {\n        error('Component \"%s\" contains the string ref \"%s\". ' + 'Support for string refs will be removed in a future major release. ' + 'This case cannot be automatically converted to an arrow function. ' + 'We ask you to manually fix this case by using useRef() or createRef() instead. ' + 'Learn more about using refs safely here: ' + 'https://reactjs.org/link/strict-mode-string-ref', getComponentNameFromType(ReactCurrentOwner.current.type), config.ref);\n\n        didWarnAboutStringRefs[componentName] = true;\n      }\n    }\n  }\n}\n\nfunction defineKeyPropWarningGetter(props, displayName) {\n  {\n    var warnAboutAccessingKey = function () {\n      if (!specialPropKeyWarningShown) {\n        specialPropKeyWarningShown = true;\n\n        error('%s: `key` is not a prop. Trying to access it will result ' + 'in `undefined` being returned. If you need to access the same ' + 'value within the child component, you should pass it as a different ' + 'prop. (https://reactjs.org/link/special-props)', displayName);\n      }\n    };\n\n    warnAboutAccessingKey.isReactWarning = true;\n    Object.defineProperty(props, 'key', {\n      get: warnAboutAccessingKey,\n      configurable: true\n    });\n  }\n}\n\nfunction defineRefPropWarningGetter(props, displayName) {\n  {\n    {\n      var warnAboutAccessingRef = function () {\n        if (!specialPropRefWarningShown) {\n          specialPropRefWarningShown = true;\n\n          error('%s: `ref` is not a prop. Trying to access it will result ' + 'in `undefined` being returned. If you need to access the same ' + 'value within the child component, you should pass it as a different ' + 'prop. (https://reactjs.org/link/special-props)', displayName);\n        }\n      };\n\n      warnAboutAccessingRef.isReactWarning = true;\n      Object.defineProperty(props, 'ref', {\n        get: warnAboutAccessingRef,\n        configurable: true\n      });\n    }\n  }\n}\n/**\n * Factory method to create a new React element. This no longer adheres to\n * the class pattern, so do not use new to call it. Also, instanceof check\n * will not work. Instead test $$typeof field against Symbol.for('react.element') to check\n * if something is a React Element.\n *\n * @param {*} type\n * @param {*} props\n * @param {*} key\n * @param {string|object} ref\n * @param {*} owner\n * @param {*} self A *temporary* helper to detect places where `this` is\n * different from the `owner` when React.createElement is called, so that we\n * can warn. We want to get rid of owner and replace string `ref`s with arrow\n * functions, and as long as `this` and owner are the same, there will be no\n * change in behavior.\n * @param {*} source An annotation object (added by a transpiler or otherwise)\n * indicating filename, line number, and/or other information.\n * @internal\n */\n\n\nfunction ReactElement(type, key, _ref, self, source, owner, props) {\n  var ref;\n\n  {\n    ref = _ref;\n  }\n\n  var element;\n\n  {\n    // In prod, `ref` is a regular property. It will be removed in a\n    // future release.\n    element = {\n      // This tag allows us to uniquely identify this as a React Element\n      $$typeof: REACT_ELEMENT_TYPE,\n      // Built-in properties that belong on the element\n      type: type,\n      key: key,\n      ref: ref,\n      props: props,\n      // Record the component responsible for creating this element.\n      _owner: owner\n    };\n  }\n\n  {\n    // The validation flag is currently mutative. We put it on\n    // an external backing store so that we can freeze the whole object.\n    // This can be replaced with a WeakMap once they are implemented in\n    // commonly used development environments.\n    element._store = {}; // To make comparing ReactElements easier for testing purposes, we make\n    // the validation flag non-enumerable (where possible, which should\n    // include every environment we run tests in), so the test framework\n    // ignores it.\n\n    Object.defineProperty(element._store, 'validated', {\n      configurable: false,\n      enumerable: false,\n      writable: true,\n      value: false\n    }); // debugInfo contains Server Component debug information.\n\n    Object.defineProperty(element, '_debugInfo', {\n      configurable: false,\n      enumerable: false,\n      writable: true,\n      value: null\n    });\n\n    if (Object.freeze) {\n      Object.freeze(element.props);\n      Object.freeze(element);\n    }\n  }\n\n  return element;\n}\nvar didWarnAboutKeySpread = {};\n/**\n * https://github.com/reactjs/rfcs/pull/107\n * @param {*} type\n * @param {object} props\n * @param {string} key\n */\n\nfunction jsxDEV$1(type, config, maybeKey, isStaticChildren, source, self) {\n  {\n    if (!isValidElementType(type)) {\n      // This is an invalid element type.\n      //\n      // We warn in this case but don't throw. We expect the element creation to\n      // succeed and there will likely be errors in render.\n      var info = '';\n\n      if (type === undefined || typeof type === 'object' && type !== null && Object.keys(type).length === 0) {\n        info += ' You likely forgot to export your component from the file ' + \"it's defined in, or you might have mixed up default and named imports.\";\n      }\n\n      var typeString;\n\n      if (type === null) {\n        typeString = 'null';\n      } else if (isArray(type)) {\n        typeString = 'array';\n      } else if (type !== undefined && type.$$typeof === REACT_ELEMENT_TYPE) {\n        typeString = \"<\" + (getComponentNameFromType(type.type) || 'Unknown') + \" />\";\n        info = ' Did you accidentally export a JSX literal instead of a component?';\n      } else {\n        typeString = typeof type;\n      }\n\n      error('React.jsx: type is invalid -- expected a string (for ' + 'built-in components) or a class/function (for composite ' + 'components) but got: %s.%s', typeString, info);\n    } else {\n      // This is a valid element type.\n      // Skip key warning if the type isn't valid since our key validation logic\n      // doesn't expect a non-string/function type and can throw confusing\n      // errors. We don't want exception behavior to differ between dev and\n      // prod. (Rendering will throw with a helpful message and as soon as the\n      // type is fixed, the key warnings will appear.)\n      var children = config.children;\n\n      if (children !== undefined) {\n        if (isStaticChildren) {\n          if (isArray(children)) {\n            for (var i = 0; i < children.length; i++) {\n              validateChildKeys(children[i], type);\n            }\n\n            if (Object.freeze) {\n              Object.freeze(children);\n            }\n          } else {\n            error('React.jsx: Static children should always be an array. ' + 'You are likely explicitly calling React.jsxs or React.jsxDEV. ' + 'Use the Babel transform instead.');\n          }\n        } else {\n          validateChildKeys(children, type);\n        }\n      }\n    } // Warn about key spread regardless of whether the type is valid.\n\n\n    if (hasOwnProperty.call(config, 'key')) {\n      var componentName = getComponentNameFromType(type);\n      var keys = Object.keys(config).filter(function (k) {\n        return k !== 'key';\n      });\n      var beforeExample = keys.length > 0 ? '{key: someKey, ' + keys.join(': ..., ') + ': ...}' : '{key: someKey}';\n\n      if (!didWarnAboutKeySpread[componentName + beforeExample]) {\n        var afterExample = keys.length > 0 ? '{' + keys.join(': ..., ') + ': ...}' : '{}';\n\n        error('A props object containing a \"key\" prop is being spread into JSX:\\n' + '  let props = %s;\\n' + '  <%s {...props} />\\n' + 'React keys must be passed directly to JSX without using spread:\\n' + '  let props = %s;\\n' + '  <%s key={someKey} {...props} />', beforeExample, componentName, afterExample, componentName);\n\n        didWarnAboutKeySpread[componentName + beforeExample] = true;\n      }\n    }\n\n    var propName; // Reserved names are extracted\n\n    var props = {};\n    var key = null;\n    var ref = null; // Currently, key can be spread in as a prop. This causes a potential\n    // issue if key is also explicitly declared (ie. <div {...props} key=\"Hi\" />\n    // or <div key=\"Hi\" {...props} /> ). We want to deprecate key spread,\n    // but as an intermediary step, we will use jsxDEV for everything except\n    // <div {...props} key=\"Hi\" />, because we aren't currently able to tell if\n    // key is explicitly declared to be undefined or not.\n\n    if (maybeKey !== undefined) {\n      {\n        checkKeyStringCoercion(maybeKey);\n      }\n\n      key = '' + maybeKey;\n    }\n\n    if (hasValidKey(config)) {\n      {\n        checkKeyStringCoercion(config.key);\n      }\n\n      key = '' + config.key;\n    }\n\n    if (hasValidRef(config)) {\n      {\n        ref = config.ref;\n      }\n\n      warnIfStringRefCannotBeAutoConverted(config, self);\n    } // Remaining properties are added to a new props object\n\n\n    for (propName in config) {\n      if (hasOwnProperty.call(config, propName) && // Skip over reserved prop names\n      propName !== 'key' && (propName !== 'ref')) {\n        props[propName] = config[propName];\n      }\n    } // Resolve default props\n\n\n    if (type && type.defaultProps) {\n      var defaultProps = type.defaultProps;\n\n      for (propName in defaultProps) {\n        if (props[propName] === undefined) {\n          props[propName] = defaultProps[propName];\n        }\n      }\n    }\n\n    if (key || ref) {\n      var displayName = typeof type === 'function' ? type.displayName || type.name || 'Unknown' : type;\n\n      if (key) {\n        defineKeyPropWarningGetter(props, displayName);\n      }\n\n      if (ref) {\n        defineRefPropWarningGetter(props, displayName);\n      }\n    }\n\n    var element = ReactElement(type, key, ref, self, source, ReactCurrentOwner.current, props);\n\n    if (type === REACT_FRAGMENT_TYPE) {\n      validateFragmentProps(element);\n    }\n\n    return element;\n  }\n}\n\nfunction getDeclarationErrorAddendum() {\n  {\n    if (ReactCurrentOwner.current) {\n      var name = getComponentNameFromType(ReactCurrentOwner.current.type);\n\n      if (name) {\n        return '\\n\\nCheck the render method of `' + name + '`.';\n      }\n    }\n\n    return '';\n  }\n}\n/**\n * Ensure that every element either is passed in a static location, in an\n * array with an explicit keys property defined, or in an object literal\n * with valid key property.\n *\n * @internal\n * @param {ReactNode} node Statically passed child of any type.\n * @param {*} parentType node's parent's type.\n */\n\n\nfunction validateChildKeys(node, parentType) {\n  {\n    if (typeof node !== 'object' || !node) {\n      return;\n    }\n\n    if (node.$$typeof === REACT_CLIENT_REFERENCE) ; else if (isArray(node)) {\n      for (var i = 0; i < node.length; i++) {\n        var child = node[i];\n\n        if (isValidElement(child)) {\n          validateExplicitKey(child, parentType);\n        }\n      }\n    } else if (isValidElement(node)) {\n      // This element was passed in a valid location.\n      if (node._store) {\n        node._store.validated = true;\n      }\n    } else {\n      var iteratorFn = getIteratorFn(node);\n\n      if (typeof iteratorFn === 'function') {\n        // Entry iterators used to provide implicit keys,\n        // but now we print a separate warning for them later.\n        if (iteratorFn !== node.entries) {\n          var iterator = iteratorFn.call(node);\n          var step;\n\n          while (!(step = iterator.next()).done) {\n            if (isValidElement(step.value)) {\n              validateExplicitKey(step.value, parentType);\n            }\n          }\n        }\n      }\n    }\n  }\n}\n/**\n * Verifies the object is a ReactElement.\n * See https://reactjs.org/docs/react-api.html#isvalidelement\n * @param {?object} object\n * @return {boolean} True if `object` is a ReactElement.\n * @final\n */\n\n\nfunction isValidElement(object) {\n  return typeof object === 'object' && object !== null && object.$$typeof === REACT_ELEMENT_TYPE;\n}\nvar ownerHasKeyUseWarning = {};\n/**\n * Warn if the element doesn't have an explicit key assigned to it.\n * This element is in an array. The array could grow and shrink or be\n * reordered. All children that haven't already been validated are required to\n * have a \"key\" property assigned to it. Error statuses are cached so a warning\n * will only be shown once.\n *\n * @internal\n * @param {ReactElement} element Element that requires a key.\n * @param {*} parentType element's parent's type.\n */\n\nfunction validateExplicitKey(element, parentType) {\n  {\n    if (!element._store || element._store.validated || element.key != null) {\n      return;\n    }\n\n    element._store.validated = true;\n    var currentComponentErrorInfo = getCurrentComponentErrorInfo(parentType);\n\n    if (ownerHasKeyUseWarning[currentComponentErrorInfo]) {\n      return;\n    }\n\n    ownerHasKeyUseWarning[currentComponentErrorInfo] = true; // Usually the current owner is the offender, but if it accepts children as a\n    // property, it may be the creator of the child that's responsible for\n    // assigning it a key.\n\n    var childOwner = '';\n\n    if (element && element._owner && element._owner !== ReactCurrentOwner.current) {\n      // Give the component that originally created this child.\n      childOwner = \" It was passed a child from \" + getComponentNameFromType(element._owner.type) + \".\";\n    }\n\n    setCurrentlyValidatingElement(element);\n\n    error('Each child in a list should have a unique \"key\" prop.' + '%s%s See https://reactjs.org/link/warning-keys for more information.', currentComponentErrorInfo, childOwner);\n\n    setCurrentlyValidatingElement(null);\n  }\n}\n\nfunction setCurrentlyValidatingElement(element) {\n  {\n    if (element) {\n      var owner = element._owner;\n      var stack = describeUnknownElementTypeFrameInDEV(element.type, owner ? owner.type : null);\n      ReactDebugCurrentFrame.setExtraStackFrame(stack);\n    } else {\n      ReactDebugCurrentFrame.setExtraStackFrame(null);\n    }\n  }\n}\n\nfunction getCurrentComponentErrorInfo(parentType) {\n  {\n    var info = getDeclarationErrorAddendum();\n\n    if (!info) {\n      var parentName = getComponentNameFromType(parentType);\n\n      if (parentName) {\n        info = \"\\n\\nCheck the top-level render call using <\" + parentName + \">.\";\n      }\n    }\n\n    return info;\n  }\n}\n/**\n * Given a fragment, validate that it can only be provided with fragment props\n * @param {ReactElement} fragment\n */\n\n\nfunction validateFragmentProps(fragment) {\n  // TODO: Move this to render phase instead of at element creation.\n  {\n    var keys = Object.keys(fragment.props);\n\n    for (var i = 0; i < keys.length; i++) {\n      var key = keys[i];\n\n      if (key !== 'children' && key !== 'key') {\n        setCurrentlyValidatingElement(fragment);\n\n        error('Invalid prop `%s` supplied to `React.Fragment`. ' + 'React.Fragment can only have `key` and `children` props.', key);\n\n        setCurrentlyValidatingElement(null);\n        break;\n      }\n    }\n\n    if (fragment.ref !== null) {\n      setCurrentlyValidatingElement(fragment);\n\n      error('Invalid attribute `ref` supplied to `React.Fragment`.');\n\n      setCurrentlyValidatingElement(null);\n    }\n  }\n}\n\nvar jsxDEV = jsxDEV$1 ;\n\nexports.Fragment = REACT_FRAGMENT_TYPE;\nexports.jsxDEV = jsxDEV;\n  })();\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \******************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSw4TEFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jb21waWxlZC9yZWFjdC9qc3gtZGV2LXJ1bnRpbWUuanM/YTk1MyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ3Byb2R1Y3Rpb24nKSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9janMvcmVhY3QtanN4LWRldi1ydW50aW1lLnByb2R1Y3Rpb24ubWluLmpzJyk7XG59IGVsc2Uge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vY2pzL3JlYWN0LWpzeC1kZXYtcnVudGltZS5kZXZlbG9wbWVudC5qcycpO1xufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["main-app"], function() { return __webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmahll%5C%5CDocuments%5C%5Cworkspace%5C%5Cprojects%5C%5Cdido-distribution%5C%5Cfrontend%5C%5Capp%5C%5Cauth%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);