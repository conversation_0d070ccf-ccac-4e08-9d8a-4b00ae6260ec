import { ApiProperty } from "@nestjs/swagger";

export class VanDto {
  @ApiProperty({ example: "uuid-string", description: "Van UUID" })
  uuid: string;

  @ApiProperty({ example: "Van 001", description: "Van name" })
  name: string;

  @ApiProperty({
    example: "warehouse-uuid-string",
    description: "Warehouse UUID",
  })
  warehouseUuid: string;

  @ApiProperty({ example: "storage-uuid-string", description: "Storage UUID" })
  storageUuid: string;

  @ApiProperty({
    example: "ABC-123",
    description: "Van license plate",
    required: false,
  })
  licensePlate?: string;

  @ApiProperty({
    example: "Ford Transit",
    description: "Van model",
    required: false,
  })
  model?: string;

  @ApiProperty({ example: 2020, description: "Van year", required: false })
  year?: number;

  @ApiProperty({
    example: "2025-06-17T19:36:22.000Z",
    description: "Creation timestamp",
  })
  createdAt: Date;

  @ApiProperty({
    example: "2025-06-17T19:36:22.000Z",
    description: "Last update timestamp",
  })
  updatedAt: Date;

  @ApiProperty({ example: false, description: "Soft delete flag" })
  isDeleted: boolean;
}
