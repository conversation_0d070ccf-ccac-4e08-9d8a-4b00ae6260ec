import {
  IsString,
  IsOptional,
  IsUUID,
  IsArray,
  IsNumber,
  ValidateNested,
  ArrayMinSize,
} from "class-validator";
import { Type } from "class-transformer";
import { ApiProperty } from "@nestjs/swagger";

export class CustomerLocationDto {
  @IsNumber()
  @ApiProperty({
    example: 40.7128,
    description: "Latitude coordinate of the customer location",
  })
  latitude: number;

  @IsNumber()
  @ApiProperty({
    example: -74.006,
    description: "Longitude coordinate of the customer location",
  })
  longitude: number;

  @IsString()
  @IsOptional()
  @ApiProperty({
    example: "Customer A",
    description: "Name of the customer (optional)",
    required: false,
  })
  customerName?: string;
}

export class ComputeRouteDto {
  @IsString()
  @ApiProperty({
    example: "Morning Delivery Route",
    description: "Route name (required)",
  })
  name: string;

  @IsString()
  @IsOptional()
  @ApiProperty({
    example: "Optimized route for morning deliveries in downtown area",
    description: "Route description (optional)",
    required: false,
  })
  description?: string;

  @IsArray()
  @ArrayMinSize(2, {
    message: "At least 2 customer locations are required to compute a route",
  })
  @ValidateNested({ each: true })
  @Type(() => CustomerLocationDto)
  @ApiProperty({
    example: [
      { latitude: 40.7128, longitude: -74.006, customerName: "Customer A" },
      { latitude: 40.7589, longitude: -73.9851, customerName: "Customer B" },
      { latitude: 40.7505, longitude: -73.9934, customerName: "Customer C" },
    ],
    description:
      "Array of customer locations to optimize route for (minimum 2 required)",
    type: [CustomerLocationDto],
  })
  customerLocations: CustomerLocationDto[];

  @IsUUID("all")
  @ApiProperty({
    example: "uuid-v7-string",
    description: "UUID of the warehouse this route belongs to (required)",
  })
  warehouseUuid: string;
}
