import { ApiProperty } from "@nestjs/swagger";
import { mapEntityIdToUuid, mapUuidFieldToString } from "../../common/dto-utils";

export class WarehouseDto {
  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the warehouse (primary key)",
  })
  uuid: string;

  @ApiProperty({
    example: "Acme Warehouse",
    description: "Warehouse name",
    required: false,
  })
  name?: string;

  @ApiProperty({
    example: "Main distribution center for Acme Corporation",
    description: "Warehouse description",
    required: false,
  })
  description?: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the user who owns this warehouse",
  })
  userUuidString: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the main storage location for this warehouse",
    required: false,
  })
  mainStorageUuidString?: string;

  @ApiProperty({ example: false, description: "Soft delete flag" })
  isDeleted: boolean;

  @ApiProperty({
    example: "2024-01-15T10:30:00.000Z",
    description: "Creation timestamp",
  })
  createdAt: Date;

  @ApiProperty({
    example: "2024-01-15T10:30:00.000Z",
    description: "Last update timestamp",
  })
  updatedAt: Date;
}

export function toWarehouseDto(entity: any): WarehouseDto {
  return {
    uuid: mapEntityIdToUuid(entity),
    name: entity.name,
    description: entity.description,
    userUuidString: mapUuidFieldToString(entity, 'userUuid'),
    mainStorageUuidString: mapUuidFieldToString(entity, 'mainStorageUuid'),
    isDeleted: entity.isDeleted,
    createdAt: entity.createdAt,
    updatedAt: entity.updatedAt,
  };
}
