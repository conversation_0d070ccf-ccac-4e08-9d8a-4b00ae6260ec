import React, { useEffect, useRef, useState } from 'react';
import { Dialog } from '@headlessui/react';
import { useQuery } from '@tanstack/react-query';
import { fetchUserById, fetchRoles, User, Role } from '../usersApi';
import { listVans, VanDto } from '@/app/logistics/vans/vansApi';
import { useAuth } from '@/contexts/AuthContext';
import QRCode from 'qrcode';

interface UserDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  userUuid: string | null;
}

const UserDetailsModal: React.FC<UserDetailsModalProps> = ({ isOpen, onClose, userUuid }) => {
  const { user: currentUser } = useAuth();
  const warehouseUuid = currentUser?.warehouseUuid;
  const modalRef = useRef<HTMLDivElement>(null);
  const [qrCodeDataUrl, setQrCodeDataUrl] = useState<string>('');

  // Fetch user details
  const { data: user, isLoading: loadingUser, error: userError } = useQuery<User, Error>({
    queryKey: ['user', userUuid],
    queryFn: () => {
      if (!userUuid) throw new Error("User UUID is required");
      return fetchUserById(userUuid);
    },
    enabled: !!userUuid && isOpen,
  });

  // Fetch roles for role name lookup
  const { data: roles = [] } = useQuery<Role[], Error>({
    queryKey: ['roles', warehouseUuid],
    queryFn: () => {
      if (!warehouseUuid) throw new Error("Warehouse UUID is not available.");
      return fetchRoles(warehouseUuid);
    },
    enabled: !!warehouseUuid && isOpen,
  });

  // Fetch vans for van name lookup
  const { data: vans = [] } = useQuery<VanDto[], Error>({
    queryKey: ['vans', warehouseUuid],
    queryFn: () => {
      if (!warehouseUuid) throw new Error("Warehouse UUID is not available.");
      return listVans({ warehouseUuid });
    },
    enabled: !!warehouseUuid && isOpen,
  });

  // Generate QR code when user data is available
  useEffect(() => {
    if (user?.uuid && isOpen) {
      QRCode.toDataURL(user.uuid, {
        width: 200,
        margin: 2,
        color: {
          dark: '#000000',
          light: '#FFFFFF'
        }
      })
      .then(url => setQrCodeDataUrl(url))
      .catch(err => console.error('Error generating QR code:', err));
    }
  }, [user?.uuid, isOpen]);

  // Handle escape key and focus management
  useEffect(() => {
    if (!isOpen) return;

    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') onClose();
    };

    // Focus the modal when it opens
    if (modalRef.current) {
      modalRef.current.focus();
    }

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, onClose]);

  if (!isOpen || !userUuid) return null;

  // Find role and van names
  const roleName = user?.roleUuid ? roles.find(r => r.uuid === user.roleUuid)?.name : 'No role assigned';
  const vanName = user?.vanUuidString ? vans.find(v => v.uuid === user.vanUuidString)?.name : 'No van assigned';

  return (
    <Dialog open={isOpen} onClose={onClose} className="relative z-50">
      <div className="fixed inset-0 bg-black/30" aria-hidden="true" />
      <div className="fixed inset-0 flex items-center justify-center p-4">
        <Dialog.Panel 
          ref={modalRef}
          className="w-full max-w-md rounded-lg bg-white p-6 shadow-xl"
          tabIndex={-1}
        >
          <Dialog.Title className="text-lg font-bold text-gray-900 mb-4">
            User Details
          </Dialog.Title>
          
          {loadingUser ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              <span className="ml-2 text-gray-600">Loading user details...</span>
            </div>
          ) : userError ? (
            <div className="text-red-600 py-4">
              <p>Error loading user details: {userError.message}</p>
            </div>
          ) : user ? (
                         <div className="space-y-4">
               {/* User Information */}
               <div className="grid grid-cols-1 gap-4">
                 <div>
                   <label className="block text-sm font-medium text-gray-700 mb-1">Name</label>
                   <p className="text-sm text-gray-900 bg-gray-50 rounded-md px-3 py-2">{user.name}</p>
                 </div>
               </div>

               {/* QR Code Section */}
               <div className="mt-6 pt-4 border-t border-gray-200">
                 <label className="block text-sm font-medium text-gray-700 mb-3 text-center">QR Code</label>
                 <div className="flex justify-center">
                   {qrCodeDataUrl ? (
                     <img 
                       src={qrCodeDataUrl} 
                       alt={`QR code for user: ${user.name}`}
                       className="border border-gray-200 rounded-md"
                     />
                   ) : (
                     <div className="flex items-center justify-center w-48 h-48 bg-gray-100 border border-gray-200 rounded-md">
                       <span className="text-gray-500">Generating QR code...</span>
                     </div>
                   )}
                 </div>
                 <p className="text-xs text-gray-500 text-center mt-2">
                   Scan this QR code for user information
                 </p>
               </div>
             </div>
          ) : null}

          {/* Close Button */}
          <div className="mt-6 flex justify-end">
            <button
              type="button"
              onClick={onClose}
              className="inline-flex justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
            >
              Close
            </button>
          </div>
        </Dialog.Panel>
      </div>
    </Dialog>
  );
};

export default UserDetailsModal; 