import { useState, useCallback, useEffect } from 'react';
import type { UseKeyboardNavigationReturn } from '../types';

interface UseKeyboardNavigationProps {
  itemCount: number;
  onSelect?: (index: number) => void;
  onEscape?: () => void;
  onF2?: () => void;
  onNewSale?: () => void;
  isModalOpen?: boolean;
  isQuantityMode?: boolean;
}

export function useKeyboardNavigation({
  itemCount,
  onSelect,
  onEscape,
  onF2,
  onNewSale,
  isModalOpen = false,
  isQuantityMode = false,
}: UseKeyboardNavigationProps): UseKeyboardNavigationReturn {
  const [selectedIndex, setSelectedIndex] = useState(0);

  // Reset selected index when item count changes
  useEffect(() => {
    if (selectedIndex >= itemCount) {
      setSelectedIndex(Math.max(0, itemCount - 1));
    }
  }, [itemCount, selectedIndex]);

  const handleKeyDown = useCallback((e: KeyboardEvent) => {
    // Don't handle keyboard events when modals are open or in quantity mode
    if (isModalOpen || isQuantityMode) return;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedIndex(prev => Math.min(prev + 1, itemCount - 1));
        break;
      
      case 'ArrowUp':
        e.preventDefault();
        setSelectedIndex(prev => Math.max(prev - 1, 0));
        break;
      
      case 'Enter':
        e.preventDefault();
        if (onSelect && selectedIndex < itemCount) {
          onSelect(selectedIndex);
        }
        break;
      
      case 'Escape':
        e.preventDefault();
        if (onEscape) {
          onEscape();
        }
        break;
      
      case 'F2':
        e.preventDefault();
        if (onNewSale) {
          onNewSale();
        } else if (onF2) {
          // Fallback to original F2 behavior if onNewSale is not provided
          onF2();
        }
        break;
    }
  }, [selectedIndex, itemCount, onSelect, onEscape, onF2, onNewSale, isModalOpen, isQuantityMode]);

  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [handleKeyDown]);

  return {
    selectedIndex,
    setSelectedIndex,
    handleKeyDown,
  };
} 