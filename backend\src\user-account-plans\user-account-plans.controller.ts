import { <PERSON>, Get, Param, ParseEnumPipe, UseGuards } from "@nestjs/common";
import { ApiTags, ApiOperation, ApiResponse, ApiParam } from "@nestjs/swagger";
import { UserAccountPlansService } from "./user-account-plans.service.typeorm";
import { AccountPlanDto } from "./dto/account-plan.dto";
import { FeatureDto } from "./dto/feature.dto";
import { AccountPlanIdentifier } from "./enums/account-plan.enum";
import { FeatureIdentifier } from "./enums/feature.enum";

@ApiTags("User Account Plans")
@Controller("user-account-plans")
export class UserAccountPlansController {
  constructor(
    private readonly userAccountPlansService: UserAccountPlansService,
  ) {}

  @Get("plans")
  @ApiOperation({ summary: "Get all account plans" })
  @ApiResponse({
    status: 200,
    description: "List of all available account plans",
    type: [AccountPlanDto],
  })
  async getAllAccountPlans(): Promise<AccountPlanDto[]> {
    return this.userAccountPlansService.getAllAccountPlans();
  }

  @Get("plans/:identifier")
  @ApiOperation({ summary: "Get account plan by identifier" })
  @ApiParam({
    name: "identifier",
    enum: AccountPlanIdentifier,
    description: "The identifier of the account plan",
    example: "FREE",
  })
  @ApiResponse({
    status: 200,
    description: "Account plan details",
    type: AccountPlanDto,
  })
  @ApiResponse({
    status: 404,
    description: "Account plan not found",
  })
  async getAccountPlanByIdentifier(
    @Param("identifier", new ParseEnumPipe(AccountPlanIdentifier))
    identifier: AccountPlanIdentifier,
  ): Promise<AccountPlanDto> {
    return this.userAccountPlansService.getAccountPlanByIdentifier(identifier);
  }

  @Get("features")
  @ApiOperation({ summary: "Get all features" })
  @ApiResponse({
    status: 200,
    description: "List of all available features",
    type: [FeatureDto],
  })
  async getAllFeatures(): Promise<FeatureDto[]> {
    return this.userAccountPlansService.getAllFeatures();
  }

  @Get("features/:identifier")
  @ApiOperation({ summary: "Get feature by identifier" })
  @ApiParam({
    name: "identifier",
    enum: FeatureIdentifier,
    description: "The identifier of the feature",
    example: "INVENTORY_TRACKING",
  })
  @ApiResponse({
    status: 200,
    description: "Feature details",
    type: FeatureDto,
  })
  @ApiResponse({
    status: 404,
    description: "Feature not found",
  })
  async getFeatureByIdentifier(
    @Param("identifier", new ParseEnumPipe(FeatureIdentifier))
    identifier: FeatureIdentifier,
  ): Promise<FeatureDto> {
    return this.userAccountPlansService.getFeatureByIdentifier(identifier);
  }

  @Get("plans/:identifier/features")
  @ApiOperation({ summary: "Get all features for a specific account plan" })
  @ApiParam({
    name: "identifier",
    enum: AccountPlanIdentifier,
    description: "The identifier of the account plan",
    example: "FREE",
  })
  @ApiResponse({
    status: 200,
    description: "List of features included in the account plan",
    type: [FeatureDto],
  })
  @ApiResponse({
    status: 404,
    description: "Account plan not found",
  })
  async getFeaturesForPlan(
    @Param("identifier", new ParseEnumPipe(AccountPlanIdentifier))
    identifier: AccountPlanIdentifier,
  ): Promise<FeatureDto[]> {
    return this.userAccountPlansService.getFeaturesForPlan(identifier);
  }
}
