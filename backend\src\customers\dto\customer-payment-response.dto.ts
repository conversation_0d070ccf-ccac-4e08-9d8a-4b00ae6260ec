import { ApiProperty } from "@nestjs/swagger";
import { PaymentMethod, PaymentStatus, CustomerPayment } from "../customer-payment.entity";

export class CustomerPaymentResponseDto {
  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the customer payment",
  })
  uuid: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the customer",
  })
  customerUuidString: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the user who processed the payment",
  })
  userUuidString: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the warehouse",
  })
  warehouseUuidString: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the related sale",
    required: false,
  })
  saleUuidString?: string;

  @ApiProperty({
    example: "cash",
    description: "Payment method used",
    enum: Object.values(PaymentMethod),
  })
  paymentMethod: PaymentMethod;

  @ApiProperty({
    example: 100.0,
    description: "Payment amount",
  })
  amount: number;

  @ApiProperty({
    example: "completed",
    description: "Current status of the payment",
    enum: Object.values(PaymentStatus),
  })
  status: PaymentStatus;

  @ApiProperty({
    example: "Payment for invoice #INV-001",
    description: "Description or note for the payment",
  })
  description: string;

  @ApiProperty({
    example: "REF-123456",
    description: "External reference number",
    required: false,
  })
  referenceNumber?: string;

  @ApiProperty({
    example: "2024-01-15T10:30:00.000Z",
    description: "Date and time when the payment was processed",
    required: false,
  })
  processedAt?: Date;

  @ApiProperty({
    example: 50.0,
    description: "Customer credit balance before this payment",
  })
  previousCreditBalance: number;

  @ApiProperty({
    example: 150.0,
    description: "Customer credit balance after this payment",
  })
  newCreditBalance: number;

  @ApiProperty({
    example: "2024-01-15T09:00:00.000Z",
    description: "Date and time when the payment was created",
  })
  createdAt: Date;

  @ApiProperty({
    example: "2024-01-15T10:30:00.000Z",
    description: "Date and time when the payment was last updated",
  })
  updatedAt: Date;
}

/**
 * Convert CustomerPayment document to CustomerPaymentResponseDto
 * @param payment - CustomerPayment entity from TypeORM
 * @returns CustomerPaymentResponseDto
 */
export function toCustomerPaymentResponseDto(
  payment: CustomerPayment,
): CustomerPaymentResponseDto {
  return {
    uuid: payment.id,
    customerUuidString: payment.customerUuid,
    userUuidString: payment.userUuid,
    warehouseUuidString: payment.warehouseUuid,
    saleUuidString: payment.saleUuid,
    paymentMethod: payment.paymentMethod,
    amount: payment.amount,
    status: payment.status,
    description: payment.description,
    referenceNumber: payment.referenceNumber,
    processedAt: payment.processedAt,
    previousCreditBalance: payment.previousCreditBalance,
    newCreditBalance: payment.newCreditBalance,
    createdAt: payment.createdAt,
    updatedAt: payment.updatedAt,
  };
} 