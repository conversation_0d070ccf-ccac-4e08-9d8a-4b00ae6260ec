import { Module, forwardRef } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { InventoryService } from "./inventory.service.typeorm";
import { InventoryController } from "./inventory.controller";
import { StockAdjustmentService } from "./stock-adjustment.service";
import { CommonModule } from "../common/common.module";
import { Storage } from "./storage.entity";
import { InventoryItem } from "./inventory-item.entity";
import { Warehouse } from "../warehouses/warehouse.entity";
import { Product } from "../products/product.entity";
import { User } from "../users/user.entity";

@Module({
  imports: [
    TypeOrmModule.forFeature([Storage, InventoryItem, Warehouse, Product, User]),
    forwardRef(() => CommonModule), // CommonModule provides all the models and services
  ],
  controllers: [InventoryController],
  providers: [InventoryService, StockAdjustmentService],
  exports: [InventoryService, StockAdjustmentService], // Export both services for other modules
})
export class InventoryModule {}
