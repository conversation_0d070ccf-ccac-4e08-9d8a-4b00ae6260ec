import { Test, TestingModule } from '@nestjs/testing';
import { GoogleStrategy } from '../src/auth/google.strategy';
import { UsersService } from '../src/users/users.service';
import { RolesService } from '../src/roles/roles.service';

describe('Google OAuth Security Fix', () => {
  let googleStrategy: GoogleStrategy;
  let usersService: UsersService;

  const mockUsersService = {
    findByEmail: jest.fn(),
    create: jest.fn(),
    assignAdminRoleToUser: jest.fn(),
  };

  const mockRolesService = {};

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        GoogleStrategy,
        {
          provide: UsersService,
          useValue: mockUsersService,
        },
        {
          provide: RolesService,
          useValue: mockRolesService,
        },
      ],
    }).compile();

    googleStrategy = module.get<GoogleStrategy>(GoogleStrategy);
    usersService = module.get<UsersService>(UsersService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('validate method', () => {
    const mockProfile = {
      name: { givenName: 'John', familyName: 'Doe' },
      emails: [{ value: '<EMAIL>' }],
      photos: [],
    };

    const mockDone = jest.fn();

    it('should create new user when user does not exist', async () => {
      // Arrange
      mockUsersService.findByEmail.mockResolvedValue(null);
      mockUsersService.create.mockResolvedValue({
        uuid: 'user-uuid',
        email: '<EMAIL>',
        name: 'John Doe',
        roleUuidString: 'admin-role-uuid',
        userType: 'super',
        warehouseUuidString: 'warehouse-uuid',
      });

      // Act
      await googleStrategy.validate('access-token', 'refresh-token', mockProfile, mockDone);

      // Assert
      expect(mockUsersService.findByEmail).toHaveBeenCalledWith('<EMAIL>');
      expect(mockUsersService.create).toHaveBeenCalledWith(
        undefined,
        '<EMAIL>',
        'John Doe',
        undefined,
        'super'
      );
      expect(mockDone).toHaveBeenCalledWith(null, expect.objectContaining({
        uuid: 'user-uuid',
        roleUuidString: 'admin-role-uuid',
      }));
    });

    it('should allow existing user with role to login', async () => {
      // Arrange
      mockUsersService.findByEmail.mockResolvedValue({
        uuid: 'user-uuid',
        email: '<EMAIL>',
        name: 'John Doe',
        roleUuidString: 'admin-role-uuid',
        userType: 'super',
        warehouseUuidString: 'warehouse-uuid',
      });

      // Act
      await googleStrategy.validate('access-token', 'refresh-token', mockProfile, mockDone);

      // Assert
      expect(mockUsersService.findByEmail).toHaveBeenCalledWith('<EMAIL>');
      expect(mockUsersService.create).not.toHaveBeenCalled();
      expect(mockUsersService.assignAdminRoleToUser).not.toHaveBeenCalled();
      expect(mockDone).toHaveBeenCalledWith(null, expect.objectContaining({
        uuid: 'user-uuid',
        roleUuidString: 'admin-role-uuid',
      }));
    });

    it('should deny access to existing user without role (SECURITY FIX)', async () => {
      // Arrange
      mockUsersService.findByEmail.mockResolvedValue({
        uuid: 'user-uuid',
        email: '<EMAIL>',
        name: 'John Doe',
        roleUuidString: null, // No role assigned
        userType: 'user',
        warehouseUuidString: 'warehouse-uuid',
      });

      // Act
      await googleStrategy.validate('access-token', 'refresh-token', mockProfile, mockDone);

      // Assert
      expect(mockUsersService.findByEmail).toHaveBeenCalledWith('<EMAIL>');
      expect(mockUsersService.create).not.toHaveBeenCalled();
      expect(mockUsersService.assignAdminRoleToUser).not.toHaveBeenCalled(); // SECURITY: No automatic role assignment
      
      // Should call done with error
      expect(mockDone).toHaveBeenCalledWith(expect.objectContaining({
        message: expect.stringContaining('User <EMAIL> exists but has no role assigned'),
        name: 'ROLE_REQUIRED',
      }));
    });

    it('should handle user creation errors gracefully', async () => {
      // Arrange
      mockUsersService.findByEmail.mockResolvedValue(null);
      mockUsersService.create.mockRejectedValue(new Error('Database connection failed'));

      // Act
      await googleStrategy.validate('access-token', 'refresh-token', mockProfile, mockDone);

      // Assert
      expect(mockDone).toHaveBeenCalledWith(expect.objectContaining({
        message: expect.stringContaining('Google OAuth user validation failed: Database connection failed'),
      }));
    });

    it('should handle missing email in profile', async () => {
      // Arrange
      const invalidProfile = {
        name: { givenName: 'John', familyName: 'Doe' },
        emails: [], // No emails
        photos: [],
      };

      // Act
      await googleStrategy.validate('access-token', 'refresh-token', invalidProfile, mockDone);

      // Assert
      expect(mockDone).toHaveBeenCalledWith(expect.objectContaining({
        message: expect.stringContaining('Google OAuth user validation failed'),
      }));
    });
  });

  describe('Security Validation', () => {
    it('should not automatically assign admin role to users without roles', async () => {
      // This test verifies that the security vulnerability has been fixed
      const mockProfile = {
        name: { givenName: 'John', familyName: 'Doe' },
        emails: [{ value: '<EMAIL>' }],
        photos: [],
      };

      const mockDone = jest.fn();

      // Arrange: User exists but has no role
      mockUsersService.findByEmail.mockResolvedValue({
        uuid: 'user-uuid',
        email: '<EMAIL>',
        name: 'John Doe',
        roleUuidString: null,
        userType: 'user',
        warehouseUuidString: 'warehouse-uuid',
      });

      // Act
      await googleStrategy.validate('access-token', 'refresh-token', mockProfile, mockDone);

      // Assert: assignAdminRoleToUser should NEVER be called
      expect(mockUsersService.assignAdminRoleToUser).not.toHaveBeenCalled();
      
      // Assert: Should return error instead of granting access
      expect(mockDone).toHaveBeenCalledWith(expect.objectContaining({
        name: 'ROLE_REQUIRED',
      }));
    });
  });
}); 