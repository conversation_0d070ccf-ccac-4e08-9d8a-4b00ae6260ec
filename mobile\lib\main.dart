import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/services.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'app/app.dart';
import 'core/network/api_client.dart';
import 'core/storage/secure_storage_service.dart';

Future<void> main() async {
  // Ensure Flutter bindings are initialized
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize core services
  await _initializeServices();

  // Set preferred orientations
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  // Run the app with Riverpod provider scope
  runApp(
    const ProviderScope(
      child: DidoDistributionApp(),
    ),
  );
}

Future<void> _initializeServices() async {
  try {
    // Load environment variables
    try {
      await dotenv.load();
      debugPrint('✅ Environment variables loaded successfully');
      
      // Debug: Show loaded environment values
      debugPrint('=== ENVIRONMENT CONFIGURATION DEBUG ===');
      debugPrint('API_BASE_URL: ${dotenv.env['API_BASE_URL'] ?? 'NOT SET'}');
      debugPrint('API_TIMEOUT: ${dotenv.env['API_TIMEOUT'] ?? 'NOT SET'}');
      debugPrint('DEBUG_MODE: ${dotenv.env['DEBUG_MODE'] ?? 'NOT SET'}');
      debugPrint('LOG_LEVEL: ${dotenv.env['LOG_LEVEL'] ?? 'NOT SET'}');
      
      // Google OAuth debugging (don't log sensitive values, just presence)
      final hasGoogleSecret = dotenv.env['GOOGLE_CLIENT_SECRET'] != null && 
                              dotenv.env['GOOGLE_CLIENT_SECRET'] != 'your_google_client_secret_here';
      debugPrint('GOOGLE_CLIENT_SECRET: ${hasGoogleSecret ? 'CONFIGURED' : 'NOT CONFIGURED'}');
      debugPrint('=== END ENVIRONMENT DEBUG ===');
    } catch (e) {
      debugPrint('⚠️ Could not load .env file: $e (using default configuration)');
    }

    // Initialize API client
    ApiClient().initialize();

    // Initialize secure storage
    SecureStorageService();

    // Additional service initialization can be added here
    // await NotificationService.initialize();
    // await DatabaseService.initialize();
    
    debugPrint('✅ Core services initialized successfully');
  } catch (e) {
    debugPrint('❌ Error initializing services: $e');
    // Handle initialization errors gracefully
    // Could show an error dialog or fallback to offline mode
  }
}

 