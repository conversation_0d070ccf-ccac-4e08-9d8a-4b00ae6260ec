import { ApiProperty } from "@nestjs/swagger";
import {
  IsOptional,
  IsString,
  IsPositive,
  IsInt,
  Min,
  Max,
  IsUUID,
} from "class-validator";
import { Type } from "class-transformer";

export class WarehouseFilterDto {
  @ApiProperty({
    example: "Acme",
    description: "Optional warehouse name (partial, case-insensitive)",
    required: false,
  })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "Optional user UUID to filter warehouses by owner",
    required: false,
  })
  @IsOptional()
  @IsUUID("all")
  userUuid?: string;

  @ApiProperty({
    example: 1,
    description: "Page number (1-based)",
    required: false,
    minimum: 1,
    default: 1,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  page?: number = 1;

  @ApiProperty({
    example: 10,
    description: "Number of items per page",
    required: false,
    minimum: 1,
    maximum: 100,
    default: 10,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @Max(100)
  limit?: number = 10;
}
