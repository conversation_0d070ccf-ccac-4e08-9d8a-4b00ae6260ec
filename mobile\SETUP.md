# Dido Distribution Mobile - Setup Guide

This guide will help you set up the Dido Distribution mobile application development environment and get the app running on your local machine.

## 📋 Prerequisites

Before you begin, ensure you have the following installed on your development machine:

### Required Software

1. **Flutter SDK** (>=3.0.0)
   - Download from [flutter.dev](https://flutter.dev/docs/get-started/install)
   - Add Flutter to your PATH environment variable

2. **Dart SDK** (>=3.0.0)
   - Automatically included with Flutter SDK

3. **Git**
   - For version control and dependency management

### Development Environment

Choose one of the following IDEs:

#### Option 1: Android Studio (Recommended)
- Download from [developer.android.com](https://developer.android.com/studio)
- Install Flutter and Dart plugins
- Configure Android SDK and emulator

#### Option 2: Visual Studio Code
- Download from [code.visualstudio.com](https://code.visualstudio.com/)
- Install Flutter extension
- Install Dart extension

### Platform-Specific Requirements

#### For Android Development
- **Android Studio** with Android SDK
- **Android SDK Platform-Tools**
- **Android SDK Build-Tools**
- **Android Emulator** or physical Android device
- **Java Development Kit (JDK)** 11 or higher

#### For iOS Development (macOS only)
- **Xcode** (latest version)
- **iOS Simulator** or physical iOS device
- **CocoaPods** for dependency management
- **Apple Developer Account** (for device testing and App Store deployment)

## 🚀 Installation Steps

### 1. Verify Flutter Installation

```bash
flutter doctor
```

This command checks your environment and displays a report of the status of your Flutter installation. Address any issues shown.

### 2. Clone the Repository

```bash
git clone <repository-url>
cd dido-distribution/mobile
```

### 3. Install Dependencies

```bash
flutter pub get
```

This command downloads all the required packages specified in `pubspec.yaml`.

### 4. Environment Configuration

#### Create Environment File
```bash
cp .env.example .env
```

#### Configure Environment Variables
Edit the `.env` file with your specific configuration:

```env
# API Configuration
API_BASE_URL=http://localhost:8000
API_TIMEOUT=30000

# Authentication

OAUTH_REDIRECT_URL=your_redirect_url_here

# Features
ENABLE_BIOMETRIC_AUTH=true
ENABLE_OFFLINE_MODE=true
ENABLE_PUSH_NOTIFICATIONS=true

# Development
DEBUG_MODE=true
LOG_LEVEL=debug

# Analytics (optional)
ENABLE_ANALYTICS=false
ANALYTICS_KEY=your_analytics_key_here
```

### 5. Backend Setup

Ensure the backend server is running:

```bash
# Navigate to backend directory
cd ../backend

# Install dependencies
npm install

# Start the backend server
npm run start:dev
```

The backend should be running on `http://localhost:8000`.

### 6. Code Generation

Generate necessary code files:

```bash
flutter packages pub run build_runner build
```

This generates model classes, API clients, and other auto-generated code.

## 🔧 Platform-Specific Setup

### Android Setup

#### 1. Configure Android SDK
- Open Android Studio
- Go to `Tools > SDK Manager`
- Install the latest Android SDK Platform
- Install Android SDK Build-Tools
- Install Android SDK Platform-Tools

#### 2. Create Android Virtual Device (AVD)
- Open Android Studio
- Go to `Tools > AVD Manager`
- Create a new virtual device
- Choose a device definition and system image
- Start the emulator

#### 3. Enable USB Debugging (for physical devices)
- Enable Developer Options on your Android device
- Enable USB Debugging
- Connect device via USB

### iOS Setup (macOS only)

#### 1. Install Xcode
- Download from Mac App Store
- Open Xcode and accept license agreements
- Install additional components when prompted

#### 2. Install CocoaPods
```bash
sudo gem install cocoapods
```

#### 3. Configure iOS Simulator
- Open Xcode
- Go to `Window > Devices and Simulators`
- Create a new simulator if needed

#### 4. Install iOS Dependencies
```bash
cd ios
pod install
cd ..
```

## 🏃‍♂️ Running the Application

### 1. Check Connected Devices
```bash
flutter devices
```

### 2. Run on Android
```bash
# Run on Android emulator or connected device
flutter run

# Or specify Android explicitly
flutter run -d android
```

### 3. Run on iOS (macOS only)
```bash
# Run on iOS simulator or connected device
flutter run -d ios
```

### 4. Run in Debug Mode
```bash
flutter run --debug
```

### 5. Run in Release Mode
```bash
flutter run --release
```

## 🧪 Testing Setup

### Unit Tests
```bash
flutter test
```

### Widget Tests
```bash
flutter test test/widget/
```

### Integration Tests
```bash
flutter test integration_test/
```

### Test Coverage
```bash
flutter test --coverage
genhtml coverage/lcov.info -o coverage/html
```

## 🔨 Development Tools

### Hot Reload
While the app is running, you can use hot reload to see changes instantly:
- Press `r` in the terminal
- Or use the hot reload button in your IDE

### Hot Restart
For more significant changes:
- Press `R` in the terminal
- Or use the hot restart button in your IDE

### Flutter Inspector
Use Flutter Inspector to debug widget trees:
- Available in Android Studio and VS Code
- Helps with layout debugging and performance analysis

## 📦 Build Configuration

### Debug Build
```bash
# Android
flutter build apk --debug

# iOS
flutter build ios --debug
```

### Release Build
```bash
# Android
flutter build apk --release

# iOS
flutter build ios --release
```

### Build for Different Flavors
```bash
# Development flavor
flutter run --flavor dev

# Production flavor
flutter run --flavor prod
```

## 🐛 Troubleshooting

### Common Issues

#### Flutter Doctor Issues
- **Android SDK not found**: Set `ANDROID_HOME` environment variable
- **Xcode not found**: Install Xcode from Mac App Store
- **CocoaPods not installed**: Run `sudo gem install cocoapods`

#### Build Issues
- **Gradle build failed**: Clean and rebuild
  ```bash
  flutter clean
  flutter pub get
  flutter build apk
  ```

- **iOS build failed**: Clean iOS build folder
  ```bash
  cd ios
  rm -rf Pods
  rm Podfile.lock
  pod install
  cd ..
  flutter clean
  flutter build ios
  ```

#### Runtime Issues
- **API connection failed**: Check backend server is running
- **Authentication errors**: Verify API keys and configuration
- **Permission denied**: Check app permissions on device

### Debug Commands

```bash
# Check Flutter version
flutter --version

# Analyze code for issues
flutter analyze

# Format code
flutter format .

# Clean build cache
flutter clean

# Verbose output
flutter run -v
```

## 🔐 Security Setup

### API Security
- Use HTTPS in production
- Implement certificate pinning
- Store API keys securely

### Authentication
- Configure OAuth providers
- Set up biometric authentication
- Implement secure token storage

### Data Protection
- Enable app-level encryption
- Use secure storage for sensitive data
- Implement data loss prevention

## 📱 Device Testing

### Android Testing
- Test on various Android versions (API 21+)
- Test on different screen sizes
- Test on physical devices

### iOS Testing
- Test on various iOS versions (iOS 12+)
- Test on different iPhone/iPad models
- Test on physical devices

## 🚀 Deployment Preparation

### Android Deployment
1. **Generate Signing Key**
   ```bash
   keytool -genkey -v -keystore ~/upload-keystore.jks -keyalg RSA -keysize 2048 -validity 10000 -alias upload
   ```

2. **Configure Signing**
   - Create `android/key.properties`
   - Configure `android/app/build.gradle`

3. **Build Release APK**
   ```bash
   flutter build apk --release
   ```

### iOS Deployment
1. **Configure Xcode Project**
   - Set bundle identifier
   - Configure signing certificates
   - Set deployment target

2. **Build for App Store**
   ```bash
   flutter build ios --release
   ```

## 📚 Additional Resources

### Documentation
- [Flutter Documentation](https://flutter.dev/docs)
- [Dart Documentation](https://dart.dev/guides)
- [Material Design](https://material.io/design)
- [Cupertino Design](https://developer.apple.com/design/human-interface-guidelines/)

### Community
- [Flutter Community](https://flutter.dev/community)
- [Stack Overflow](https://stackoverflow.com/questions/tagged/flutter)
- [GitHub Issues](https://github.com/flutter/flutter/issues)

### Tools
- [Flutter Inspector](https://flutter.dev/docs/development/tools/flutter-inspector)
- [Dart DevTools](https://dart.dev/tools/dart-devtools)
- [Firebase Console](https://console.firebase.google.com/)

## 🆘 Getting Help

If you encounter issues during setup:

1. Check the troubleshooting section above
2. Search existing issues in the repository
3. Create a new issue with detailed information
4. Contact the development team

---

## 📝 Next Steps

After completing the setup:

1. **Read the Documentation**: Familiarize yourself with the project structure
2. **Run Tests**: Ensure all tests pass
3. **Explore Features**: Try different app features
4. **Start Development**: Begin implementing new features

Welcome to the Dido Distribution mobile development team! 🎉 