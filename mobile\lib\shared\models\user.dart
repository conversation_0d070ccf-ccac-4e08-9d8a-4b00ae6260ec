import 'package:json_annotation/json_annotation.dart';
import 'role.dart';

part 'user.g.dart';

@JsonSerializable()
class User {

  User({
    required this.uuid,
    required this.email,
    this.firstName,
    this.lastName,
    this.phone,
    required this.isActive,
    required this.roles,
    this.warehouseUuid,
    this.vanUuid,
    required this.createdAt,
    required this.updatedAt,
  });

  factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);
  final String uuid;
  final String email;
  final String? firstName;
  final String? lastName;
  final String? phone;
  final bool isActive;
  final List<Role> roles;
  final String? warehouseUuid;
  final String? vanUuid;
  final DateTime createdAt;
  final DateTime updatedAt;
  Map<String, dynamic> toJson() => _$UserToJson(this);

  String get fullName => '${firstName ?? ''} ${lastName ?? ''}'.trim();
  
  String get displayName => fullName.isNotEmpty ? fullName : email;

  /// Check if user has a specific permission
  bool hasPermission(String permission) => roles.any((role) => role.permissions.contains(permission));

  /// Check if user has a specific role
  bool hasRole(String roleName) => roles.any((role) => role.name == roleName);

  /// Get all permissions from all roles
  List<String> get allPermissions => roles.expand((role) => role.permissions).toSet().toList();

  /// Check if user is admin
  bool get isAdmin => hasRole('admin');

  /// Check if user is mobile sale agent
  bool get isMobileSaleAgent => hasRole('mobile sale agent');

  /// Check if user is manager
  bool get isManager => hasRole('manager');

  /// Check if user can view dashboard
  bool get canViewDashboard => hasPermission('dashboard.view');

  /// Check if user can view sales
  bool get canViewSales => hasPermission('sales.view');

  /// Check if user can edit sales
  bool get canEditSales => hasPermission('sales.edit');

  /// Check if user can view inventory
  bool get canViewInventory => hasPermission('inventory.view');

  /// Check if user can manage inventory
  bool get canManageInventory => hasPermission('inventory.manage');

  /// Check if user can view logistics
  bool get canViewLogistics => hasPermission('logistics.view');

  /// Check if user can manage logistics
  bool get canManageLogistics => hasPermission('logistics.manage');

  /// Check if user can view purchasing
  bool get canViewPurchasing => hasPermission('purchasing.view');

  /// Check if user can edit purchasing
  bool get canEditPurchasing => hasPermission('purchasing.edit');

  /// Check if user can view reports
  bool get canViewReports => hasPermission('reports.view');

  /// Check if user can manage users
  bool get canManageUsers => hasPermission('user.manage');

  /// Check if user can manage settings
  bool get canManageSettings => hasPermission('settings.manage');
} 