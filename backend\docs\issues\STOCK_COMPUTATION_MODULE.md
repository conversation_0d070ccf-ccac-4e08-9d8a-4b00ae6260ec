# Stock Computation Module Documentation

**Location:** `backend/src/stock-computation/`

## Purpose
Provides real-time stock computation and recalculation services for accurate inventory tracking.

## Structure
```
stock-computation/
├── stock-computation.controller.ts  # Computation endpoints
├── stock-computation.service.ts     # Computation logic
├── stock-computation.module.ts      # Module configuration
└── index.ts                         # Module exports
```

## Key Features
- Real-time stock calculation
- Stock recomputation on demand
- Product-specific calculations
- Storage-specific calculations
- Warehouse-specific calculations
- Historical stock tracking
- Performance optimization

## Endpoints

### Stock Computation
- `GET /stock-computation/product/:productUuid` - Compute product stock
  - Query params: `storageUuid`, `warehouseUuid`
- `GET /stock-computation/storage/:storageUuid` - Compute storage stock
- `GET /stock-computation/warehouse/:warehouseUuid` - Compute warehouse stock

## Business Logic

### Stock Calculation Process
1. **Data Collection:** Gathers all stock adjustments and sales
2. **Zero-based Calculation:** Starts from zero and applies all changes
3. **Real-time Processing:** Calculates current stock from historical data
4. **Accuracy Verification:** Ensures data consistency
5. **Performance Optimization:** Uses efficient algorithms for large datasets

### Computation Methods

#### Product Stock Calculation
```typescript
// Calculate stock for a specific product
async computeProductStock(
  productUuid: string,
  storageUuid?: string,
  warehouseUuid?: string
): Promise<{
  productUuid: string;
  currentStock: number;
  storageUuid?: string;
  warehouseUuid?: string;
  lastUpdated: Date;
}>
```

#### Storage Stock Calculation
```typescript
// Calculate stock for all products in a storage
async computeStorageStock(
  storageUuid: string
): Promise<Array<{
  productUuid: string;
  productName: string;
  currentStock: number;
  lastUpdated: Date;
}>>
```

#### Warehouse Stock Calculation
```typescript
// Calculate stock for all products in a warehouse
async computeWarehouseStock(
  warehouseUuid: string
): Promise<Array<{
  productUuid: string;
  productName: string;
  currentStock: number;
  lastUpdated: Date;
}>>
```

## Data Sources

### Stock Adjustments
- **Purpose:** Manual stock changes
- **Fields:** productUuid, storageUuid, quantityAdjusted, reason
- **Impact:** Direct stock modifications

### Sales Transactions
- **Purpose:** Stock consumption from sales
- **Fields:** productUuid, quantity, status
- **Impact:** Stock reduction for completed sales

### Purchase Orders
- **Purpose:** Stock additions from purchases
- **Fields:** productUuid, quantity, status
- **Impact:** Stock increase for received items

## Calculation Algorithm

### Base Formula
```
Current Stock = Σ(Stock Adjustments) - Σ(Completed Sales) + Σ(Received Purchases)
```

### Filtering Logic
1. **Product Filter:** Only includes specified product
2. **Storage Filter:** Only includes specified storage
3. **Warehouse Filter:** Only includes specified warehouse
4. **Date Filter:** Only includes relevant time period
5. **Status Filter:** Only includes valid transactions

## Performance Optimizations

### Database Queries
- **Indexed Fields:** Uses indexed UUID fields for fast lookups
- **Aggregation Pipeline:** Uses MongoDB aggregation for efficient calculations
- **Batch Processing:** Processes multiple products in batches
- **Caching:** Implements result caching for repeated calculations

### Memory Management
- **Streaming:** Uses streaming for large datasets
- **Pagination:** Processes data in chunks
- **Memory Cleanup:** Properly releases memory after calculations

## Response Format

### Product Stock Response
```typescript
{
  productUuid: string,           // Product UUID
  productName: string,           // Product name
  currentStock: number,          // Current stock quantity
  storageUuid?: string,          // Storage UUID (if filtered)
  warehouseUuid?: string,        // Warehouse UUID (if filtered)
  lastUpdated: Date,             // Last calculation timestamp
  calculationMethod: string      // Method used for calculation
}
```

### Storage Stock Response
```typescript
{
  storageUuid: string,           // Storage UUID
  storageName: string,           // Storage name
  totalProducts: number,         // Number of products
  totalStock: number,            // Total stock across all products
  products: Array<{              // Product details
    productUuid: string,
    productName: string,
    currentStock: number,
    lastUpdated: Date
  }>,
  lastUpdated: Date              // Last calculation timestamp
}
```

### Warehouse Stock Response
```typescript
{
  warehouseUuid: string,         // Warehouse UUID
  warehouseName: string,         // Warehouse name
  totalProducts: number,         // Number of products
  totalStock: number,            // Total stock across all products
  products: Array<{              // Product details
    productUuid: string,
    productName: string,
    currentStock: number,
    lastUpdated: Date
  }>,
  lastUpdated: Date              // Last calculation timestamp
}
```

## Dependencies
- `@nestjs/mongoose` - MongoDB integration
- `../utils/uuid7` - UUID generation and conversion
- `../inventory` - Stock adjustment data
- `../sales` - Sales transaction data
- `../purchase` - Purchase transaction data
- `../products` - Product data

## Related Modules
- **Inventory Module** - Stock adjustment data
- **Sales Module** - Sales transaction data
- **Purchase Module** - Purchase transaction data
- **Products Module** - Product information
- **Warehouses Module** - Warehouse data

## Issues Found

### ✅ Good Practices
1. **Well-Implemented Functionality**
   - Proper calculation algorithms
   - Performance optimizations
   - Accurate data processing

2. **Clean Architecture**
   - Clear separation of concerns
   - Modular design
   - Good error handling

3. **Performance Considerations**
   - Efficient database queries
   - Memory management
   - Scalable design

### 🟡 Minor Issues
1. **Limited Scope**
   - Issue: Only basic computation features
   - Impact: Could benefit from more advanced features

## Error Handling
- **Invalid UUIDs:** Validates UUID format before processing
- **Missing Data:** Handles cases where data is not found
- **Calculation Errors:** Provides meaningful error messages
- **Performance Issues:** Handles timeout scenarios

## Security Considerations
- **UUID Validation:** Validates all UUID inputs
- **Data Access:** Respects warehouse scoping
- **Input Sanitization:** Sanitizes all input parameters
- **Error Information:** Prevents information leakage

## Future Improvements
1. Implement stock forecasting
2. Add low stock alerts
3. Implement stock movement tracking
4. Add stock analytics and reporting
5. Implement automated stock reconciliation
6. Add stock trend analysis
7. Implement stock optimization suggestions
8. Add real-time stock notifications 