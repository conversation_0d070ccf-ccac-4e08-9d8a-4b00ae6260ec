# Filter Patterns

This document summarizes the standard filter patterns used across the Dido Distribution backend.

## Overview

All filtering endpoints should follow consistent patterns for handling different data types and edge cases.

## Filter Types

### 1. UUID Filters
- **Pattern**: Use UUIDv7 strings directly in TypeORM/PostgreSQL queries
- **Example**: `customerUuid`, `warehouseUuid`, `productUuid`, `userUuid`
- **Implementation**: Use string UUIDs directly in queries and DTOs
- **Constants**: `EMPTY_UUID_FILTER = ""`
- **Note**: All UUID validation is performed at the application layer using `@IsUUID('7')` (requires custom validator)

### 2. String Filters
- **Pattern**: Exact or partial string matching
- **Example**: `status`, `paymentMethod`
- **Implementation**: Direct string comparison or regex for partial matches
- **Constants**: `EMPTY_STRING_FILTER = ""`

### 3. Date Filters
- **Pattern**: Date range filtering with fallback constants
- **Example**: `createdFrom`, `createdTo`, `startDate`, `endDate`
- **Implementation**: Use `$gte` and `$lte` operators
- **Constants**: `MIN_DATE_FILTER`, `MAX_DATE_FILTER`

### 4. Numeric Filters
- **Pattern**: Range filtering with robust empty/NaN handling
- **Example**: `minAmount`, `maxAmount`, `minQuantity`, `maxPrice`
- **Implementation**: Parse strings, validate numbers, use constants for defaults
- **Constants**: `MIN_NUMBER_FILTER`, `MAX_NUMBER_FILTER`

## Numeric Filter Implementation

### Constants
```typescript
export const MIN_NUMBER_FILTER = Number.MIN_SAFE_INTEGER;
export const MAX_NUMBER_FILTER = Number.MAX_SAFE_INTEGER;
```

### Controller Pattern
```typescript
@Query("minAmount") minAmount?: string,
@Query("maxAmount") maxAmount?: string,

// Parse and validate
const parsedMinAmount = minAmount ? parseFloat(minAmount) : undefined;
const parsedMaxAmount = maxAmount ? parseFloat(maxAmount) : undefined;
```

### Service Pattern
```typescript
async findAll({
  minAmount = MIN_NUMBER_FILTER,
  maxAmount = MAX_NUMBER_FILTER,
}: {
  minAmount?: number;
  maxAmount?: number;
} = {}) {
  const validMinAmount = typeof minAmount === 'number' && !isNaN(minAmount) ? minAmount : MIN_NUMBER_FILTER;
  const validMaxAmount = typeof maxAmount === 'number' && !isNaN(maxAmount) ? maxAmount : MAX_NUMBER_FILTER;
  
  if (validMinAmount !== MIN_NUMBER_FILTER || validMaxAmount !== MAX_NUMBER_FILTER) {
    query.totalAmount = {};
    if (validMinAmount !== MIN_NUMBER_FILTER) {
      query.totalAmount.$gte = validMinAmount;
    }
    if (validMaxAmount !== MAX_NUMBER_FILTER) {
      query.totalAmount.$lte = validMaxAmount;
    }
  }
}
```

## Benefits

- **Robust handling**: Empty values, NaN, and invalid inputs handled gracefully
- **Performance**: No unnecessary database filters when values are empty/invalid
- **Consistency**: All filters follow the same patterns across the application
- **Frontend friendly**: Frontend can send empty strings or omit parameters without errors

## References

- [UUID Usage Guidelines](./UUID_USAGE_GUIDELINES.md)
- [API Layer Patterns](./API_LAYER.md#numeric-filter-handling)
- [Code Generation Guidelines](./CODE_GENERATION.md)

---

**Note:** All UUID validation and transformation is performed at the application layer. PostgreSQL/YugabyteDB stores UUIDs as type `uuid`, but does not enforce the UUID version. Ensure your application logic enforces UUIDv7 compliance. Also, `@IsUUID('7')` and `ParseUUIDPipe({ version: '7' })` require custom implementations for UUIDv7 support. 