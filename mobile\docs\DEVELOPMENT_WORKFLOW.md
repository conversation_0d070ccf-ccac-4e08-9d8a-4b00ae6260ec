# Development Workflow Guide

## Overview

This document outlines the development workflow for the Dido Distribution mobile application, including coding standards, Git workflows, testing procedures, and deployment processes.

## 🔄 Git Workflow

### Branch Strategy

We follow a **Git Flow** strategy with the following branches:

#### Main Branches
- `main` - Production-ready code
- `develop` - Integration branch for features

#### Supporting Branches
- `feature/*` - New features
- `bugfix/*` - Bug fixes
- `hotfix/*` - Critical production fixes
- `release/*` - Release preparation

### Branch Naming Convention

```
feature/[ticket-number]-[short-description]
bugfix/[ticket-number]-[short-description]
hotfix/[ticket-number]-[short-description]
release/[version-number]

Examples:
feature/DIDO-123-mobile-pos-screen
bugfix/DIDO-456-fix-login-validation
hotfix/DIDO-789-critical-inventory-sync
release/1.2.0
```

### Commit Message Format

```
type(scope): subject

body

footer
```

#### Types
- `feat` - New feature
- `fix` - Bug fix
- `docs` - Documentation changes
- `style` - Code style changes
- `refactor` - Code refactoring
- `test` - Adding or updating tests
- `chore` - Maintenance tasks

#### Examples
```
feat(pos): add barcode scanning functionality

Implemented barcode scanning for product lookup in POS screen.
Added camera permission handling and barcode validation.

Closes #123
```

```
fix(auth): resolve token refresh issue

Fixed infinite loop in token refresh mechanism.
Added proper error handling for expired refresh tokens.

Fixes #456
```

## 🏗️ Development Setup

### Environment Setup

1. **Clone Repository**
   ```bash
   git clone <repository-url>
   cd dido-distribution/mobile
   ```

2. **Install Dependencies**
   ```bash
   flutter pub get
   ```

3. **Environment Configuration**
   ```bash
   cp .env.example .env.development
   cp .env.example .env.staging
   cp .env.example .env.production
   ```

4. **Code Generation**
   ```bash
   flutter packages pub run build_runner build
   ```

### IDE Configuration

#### VS Code Extensions
- Flutter
- Dart
- GitLens
- Bracket Pair Colorizer
- Flutter Widget Snippets

#### Android Studio Plugins
- Flutter
- Dart
- Git Integration

### Code Style Configuration

#### analysis_options.yaml
```yaml
include: package:flutter_lints/flutter.yaml

analyzer:
  exclude:
    - "**/*.g.dart"
    - "**/*.freezed.dart"
  strong-mode:
    implicit-casts: false
    implicit-dynamic: false

linter:
  rules:
    - avoid_print
    - prefer_const_constructors
    - prefer_const_literals_to_create_immutables
    - prefer_final_fields
    - prefer_final_locals
    - sort_constructors_first
    - sort_unnamed_constructors_first
    - use_key_in_widget_constructors
```

## 📝 Coding Standards

### Dart/Flutter Conventions

#### File Organization
```
lib/
├── main.dart
├── app/
│   ├── app.dart
│   ├── router.dart
│   └── theme.dart
├── features/
│   └── [feature_name]/
│       ├── data/
│       ├── domain/
│       └── presentation/
├── shared/
│   ├── models/
│   ├── services/
│   ├── providers/
│   └── widgets/
└── core/
    ├── constants/
    ├── errors/
    ├── network/
    └── utils/
```

#### Naming Conventions
- **Files**: `snake_case.dart`
- **Classes**: `PascalCase`
- **Variables**: `camelCase`
- **Constants**: `SCREAMING_SNAKE_CASE`
- **Private members**: `_leadingUnderscore`

#### Code Documentation
```dart
/// Service for handling user authentication
/// 
/// This service manages user login, logout, and token refresh
/// operations. It integrates with the backend API and manages
/// local token storage.
class AuthService {
  /// Authenticates user with username and password
  /// 
  /// Returns [AuthResult] containing user data on success
  /// or error message on failure.
  /// 
  /// Throws [AuthException] if authentication fails.
  Future<AuthResult> login({
    required String username,
    required String password,
  }) async {
    // Implementation
  }
}
```

### Widget Guidelines

#### Stateless vs Stateful Widgets
- Use `StatelessWidget` for UI that doesn't change
- Use `StatefulWidget` for UI that changes over time
- Consider `Consumer` widgets for state management

#### Widget Composition
```dart
class ProductCard extends StatelessWidget {
  final Product product;
  final VoidCallback? onTap;
  
  const ProductCard({
    Key? key,
    required this.product,
    this.onTap,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return Card(
      child: InkWell(
        onTap: onTap,
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildHeader(),
              const SizedBox(height: 8),
              _buildContent(),
              const SizedBox(height: 8),
              _buildFooter(),
            ],
          ),
        ),
      ),
    );
  }
  
  Widget _buildHeader() {
    return Row(
      children: [
        Text(
          product.name,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const Spacer(),
        Text(
          '\$${product.price.toStringAsFixed(2)}',
          style: const TextStyle(
            fontSize: 14,
            color: Colors.green,
          ),
        ),
      ],
    );
  }
  
  Widget _buildContent() {
    return Text(
      product.description,
      style: const TextStyle(fontSize: 14),
      maxLines: 2,
      overflow: TextOverflow.ellipsis,
    );
  }
  
  Widget _buildFooter() {
    return Row(
      children: [
        Icon(
          Icons.inventory,
          size: 16,
          color: Colors.grey[600],
        ),
        const SizedBox(width: 4),
        Text(
          '${product.stock} in stock',
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }
}
```

## 🧪 Testing Strategy

### Test Structure
```
test/
├── unit/
│   ├── services/
│   ├── providers/
│   ├── models/
│   └── utils/
├── widget/
│   ├── screens/
│   ├── components/
│   └── forms/
└── integration/
    ├── auth_flow_test.dart
    ├── inventory_flow_test.dart
    └── sales_flow_test.dart
```

### Unit Testing

#### Service Testing
```dart
void main() {
  group('AuthService', () {
    late AuthService authService;
    late MockApiClient mockApiClient;
    
    setUp(() {
      mockApiClient = MockApiClient();
      authService = AuthService(mockApiClient);
    });
    
    test('should login successfully with valid credentials', () async {
      // Arrange
      const username = 'testuser';
      const password = 'testpass';
      final mockResponse = AuthResponse(
        user: User(id: '1', username: username),
        token: 'mock_token',
      );
      
      when(mockApiClient.post('/auth/login', data: anyNamed('data')))
          .thenAnswer((_) async => Response(
                data: mockResponse.toJson(),
                statusCode: 200,
                requestOptions: RequestOptions(path: '/auth/login'),
              ));
      
      // Act
      final result = await authService.login(
        username: username,
        password: password,
      );
      
      // Assert
      expect(result.isSuccess, true);
      expect(result.user?.username, username);
      verify(mockApiClient.post('/auth/login', data: {
        'username': username,
        'password': password,
      })).called(1);
    });
    
    test('should handle login failure', () async {
      // Arrange
      when(mockApiClient.post('/auth/login', data: anyNamed('data')))
          .thenThrow(DioError(
            requestOptions: RequestOptions(path: '/auth/login'),
            response: Response(
              statusCode: 401,
              data: {'message': 'Invalid credentials'},
              requestOptions: RequestOptions(path: '/auth/login'),
            ),
          ));
      
      // Act
      final result = await authService.login(
        username: 'invalid',
        password: 'invalid',
      );
      
      // Assert
      expect(result.isSuccess, false);
      expect(result.error, 'Invalid credentials');
    });
  });
}
```

### Widget Testing

#### Screen Testing
```dart
void main() {
  group('LoginScreen', () {
    testWidgets('should display login form', (tester) async {
      // Arrange
      await tester.pumpWidget(
        MaterialApp(
          home: LoginScreen(),
        ),
      );
      
      // Assert
      expect(find.text('Login'), findsOneWidget);
      expect(find.byType(TextField), findsNWidgets(2));
      expect(find.text('Username'), findsOneWidget);
      expect(find.text('Password'), findsOneWidget);
      expect(find.byType(ElevatedButton), findsOneWidget);
    });
    
    testWidgets('should validate empty fields', (tester) async {
      // Arrange
      await tester.pumpWidget(
        MaterialApp(
          home: LoginScreen(),
        ),
      );
      
      // Act
      await tester.tap(find.byType(ElevatedButton));
      await tester.pump();
      
      // Assert
      expect(find.text('Username is required'), findsOneWidget);
      expect(find.text('Password is required'), findsOneWidget);
    });
    
    testWidgets('should call login on valid submission', (tester) async {
      // Arrange
      final mockAuthService = MockAuthService();
      when(mockAuthService.login(
        username: anyNamed('username'),
        password: anyNamed('password'),
      )).thenAnswer((_) async => AuthResult.success(mockUser));
      
      await tester.pumpWidget(
        MaterialApp(
          home: Provider<AuthService>.value(
            value: mockAuthService,
            child: LoginScreen(),
          ),
        ),
      );
      
      // Act
      await tester.enterText(find.byType(TextField).first, 'testuser');
      await tester.enterText(find.byType(TextField).last, 'testpass');
      await tester.tap(find.byType(ElevatedButton));
      await tester.pump();
      
      // Assert
      verify(mockAuthService.login(
        username: 'testuser',
        password: 'testpass',
      )).called(1);
    });
  });
}
```

### Integration Testing

#### Feature Flow Testing
```dart
void main() {
  group('Authentication Flow', () {
    IntegrationTestWidgetsFlutterBinding.ensureInitialized();
    
    testWidgets('complete login flow', (tester) async {
      // Arrange
      await tester.pumpWidget(MyApp());
      
      // Act - Navigate to login
      await tester.tap(find.text('Login'));
      await tester.pumpAndSettle();
      
      // Act - Enter credentials
      await tester.enterText(find.byKey(Key('username_field')), 'testuser');
      await tester.enterText(find.byKey(Key('password_field')), 'testpass');
      await tester.tap(find.byKey(Key('login_button')));
      await tester.pumpAndSettle();
      
      // Assert - Should navigate to dashboard
      expect(find.text('Dashboard'), findsOneWidget);
      expect(find.text('Welcome, testuser'), findsOneWidget);
    });
  });
}
```

## 🚀 Build and Deployment

### Build Configurations

#### Development Build
```bash
flutter build apk --debug --flavor dev
flutter build ios --debug --flavor dev
```

#### Staging Build
```bash
flutter build apk --release --flavor staging
flutter build ios --release --flavor staging
```

#### Production Build
```bash
flutter build apk --release --flavor prod
flutter build ios --release --flavor prod
```

### Flavor Configuration

#### android/app/build.gradle
```gradle
android {
    flavorDimensions "default"
    
    productFlavors {
        dev {
            dimension "default"
            applicationIdSuffix ".dev"
            versionNameSuffix "-dev"
        }
        staging {
            dimension "default"
            applicationIdSuffix ".staging"
            versionNameSuffix "-staging"
        }
        prod {
            dimension "default"
        }
    }
}
```

#### iOS Configuration
Create separate schemes for each flavor in Xcode.

### Continuous Integration

#### GitHub Actions Workflow
```yaml
name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.16.0'
    
    - name: Install dependencies
      run: flutter pub get
    
    - name: Run code generation
      run: flutter packages pub run build_runner build --delete-conflicting-outputs
    
    - name: Run tests
      run: flutter test --coverage
    
    - name: Upload coverage
      uses: codecov/codecov-action@v3
      with:
        file: coverage/lcov.info
  
  build:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.16.0'
    
    - name: Install dependencies
      run: flutter pub get
    
    - name: Build Android APK
      run: flutter build apk --release --flavor prod
    
    - name: Upload APK
      uses: actions/upload-artifact@v3
      with:
        name: app-release.apk
        path: build/app/outputs/flutter-apk/app-prod-release.apk
```

## 📊 Code Quality

### Static Analysis

#### Custom Lint Rules
```yaml
# analysis_options.yaml
include: package:flutter_lints/flutter.yaml

analyzer:
  exclude:
    - "**/*.g.dart"
    - "**/*.freezed.dart"
  strong-mode:
    implicit-casts: false
    implicit-dynamic: false

linter:
  rules:
    # Errors
    - avoid_print
    - avoid_returning_null_for_void
    - avoid_slow_async_io
    - cancel_subscriptions
    - close_sinks
    - comment_references
    - control_flow_in_finally
    - empty_statements
    - hash_and_equals
    - invariant_booleans
    - iterable_contains_unrelated_type
    - list_remove_unrelated_type
    - literal_only_boolean_expressions
    - no_adjacent_strings_in_list
    - no_duplicate_case_values
    - no_logic_in_create_state
    - prefer_void_to_null
    - test_types_in_equals
    - throw_in_finally
    - unnecessary_statements
    - unrelated_type_equality_checks
    - use_key_in_widget_constructors
    - valid_regexps
    
    # Style
    - always_declare_return_types
    - always_put_control_body_on_new_line
    - always_put_required_named_parameters_first
    - always_require_non_null_named_parameters
    - annotate_overrides
    - avoid_annotating_with_dynamic
    - avoid_as
    - avoid_bool_literals_in_conditional_expressions
    - avoid_catches_without_on_clauses
    - avoid_catching_errors
    - avoid_double_and_int_checks
    - avoid_empty_else
    - avoid_field_initializers_in_const_classes
    - avoid_function_literals_in_foreach_calls
    - avoid_init_to_null
    - avoid_null_checks_in_equality_operators
    - avoid_positional_boolean_parameters
    - avoid_private_typedef_functions
    - avoid_redundant_argument_values
    - avoid_renaming_method_parameters
    - avoid_return_types_on_setters
    - avoid_returning_null
    - avoid_returning_null_for_future
    - avoid_returning_this
    - avoid_setters_without_getters
    - avoid_shadowing_type_parameters
    - avoid_single_cascade_in_expression_statements
    - avoid_types_as_parameter_names
    - avoid_types_on_closure_parameters
    - avoid_unnecessary_containers
    - avoid_unused_constructor_parameters
    - avoid_void_async
    - await_only_futures
    - camel_case_extensions
    - camel_case_types
    - cascade_invocations
    - cast_nullable_to_non_nullable
    - constant_identifier_names
    - curly_braces_in_flow_control_structures
    - directives_ordering
    - empty_catches
    - empty_constructor_bodies
    - file_names
    - flutter_style_todos
    - implementation_imports
    - join_return_with_assignment
    - leading_newlines_in_multiline_strings
    - library_names
    - library_prefixes
    - lines_longer_than_80_chars
    - missing_whitespace_between_adjacent_strings
    - no_runtimeType_toString
    - non_constant_identifier_names
    - null_closures
    - omit_local_variable_types
    - one_member_abstracts
    - only_throw_errors
    - overridden_fields
    - package_api_docs
    - package_names
    - package_prefixed_library_names
    - parameter_assignments
    - prefer_adjacent_string_concatenation
    - prefer_asserts_in_initializer_lists
    - prefer_asserts_with_message
    - prefer_collection_literals
    - prefer_conditional_assignment
    - prefer_const_constructors
    - prefer_const_constructors_in_immutables
    - prefer_const_declarations
    - prefer_const_literals_to_create_immutables
    - prefer_constructors_over_static_methods
    - prefer_contains
    - prefer_equal_for_default_values
    - prefer_expression_function_bodies
    - prefer_final_fields
    - prefer_final_in_for_each
    - prefer_final_locals
    - prefer_for_elements_to_map_fromIterable
    - prefer_function_declarations_over_variables
    - prefer_generic_function_type_aliases
    - prefer_if_elements_to_conditional_expressions
    - prefer_if_null_operators
    - prefer_initializing_formals
    - prefer_inlined_adds
    - prefer_int_literals
    - prefer_interpolation_to_compose_strings
    - prefer_is_empty
    - prefer_is_not_empty
    - prefer_is_not_operator
    - prefer_iterable_whereType
    - prefer_mixin
    - prefer_null_aware_operators
    - prefer_relative_imports
    - prefer_single_quotes
    - prefer_spread_collections
    - prefer_typing_uninitialized_variables
    - provide_deprecation_message
    - recursive_getters
    - slash_for_doc_comments
    - sort_child_properties_last
    - sort_constructors_first
    - sort_pub_dependencies
    - sort_unnamed_constructors_first
    - type_annotate_public_apis
    - type_init_formals
    - unawaited_futures
    - unnecessary_await_in_return
    - unnecessary_brace_in_string_interps
    - unnecessary_const
    - unnecessary_getters_setters
    - unnecessary_lambdas
    - unnecessary_new
    - unnecessary_null_aware_assignments
    - unnecessary_null_checks
    - unnecessary_null_in_if_null_operators
    - unnecessary_overrides
    - unnecessary_parenthesis
    - unnecessary_raw_strings
    - unnecessary_string_escapes
    - unnecessary_string_interpolations
    - unnecessary_this
    - use_full_hex_values_for_flutter_colors
    - use_function_type_syntax_for_parameters
    - use_rethrow_when_possible
    - use_setters_to_change_properties
    - use_string_buffers
    - use_to_and_as_if_applicable
    - void_checks
```

### Code Coverage

#### Coverage Configuration
```bash
# Generate coverage report
flutter test --coverage

# Generate HTML report
genhtml coverage/lcov.info -o coverage/html

# View coverage report
open coverage/html/index.html
```

#### Coverage Targets
- Unit Tests: 90%+ coverage
- Widget Tests: 80%+ coverage
- Integration Tests: 70%+ coverage

## 📝 Documentation

### Code Documentation
- Document all public APIs
- Use dartdoc comments (`///`)
- Include examples for complex functions
- Document parameters and return values

### README Updates
- Keep README.md current
- Update setup instructions
- Document new features
- Include troubleshooting guides

### Changelog
- Maintain CHANGELOG.md
- Follow semantic versioning
- Document breaking changes
- Include migration guides

## 🔄 Review Process

### Pull Request Guidelines

#### PR Template
```markdown
## Description
Brief description of changes

## Type of Change
- [ ] Bug fix
- [ ] New feature
- [ ] Breaking change
- [ ] Documentation update

## Testing
- [ ] Unit tests added/updated
- [ ] Widget tests added/updated
- [ ] Integration tests added/updated
- [ ] Manual testing completed

## Checklist
- [ ] Code follows style guidelines
- [ ] Self-review completed
- [ ] Documentation updated
- [ ] No new warnings/errors
- [ ] Tests pass locally
```

#### Review Checklist
- [ ] Code quality and style
- [ ] Test coverage
- [ ] Documentation updates
- [ ] Performance implications
- [ ] Security considerations
- [ ] Accessibility compliance

### Code Review Standards
- At least one approval required
- All tests must pass
- No merge conflicts
- Documentation updated
- Breaking changes documented

---

This development workflow ensures high code quality, maintainability, and efficient collaboration across the development team. 