// Supplier types for the suppliers module

export interface Supplier {
  uuid: string;
  warehouseUuid?: string;
  name: string;
  fiscalId?: string;
  rc?: string;
  articleNumber?: string;
  email?: string;
  phone?: string;
  address?: string;
  latitude?: number;
  longitude?: number;
  notes?: string;
  isDeleted: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface CreateSupplierDto {
  name: string;
  fiscalId?: string;
  rc?: string;
  articleNumber?: string;
  email?: string;
  phone?: string;
  address?: string;
  latitude?: number;
  longitude?: number;
  notes?: string;
  warehouseUuid?: string;
}

export interface UpdateSupplierDto {
  name?: string;
  fiscalId?: string;
  rc?: string;
  articleNumber?: string;
  email?: string;
  phone?: string;
  address?: string;
  latitude?: number;
  longitude?: number;
  notes?: string;
  warehouseUuid?: string;
}
