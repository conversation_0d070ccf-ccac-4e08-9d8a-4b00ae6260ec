"use client";

import React, { useState, useEffect } from "react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import styles from "./TopTaskBar.module.css";
import { FiChevronDown, FiUser, FiLogOut, FiGrid, FiPlus } from "react-icons/fi";
import { useAuth } from "@/contexts/AuthContext";
import { fetchWarehouses, fetchWarehouseById, Warehouse } from "@/app/settings/warehousesApi";

const TopTaskBar: React.FC = () => {
  const { user, fetchAndPersistWarehouseInfo, token, switchWarehouse } = useAuth();
  const router = useRouter();
  const currentWarehouse = user?.warehouseName || "Your Warehouse";
  const [isUserDropdownOpen, setIsUserDropdownOpen] = useState(false);
  const [isWarehouseDropdownOpen, setIsWarehouseDropdownOpen] = useState(false);
  const [warehouses, setWarehouses] = useState<Warehouse[]>([]);
  const [loadingWarehouses, setLoadingWarehouses] = useState(false);

  // Helper function to get display name
  const getDisplayName = () => {
    if (!user) return 'User';
    
    // Try to construct name from firstName and lastName
    if (user.firstName || user.lastName) {
      const firstName = user.firstName || '';
      const lastName = user.lastName || '';
      return `${firstName} ${lastName}`.trim();
    }
    
    // Fallback to legacy name field
    if (user.name) {
      return user.name;
    }
    
    // Fallback to email
    if (user.email) {
      return user.email;
    }
    
    return 'User';
  };

  // Helper function to get avatar initials
  const getAvatarInitials = () => {
    if (!user) return 'U';
    
    // Try to get initials from firstName and lastName
    if (user.firstName || user.lastName) {
      const firstName = user.firstName || '';
      const lastName = user.lastName || '';
      const firstInitial = firstName.charAt(0).toUpperCase();
      const lastInitial = lastName.charAt(0).toUpperCase();
      return firstInitial || lastInitial || 'U';
    }
    
    // Fallback to legacy name field
    if (user.name) {
      return user.name.charAt(0).toUpperCase();
    }
    
    // Fallback to email
    if (user.email) {
      return user.email.charAt(0).toUpperCase();
    }
    
    return 'U';
  };

  React.useEffect(() => {
    // Only refresh warehouse info if user exists but warehouse info is missing
    if (user?.uuid && (!user.warehouseUuid || !user.warehouseName)) {
      // Check if we have a valid token before making API calls
      const storedToken = localStorage.getItem('dido_token');
      if (!storedToken) {
        console.log('TopTaskBar: No valid token found, skipping warehouse info fetch');
        return;
      }
      // Pass the user explicitly to ensure we have the UUID
      fetchAndPersistWarehouseInfo(user, storedToken);
    }
    // After fetch, user context will update and localStorage will be set by context
  }, [user?.uuid, user?.warehouseUuid, user?.warehouseName]); // Remove fetchAndPersistWarhouseInfo from dependencies to prevent infinite loop

  // Fetch warehouses for dropdown - only show user's accessible warehouses
  useEffect(() => {
    const loadWarehouses = async () => {
      if (!user?.uuid) return;
      
      // Check if we have a valid token before making API calls
      const storedToken = localStorage.getItem('dido_token');
      if (!storedToken) {
        console.log('TopTaskBar: No valid token found, skipping warehouse fetch');
        return;
      }
      
      setLoadingWarehouses(true);
      try {
        // Fetch warehouses for the current user
        const warehousesData = await fetchWarehouses(user.uuid);
        setWarehouses(warehousesData);
        
        // If user has a specific warehouseUuid but it's not in the list, try to fetch it directly
        if (user.warehouseUuid && !warehousesData.find(w => w.uuid === user.warehouseUuid)) {
          try {
            const currentWarehouse = await fetchWarehouseById(user.warehouseUuid);
            setWarehouses(prev => [...prev, currentWarehouse]);
          } catch (error) {
            console.error('Failed to load user warehouse:', error);
          }
        }
      } catch (error) {
        console.error('Failed to load warehouses:', error);
        // If we can't fetch warehouses by userUuid, try to get all warehouses as fallback
        try {
          const warehousesData = await fetchWarehouses();
          setWarehouses(warehousesData);
        } catch (fallbackError) {
          console.error('Failed to load warehouses as fallback:', fallbackError);
        }
      } finally {
        setLoadingWarehouses(false);
      }
    };

    loadWarehouses();
  }, [user?.uuid, user?.warehouseUuid]);

  const handleWarehouseChange = async (warehouseUuid: string) => {
    try {
      // Find the selected warehouse
      const selectedWarehouse = warehouses.find(w => w.uuid === warehouseUuid);
      if (!selectedWarehouse) {
        console.error('Selected warehouse not found');
        return;
      }

      // Use the new switchWarehouse function from AuthContext
      await switchWarehouse(warehouseUuid, selectedWarehouse.name);
      
      // Close dropdown
      setIsWarehouseDropdownOpen(false);
    } catch (error) {
      console.error('Failed to change warehouse:', error);
      setIsWarehouseDropdownOpen(false);
    }
  };

  const handleAddWarehouse = () => {
    router.push('/logistics/warehouses');
    setIsWarehouseDropdownOpen(false);
  };

  return (
    <header className={styles.topTaskBar}>
      <div className={styles.leftSection}>
        <div className={styles.warehouseDropdown}>
          <button
            className={styles.warehouseDropdownTrigger}
            onClick={() => setIsWarehouseDropdownOpen(!isWarehouseDropdownOpen)}
            onBlur={() => setTimeout(() => setIsWarehouseDropdownOpen(false), 150)}
            disabled={loadingWarehouses}
          >
            <span className={styles.warhouseLabel}>Current Warehouse:</span>
            <span className={styles.warehouseName}>{currentWarehouse}</span>
            <FiChevronDown className={`${styles.warehouseDropdownIcon} ${isWarehouseDropdownOpen ? styles.rotated : ''}`} />
          </button>
          
          {isWarehouseDropdownOpen && (
            <div className={styles.warehouseDropdownMenu}>
              {loadingWarehouses ? (
                <div className={styles.warehouseDropdownItem}>
                  <span>Loading warehouse...</span>
                </div>
              ) : warehouses.length > 0 ? (
                warehouses.length === 1 ? (
                  <div className={styles.warehouseDropdownItem}>
                    <span>Current warehouse: {warehouses[0].name}</span>
                  </div>
                ) : (
                  warehouses.map((warehouse) => (
                    <button
                      key={warehouse.uuid}
                      className={`${styles.warehouseDropdownItem} ${user?.warehouseUuid === warehouse.uuid ? styles.active : ''}`}
                      onClick={() => handleWarehouseChange(warehouse.uuid)}
                    >
                      {warehouse.name}
                    </button>
                  ))
                )
              ) : (
                <div className={styles.warehouseDropdownItem}>
                  <span>No warehouses available</span>
                </div>
              )}
              <div className={styles.warehouseDropdownDivider}></div>
              <button
                className={`${styles.warehouseDropdownItem} ${styles.addWarehouseButton}`}
                onClick={handleAddWarehouse}
              >
                <FiPlus className={styles.warehouseDropdownItemIcon} />
                Add Warehouse
              </button>
            </div>
          )}
        </div>
      </div>
      <div className={styles.rightSection}>

        <div className={styles.userSection}>
          <div className={styles.userDropdown}>
            <button
              className={styles.userDropdownTrigger}
              onClick={() => setIsUserDropdownOpen(!isUserDropdownOpen)}
              onBlur={() => setTimeout(() => setIsUserDropdownOpen(false), 150)}
            >
              {user?.avatarUrl ? (
                <Image src={user.avatarUrl} alt="User avatar" width={32} height={32} className={styles.avatar} />
              ) : (
                <span className={styles.avatarPlaceholder}>
                  {getAvatarInitials()}
                </span>
              )}
              <span className={styles.userName}>{getDisplayName()}</span>
              <FiChevronDown className={`${styles.userDropdownIcon} ${isUserDropdownOpen ? styles.rotated : ''}`} />
            </button>
            
            {isUserDropdownOpen && (
              <div className={styles.userDropdownMenu}>
                <button 
                  className={styles.userDropdownItem}
                  onClick={() => {
                    router.push('/settings/profile');
                    setIsUserDropdownOpen(false);
                  }}
                >
                  <FiUser className={styles.userDropdownItemIcon} />
                  Profile
                </button>
                <button 
                  className={styles.userDropdownItem}
                  onClick={() => {
                    // Use the logout function from AuthContext instead of manual cleanup
                    localStorage.removeItem("dido_user");
                    localStorage.removeItem("dido_token");
                    localStorage.removeItem("dido_refresh_token");
                    router.replace("/auth");
                  }}
                >
                  <FiLogOut className={styles.userDropdownItemIcon} />
                  Logout
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
    </header>
  );
};

export default TopTaskBar;
