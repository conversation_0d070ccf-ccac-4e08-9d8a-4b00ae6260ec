import { IsOptional, IsString, IsUUID } from "class-validator";
import { ApiProperty } from "@nestjs/swagger";

export class FilterRegionDto {
  @IsOptional()
  @IsUUID("all")
  @ApiProperty({
    example: "uuid-v7-string",
    description: "Filter by warehouse UUID",
    required: false,
  })
  warehouseUuid?: string;

  @IsOptional()
  @IsString()
  @ApiProperty({
    example: "Downtown",
    description: "Filter by region name (partial match)",
    required: false,
  })
  name?: string;

  @IsOptional()
  @IsString()
  @ApiProperty({
    example: "business district",
    description: "Filter by region description (partial match)",
    required: false,
  })
  description?: string;
}
