import {
  Controller,
  Get,
  Post,
  Patch,
  Delete,
  Param,
  Body,
  Query,
  ParseUUIDPipe,
  BadRequestException,
} from "@nestjs/common";
import { CreatePurchaseDto } from "./dto/create-purchase.dto";
import { FilterPurchaseDto } from "./dto/filter-purchase.dto";
import { PurchaseDto } from "./dto/purchase.dto";
import { PurchaseService } from "./purchase.service";
import {
  ApiTags,
  ApiQuery,
  ApiBody,
  ApiOperation,
  ApiResponse,
  ApiParam,
} from "@nestjs/swagger";
import { PaymentMethods } from "./purchase.entity";

@ApiTags("purchases")
@Controller("purchases")
export class PurchaseController {
  constructor(private readonly purchaseService: PurchaseService) {}

  @Post()
  @ApiOperation({
    summary: "Create a new purchase",
    description:
      "Creates a new purchase with items. userUuid and warehouseUuid are required. supplierUuid is optional.",
  })
  @ApiBody({
    type: CreatePurchaseDto,
    description: "Purchase creation payload",
  })
  @ApiResponse({
    status: 201,
    description: "Purchase created successfully",
    type: PurchaseDto,
  })
  @ApiResponse({
    status: 400,
    description:
      "Bad request - validation error or required entities not found",
  })
  async create(
    @Body() createPurchaseDto: CreatePurchaseDto,
  ): Promise<PurchaseDto> {
    return this.purchaseService.create(createPurchaseDto);
  }

  @Post("filter")
  @ApiOperation({
    summary: "Filter purchases with advanced criteria",
    description:
      "Advanced filtering for purchases using POST body. Supports filtering by warehouse, supplier, status, and date ranges.",
  })
  @ApiBody({
    type: FilterPurchaseDto,
    description: "Advanced purchase filtering payload",
  })
  @ApiResponse({
    status: 200,
    description: "Filtered purchases retrieved successfully",
    type: [PurchaseDto],
  })
  async filter(@Body() filterDto: FilterPurchaseDto): Promise<PurchaseDto[]> {
    return this.purchaseService.filterPurchases(filterDto);
  }

  @Post(":uuid/products")
  @ApiOperation({
    summary: "Add products to existing purchase",
    description:
      "Adds or updates products in an existing purchase. Recalculates totals automatically.",
  })
  @ApiParam({
    name: "uuid",
    description: "UUIDv7 string of the purchase",
    example: "018e6b7c-9c2a-7b73-bb6d-0d1e8e2f4b1a",
  })
  @ApiBody({
    description: "Products to add to the purchase",
    schema: {
      type: "object",
      properties: {
        userUuid: { type: "string", example: "uuid-v7-string", description: "UUID of the user making the change" },
        items: {
          type: "array",
          items: {
            type: "object",
            properties: {
              productUuid: { type: "string", example: "uuid-v7-string" },
              name: { type: "string", example: "Product Name" },
              quantity: { type: "number", example: 5 },
              unitPrice: { type: "number", example: 9.99 },
              lineTotal: { type: "number", example: 49.95 },
            },
          },
        },
      },
    },
  })
  @ApiResponse({
    status: 200,
    description: "Products added successfully",
    type: PurchaseDto,
  })
  @ApiResponse({
    status: 404,
    description: "Purchase not found",
  })
  async addProductsToPurchase(
    @Param("uuid", ParseUUIDPipe) uuid: string,
    @Body() body: { userUuid: string; items: any[] },
  ): Promise<PurchaseDto> {
    return this.purchaseService.addProducts(uuid, body.items, body.userUuid);
  }

  @Patch(":uuid/payment")
  @ApiOperation({
    summary: "Set payment for purchase",
    description:
      "Updates payment information for a purchase and recalculates status based on amount paid.",
  })
  @ApiParam({
    name: "uuid",
    description: "UUIDv7 string of the purchase",
    example: "018e6b7c-9c2a-7b73-bb6d-0d1e8e2f4b1a",
  })
  @ApiBody({
    description: "Payment information",
    schema: {
      type: "object",
      properties: {
        userUuid: { type: "string", example: "uuid-v7-string", description: "UUID of the user making the change" },
        paymentMethod: {
          type: "string",
          enum: Object.values(PaymentMethods),
          example: PaymentMethods.CASH,
        },
        amountPaid: { type: "number", example: 50.0 },
      },
    },
  })
  @ApiResponse({
    status: 200,
    description: "Payment updated successfully",
    type: PurchaseDto,
  })
  @ApiResponse({
    status: 404,
    description: "Purchase not found",
  })
  async setPayment(
    @Param("uuid", ParseUUIDPipe) uuid: string,
    @Body() body: { userUuid: string; paymentMethod: string; amountPaid: number },
  ): Promise<PurchaseDto> {
    return this.purchaseService.setPayment(
      uuid,
      body.paymentMethod as PaymentMethods,
      body.amountPaid,
      body.userUuid,
    );
  }

  @Get()
  @ApiOperation({
    summary: "Get all purchases with optional filtering",
    description:
      "Returns all non-deleted purchases with optional query parameter filtering.",
  })
  @ApiQuery({
    name: "userUuid",
    required: false,
    type: String,
    description: "Filter by user UUID (optional)",
  })
  @ApiQuery({
    name: "warehouseUuid",
    required: false,
    type: String,
    description: "Filter by warehouse UUID (optional)",
  })
  @ApiQuery({
    name: "supplierUuid",
    required: false,
    type: String,
    description: "Filter by supplier UUID (optional)",
  })
  @ApiQuery({
    name: "status",
    required: false,
    type: String,
    description: "Filter by purchase status (optional)",
  })
  @ApiQuery({
    name: "createdFrom",
    required: false,
    type: String,
    description:
      "Filter purchases created after this date (optional, ISO 8601)",
  })
  @ApiQuery({
    name: "createdTo",
    required: false,
    type: String,
    description:
      "Filter purchases created before this date (optional, ISO 8601)",
  })
  @ApiResponse({
    status: 200,
    description: "Purchases retrieved successfully",
    type: [PurchaseDto],
  })
  async findAll(@Query() query: any): Promise<PurchaseDto[]> {
    return this.purchaseService.findAll(query, query.userUuid);
  }

  @Get(":uuid")
  @ApiOperation({
    summary: "Get purchase by UUID",
    description: "Returns a specific purchase by its UUID",
  })
  @ApiParam({
    name: "uuid",
    description: "UUIDv7 string of the purchase",
    example: "018e6b7c-9c2a-7b73-bb6d-0d1e8e2f4b1a",
  })
  @ApiQuery({
    name: "userUuid",
    required: false,
    type: String,
    description: "UUID of the user requesting the purchase (optional for authorization)",
  })
  @ApiResponse({
    status: 200,
    description: "Purchase retrieved successfully",
    type: PurchaseDto,
  })
  @ApiResponse({
    status: 404,
    description: "Purchase not found",
  })
  async findOne(@Param("uuid", ParseUUIDPipe) uuid: string, @Query("userUuid") userUuid?: string): Promise<PurchaseDto> {
    return this.purchaseService.findOne(uuid, userUuid);
  }

  @Delete(":uuid")
  @ApiOperation({
    summary: "Soft delete purchase",
    description:
      "Soft deletes a purchase by setting isDeleted to true. The purchase will not appear in normal queries but data is preserved.",
  })
  @ApiParam({
    name: "uuid",
    description: "UUIDv7 string of the purchase",
    example: "018e6b7c-9c2a-7b73-bb6d-0d1e8e2f4b1a",
  })
  @ApiBody({
    description: "User information for authorization",
    schema: {
      type: "object",
      properties: {
        userUuid: { type: "string", example: "uuid-v7-string", description: "UUID of the user deleting the purchase" },
      },
    },
  })
  @ApiResponse({
    status: 200,
    description: "Purchase soft deleted successfully",
  })
  @ApiResponse({
    status: 404,
    description: "Purchase not found",
  })
  async remove(@Param("uuid", ParseUUIDPipe) uuid: string, @Body() body: { userUuid: string }): Promise<void> {
    return this.purchaseService.remove(uuid, body.userUuid);
  }
}
