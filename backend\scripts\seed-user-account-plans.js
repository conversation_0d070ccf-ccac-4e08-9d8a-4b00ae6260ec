const { Client } = require('pg');
require('dotenv').config();

const client = new Client({
  host: process.env.YUGABYTE_HOST,
  port: process.env.YUGABYTE_PORT,
  database: process.env.YUGABYTE_DATABASE,
  user: process.env.YUGABYTE_USER,
  password: process.env.YUGABYTE_PASSWORD,
});

// Account plans data
const accountPlans = [
  {
    id: '018f1234-5678-9abc-def0-123456789abc', // You can generate proper UUIDs
    identifier: 'FREE',
    name: 'Free Plan',
    features: ['INVENTORY_TRACKING', 'SALES_ORDERS', 'CUSTOMER_MANAGEMENT'],
  },
  {
    id: '018f1234-5678-9abc-def0-123456789abd',
    identifier: 'BASIC',
    name: 'Basic Plan',
    features: [
      'INVENTORY_TRACKING',
      'STOCK_ALERTS',
      'SALES_ORDERS',
      'CASH_REGISTER',
      'INVOICES',
      'CUSTOMER_MANAGEMENT',
      'WAREHOUSE_MANAGEMENT',
      'SALES_REPORTS',
      'INVENTORY_REPORTS',
    ],
  },
  {
    id: '018f1234-5678-9abc-def0-123456789abe',
    identifier: 'PROFESSIONAL',
    name: 'Professional Plan',
    features: [
      'INVENTORY_TRACKING',
      'STOCK_ALERTS',
      'STOCK_TRANSFERS',
      'STOCK_ADJUSTMENTS',
      'SALES_ORDERS',
      'CASH_REGISTER',
      'INVOICES',
      'QUOTES',
      'RETURNS',
      'PURCHASE_ORDERS',
      'GOODS_RECEIPT',
      'SUPPLIER_MANAGEMENT',
      'WAREHOUSE_MANAGEMENT',
      'VAN_MANAGEMENT',
      'ROUTE_PLANNING',
      'VAN_STOCK_TRACKING',
      'SALES_REPORTS',
      'INVENTORY_REPORTS',
      'FINANCIAL_REPORTS',
      'VAN_PERFORMANCE_REPORTS',
      'USER_MANAGEMENT',
      'ROLE_MANAGEMENT',
      'CUSTOMER_MANAGEMENT',
      'MULTI_WAREHOUSE',
      'DATA_EXPORT',
    ],
  },
  {
    id: '018f1234-5678-9abc-def0-123456789abf',
    identifier: 'ENTERPRISE',
    name: 'Enterprise Plan',
    features: [
      'INVENTORY_TRACKING',
      'STOCK_ALERTS',
      'STOCK_TRANSFERS',
      'STOCK_ADJUSTMENTS',
      'SALES_ORDERS',
      'CASH_REGISTER',
      'INVOICES',
      'QUOTES',
      'RETURNS',
      'PURCHASE_ORDERS',
      'GOODS_RECEIPT',
      'SUPPLIER_MANAGEMENT',
      'WAREHOUSE_MANAGEMENT',
      'VAN_MANAGEMENT',
      'ROUTE_PLANNING',
      'VAN_STOCK_TRACKING',
      'SALES_REPORTS',
      'INVENTORY_REPORTS',
      'FINANCIAL_REPORTS',
      'VAN_PERFORMANCE_REPORTS',
      'USER_MANAGEMENT',
      'ROLE_MANAGEMENT',
      'CUSTOMER_MANAGEMENT',
      'MULTI_WAREHOUSE',
      'API_ACCESS',
      'DATA_EXPORT',
      'ADVANCED_ANALYTICS',
    ],
  },
];

// Features data
const features = [
  // Inventory Management
  { id: '018f1234-5678-9abc-def0-123456789ac0', identifier: 'INVENTORY_TRACKING', name: 'Inventory Tracking' },
  { id: '018f1234-5678-9abc-def0-123456789ac1', identifier: 'STOCK_ALERTS', name: 'Stock Alerts' },
  { id: '018f1234-5678-9abc-def0-123456789ac2', identifier: 'STOCK_TRANSFERS', name: 'Stock Transfers' },
  { id: '018f1234-5678-9abc-def0-123456789ac3', identifier: 'STOCK_ADJUSTMENTS', name: 'Stock Adjustments' },

  // Sales Management
  { id: '018f1234-5678-9abc-def0-123456789ac4', identifier: 'SALES_ORDERS', name: 'Sales Orders' },
  { id: '018f1234-5678-9abc-def0-123456789ac5', identifier: 'CASH_REGISTER', name: 'Cash Register' },
  { id: '018f1234-5678-9abc-def0-123456789ac6', identifier: 'INVOICES', name: 'Invoices' },
  { id: '018f1234-5678-9abc-def0-123456789ac7', identifier: 'QUOTES', name: 'Quotes' },
  { id: '018f1234-5678-9abc-def0-123456789ac8', identifier: 'RETURNS', name: 'Returns' },

  // Purchasing
  { id: '018f1234-5678-9abc-def0-123456789ac9', identifier: 'PURCHASE_ORDERS', name: 'Purchase Orders' },
  { id: '018f1234-5678-9abc-def0-123456789aca', identifier: 'GOODS_RECEIPT', name: 'Goods Receipt' },
  { id: '018f1234-5678-9abc-def0-123456789acb', identifier: 'SUPPLIER_MANAGEMENT', name: 'Supplier Management' },

  // Logistics
  { id: '018f1234-5678-9abc-def0-123456789acc', identifier: 'WAREHOUSE_MANAGEMENT', name: 'Warehouse Management' },
  { id: '018f1234-5678-9abc-def0-123456789acd', identifier: 'VAN_MANAGEMENT', name: 'Van Management' },
  { id: '018f1234-5678-9abc-def0-123456789ace', identifier: 'ROUTE_PLANNING', name: 'Route Planning' },
  { id: '018f1234-5678-9abc-def0-123456789acf', identifier: 'VAN_STOCK_TRACKING', name: 'Van Stock Tracking' },

  // Reporting
  { id: '018f1234-5678-9abc-def0-123456789ad0', identifier: 'SALES_REPORTS', name: 'Sales Reports' },
  { id: '018f1234-5678-9abc-def0-123456789ad1', identifier: 'INVENTORY_REPORTS', name: 'Inventory Reports' },
  { id: '018f1234-5678-9abc-def0-123456789ad2', identifier: 'FINANCIAL_REPORTS', name: 'Financial Reports' },
  { id: '018f1234-5678-9abc-def0-123456789ad3', identifier: 'VAN_PERFORMANCE_REPORTS', name: 'Van Performance Reports' },

  // User Management
  { id: '018f1234-5678-9abc-def0-123456789ad4', identifier: 'USER_MANAGEMENT', name: 'User Management' },
  { id: '018f1234-5678-9abc-def0-123456789ad5', identifier: 'ROLE_MANAGEMENT', name: 'Role Management' },
  { id: '018f1234-5678-9abc-def0-123456789ad6', identifier: 'CUSTOMER_MANAGEMENT', name: 'Customer Management' },

  // Advanced Features
  { id: '018f1234-5678-9abc-def0-123456789ad7', identifier: 'MULTI_WAREHOUSE', name: 'Multi-Warehouse Support' },
  { id: '018f1234-5678-9abc-def0-123456789ad8', identifier: 'API_ACCESS', name: 'API Access' },
  { id: '018f1234-5678-9abc-def0-123456789ad9', identifier: 'DATA_EXPORT', name: 'Data Export' },
  { id: '018f1234-5678-9abc-def0-123456789ada', identifier: 'ADVANCED_ANALYTICS', name: 'Advanced Analytics' },
];

async function seedData() {
  try {
    await client.connect();
    console.log('Connected to database');

    // Clear existing data
    await client.query('DELETE FROM account_plans');
    await client.query('DELETE FROM features');
    console.log('Cleared existing data');

    // Insert features
    for (const feature of features) {
      await client.query(
        'INSERT INTO features (id, identifier, name, "isDeleted", "createdAt", "updatedAt") VALUES ($1, $2, $3, $4, $5, $6)',
        [feature.id, feature.identifier, feature.name, false, new Date(), new Date()]
      );
    }
    console.log(`Inserted ${features.length} features`);

    // Insert account plans
    for (const plan of accountPlans) {
      await client.query(
        'INSERT INTO account_plans (id, identifier, name, features, "isDeleted", "createdAt", "updatedAt") VALUES ($1, $2, $3, $4, $5, $6, $7)',
        [plan.id, plan.identifier, plan.name, plan.features, false, new Date(), new Date()]
      );
    }
    console.log(`Inserted ${accountPlans.length} account plans`);

    console.log('Seeding completed successfully');
  } catch (error) {
    console.error('Error seeding data:', error);
  } finally {
    await client.end();
  }
}

seedData(); 