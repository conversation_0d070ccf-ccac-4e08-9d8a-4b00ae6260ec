import {
  <PERSON>S<PERSON>,
  IsBoolean,
  IsNumber,
  IsOptional,
  IsUUID,
  Min,
  Max,
  IsArray,
  ValidateNested,
  ArrayMinSize,
  IsEnum,
} from "class-validator";
import { ApiProperty } from "@nestjs/swagger";
import { Type } from "class-transformer";
import { SaleStatus, PaymentMethods } from "../sale.entity";

export class SaleItemDto {
  @ApiProperty({
    description: "UUID of the product",
    example: "018ea2bb-6b8d-7e9a-8a2a-7e9a8a2a7e9a",
  })
  @IsString()
  @IsUUID('all')
  productUuid: string;

  @ApiProperty({
    description: "Product name",
    example: "Sample Product",
  })
  @IsString()
  name: string;

  @ApiProperty({
    description: "Quantity of the product",
    example: 2,
  })
  @IsNumber()
  @Min(0.01, { message: "Quantity must be greater than 0" })
  quantity: number;

  @ApiProperty({
    description: "Unit price of the product",
    example: 25.50,
  })
  @IsNumber()
  @Min(0, { message: "Unit price must be at least 0" })
  unitPrice: number;

  @ApiProperty({
    description: "Total price for this line item (quantity * unitPrice)",
    example: 51.00,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0, { message: "Total price must be at least 0" })
  totalPrice?: number;
}

export class CreateSaleDto {
  @ApiProperty({
    description: "UUID of the user creating the sale",
    example: "018ea2bb-6b8d-7e9a-8a2a-7e9a8a2a7e9a",
  })
  @IsString()
  @IsUUID('all', { message: "userUuid must be a valid UUID" })
  userUuid!: string;

  @ApiProperty({
    description: "UUID of the warehouse where the sale is being made",
    example: "018ea2bb-6b8d-7e9a-8a2a-7e9a8a2a7e9a",
  })
  @IsString()
  @IsUUID('all', { message: "warehouseUuid must be a valid UUID" })
  warehouseUuid!: string;

  @ApiProperty({
    description: "UUID of the customer for the sale (optional for draft sales)",
    example: "018ea2bb-6b8d-7e9a-8a2a-7e9a8a2a7e9a",
    required: false,
  })
  @IsOptional()
  @IsString()
  @IsUUID('all', { message: "customerUuid must be a valid UUID" })
  customerUuid?: string;

  @ApiProperty({
    description: "List of items in the sale (optional for draft sales)",
    type: [SaleItemDto],
    example: [
      {
        productUuid: "018ea2bb-6b8d-7e9a-8a2a-7e9a8a2a7e9a",
        name: "Sample Product",
        quantity: 2,
        unitPrice: 25.50
      }
    ],
    required: false,
  })
  @IsOptional()
  @IsArray({ message: "items must be an array" })
  @ValidateNested({ each: true })
  @Type(() => SaleItemDto)
  items?: SaleItemDto[];

  @ApiProperty({
    example: true,
    description: "Whether tax is applied to this sale",
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  useTax?: boolean;

  @ApiProperty({
    example: 0.1,
    description: "Tax rate as decimal (e.g., 0.1 for 10%)",
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: "Tax rate must be a number" })
  @Min(0, { message: "Tax rate must be at least 0" })
  @Max(1, { message: "Tax rate must be at most 1" })
  taxRate?: number;

  @ApiProperty({
    enum: Object.values(SaleStatus),
    example: SaleStatus.PAID,
    description: "Status of the sale (PAID, PARTIALLY_PAID, UNPAID, CANCELLED)",
    required: false,
  })
  @IsOptional()
  @IsEnum(SaleStatus, { message: "status must be a valid sale status" })
  status?: SaleStatus;

  @ApiProperty({
    enum: Object.values(PaymentMethods),
    example: PaymentMethods.CASH,
    description: "Payment method for the sale",
    required: false,
  })
  @IsOptional()
  @IsEnum(PaymentMethods, { message: "paymentMethod must be a valid payment method" })
  paymentMethod?: PaymentMethods;

  @ApiProperty({
    example: 150.0,
    description: "Amount paid for the sale (cannot exceed total amount)",
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: "amountPaid must be a number" })
  @Min(0, { message: "amountPaid must be at least 0" })
  amountPaid?: number;
}
