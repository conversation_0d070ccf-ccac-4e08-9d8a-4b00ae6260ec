// Customer Modal Styles - Tailwind CSS classes for the Customer Modal

export const customerModalStyles = {
  modal: {
    overlay: "fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",
    container: "bg-white rounded-lg shadow-lg max-w-2xl w-full mx-4 max-h-[80vh] overflow-hidden flex flex-col",
    header: "p-6 border-b border-gray-200 flex-shrink-0",
    title: "text-xl font-bold text-gray-900",
    content: "p-6 flex-1 overflow-y-auto",
    footer: "p-6 border-t border-gray-200 flex gap-3 flex-shrink-0",
    
    form: {
      field: "mb-4",
      label: "block text-sm font-medium text-gray-700 mb-1",
      input: "w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",
      textarea: "w-full p-3 border border-gray-300 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-blue-500",
      error: "text-red-500 text-sm mt-1",
    },

    buttons: {
      primary: "flex-1 bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 font-medium transition-colors",
      secondary: "flex-1 bg-gray-300 text-gray-700 py-3 px-6 rounded-lg hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500 font-medium transition-colors",
      danger: "flex-1 bg-red-600 text-white py-3 px-6 rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 font-medium transition-colors",
    },
  },

  customer: {
    list: "space-y-3 max-h-96 overflow-y-auto pr-2",
    item: "p-4 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50 hover:border-blue-300 transition-all duration-200",
    itemSelected: "p-4 border-2 border-blue-500 rounded-lg cursor-pointer bg-blue-50 transition-all duration-200",
    itemName: "font-semibold text-gray-900 text-lg",
    itemInfo: "text-sm text-gray-600 mt-1",
    newCustomer: "space-y-4 mt-6 pt-6 border-t border-gray-200",
  },

  error: {
    container: "mb-4 p-4 bg-red-50 border border-red-200 rounded-lg",
    text: "text-red-700",
    icon: "text-red-500 mr-2",
  },

  loading: {
    container: "flex items-center justify-center py-8",
    spinner: "animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600",
    text: "ml-3 text-gray-600",
  },

  empty: {
    container: "text-center py-12",
    icon: "mx-auto h-12 w-12 text-gray-400 mb-4",
    text: "text-gray-500 text-lg",
    subtext: "text-gray-400 text-sm mt-2",
  },
}; 