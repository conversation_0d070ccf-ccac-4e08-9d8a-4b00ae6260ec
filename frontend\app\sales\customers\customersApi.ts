// customersApi.ts - API utilities for Customers endpoints
import axios from 'axios';
import { getAxiosAuthHeaders } from '@/utils/authHeaders';

const API_BASE = '/api/customers';

export interface Customer {
  uuid: string;
  name: string;
  email?: string;
  phone?: string;
  address?: string;
  fiscalId?: string;
  rc?: string;
  articleNumber?: string;
  customerType: 'retail' | 'wholesale' | 'mid-wholesale' | 'institutional';
  latitude?: number;
  longitude?: number;
  warehouseUuid: string;
  warehouseUuidString?: string;
  regionUuid?: string;
  regionUuidString?: string;
  creditLimit: number;
  currentCredit: number;
  isDeleted: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface CreateCustomerDto {
  name: string;
  email?: string;
  phone?: string;
  address?: string;
  fiscalId?: string;
  rc?: string;
  articleNumber?: string;
  customerType: 'retail' | 'wholesale' | 'mid-wholesale' | 'institutional';
  latitude?: number;
  longitude?: number;
  warehouseUuid: string;
  regionUuid?: string;
  creditLimit?: number;
}

export interface UpdateCustomerDto {
  name?: string;
  email?: string;
  phone?: string;
  address?: string;
  fiscalId?: string;
  rc?: string;
  articleNumber?: string;
  customerType?: 'retail' | 'wholesale' | 'mid-wholesale' | 'institutional';
  latitude?: number;
  longitude?: number;
  regionUuid?: string;
  creditLimit?: number;
}

export interface FilterCustomerDto {
  warehouseUuid?: string;
  name?: string;
  email?: string;
  phone?: string;
  fiscalId?: string;
  customerType?: 'retail' | 'wholesale' | 'mid-wholesale' | 'institutional';
  regionUuid?: string;
  currentCredit?: number;
  minCredit?: number;
  maxCredit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface PaginationQueryDto {
  page?: number;
  limit?: number;
}

export interface PaginatedResponseDto<T> {
  data: T[];
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export interface CustomerCredit {
  uuid: string;
  customerUuid: string;
  amount: number;
  type: 'credit' | 'debit';
  description: string;
  createdAt: string;
}

export interface CreditAdjustmentDto {
  amount: number;
  type: 'credit' | 'debit';
  description: string;
}

export interface CustomerPayment {
  uuid: string;
  customerUuid: string;
  amount: number;
  paymentMethod: string;
  paymentDate: string;
  description?: string;
  createdAt: string;
}

export interface PaymentDto {
  amount: number;
  paymentMethod: string;
  paymentDate: string;
  description?: string;
}

export interface CustomerSale {
  uuid: string;
  customerUuid: string;
  totalAmount: number;
  amountPaid: number;
  balanceDue: number;
  status: string;
  paymentMethod?: string;
  invoiceNumber?: string;
  invoiceDate?: string;
  createdAt: string;
}

/**
 * Get all customers with pagination and filtering
 */
export async function getCustomers(
  paginationQuery: PaginationQueryDto = { page: 1, limit: 10 }, 
  filter?: FilterCustomerDto
): Promise<PaginatedResponseDto<Customer>> {

  const params = new URLSearchParams();
  // Use paginationQuery values for pagination
  const page = paginationQuery.page;
  const limit = paginationQuery.limit;
  if (page) params.set('page', String(page));
  if (limit) params.set('limit', String(limit));

  // Add filter parameters
  if (filter?.warehouseUuid) params.set('warehouseUuid', filter.warehouseUuid);
  if (filter?.name) params.set('name', filter.name);
  if (filter?.email) params.set('email', filter.email);
  if (filter?.phone) params.set('phone', filter.phone);
  if (filter?.fiscalId) params.set('fiscalId', filter.fiscalId);

  const url = `${API_BASE}?${params.toString()}`;
  try {
    const response = await axios.get(url, {
      headers: getAxiosAuthHeaders(),
    });
    return response.data;
  } catch (error: any) {
    console.error('Customers API Error:', error.response?.data || error.message);
    console.error('Customers API Error Status:', error.response?.status);
    console.error('Customers API Error Headers:', error.response?.headers);
    
    // If GET endpoint fails with 400, try POST filter endpoint as fallback
    if (error.response?.status === 400) {
      console.log('GET endpoint failed, trying POST filter endpoint as fallback');
      try {
        return await filterCustomers(filter, paginationQuery);
      } catch (fallbackError: any) {
        console.error('Fallback POST filter also failed:', fallbackError);
        // Continue with original error handling
      }
    }
    
    // Provide more specific error messages
    if (error.response?.status === 400) {
      const errorMessage = error.response?.data?.message || 'Invalid request parameters';
      throw new Error(`Bad Request: ${errorMessage}. Please check warehouse UUID format and other parameters.`);
    } else if (error.response?.status === 401) {
      throw new Error('Authentication failed. Please log in again.');
    } else if (error.response?.status === 403) {
      throw new Error('Access denied. You do not have permission to view customers.');
    } else if (error.response?.status === 404) {
      throw new Error('Customers endpoint not found. Please check API configuration.');
    } else if (error.response?.status >= 500) {
      throw new Error('Server error. Please try again later.');
    } else if (error.code === 'NETWORK_ERROR') {
      throw new Error('Network error. Please check your connection and try again.');
    } else {
      throw new Error(`Failed to load customers: ${error.response?.data?.message || error.message}`);
    }
  }
}

/**
 * Filter customers using POST endpoint (for POS modal and advanced filtering)
 */
export async function filterCustomers(
  filter?: FilterCustomerDto,
  paginationQuery: PaginationQueryDto = { page: 1, limit: 10 }
): Promise<PaginatedResponseDto<Customer>> {
  
  console.log('Filter customers API call:', { filter, paginationQuery });
  
  try {
    const response = await axios.post(`${API_BASE}/filter`, {
      ...filter,
      ...paginationQuery
    }, {
      headers: getAxiosAuthHeaders(),
    });
    return response.data;
  } catch (error: any) {
    console.error('Filter customers API Error:', error.response?.data || error.message);
    console.error('Filter customers API Error Status:', error.response?.status);
    
    // Provide specific error messages for filter endpoint
    if (error.response?.status === 400) {
      const errorMessage = error.response?.data?.message || 'Invalid filter parameters';
      throw new Error(`Filter failed: ${errorMessage}. Please check your search criteria.`);
    } else if (error.response?.status === 404) {
      throw new Error('Filter endpoint not available. Please use the main customers endpoint.');
    } else if (error.response?.status >= 500) {
      throw new Error('Server error during filtering. Please try again later.');
    } else {
      throw new Error(`Failed to filter customers: ${error.response?.data?.message || error.message}`);
    }
  }
}

/**
 * Get a single customer by UUID
 */
export async function getCustomer(uuid: string): Promise<Customer> {
  const res = await axios.get(`${API_BASE}/${uuid}`, {
    headers: getAxiosAuthHeaders(),
  });
  return res.data;
}

/**
 * Get customer sales history
 */
export async function getCustomerSales(
  customerUuid: string,
  paginationQuery: PaginationQueryDto = { page: 1, limit: 10 }
): Promise<PaginatedResponseDto<CustomerSale>> {
  const params = new URLSearchParams();
  if (paginationQuery.page) params.append('page', paginationQuery.page.toString());
  if (paginationQuery.limit) params.append('limit', paginationQuery.limit.toString());

  const res = await axios.get(`${API_BASE}/${customerUuid}/sales?${params.toString()}`, {
    headers: getAxiosAuthHeaders(),
  });
  return res.data;
}

/**
 * Create a new customer
 */
export async function createCustomer(data: CreateCustomerDto): Promise<Customer> {
  const res = await axios.post(API_BASE, data, {
    headers: getAxiosAuthHeaders(),
  });
  return res.data;
}

/**
 * Update customer details
 */
export async function updateCustomer(uuid: string, data: UpdateCustomerDto): Promise<Customer> {
  const res = await axios.patch(`${API_BASE}/${uuid}`, data, {
    headers: getAxiosAuthHeaders(),
  });
  return res.data;
}

/**
 * Delete a customer (soft delete)
 */
export async function deleteCustomer(uuid: string): Promise<void> {
  await axios.delete(`${API_BASE}/${uuid}`, {
    headers: getAxiosAuthHeaders(),
  });
}

/**
 * Delete all customers (admin function)
 */
export async function deleteAllCustomers(): Promise<void> {
  await axios.delete(`${API_BASE}/all`, {
    headers: getAxiosAuthHeaders(),
  });
}

/**
 * Get customer credit information
 */
export async function getCustomerCredit(customerUuid: string): Promise<CustomerCredit[]> {
  const res = await axios.get(`${API_BASE}/${customerUuid}/credit`, {
    headers: getAxiosAuthHeaders(),
  });
  return res.data;
}

/**
 * Add credit adjustment to customer
 */
export async function addCustomerCreditAdjustment(
  customerUuid: string,
  data: CreditAdjustmentDto
): Promise<CustomerCredit> {
  const res = await axios.post(`${API_BASE}/${customerUuid}/credit`, data, {
    headers: getAxiosAuthHeaders(),
  });
  return res.data;
}

/**
 * Get customer payment history
 */
export async function getCustomerPayments(
  customerUuid: string,
  paginationQuery: PaginationQueryDto = { page: 1, limit: 10 }
): Promise<PaginatedResponseDto<CustomerPayment>> {
  const params = new URLSearchParams();
  if (paginationQuery.page) params.append('page', paginationQuery.page.toString());
  if (paginationQuery.limit) params.append('limit', paginationQuery.limit.toString());

  const res = await axios.get(`${API_BASE}/${customerUuid}/payments?${params.toString()}`, {
    headers: getAxiosAuthHeaders(),
  });
  return res.data;
}

/**
 * Add payment to customer
 */
export async function addCustomerPayment(
  customerUuid: string,
  data: PaymentDto
): Promise<CustomerPayment> {
  const res = await axios.post(`${API_BASE}/${customerUuid}/payments`, data, {
    headers: getAxiosAuthHeaders(),
  });
  return res.data;
}

// Additional interfaces for credit adjustments
export interface CreateCreditAdjustmentDto {
  amount: number;
  type: 'credit' | 'debit';
  description: string;
  customerUuid: string;
}

export interface CreditAdjustmentResponseDto {
  uuid: string;
  customerUuid: string;
  amount: number;
  type: 'credit' | 'debit';
  description: string;
  createdAt: string;
}

/**
 * Get a single customer by UUID (alias for getCustomer)
 */
export async function getCustomerByUuid(uuid: string): Promise<Customer> {
  return getCustomer(uuid);
}

/**
 * Get customer credit adjustment history
 */
export async function getCreditAdjustmentHistory(
  customerUuid: string,
  page: number = 1,
  limit: number = 10
): Promise<PaginatedResponseDto<CreditAdjustmentResponseDto>> {
  const params = new URLSearchParams();
  params.append('page', page.toString());
  params.append('limit', limit.toString());

  const res = await axios.get(`${API_BASE}/${customerUuid}/credit/history?${params.toString()}`, {
    headers: getAxiosAuthHeaders(),
  });
  return res.data;
}

/**
 * Hard delete all customers (admin function)
 */
export async function hardDeleteAllCustomers(): Promise<void> {
  await axios.delete(`${API_BASE}/all`, {
    headers: getAxiosAuthHeaders(),
  });
}

/**
 * Adjust customer credit balance
 */
export async function adjustCustomerCredit(
  customerUuid: string,
  data: Omit<CreateCreditAdjustmentDto, 'customerUuid'>
): Promise<CreditAdjustmentResponseDto> {
  const res = await axios.post(`${API_BASE}/${customerUuid}/credit/adjust`, data, {
    headers: getAxiosAuthHeaders(),
  });
  return res.data;
}

/**
 * Adjust customer credit balance allowing negative balance
 */
export async function adjustCustomerCreditAllowingNegative(
  customerUuid: string,
  data: Omit<CreateCreditAdjustmentDto, 'customerUuid'>
): Promise<CreditAdjustmentResponseDto> {
  const res = await axios.post(`${API_BASE}/${customerUuid}/credit/adjust-allow-negative`, data, {
    headers: getAxiosAuthHeaders(),
  });
  return res.data;
}
