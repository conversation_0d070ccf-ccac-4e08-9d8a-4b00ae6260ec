import { ApiProperty } from "@nestjs/swagger";
import { IsUUID, IsPositive, IsEnum, IsString, IsOptional, IsDateString } from "class-validator";
import { PaymentMethod, PaymentStatus } from "../customer-payment.entity";

export class UpdateCustomerPaymentDto {
  @ApiProperty({
    example: "uuid-v7-string",
    description: "UUID of the user updating the payment",
    required: false,
  })
  @IsOptional()
  @IsUUID("all")
  userUuid?: string;

  @ApiProperty({
    example: "cash",
    description: "Payment method used",
    enum: Object.values(PaymentMethod),
    required: false,
  })
  @IsOptional()
  @IsEnum(PaymentMethod)
  paymentMethod?: PaymentMethod;

  @ApiProperty({
    example: 100.0,
    description: "Payment amount (must be positive)",
    required: false,
  })
  @IsOptional()
  @IsPositive()
  amount?: number;

  @ApiProperty({
    example: "completed",
    description: "Payment status",
    enum: Object.values(PaymentStatus),
    required: false,
  })
  @IsOptional()
  @IsEnum(PaymentStatus)
  status?: PaymentStatus;

  @ApiProperty({
    example: "Updated payment for invoice #INV-001",
    description: "Description or note for the payment",
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    example: "REF-123456",
    description: "External reference number (e.g., transaction ID)",
    required: false,
  })
  @IsOptional()
  @IsString()
  referenceNumber?: string;

  @ApiProperty({
    example: "2024-01-15T10:30:00.000Z",
    description: "Date and time when the payment was processed",
    required: false,
  })
  @IsOptional()
  @IsDateString()
  processedAt?: string;
} 