/**
 * Customer Seeding Script for Yugabyte PostgreSQL
 * 
 * Usage: npx ts-node scripts/seed-customers-postgresql.ts
 * 
 * ⚠️ CRITICAL: This script performs a full database backup before execution
 */

import axios from 'axios';
const { exec } = require('child_process');
const { promisify } = require('util');
const path = require('path');
const fs = require('fs');

// Node.js global declarations
declare const process: any;
declare const require: any;
declare const module: any;
declare const __dirname: string;

// Configuration
const API_BASE_URL = 'http://localhost:8000';
const TOTAL_CUSTOMERS = 1000;
const TOTAL_REGIONS = 15;
const BATCH_SIZE = 50; // Increased batch size for PostgreSQL

// Algiers coordinates (center of the city)
const ALGIERS_CENTER = {
  latitude: 36.7538,
  longitude: 3.0588
};

// Algiers regions with realistic names and approximate coordinates
const ALGIERS_REGIONS = [
  { name: 'Alger Centre', description: 'Central business district and administrative center', latitude: 36.7538, longitude: 3.0588 },
  { name: 'Bab <PERSON>', description: 'Historic coastal district with traditional markets', latitude: 36.7900, longitude: 3.0500 },
  { name: 'Casbah', description: 'UNESCO World Heritage site, historic old town', latitude: 36.7850, longitude: 3.0600 },
  { name: 'El Madania', description: 'Residential area with modern housing', latitude: 36.7500, longitude: 3.0400 },
  { name: 'Sidi M\'Hamed', description: 'Government and embassy district', latitude: 36.7600, longitude: 3.0500 },
  { name: 'El Mouradia', description: 'Presidential palace and diplomatic area', latitude: 36.7700, longitude: 3.0400 },
  { name: 'Bologhine', description: 'Coastal residential area with beaches', latitude: 36.8000, longitude: 3.0200 },
  { name: 'Oued Koriche', description: 'Port area and industrial zone', latitude: 36.7800, longitude: 3.0800 },
  { name: 'Bir Mourad Rais', description: 'University district and student area', latitude: 36.7400, longitude: 3.0300 },
  { name: 'El Biar', description: 'Upscale residential neighborhood', latitude: 36.7600, longitude: 3.0200 },
  { name: 'Bouzareah', description: 'Hilly residential area with panoramic views', latitude: 36.7800, longitude: 3.0100 },
  { name: 'Cheraga', description: 'Western suburbs with modern developments', latitude: 36.7700, longitude: 2.9500 },
  { name: 'Draria', description: 'Eastern residential area', latitude: 36.7200, longitude: 3.0800 },
  { name: 'Zeralda', description: 'Coastal resort area west of Algiers', latitude: 36.7500, longitude: 2.8500 },
  { name: 'Staoueli', description: 'Beach resort and residential area', latitude: 36.7600, longitude: 2.9000 }
];

// Generate random coordinates within Algiers metropolitan area (approximately 20km radius)
function generateAlgiersCoordinates() {
  const radiusInDegrees = 0.18; // Approximately 20km
  const angle = Math.random() * 2 * Math.PI;
  const radius = Math.random() * radiusInDegrees;
  
  return {
    latitude: ALGIERS_CENTER.latitude + radius * Math.cos(angle),
    longitude: ALGIERS_CENTER.longitude + radius * Math.sin(angle)
  };
}

// Sample data for generating realistic Algerian customers
const algerianFirstNames = [
  'Ahmed', 'Mohamed', 'Fatima', 'Aicha', 'Omar', 'Khadija', 'Youssef', 'Amina',
  'Ali', 'Nadia', 'Karim', 'Zohra', 'Rachid', 'Samira', 'Hamid', 'Leila',
  'Abderrahmane', 'Malika', 'Mustapha', 'Yamina', 'Salim', 'Djamila', 'Tarek', 'Souad',
  'Abdelkader', 'Farida', 'Brahim', 'Naima', 'Djamel', 'Zineb', 'Nordine', 'Warda',
  'Farid', 'Karima', 'Hocine', 'Houria', 'Samir', 'Dalila', 'Abdelaziz', 'Hayat'
];

const algerianLastNames = [
  'Benali', 'Belaidi', 'Bensalem', 'Boumediene', 'Cherif', 'Djelloul', 'Ferhat', 'Ghali',
  'Hadj', 'Ikhlef', 'Kaci', 'Larbi', 'Meddour', 'Naceur', 'Ouali', 'Ramdane',
  'Sahraoui', 'Tebboune', 'Yahiaoui', 'Zidane', 'Amara', 'Boucherit', 'Chabane', 'Derradji',
  'Essaid', 'Fares', 'Guechi', 'Hamza', 'Idir', 'Khelifa', 'Lakhdar', 'Mammeri',
  'Nouri', 'Ould', 'Rebai', 'Slimani', 'Tounsi', 'Yacine', 'Zerrouki', 'Ait'
];

const algiersDistricts = [
  'Alger Centre', 'Bab El Oued', 'Casbah', 'El Madania', 'Sidi M\'Hamed',
  'El Mouradia', 'Bologhine', 'Oued Koriche', 'Bir Mourad Rais', 'El Biar',
  'Bouzareah', 'Cheraga', 'Draria', 'Zeralda', 'Staoueli', 'Ain Benian',
  'Bordj El Kiffan', 'El Marsa', 'Rouiba', 'Reghaia', 'Dar El Beida',
  'Bab Ezzouar', 'Boumerdes', 'Khraicia', 'Ain Taya', 'Heraoua',
  'Souidania', 'Tessala El Merdja', 'Ouled Chebel', 'Saoula', 'Birtouta'
];

const streetTypes = ['Rue', 'Avenue', 'Boulevard', 'Impasse', 'Place', 'Cité'];
const streetNames = [
  'des Martyrs', 'de l\'Indépendance', 'Didouche Mourad', 'Hassiba Ben Bouali',
  'Colonel Amirouche', 'Larbi Ben M\'hidi', 'Abane Ramdane', 'Krim Belkacem',
  '1er Novembre', 'Ben Bella', 'Houari Boumediene', 'Mohamed Khemisti',
  'Frantz Fanon', 'Emir Abdelkader', 'Ibn Khaldoun', 'El Mokrani',
  'Cheikh Bouamama', 'Si El Haouès', 'Mohamed Belouizdad', 'Bachir Attar'
];

const customerTypes: ('retail' | 'wholesale' | 'mid-wholesale' | 'institutional')[] = [
  'retail', 'wholesale', 'mid-wholesale', 'institutional'
];

const businessSectors = [
  'Épicerie', 'Pharmacie', 'Boulangerie', 'Café', 'Restaurant', 'Magasin de vêtements',
  'Quincaillerie', 'Librairie', 'Bijouterie', 'Coiffeur', 'Électronique', 'Automobile',
  'Textile', 'Alimentation générale', 'Parfumerie', 'Chaussures', 'Meubles', 'Jouets'
];

interface Warehouse {
  uuid: string;
  name: string;
  description?: string;
  userUuidString: string;
  mainStorageUuidString?: string;
  isDeleted: boolean;
  createdAt: Date;
  updatedAt: Date;
}

interface Region {
  uuid: string;
  name: string;
  description?: string;
  latitude?: number;
  longitude?: number;
  warehouseUuidString?: string;
  isDeleted: boolean;
  createdAt: Date;
  updatedAt: Date;
}

interface CreateRegionDto {
  name: string;
  description?: string;
  latitude?: number;
  longitude?: number;
  warehouseUuid?: string;
}

interface CreateCustomerDto {
  name: string;
  fiscalId?: string;
  email?: string;
  phone?: string;
  address?: string;
  rc?: string;
  articleNumber?: string;
  customerType: 'retail' | 'wholesale' | 'mid-wholesale' | 'institutional';
  latitude?: number;
  longitude?: number;
  warehouseUuid?: string;
  regionUuid?: string;
}

// Generate random Algerian phone number
function generateAlgerianPhone(): string {
  const prefixes = ['05', '06', '07']; // Algerian mobile prefixes
  const prefix = prefixes[Math.floor(Math.random() * prefixes.length)];
  const number = Math.floor(Math.random() * 90000000) + 10000000; // 8 digits
  return `+213 ${prefix} ${number.toString().substring(0, 4)} ${number.toString().substring(4)}`;
}

// Generate random Algerian fiscal ID
function generateFiscalId(): string {
  return (Math.floor(Math.random() * 900000000000000) + 100000000000000).toString(); // 15 digits
}

// Find the closest region to given coordinates
function findClosestRegion(latitude: number, longitude: number, regions: Region[]): Region | null {
  if (regions.length === 0) return null;
  
  let closestRegion = regions[0];
  let minDistance = Number.MAX_VALUE;
  
  for (const region of regions) {
    if (!region.latitude || !region.longitude) continue;
    
    const distance = Math.sqrt(
      Math.pow(latitude - region.latitude, 2) + 
      Math.pow(longitude - region.longitude, 2)
    );
    
    if (distance < minDistance) {
      minDistance = distance;
      closestRegion = region;
    }
  }
  
  return closestRegion;
}

// Generate random customer data
function generateRandomCustomer(warehouses: Warehouse[], regions: Region[]): CreateCustomerDto {
  const firstName = algerianFirstNames[Math.floor(Math.random() * algerianFirstNames.length)];
  const lastName = algerianLastNames[Math.floor(Math.random() * algerianLastNames.length)];
  const customerType = customerTypes[Math.floor(Math.random() * customerTypes.length)];
  
  let name = `${firstName} ${lastName}`;
  
  // For business customers, add business type
  if (customerType !== 'retail') {
    const businessType = businessSectors[Math.floor(Math.random() * businessSectors.length)];
    name = `${businessType} ${firstName} ${lastName}`;
  }
  
  // Generate address
  const streetType = streetTypes[Math.floor(Math.random() * streetTypes.length)];
  const streetName = streetNames[Math.floor(Math.random() * streetNames.length)];
  const streetNumber = Math.floor(Math.random() * 200) + 1;
  const district = algiersDistricts[Math.floor(Math.random() * algiersDistricts.length)];
  const address = `${streetNumber} ${streetType} ${streetName}, ${district}, Alger`;
  
  // Generate coordinates
  const coordinates = generateAlgiersCoordinates();
  
  // Find closest region and assign customer to it
  const closestRegion = findClosestRegion(coordinates.latitude, coordinates.longitude, regions);
  
  // Generate email (70% chance)
  const email = Math.random() < 0.7 ? 
    `${firstName.toLowerCase()}.${lastName.toLowerCase()}@${Math.random() < 0.5 ? 'gmail.com' : 'yahoo.com'}` : 
    undefined;
  
  // Generate phone (90% chance)
  const phone = Math.random() < 0.9 ? generateAlgerianPhone() : undefined;
  
  // Generate fiscal ID (80% chance for business customers, 30% for retail)
  const fiscalId = Math.random() < (customerType === 'retail' ? 0.3 : 0.8) ? 
    generateFiscalId() : 
    undefined;
  
  // Assign warehouse (REQUIRED - 100% chance)
  const warehouseUuid = warehouses.length > 0 ? 
    warehouses[Math.floor(Math.random() * warehouses.length)].uuid : 
    undefined;
  
  // Generate additional IDs for business customers
  const rc = customerType !== 'retail' && Math.random() < 0.6 ? 
    `RC${Math.floor(Math.random() * 900000) + 100000}` : // Commercial register
    undefined;
  
  const articleNumber = customerType === 'institutional' && Math.random() < 0.4 ? 
    `ART${Math.floor(Math.random() * 9000) + 1000}` : // Article number
    undefined;
  
  return {
    name,
    fiscalId,
    email,
    phone,
    address,
    rc,
    articleNumber,
    customerType,
    latitude: coordinates.latitude,
    longitude: coordinates.longitude,
    warehouseUuid,
    regionUuid: closestRegion?.uuid
  };
}

// Fetch all warehouses with timeout
async function fetchWarehouses(): Promise<Warehouse[]> {
  try {
    console.log('Fetching warehouses...');
    const response = await axios.get(`${API_BASE_URL}/warehouses/list`, {
      timeout: 10000, // 10 second timeout
      headers: {
        'Connection': 'close'
      }
    });
    // Handle both paginated and non-paginated responses
    const warehouses = response.data.data || response.data;
    const activeWarehouses = warehouses.filter((warehouse: Warehouse) => !warehouse.isDeleted);
    console.log(`Found ${activeWarehouses.length} active warehouses`);
    return activeWarehouses;
  } catch (error) {
    console.error('Error fetching warehouses:', error.message || error);
    console.log('Continuing without warehouse assignment...');
    return [];
  }
}

// Create a single region with retry logic
async function createRegion(region: CreateRegionDto, retries = 3): Promise<Region | null> {
  for (let attempt = 1; attempt <= retries; attempt++) {
    try {
      const response = await axios.post(`${API_BASE_URL}/regions`, region, {
        timeout: 10000, // 10 second timeout
        headers: {
          'Connection': 'close' // Force connection close to prevent pooling issues
        }
      });
      return response.data;
    } catch (error) {
      console.error(`Error creating region ${region.name} (attempt ${attempt}/${retries}):`, error.message || error);
      
      if (attempt < retries) {
        // Wait before retry with exponential backoff
        const delay = Math.min(1000 * Math.pow(2, attempt - 1), 5000);
        console.log(`Retrying in ${delay}ms...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }
  return null;
}

// Create all regions
async function createRegions(warehouses: Warehouse[]): Promise<Region[]> {
  console.log(`\n🏘️ Creating ${TOTAL_REGIONS} regions in Algiers...`);
  
  const createdRegions: Region[] = [];
  
  for (let i = 0; i < TOTAL_REGIONS; i++) {
    const regionData = ALGIERS_REGIONS[i];
    
    // Assign warehouse (REQUIRED - 100% chance)
    const warehouseUuid = warehouses.length > 0 ? 
      warehouses[Math.floor(Math.random() * warehouses.length)].uuid : 
      undefined;
    
    if (!warehouseUuid) {
      console.error(`❌ No warehouses available. Cannot create region: ${regionData.name}`);
      continue;
    }
    
    const region: CreateRegionDto = {
      name: regionData.name,
      description: regionData.description,
      latitude: regionData.latitude,
      longitude: regionData.longitude,
      warehouseUuid
    };
    
    const createdRegion = await createRegion(region);
    if (createdRegion) {
      createdRegions.push(createdRegion);
      console.log(`  ✅ Created region: ${regionData.name}`);
    } else {
      console.log(`  ❌ Failed to create region: ${regionData.name}`);
    }
    
    // Small delay to avoid overwhelming the server
    await new Promise(resolve => setTimeout(resolve, 200));
  }
  
  console.log(`Completed region creation: ${createdRegions.length}/${TOTAL_REGIONS} regions created`);
  return createdRegions;
}

// Create a single customer with retry logic
async function createCustomer(customer: CreateCustomerDto, retries = 3): Promise<boolean> {
  for (let attempt = 1; attempt <= retries; attempt++) {
    try {
      await axios.post(`${API_BASE_URL}/customers`, customer, {
        timeout: 10000, // 10 second timeout
        headers: {
          'Connection': 'close' // Force connection close to prevent pooling issues
        }
      });
      return true;
    } catch (error) {
      console.error(`Error creating customer ${customer.name} (attempt ${attempt}/${retries}):`, error.message || error);
      
      if (attempt < retries) {
        // Wait before retry with exponential backoff
        const delay = Math.min(1000 * Math.pow(2, attempt - 1), 5000);
        console.log(`Retrying in ${delay}ms...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }
  return false;
}

// Create customers in batches
async function createCustomersBatch(customers: CreateCustomerDto[]): Promise<number> {
  const promises = customers.map(customer => createCustomer(customer));
  const results = await Promise.all(promises);
  return results.filter(result => result).length;
}

// Main seeding function
async function seedCustomers(warehouses: Warehouse[], regions: Region[]): Promise<void> {
  console.log(`\n👥 Seeding ${TOTAL_CUSTOMERS} customers in Algiers...`);
  
  let totalCreated = 0;
  const totalBatches = Math.ceil(TOTAL_CUSTOMERS / BATCH_SIZE);
  
  for (let batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
    const batchStart = batchIndex * BATCH_SIZE;
    const batchEnd = Math.min(batchStart + BATCH_SIZE, TOTAL_CUSTOMERS);
    const batchSize = batchEnd - batchStart;
    
    // Generate customers for this batch
    const customers: CreateCustomerDto[] = [];
    for (let i = 0; i < batchSize; i++) {
      customers.push(generateRandomCustomer(warehouses, regions));
    }
    
    // Create customers in batch
    const created = await createCustomersBatch(customers);
    totalCreated += created;
    
    const progress = ((batchIndex + 1) / totalBatches * 100).toFixed(1);
    console.log(`  Batch ${batchIndex + 1}/${totalBatches} (${progress}%): ${created}/${batchSize} customers created successfully`);
    
    // Small delay to avoid overwhelming the server
    await new Promise(resolve => setTimeout(resolve, 300));
  }
  
  console.log(`Completed seeding: ${totalCreated}/${TOTAL_CUSTOMERS} customers created`);
}

// Generate statistics about the seeded data
function generateStats(warehouses: Warehouse[], regions: Region[]): void {
  console.log('\n📊 Seeding Statistics:');
  console.log(`- Total customers to create: ${TOTAL_CUSTOMERS}`);
  console.log(`- Total regions to create: ${TOTAL_REGIONS}`);
  console.log(`- Location: Algiers, Algeria`);
  console.log(`- Coordinate range: ${ALGIERS_CENTER.latitude}±0.18, ${ALGIERS_CENTER.longitude}±0.18`);
  console.log(`- Districts covered: ${algiersDistricts.length} districts`);
  console.log(`- Available warehouses: ${warehouses.length}`);
  console.log(`- Customer types: ${customerTypes.join(', ')}`);
  console.log(`- Batch size: ${BATCH_SIZE} customers per batch`);
  console.log(`- Expected completion time: ~${Math.ceil((TOTAL_REGIONS + TOTAL_CUSTOMERS / BATCH_SIZE) * 0.3)} seconds`);
}

// ⚠️ CRITICAL: Full Database Backup Function using Backend API
async function performFullDatabaseBackup(): Promise<void> {
  console.log('🔄 Starting FULL DATABASE BACKUP (mandatory)...');
  
  try {
    // Get backup API configuration
    const BACKUP_API_BASE_URL = process.env.BACKUP_API_BASE_URL || 'http://localhost:5000';
    
    // Validate backup API connectivity
    try {
      const healthResponse = await axios.get(`${BACKUP_API_BASE_URL}/health/`, { timeout: 10000 });
      if (healthResponse.status !== 200 || healthResponse.data.status !== 'healthy') {
        throw new Error('Backup API is not healthy');
      }
      console.log('✅ Backup API is healthy and accessible');
    } catch (healthError) {
      console.log('❌ Backup API is not accessible, attempting fallback backup...');
      throw new Error('Backup API not available');
    }
    
    // Create backup using the API
    console.log('🔄 Creating backup via API...');
    const backupResponse = await axios.post(`${BACKUP_API_BASE_URL}/backup/`, {
      prefix: 'seed-customers'
    }, {
      headers: { 'Content-Type': 'application/json' },
      timeout: 30000
    });
    
    if (backupResponse.data.status === 'success') {
      const backupData = backupResponse.data.data;
      const backupName = backupData.backup_name;
      const timestamp = backupData.timestamp;
      const sizeBytes = backupData.size_bytes || 0;
      const sizeMb = sizeBytes / (1024 * 1024);
      
      console.log('✅ FULL DATABASE BACKUP completed successfully');
      console.log(`   [NAME] ${backupName}`);
      console.log(`   [TIME] ${timestamp}`);
      console.log(`   [SIZE] ${sizeMb.toFixed(2)} MB`);
      console.log(`   [RESTORE] To restore: python scripts/migration_manager_ysqlsh.py restore-from ${backupName}`);
      
    } else {
      throw new Error(`Backup API returned error: ${backupResponse.data}`);
    }
    
  } catch (error) {
    console.error('❌ CRITICAL ERROR: Full database backup failed');
    console.error('🔴 Script execution aborted for safety');
    console.error('📋 Error details:', error instanceof Error ? error.message : String(error));
    console.error('\n💡 Troubleshooting:');
    console.error('   1. Ensure the backup backend is running');
    console.error('   2. Check BACKUP_API_BASE_URL environment variable');
    console.error('   3. Verify network connectivity to backup API');
    process.exit(1);
  }
}

// Main function
async function main() {
  console.log('🇩🇿 Starting Algerian customer and region seeding process for Yugabyte PostgreSQL...');
  console.log(`Configuration: ${TOTAL_CUSTOMERS} customers, ${TOTAL_REGIONS} regions in Algiers, batch size: ${BATCH_SIZE}`);
  
  try {
    // ⚠️ CRITICAL: Perform full database backup first
    await performFullDatabaseBackup();
    
    // Fetch all warehouses
    const warehouses = await fetchWarehouses();
    
    // Generate statistics
    generateStats(warehouses, []);
    
    // Create regions first
    const regions = await createRegions(warehouses);
    
    if (regions.length === 0) {
      console.error('❌ No regions were created. Cannot proceed with customer seeding.');
      process.exit(1);
    }
    
    // Seed customers with region assignment
    await seedCustomers(warehouses, regions);
    
    console.log(`\n✅ Customer and region seeding completed successfully!`);
    console.log(`📍 All customers and regions are located in Algiers, Algeria`);
    console.log(`🏘️ Created ${regions.length} regions`);
    console.log(`👥 Created ${TOTAL_CUSTOMERS} customers distributed across regions`);
    console.log(`🏢 ${warehouses.length > 0 ? `Regions and customers distributed across ${warehouses.length} warehouses` : 'No warehouses assigned'}`);
    
  } catch (error) {
    console.error('❌ Error during seeding process:', error);
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  main();
} 