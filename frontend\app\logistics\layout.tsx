import { SideTaskBar } from '@/components/SideTaskBar/SideTaskBar';
import TopTaskBar from '@/components/TopTaskBar/TopTaskBar';
import React from 'react';

export default function LogisticsLayout({ children }: { children: React.ReactNode }) {
  return (
    <div className="flex h-screen">
      <SideTaskBar />
      <div className="flex-1 flex flex-col">
        <TopTaskBar />
        <main className="flex-1 p-6 bg-gray-50/90 overflow-auto transition-all duration-200">
          <div className="max-w-7xl mx-auto">
            {children}
          </div>
        </main>
      </div>
    </div>
  );
}
