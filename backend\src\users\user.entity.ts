import { <PERSON><PERSON><PERSON>, PrimaryColumn, Column, CreateDateColumn, UpdateDateColumn, Index } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Uuid7 } from '../utils/uuid7';

@Entity('users')
export class User {
  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the user",
  })
  @PrimaryColumn('uuid')
  id: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the warehouse the user belongs to",
  })
  @Column('uuid')
  @Index()
  warehouseUuid: string;

  @ApiProperty({
    example: "<EMAIL>",
    description: "Email address",
    required: false,
  })
  @Column({ nullable: true, unique: true })
  email?: string;

  @ApiProperty({ example: "John Doe", description: "User name" })
  @Column()
  name: string;

  @ApiProperty({
    example: "018ea2bb-6b8d-7e9a-8a2a-7e9a8a2a7e9a",
    description: "UUID of the user role",
    required: false,
  })
  @Column('uuid', { nullable: true })
  @Index()
  roleUuid?: string;

  @ApiProperty({
    example: "super",
    description: "User type: 'super' (owner) or 'user' (staff/admin)",
    required: false,
    enum: ["super", "user"],
    default: "user",
  })
  @Column({ 
    type: 'enum', 
    enum: ['super', 'user'], 
    default: 'user' 
  })
  userType: "super" | "user";

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the van assigned to the user",
    required: false,
  })
  @Column('uuid', { nullable: true })
  @Index()
  vanUuid?: string;

  @ApiProperty({
    example: "$2b$10$...",
    description: "Hashed user password (bcrypt)",
    required: false,
  })
  @Column({ nullable: true })
  password?: string;

  @ApiProperty({ example: false, description: "Soft delete flag" })
  @Column({ default: false })
  isDeleted: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Helper method to generate UUID
  static generateId(): string {
    return new Uuid7().toString();
  }

  // Helper method to generate UUID
  static fromBinary(binary: any): string {
    if (!binary) return null;
    try {
      return new Uuid7(binary).toString();
    } catch {
      return null;
    }
  }
} 