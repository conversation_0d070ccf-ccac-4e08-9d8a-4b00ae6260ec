// useCustomers.ts - React Query hooks for customers endpoints
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  getCustomers,
  getCustomerByUuid,
  createCustomer,
  updateCustomer,
  deleteCustomer,
  getCustomerSales,
  hardDeleteAllCustomers,
  adjustCustomerCredit,
  adjustCustomerCreditAllowingNegative,
  getCreditAdjustmentHistory,
  getCustomerPayments,
  getCustomerCredit,
  Customer,
  CreateCustomerDto,
  UpdateCustomerDto,
  FilterCustomerDto,
  CustomerSale,
  PaginationQueryDto,
  PaginatedResponseDto,
  CreateCreditAdjustmentDto,
  CreditAdjustmentResponseDto
} from './customersApi';

// Hook for getting paginated customers with filtering and sorting
// Updated to use the correct GET endpoint with query parameters
export function useCustomers(
  paginationQuery?: PaginationQueryDto,
  filter?: FilterCustomerDto
) {
  return useQuery({
    queryKey: ['customers', paginationQuery, filter],
    queryFn: () => getCustomers(paginationQuery, filter),
  });
}

// Hook for getting a single customer
export function useCustomer(uuid?: string) {
  return useQuery({
    queryKey: ['customers', uuid],
    queryFn: () => getCustomerByUuid(uuid!),
    enabled: !!uuid,
  });
}

// Hook for getting customer sales
export function useCustomerSales(uuid?: string, page: number = 1, limit: number = 10) {
  return useQuery({
    queryKey: ['customers', uuid, 'sales', page, limit],
    queryFn: () => getCustomerSales(uuid!, { page, limit }),
    enabled: !!uuid,
  });
}

// Hook for getting customer credit
export function useCustomerCredit(uuid?: string) {
  return useQuery({
    queryKey: ['customers', uuid, 'credit'],
    queryFn: () => getCustomerCredit(uuid!),
    enabled: !!uuid,
  });
}

// Hook for getting customer credit history
export function useCustomerCreditHistory(uuid?: string, page: number = 1, limit: number = 10) {
  return useQuery({
    queryKey: ['customers', uuid, 'credit', 'history', page, limit],
    queryFn: () => getCreditAdjustmentHistory(uuid!, page, limit),
    enabled: !!uuid,
  });
}

// Hook for getting customer payments
export function useCustomerPayments(uuid?: string, page: number = 1, limit: number = 10) {
  return useQuery({
    queryKey: ['customers', uuid, 'payments', page, limit],
    queryFn: () => getCustomerPayments(uuid!, { page, limit }),
    enabled: !!uuid,
  });
}

// Mutation hooks
export function useCreateCustomer() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: createCustomer,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['customers'] });
    },
  });
}

export function useUpdateCustomer() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ uuid, data }: { uuid: string; data: UpdateCustomerDto }) =>
      updateCustomer(uuid, data),
    onSuccess: (_, { uuid }) => {
      queryClient.invalidateQueries({ queryKey: ['customers'] });
      queryClient.invalidateQueries({ queryKey: ['customers', uuid] });
    },
  });
}

export function useDeleteCustomer() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: deleteCustomer,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['customers'] });
    },
  });
}

export function useHardDeleteAllCustomers() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: hardDeleteAllCustomers,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['customers'] });
    },
  });
}

export function useAdjustCustomerCredit() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ uuid, data }: { uuid: string; data: Omit<CreateCreditAdjustmentDto, 'customerUuid'> }) =>
      adjustCustomerCredit(uuid, data),
    onSuccess: (_, { uuid }) => {
      queryClient.invalidateQueries({ queryKey: ['customers'] });
      queryClient.invalidateQueries({ queryKey: ['customers', uuid] });
      queryClient.invalidateQueries({ queryKey: ['customers', uuid, 'credit'] });
      queryClient.invalidateQueries({ queryKey: ['customers', uuid, 'credit', 'history'] });
    },
  });
}

export function useAdjustCustomerCreditAllowingNegative() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ uuid, data }: { uuid: string; data: Omit<CreateCreditAdjustmentDto, 'customerUuid'> }) =>
      adjustCustomerCreditAllowingNegative(uuid, data),
    onSuccess: (_, { uuid }) => {
      queryClient.invalidateQueries({ queryKey: ['customers'] });
      queryClient.invalidateQueries({ queryKey: ['customers', uuid] });
      queryClient.invalidateQueries({ queryKey: ['customers', uuid, 'credit'] });
      queryClient.invalidateQueries({ queryKey: ['customers', uuid, 'credit', 'history'] });
    },
  });
}
