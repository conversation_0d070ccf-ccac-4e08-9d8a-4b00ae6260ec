import React from 'react';
import { SalesFilters } from './SalesFilters';
import { SalesListView } from './SalesListView';

interface ListComponentProps {
  // Sales data props
  sales: any[];
  totalPages: number;
  currentPage: number;
  isLoading: boolean;
  isLoadingInvoice: boolean;
  isLoadingEditSale?: boolean;
  isLoadingViewDetails?: boolean;
  isLoadingPrintInvoice?: boolean;
  isLoadingCancel?: boolean;
  
  // Filter props
  showFilters: boolean;
  filter: any;
  
  // Action handlers
  onPageChange: (page: number) => void;
  onViewDetails: (sale: any) => void;
  onEdit: (sale: any) => void;
  onPrintInvoice: (sale: any) => void;
  onCancel: (sale: any) => void;
  onFilterChange: (filter: any) => void;
  onClearFilters: () => void;
  onToggleFilters: () => void;
}

export const ListComponent: React.FC<ListComponentProps> = ({
  sales,
  totalPages,
  currentPage,
  isLoading,
  isLoadingInvoice,
  isLoadingEditSale = false,
  isLoadingViewDetails = false,
  isLoadingPrintInvoice = false,
  isLoadingCancel = false,
  showFilters,
  filter,
  onPageChange,
  onViewDetails,
  onEdit,
  onPrintInvoice,
  onCancel,
  onFilterChange,
  onClearFilters,
  onToggleFilters,
}) => {
  return (
    <div className="h-full flex flex-col overflow-y-auto p-8">
      <div className="max-w-7xl mx-auto w-full flex flex-col h-full">
        {/* Main Content */}
        <div className="flex-1 flex flex-col">
          {/* Filters */}
          <SalesFilters
            showFilters={showFilters}
            filter={filter}
            onFilterChange={onFilterChange}
            onClearFilters={onClearFilters}
            onToggleFilters={onToggleFilters}
          />

          {/* Sales List */}
          <SalesListView
            sales={sales}
            totalPages={totalPages}
            currentPage={currentPage}
            isLoading={isLoading}
            isLoadingInvoice={isLoadingInvoice}
            isLoadingEditSale={isLoadingEditSale}
            isLoadingViewDetails={isLoadingViewDetails}
            isLoadingPrintInvoice={isLoadingPrintInvoice}
            isLoadingCancel={isLoadingCancel}
            onPageChange={onPageChange}
            onViewDetails={onViewDetails}
            onEdit={onEdit}
            onPrintInvoice={onPrintInvoice}
            onCancel={onCancel}
          />
        </div>
      </div>
    </div>
  );
}; 