import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  Query,
  ParseUUIDPipe,
  Patch,
  UseGuards,
} from "@nestjs/common";
import { RoutesService } from "./routes.service.typeorm";
import { ComputeRouteDto } from "./dto/compute-route.dto";
import { UpdateRouteDto } from "./dto/update-route.dto";
import { FilterRouteDto } from "./dto/filter-route.dto";
import { RouteResponseDto } from "./dto/route-response.dto";
import { HardDeleteResponseDto } from "./dto/hard-delete-response.dto";
import {
  PaginationQueryDto,
  PaginatedResponseDto,
} from "../dto/pagination.dto";
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBody,
} from "@nestjs/swagger";
@ApiTags("routes")
@Controller("routes")
export class RoutesController {
  constructor(private readonly routesService: RoutesService) {}

  @Post("compute")
  @ApiOperation({ summary: "Compute optimized route from customer locations" })
  @ApiBody({
    type: ComputeRouteDto,
    description: "Route computation data including customer locations",
  })
  @ApiResponse({
    status: 201,
    description: "Route computed and saved successfully",
    type: RouteResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: "Invalid input data or insufficient locations",
  })
  computeRoute(
    @Body() computeRouteDto: ComputeRouteDto,
  ): Promise<RouteResponseDto> {
    return this.routesService.computeRoute(computeRouteDto);
  }

  @Get()
  @ApiOperation({ summary: "Get all routes with pagination and filtering" })
  @ApiQuery({
    name: "page",
    required: false,
    type: Number,
    example: 1,
    description: "Page number (1-based)",
  })
  @ApiQuery({
    name: "limit",
    required: false,
    type: Number,
    example: 10,
    description: "Number of items per page",
  })
  @ApiQuery({
    name: "warehouseUuid",
    required: false,
    type: String,
    description: "Filter by warehouse UUID",
  })
  @ApiQuery({
    name: "name",
    required: false,
    type: String,
    description: "Filter by route name (partial match)",
  })
  @ApiQuery({
    name: "description",
    required: false,
    type: String,
    description: "Filter by route description (partial match)",
  })
  @ApiResponse({
    status: 200,
    description: "Routes retrieved successfully",
    type: PaginatedResponseDto,
  })
  findAll(
    @Query() paginationQuery: PaginationQueryDto,
    @Query() filter: FilterRouteDto,
  ): Promise<PaginatedResponseDto<RouteResponseDto>> {
    return this.routesService.findAll(paginationQuery, filter);
  }

  @Get(":uuid")
  @ApiOperation({ summary: "Get a route by UUID" })
  @ApiParam({
    name: "uuid",
    type: String,
    example: "uuid-v7-string",
    description: "Route UUID",
  })
  @ApiResponse({
    status: 200,
    description: "Route retrieved successfully",
    type: RouteResponseDto,
  })
  @ApiResponse({ status: 404, description: "Route not found" })
  findOne(
    @Param("uuid", ParseUUIDPipe) uuid: string,
  ): Promise<RouteResponseDto> {
    return this.routesService.findOne(uuid);
  }

  @Patch(":uuid")
  @ApiOperation({ summary: "Update an existing route by UUID" })
  @ApiParam({
    name: "uuid",
    type: String,
    example: "uuid-v7-string",
    description: "Route UUID",
  })
  @ApiBody({
    type: UpdateRouteDto,
    description: "Route update data (all fields optional)",
  })
  @ApiResponse({
    status: 200,
    description: "Route updated successfully",
    type: RouteResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: "Invalid input data or insufficient locations",
  })
  @ApiResponse({ status: 404, description: "Route not found" })
  update(
    @Param("uuid", ParseUUIDPipe) uuid: string,
    @Body() updateRouteDto: UpdateRouteDto,
  ): Promise<RouteResponseDto> {
    return this.routesService.update(uuid, updateRouteDto);
  }

  @Delete("all")
  @ApiOperation({
    summary:
      "Hard delete all routes (permanently removes all routes from the database)",
  })
  @ApiResponse({
    status: 200,
    description: "All routes deleted successfully",
    type: HardDeleteResponseDto,
  })
  @ApiResponse({ status: 500, description: "Internal server error" })
  hardDeleteAll(): Promise<HardDeleteResponseDto> {
    return this.routesService.hardDeleteAll();
  }

  @Delete(":uuid")
  @ApiOperation({
    summary: "Soft delete a route by UUID (sets isDeleted=true)",
  })
  @ApiParam({
    name: "uuid",
    type: String,
    example: "uuid-v7-string",
    description: "Route UUID",
  })
  @ApiResponse({ status: 200, description: "Route deleted successfully" })
  @ApiResponse({ status: 404, description: "Route not found" })
  remove(@Param("uuid", ParseUUIDPipe) uuid: string): Promise<void> {
    return this.routesService.remove(uuid);
  }
}
