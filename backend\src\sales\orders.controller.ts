import {
  Controller,
  Get,
  Post,
  Patch,
  Delete,
  Param,
  Body,
  Query,
  UseGuards,
  ParseUUIDPipe,
} from "@nestjs/common";
import {
  ApiTags,
  ApiQuery,
  ApiOperation,
  ApiResponse,
  ApiParam,
} from "@nestjs/swagger";
import { OrdersService } from "./orders.service.typeorm";
import { CreateOrderDto } from "./dto/create-order.dto";
import { UpdateOrderDto } from "./dto/update-order.dto";
import {
  OrderResponseDto,
  OrdersListResponseDto,
} from "./dto/order-response.dto";
import { OrderStatus } from "./order.entity";
import {
  EMPTY_STRING_FILTER,
  EMPTY_UUID_FILTER,
  MIN_DATE_FILTER,
  MAX_DATE_FILTER,
} from "./order.constants";

@ApiTags("orders")
@Controller("orders")
export class OrdersController {
  constructor(private readonly ordersService: OrdersService) {}

  @Post()
  @ApiOperation({ summary: "Create a new order" })
  @ApiResponse({
    status: 201,
    description: "Order created successfully",
    type: OrderResponseDto,
  })
  @ApiResponse({ status: 400, description: "Invalid input data" })
  @ApiResponse({
    status: 404,
    description: "User, warehouse, or customer not found",
  })
  async create(
    @Body() createOrderDto: CreateOrderDto,
  ): Promise<OrderResponseDto> {
    return this.ordersService.create(createOrderDto);
  }

  @Post("from-quote/:quoteUuid")
  @ApiOperation({ summary: "Create order from quote" })
  @ApiParam({
    name: "quoteUuid",
    type: String,
    example: "uuid-v7-string",
    description: "Quote UUID",
  })
  @ApiResponse({
    status: 201,
    description: "Order created from quote successfully",
    type: OrderResponseDto,
  })
  @ApiResponse({ status: 404, description: "Quote not found" })
  async createFromQuote(
    @Param("quoteUuid", ParseUUIDPipe) quoteUuid: string,
    @Body() body: { userUuid: string },
  ): Promise<OrderResponseDto> {
    return this.ordersService.createFromQuote(quoteUuid, body.userUuid);
  }

  @Post(":uuid/products")
  @ApiOperation({ summary: "Add products to an order" })
  @ApiParam({
    name: "uuid",
    type: String,
    example: "uuid-v7-string",
    description: "Order UUID",
  })
  @ApiResponse({
    status: 200,
    description: "Products added successfully",
    type: OrderResponseDto,
  })
  @ApiResponse({ status: 404, description: "Order not found" })
  async addProductsToOrder(
    @Param("uuid", ParseUUIDPipe) uuid: string,
    @Body() products: { items: any[] },
  ): Promise<OrderResponseDto> {
    return this.ordersService.addProducts(uuid, products.items);
  }

  @Get()
  @ApiOperation({ summary: "Get all orders with pagination and filtering" })
  @ApiQuery({
    name: "customerUuid",
    required: false,
    type: String,
    description: "Filter by customer UUID (optional)",
  })
  @ApiQuery({
    name: "warehouseUuid",
    required: false,
    type: String,
    description: "Filter by warehouse UUID (optional)",
  })
  @ApiQuery({
    name: "status",
    required: false,
    type: String,
    description: "Filter by order status (optional)",
  })
  @ApiQuery({
    name: "priority",
    required: false,
    type: String,
    description: "Filter by order priority (optional)",
  })
  @ApiQuery({
    name: "createdFrom",
    required: false,
    type: String,
    description: "Filter orders created after this date (optional, ISO 8601)",
  })
  @ApiQuery({
    name: "createdTo",
    required: false,
    type: String,
    description: "Filter orders created before this date (optional, ISO 8601)",
  })
  @ApiQuery({
    name: "page",
    required: false,
    type: Number,
    description: "Page number (default: 1)",
  })
  @ApiQuery({
    name: "limit",
    required: false,
    type: Number,
    description: "Items per page (default: 10)",
  })
  @ApiResponse({
    status: 200,
    description: "Orders retrieved successfully",
    type: OrdersListResponseDto,
  })
  async findAll(
    @Query("customerUuid") customerUuid?: string,
    @Query("warehouseUuid") warehouseUuid?: string,
    @Query("status") status?: string,
    @Query("priority") priority?: string,
    @Query("createdFrom") createdFrom?: string,
    @Query("createdTo") createdTo?: string,
    @Query("page") page?: number,
    @Query("limit") limit?: number,
  ): Promise<OrdersListResponseDto> {
    // Use constants for default values
    const customerUuidFilter = customerUuid ?? EMPTY_UUID_FILTER;
    const warehouseUuidFilter = warehouseUuid ?? EMPTY_UUID_FILTER;
    const statusFilter = status ?? EMPTY_STRING_FILTER;
    const priorityFilter = priority ?? EMPTY_STRING_FILTER;

    // Use wide date range if not specified
    const createdFromDate = createdFrom
      ? new Date(createdFrom)
      : MIN_DATE_FILTER;
    const createdToDate = createdTo ? new Date(createdTo) : MAX_DATE_FILTER;

    return this.ordersService.findAll({
      customerUuid: customerUuidFilter,
      warehouseUuid: warehouseUuidFilter,
      status: statusFilter,
      priority: priorityFilter,
      createdFrom: createdFromDate,
      createdTo: createdToDate,
      page: page || 1,
      limit: limit || 10,
    });
  }

  @Get(":uuid")
  @ApiOperation({ summary: "Get an order by UUID" })
  @ApiParam({
    name: "uuid",
    type: String,
    example: "uuid-v7-string",
    description: "Order UUID",
  })
  @ApiResponse({
    status: 200,
    description: "Order retrieved successfully",
    type: OrderResponseDto,
  })
  @ApiResponse({ status: 404, description: "Order not found" })
  async findOne(@Param("uuid", ParseUUIDPipe) uuid: string): Promise<OrderResponseDto> {
    return this.ordersService.findOne(uuid);
  }

  @Patch(":uuid")
  @ApiOperation({ summary: "Update an order" })
  @ApiParam({
    name: "uuid",
    type: String,
    example: "uuid-v7-string",
    description: "Order UUID",
  })
  @ApiResponse({
    status: 200,
    description: "Order updated successfully",
    type: OrderResponseDto,
  })
  @ApiResponse({ status: 404, description: "Order not found" })
  async update(
    @Param("uuid", ParseUUIDPipe) uuid: string,
    @Body() updateOrderDto: UpdateOrderDto,
  ): Promise<OrderResponseDto> {
    return this.ordersService.update(uuid, updateOrderDto);
  }

  @Patch(":uuid/status")
  @ApiOperation({ summary: "Update order status" })
  @ApiParam({
    name: "uuid",
    type: String,
    example: "uuid-v7-string",
    description: "Order UUID",
  })
  @ApiResponse({
    status: 200,
    description: "Order status updated successfully",
    type: OrderResponseDto,
  })
  @ApiResponse({ status: 404, description: "Order not found" })
  @ApiResponse({ status: 400, description: "Invalid status" })
  async updateStatus(
    @Param("uuid", ParseUUIDPipe) uuid: string,
    @Body() body: { status: OrderStatus },
  ): Promise<OrderResponseDto> {
    return this.ordersService.updateStatus(uuid, body.status);
  }

  @Patch(":uuid/cancel")
  @ApiOperation({ summary: "Cancel an order" })
  @ApiParam({
    name: "uuid",
    type: String,
    example: "uuid-v7-string",
    description: "Order UUID",
  })
  @ApiResponse({
    status: 200,
    description: "Order cancelled successfully",
    type: OrderResponseDto,
  })
  @ApiResponse({ status: 404, description: "Order not found" })
  async cancelOrder(@Param("uuid", ParseUUIDPipe) uuid: string): Promise<OrderResponseDto> {
    return this.ordersService.cancelOrder(uuid);
  }

  @Delete(":uuid")
  @ApiOperation({ summary: "Delete an order" })
  @ApiParam({
    name: "uuid",
    type: String,
    example: "uuid-v7-string",
    description: "Order UUID",
  })
  @ApiResponse({ status: 200, description: "Order deleted successfully" })
  @ApiResponse({ status: 404, description: "Order not found" })
  async remove(@Param("uuid", ParseUUIDPipe) uuid: string): Promise<{ message: string }> {
    return this.ordersService.remove(uuid);
  }

  @Get("list-by-warehouse/:warehouseUuid")
  @ApiOperation({ summary: "Get all orders for a specific warehouse" })
  @ApiParam({
    name: "warehouseUuid",
    type: String,
    example: "uuid-v7-string",
    description: "Warehouse UUID",
  })
  @ApiResponse({
    status: 200,
    description: "Orders retrieved successfully",
    type: [OrderResponseDto],
  })
  async listByWarehouse(
    @Param("warehouseUuid", ParseUUIDPipe) warehouseUuid: string,
  ): Promise<OrderResponseDto[]> {
    return this.ordersService.findByWarehouse(warehouseUuid);
  }
}
