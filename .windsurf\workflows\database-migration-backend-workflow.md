---
description: Database migration workflow with mandatory full backup
---

# ⚠️ CRITICAL STEP: FULL DATABASE BACKUP (NO EXCEPTIONS)

**Every migration must begin with an automated, full database backup using `mongodump` (or equivalent).**

> **Use the script:** `backend/scripts/create_backup.py` for all full database backups. This is the only approved way to perform backups for migrations. Do not use manual or alternative backup methods.

- _Partial, manual, or selective backups are strictly forbidden._
- _Any migration PR or script without a full, automated backup step is INVALID and must be rejected._
- _This is a non-negotiable requirement for all contributors and reviewers._

---

# Workflow

1. **Analyse Documentation:**
   - Carefully review the full content of the following documentation (up to 500 lines each): `README.md`, `UUID_USAGE_GUIDELINES.md`.
2. **Analyse User-Provided Files:**
   - Review all other files provided by the user for the migration.
3. **Report Issues:**
   - List all missing functions, features, or issues found and prompt the user for clarification or action.
4. **Fulfill the User Request:**
   - Implement the migration as specified by the user request.
5. **Maintain a TODO List:**
   - Keep and regularly update a TODO list for outstanding migration-related tasks.
6. **Report Errors:**
   - Proactively report all errors and potential issues encountered during the process.

# Migration Requirements

- **Migrations go in the `/migrations/` folder.**
- **We use migrate-mongo for migrations.**
- **Every migration must provide both an `up` and a `down` function.**
- **MANDATORY: Before each migration, a FULL DATABASE BACKUP must be performed.**
  - The backup must use `mongodump` (or equivalent) to back up the entire database, not just selected collections.
  - The backup step must be automated at the start of the migration script (not manual, not partial, not only affected collections).
  - The backup location and timestamp must be logged for traceability.
  - If the backup fails, the migration must abort and report the failure.

- **Provide Command Lines:**
  - Supply the user with command lines to execute the migration and to rollback manually.

---

## ⚠️ Environment Variable and .env Handling

- **All migration scripts must use the `MONGODB_URI` environment variable for the database connection string.**
- **The `.env` file in the backend root must define:**
  ```
  MONGODB_URI=********************************:port/database
  ```
- **Migration scripts must load the `.env` file automatically using the `dotenv` package:**
  ```js
  require('dotenv').config({ path: require('path').resolve(__dirname, '../.env') });
  ```
- **Never use a hardcoded URI or a differently named variable (e.g., `MONGO_URI`).**
- **If the variable is missing or empty, the migration must abort with a clear error.**
- **This ensures consistency, prevents accidental connection to the wrong database, and avoids migration failures due to misconfiguration.**

---

# Migration Author/Reviewer Checklist

- [ ] Automated full DB backup using `mongodump` (or equivalent) is present at the start of the migration script
- [ ] No manual, partial, or selective backup logic
- [ ] Backup location and timestamp are logged
- [ ] Migration aborts on backup failure
- [ ] Both `up` and `down` functions are present
- [ ] Command lines for migration/rollback are provided
- [ ] Migration script loads `.env` and uses `MONGODB_URI` (never `MONGO_URI` or any other variable)
- [ ] Clear error if `MONGODB_URI` is missing or invalid

---

# Additional Notes

- Never skip, partially implement, or move the backup step outside the migration script.
- Review this workflow before every migration to ensure compliance.
- Update this workflow if backup, environment variable, or migration procedures change.
