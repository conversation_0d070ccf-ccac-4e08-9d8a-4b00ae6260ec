import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:package_info_plus/package_info_plus.dart';
import '../constants/app_constants.dart';

class AppUtils {
  // Private constructor to prevent instantiation
  AppUtils._();

  // =============================================================================
  // Validation Utilities
  // =============================================================================

  /// Validates email format
  static bool isValidEmail(String email) {
    if (email.isEmpty) return false;
    return RegExp(AppConstants.emailPattern).hasMatch(email);
  }

  /// Validates phone number format
  static bool isValidPhone(String phone) {
    if (phone.isEmpty) return false;
    return RegExp(AppConstants.phonePattern).hasMatch(phone);
  }

  /// Validates password strength
  static bool isValidPassword(String password) {
    if (password.length < AppConstants.minPasswordLength) return false;
    if (password.length > AppConstants.maxPasswordLength) return false;
    
    // Check for at least one uppercase letter
    if (!RegExp('[A-Z]').hasMatch(password)) return false;
    
    // Check for at least one lowercase letter
    if (!RegExp('[a-z]').hasMatch(password)) return false;
    
    // Check for at least one digit
    if (!RegExp('[0-9]').hasMatch(password)) return false;
    
    // Check for at least one special character
    if (!RegExp(r'[!@#$%^&*(),.?":{}|<>]').hasMatch(password)) return false;
    
    return true;
  }

  /// Validates username format
  static bool isValidUsername(String username) {
    if (username.length < AppConstants.minUsernameLength) return false;
    if (username.length > AppConstants.maxUsernameLength) return false;
    
    // Check for alphanumeric characters and underscores only
    return RegExp(r'^[a-zA-Z0-9_]+$').hasMatch(username);
  }

  /// Validates if string is not empty
  static bool isNotEmpty(String? value) => value != null && value.trim().isNotEmpty;

  /// Validates if string is numeric
  static bool isNumeric(String value) => double.tryParse(value) != null;

  /// Validates if string is a valid URL
  static bool isValidUrl(String url) {
    try {
      Uri.parse(url);
      return true;
    } catch (e) {
      return false;
    }
  }

  // =============================================================================
  // Formatting Utilities
  // =============================================================================

  /// Formats currency amount
  static String formatCurrency(double amount, {String? currency}) {
    final formatter = NumberFormat.currency(
      symbol: currency ?? AppConstants.defaultCurrencySymbol,
      decimalDigits: 2,
    );
    return formatter.format(amount);
  }

  /// Formats date to string
  static String formatDate(DateTime date, {String pattern = 'yyyy-MM-dd'}) => DateFormat(pattern).format(date);

  /// Formats date and time to string
  static String formatDateTime(DateTime dateTime, {String pattern = 'yyyy-MM-dd HH:mm'}) => DateFormat(pattern).format(dateTime);

  /// Formats time to string
  static String formatTime(DateTime time, {String pattern = 'HH:mm'}) => DateFormat(pattern).format(time);

  /// Formats file size in bytes to human readable format
  static String formatFileSize(int bytes) {
    if (bytes <= 0) return '0 B';
    const suffixes = ['B', 'KB', 'MB', 'GB', 'TB'];
    final i = (bytes.bitLength - 1) ~/ 10;
    return '${(bytes / (1 << (i * 10))).toStringAsFixed(1)} ${suffixes[i]}';
  }

  /// Formats phone number
  static String formatPhoneNumber(String phone) {
    // Remove all non-digit characters
    final digitsOnly = phone.replaceAll(RegExp(r'\D'), '');
    
    if (digitsOnly.length == 10) {
      return '(${digitsOnly.substring(0, 3)}) ${digitsOnly.substring(3, 6)}-${digitsOnly.substring(6)}';
    } else if (digitsOnly.length == 11) {
      return '+${digitsOnly.substring(0, 1)} (${digitsOnly.substring(1, 4)}) ${digitsOnly.substring(4, 7)}-${digitsOnly.substring(7)}';
    }
    
    return phone; // Return original if format is not recognized
  }

  /// Capitalizes first letter of each word
  static String capitalizeWords(String text) => text.split(' ').map((word) => 
      word.isNotEmpty ? word[0].toUpperCase() + word.substring(1).toLowerCase() : word
    ).join(' ');

  /// Truncates text with ellipsis
  static String truncateText(String text, int maxLength) {
    if (text.length <= maxLength) return text;
    return '${text.substring(0, maxLength)}...';
  }

  // =============================================================================
  // Device Information
  // =============================================================================

  /// Gets device information
  static Future<Map<String, dynamic>> getDeviceInfo() async {
    final deviceInfo = DeviceInfoPlugin();
    final packageInfo = await PackageInfo.fromPlatform();
    
    final var info = <String, dynamic>{
      'appName': packageInfo.appName,
      'packageName': packageInfo.packageName,
      'version': packageInfo.version,
      'buildNumber': packageInfo.buildNumber,
    };

    if (Platform.isAndroid) {
      final androidInfo = await deviceInfo.androidInfo;
      info.addAll({
        'platform': 'Android',
        'model': androidInfo.model,
        'manufacturer': androidInfo.manufacturer,
        'version': androidInfo.version.release,
        'sdkInt': androidInfo.version.sdkInt,
        'brand': androidInfo.brand,
        'device': androidInfo.device,
        'id': androidInfo.id,
      });
    } else if (Platform.isIOS) {
      final iosInfo = await deviceInfo.iosInfo;
      info.addAll({
        'platform': 'iOS',
        'model': iosInfo.model,
        'name': iosInfo.name,
        'systemName': iosInfo.systemName,
        'systemVersion': iosInfo.systemVersion,
        'identifierForVendor': iosInfo.identifierForVendor,
        'localizedModel': iosInfo.localizedModel,
      });
    }

    return info;
  }

  /// Gets platform name
  static String getPlatformName() {
    if (Platform.isAndroid) return 'Android';
    if (Platform.isIOS) return 'iOS';
    if (Platform.isWindows) return 'Windows';
    if (Platform.isMacOS) return 'macOS';
    if (Platform.isLinux) return 'Linux';
    return 'Unknown';
  }

  /// Checks if device is mobile
  static bool isMobile() => Platform.isAndroid || Platform.isIOS;

  /// Checks if device is tablet
  static bool isTablet(BuildContext context) {
    final shortestSide = MediaQuery.of(context).size.shortestSide;
    return shortestSide >= 600;
  }

  // =============================================================================
  // UI Utilities
  // =============================================================================

  /// Shows snackbar with message
  static void showSnackBar(BuildContext context, String message, {
    Color? backgroundColor,
    Color? textColor,
    Duration duration = const Duration(seconds: 3),
    SnackBarAction? action,
  }) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message,
          style: TextStyle(color: textColor),
        ),
        backgroundColor: backgroundColor,
        duration: duration,
        action: action,
      ),
    );
  }

  /// Shows error snackbar
  static void showErrorSnackBar(BuildContext context, String message) {
    showSnackBar(
      context,
      message,
      backgroundColor: Colors.red,
      textColor: Colors.white,
    );
  }

  /// Shows success snackbar
  static void showSuccessSnackBar(BuildContext context, String message) {
    showSnackBar(
      context,
      message,
      backgroundColor: Colors.green,
      textColor: Colors.white,
    );
  }

  /// Shows warning snackbar
  static void showWarningSnackBar(BuildContext context, String message) {
    showSnackBar(
      context,
      message,
      backgroundColor: Colors.orange,
      textColor: Colors.white,
    );
  }

  /// Provides haptic feedback
  static void hapticFeedback({HapticFeedbackType type = HapticFeedbackType.lightImpact}) {
    switch (type) {
      case HapticFeedbackType.lightImpact:
        HapticFeedback.lightImpact();
        break;
      case HapticFeedbackType.mediumImpact:
        HapticFeedback.mediumImpact();
        break;
      case HapticFeedbackType.heavyImpact:
        HapticFeedback.heavyImpact();
        break;
      case HapticFeedbackType.selectionClick:
        HapticFeedback.selectionClick();
        break;
      case HapticFeedbackType.vibrate:
        HapticFeedback.vibrate();
        break;
    }
  }

  /// Dismisses keyboard
  static void dismissKeyboard(BuildContext context) {
    FocusScope.of(context).unfocus();
  }

  // =============================================================================
  // Navigation Utilities
  // =============================================================================

  /// Navigates to route and clears stack
  static void navigateAndClearStack(BuildContext context, String routeName) {
    Navigator.of(context).pushNamedAndRemoveUntil(
      routeName,
      (route) => false,
    );
  }

  /// Navigates to route and replaces current
  static void navigateAndReplace(BuildContext context, String routeName) {
    Navigator.of(context).pushReplacementNamed(routeName);
  }

  /// Pops until specific route
  static void popUntil(BuildContext context, String routeName) {
    Navigator.of(context).popUntil(ModalRoute.withName(routeName));
  }

  // =============================================================================
  // Data Utilities
  // =============================================================================

  /// Generates unique ID
  static String generateUniqueId() => DateTime.now().millisecondsSinceEpoch.toString();

  /// Debounces function calls
  static Function debounce(Function func, Duration delay) {
    Timer? timer;
    return () {
      timer?.cancel();
      timer = Timer(delay, () => func());
    };
  }

  /// Throttles function calls
  static Function throttle(Function func, Duration delay) {
    var isThrottled = false;
    return () {
      if (!isThrottled) {
        func();
        isThrottled = true;
        Timer(delay, () => isThrottled = false);
      }
    };
  }

  /// Converts map to query string
  static String mapToQueryString(Map<String, dynamic> map) => map.entries
        .where((entry) => entry.value != null)
        .map((entry) => '${entry.key}=${Uri.encodeComponent(entry.value.toString())}')
        .join('&');

  /// Parses query string to map
  static Map<String, String> parseQueryString(String queryString) {
    final map = <String, String>{};
    final pairs = queryString.split('&');
    
    for (final pair in pairs) {
      final keyValue = pair.split('=');
      if (keyValue.length == 2) {
        map[keyValue[0]] = Uri.decodeComponent(keyValue[1]);
      }
    }
    
    return map;
  }

  // =============================================================================
  // File Utilities
  // =============================================================================

  /// Gets file extension
  static String getFileExtension(String fileName) => fileName.split('.').last.toLowerCase();

  /// Checks if file is image
  static bool isImageFile(String fileName) {
    final extension = getFileExtension(fileName);
    return AppConstants.allowedImageExtensions.contains(extension);
  }

  /// Checks if file is document
  static bool isDocumentFile(String fileName) {
    final extension = getFileExtension(fileName);
    return AppConstants.allowedDocumentExtensions.contains(extension);
  }

  /// Checks if file size is valid
  static bool isValidFileSize(int fileSizeInBytes) => fileSizeInBytes <= AppConstants.maxFileSize;
}

/// Enum for haptic feedback types
enum HapticFeedbackType {
  lightImpact,
  mediumImpact,
  heavyImpact,
  selectionClick,
  vibrate,
}

 