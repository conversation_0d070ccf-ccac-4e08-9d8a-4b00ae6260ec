# Dido Distribution: Warehouse Inventory & Product Distribution Platform

> **Note:** All documentation files, including API endpoint references, are located in the `/docs/` directory.
> 
> **If you are interacting with schemas or entity models, it is MANDATORY to read [`docs/UUID_USAGE_GUIDELINES.md`](./docs/UUID_USAGE_GUIDELINES.md).**

Dido Distribution is an integrated warehouse inventory management and product distribution application. Its goal is to streamline and automate the flow of goods from suppliers to warehouses, vans, and ultimately to customers, providing robust tools for inventory tracking, sales, purchasing, logistics, and reporting.

---

## 🌟 Application Vision & Goals
- Centralize warehouse, van, and product inventory management.
- Simplify sales, purchasing, and logistics workflows for distribution businesses.
- Provide real-time stock visibility, low-stock alerts, and van stock tracking.
- Enable efficient order processing, customer management, and supplier relationships.
- Deliver actionable business insights through comprehensive reporting.

---

## 🧰 Frameworks & Technologies Used
- **NestJS** (Node.js backend framework)
- **YugabyteDB** (database, via TypeORM)
- **Passport.js** (authentication)
- **Google Auth Library** (Google OAuth token validation)
- **Swagger** (API documentation)
- **bcrypt** (secure password hashing)
- **Frontend:** Modular structure (framework-agnostic; specify if using React, Angular, etc.)

## 🔧 Google OAuth Setup

For Google OAuth authentication setup and troubleshooting, see [`docs/GOOGLE_OAUTH_TROUBLESHOOTING.md`](./docs/GOOGLE_OAUTH_TROUBLESHOOTING.md).

---

## Coding Standards

- **UUID Handling**: All entity schemas and APIs must follow the UUIDv7 storage and exposure rules detailed in [`docs/UUID_USAGE_GUIDELINES.md`](./docs/UUID_USAGE_GUIDELINES.md). This is required for all schema and DTO work.

---

The following features are planned for backend implementation, serving as the core modules and endpoints for the Dido Distribution platform:

- **Dashboard**
- **Sales**
- **Products**
  - `DELETE /products/all`: Hard deletes all products (removes all products from the database). This action is irreversible.
  - POS
  - Orders
  - Customers
  - Cash Register
  - Invoices
  - Quotes
  - Returns
- **Purchasing**
  - Suppliers
  - Purchase Orders
  - Goods Receipt
  - Returns
- **Inventory**
  - Products (implemented)
  - Stock Levels (filter by storage and product name; responses include a product snapshot)
- **Stock Computation**
  - Compute and recompute real-time stock quantities for products, storages, and warehouse
  - Endpoints to trigger recomputation for all products in a storage, all products in a warehouse, or a single product

---

### 📦 Stock Computation API

- `GET /stock-computation/product/:productUuid?storageUuid=&warehouseUuid=`: Recompute and return the current stock for a specific product (optionally filtered by storage or warehouse)
- `GET /stock-computation/product/:productUuid?storageUuid=&warehouseUuid=`: Recompute and return the current stock for a specific product (optionally filtered by storage or warhouse)
- `GET /stock-computation/storage/:storageUuid`: Recompute and return stock quantities for all products in a given storage
- `GET /stock-computation/warhouse/:warehouseUuid`: Recompute and return stock quantities for all products in a given warhouse

Stock is computed from zero using all stock adjustments (add/subtract) and finalized sales (subtract) to provide the true current quantity. See API docs for detailed request/response schemas.

---

  - Stock Transfers
  - Stock Adjustments
  - Stock Movements
  - Low Stock Alerts
- **Logistics**
  - Warehouses
  - Vans
  - Van Stock
  - Van Loading
  - Routes
- **Reports**
  - Sales
  - Inventory
  - Van Performance
  - Financial
- **Settings**
  - Users
  - Roles
  - System
  - Data

> These features are the reference for backend development. Each module and sub-feature will be implemented as backend services and endpoints to support the full application vision.

---

## 📈 Current Implementation Status
- **Backend:**
  - User management (CRUD, UUIDv7)
  - Authentication (password, Google OAuth2)
  - Modular NestJS structure (ready for new modules)
  - API documentation via Swagger
  - Complete migration from MongoDB to YugabyteDB (PostgreSQL)
- **Frontend:**
  - Modular navigation/pages scaffolded for all main features (placeholders)
  - Navigation follows a flat structure (no sub-sub-items)

---

## 🛠️ What We Need / What's Missing
- Implementation of business logic for all main modules (Sales, Purchasing, Inventory, Logistics, Reports)
- Integration between frontend and backend (API consumption, data binding)
- Data models for inventory, products, orders, warehouses, vans, etc.
- Endpoints for managing products: create, list, update, fetch by UUID, and soft delete. These endpoints follow the same conventions as other resources.
- **Update Product:** `PUT /products/{uuid}` (see `docs/PRODUCTS_ENDPOINTS.md` for details)
- **List Products by Warehouse:** `GET /products/warehouse/{warehouseUuid}` (List all products for a given warehouseUuid, excluding deleted)
- **Delete Product:** `DELETE /products/:uuid` (Soft delete a product (requires only uuid, sets isDeleted: true))
- UI/UX improvements and real-time features (alerts, dashboards)
- Automated tests, error handling, and security hardening
- Deployment scripts and production readiness

---

> **For setup and installation instructions, see [SETUP.md](./SETUP.md).**

# Dido Distribution — NestJS Backend

A robust, modular backend for the Dido Distribution platform, built with [NestJS](https://nestjs.com/), YugabyteDB, and Passport.js. This backend provides user management, authentication (including Google OAuth2), and a foundation for scalable enterprise features.

---

## 🚀 Features
- **NestJS**: Modular, scalable Node.js framework
- **YugabyteDB**: Integrated via TypeORM
- **User Management**: CRUD, UUIDv7 primary keys via `Uuid7` utility
- **UUIDs:** All entities use uuid7 from the local utils/uuid7 utility for UUID generation.
- **Password Auth**: Secure bcrypt-based helpers
- **Google OAuth2**: Social login support
- **Swagger**: Interactive API docs at `/docs`
- **Environment-based config**: Easy setup for dev/prod

---

## 🏗️ Project Structure

- `src/app.module.ts` — Root module
- `src/main.ts` — Application entrypoint
- `src/users/` — User module (CRUD, TypeORM, UUIDv7)
- `src/auth/` — Auth module (password, Google OAuth2)
- `src/utils/uuid7.ts` — `Uuid7` utility class for handling UUIDv7 generation
- `src/` — Place additional feature modules here

---

## 📖 API Endpoints

For a full list of available API endpoints, including request/response details, see [ENDPOINTS.md](./ENDPOINTS.md).

Entity management and role management endpoints are now documented in separate sections:
- **Entity Endpoints:** For creating, listing, updating, and fetching entities. All entity UUIDs are UUID7, generated and validated using the `Uuid7` class (see `src/utils/uuid7.ts`). All endpoints expect UUID7 strings.
- **See [UUID Usage Guidelines](./docs/UUID_USAGE_GUIDELINES.md) for correct UUID storage and query practices.**
- **Role Endpoints:** For managing roles, permissions, and assigning roles to entities. Roles are warhouse-specific and require a warehouseUuid when creating or updating a role.

See the respective sections in ENDPOINTS.md for details.

---

- [NestJS Documentation](https://docs.nestjs.com/)
- [TypeORM Documentation](https://typeorm.io/)
- [Passport.js Docs](http://www.passportjs.org/docs/)
- [Swagger UI](https://swagger.io/tools/swagger-ui/)

---

# Auth Module - password.js

This folder provides simple password authentication helpers for the backend using password.js and bcrypt.

## API

### hashPassword(password)
- Hashes a plaintext password using bcrypt.
- Returns a Promise that resolves to the hashed password.

### verifyPassword(password, hash)
- Compares a plaintext password to a bcrypt hash.
- Returns a Promise that resolves to `true` if the password matches, else `false`.

## Usage in NestJS

- Import the functions where needed (e.g., in your UsersService).
- Use `hashPassword` when creating users, and `verifyPassword` when authenticating.

---

## Modules

- Auth
- Users
- Companies
- Products
- Sales
- Customers
- Inventory: Manage storage locations and track inventory per product and location.
