import { ApiProperty } from "@nestjs/swagger";
import { IsString, IsOptional, IsNotEmpty, IsUUID } from "class-validator";

export class UpdateWarehouseDto {
  @ApiProperty({
    example: "Acme Warehouse Updated",
    description: "Warehouse name",
    required: false,
  })
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  name?: string;

  @ApiProperty({
    example: "Updated description for the main distribution center",
    description: "Warehouse description",
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the user who owns this warehouse",
    required: false,
  })
  @IsOptional()
  @IsUUID("all")
  userUuid?: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the main storage location for this warehouse",
    required: false,
  })
  @IsOptional()
  @IsUUID("all")
  mainStorageUuid?: string;
}
