import { <PERSON><PERSON><PERSON>, PrimaryColumn, Column, CreateDateColumn, UpdateDateColumn, Index } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { StorageType } from './storage_type.enum';
import { Uuid7 } from '../utils/uuid7';

@Entity('storages')
export class Storage {
  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the storage (primary key)",
  })
  @PrimaryColumn('uuid')
  id: string;

  @ApiProperty({ enum: StorageType, example: StorageType.WAREHOUSE })
  @Column({
    type: 'enum',
    enum: StorageType,
    default: StorageType.WAREHOUSE,
  })
  type: StorageType;

  @ApiProperty({ example: "Main Warehouse", description: "Storage name" })
  @Column()
  name: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the warehouse the storage belongs to",
  })
  @Column('uuid')
  @Index()
  warehouseUuid: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the user who owns this storage",
  })
  @Column('uuid')
  @Index()
  userUuid: string;

  @ApiProperty({ example: false, description: "Soft delete flag" })
  @Column({ default: false })
  isDeleted: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Helper method to generate UUID
  static generateId(): string {
    return new Uuid7().toString();
  }

  // Helper method to generate UUID
  static fromBinary(binary: any): string {
    if (!binary) return null;
    try {
      return new Uuid7(binary).toString();
    } catch {
      return null;
    }
  }
} 