# Entity Common Fields

## Standard Entity Fields

All entities in the Dido Distribution system should include these common fields for consistency and audit trails:

### Required Fields
- `id`: Primary key (UUIDv7, PostgreSQL UUID type, string)
- `uuid`: String representation of id (for API responses)
- `createdAt`: Creation timestamp
- `updatedAt`: Last update timestamp
- `isDeleted`: Soft delete flag (default: false)

### User Tracking Fields
- `createdBy`: User UUID who created the entity (string, UUIDv7)
- `updatedBy`: User UUID who last updated the entity (string, UUIDv7)

### Warehouse Scoping
- `warehouseUuid`: Warehouse UUID the entity belongs to (string, UUIDv7)

### Notes
- All UUIDs are stored and handled as strings (PostgreSQL UUID type) in both entities and DTOs.
- All validation and transformation of UUIDs is performed at the application layer. PostgreSQL/YugabyteDB stores UUIDs as type `uuid`, but does not enforce the UUID version. Ensure your application logic enforces UUIDv7 compliance.
- Do not use virtuals or binary fields; all UUIDs are strings.

### Missing in Product Categories
The Product Categories entity currently lacks `createdBy`, `updatedBy`, and `warehouseUuid` fields, making it inconsistent with other entities. 