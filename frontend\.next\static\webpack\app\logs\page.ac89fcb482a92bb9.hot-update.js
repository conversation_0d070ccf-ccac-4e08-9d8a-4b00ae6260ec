"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/logs/page",{

/***/ "(app-pages-browser)/./app/logs/components/LogDetailsModal.tsx":
/*!*************************************************!*\
  !*** ./app/logs/components/LogDetailsModal.tsx ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LogDetailsModal: function() { return /* binding */ LogDetailsModal; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_FiCalendar_FiChevronDown_FiChevronRight_FiDatabase_FiFileText_FiTag_FiUser_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=FiCalendar,FiChevronDown,FiChevronRight,FiDatabase,FiFileText,FiTag,FiUser,FiX!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n\nvar _s = $RefreshSig$();\n\n\nfunction LogDetailsModal(param) {\n    let { isOpen, log, onClose, userName } = param;\n    _s();\n    // State for expandable tabs\n    const [showOldDataTab, setShowOldDataTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showNewDataTab, setShowNewDataTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Handle escape key to close modal\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!isOpen) return;\n        const handleKeyDown = (e)=>{\n            if (e.key === \"Escape\") {\n                e.preventDefault();\n                onClose();\n            }\n        };\n        window.addEventListener(\"keydown\", handleKeyDown);\n        return ()=>window.removeEventListener(\"keydown\", handleKeyDown);\n    }, [\n        isOpen,\n        onClose\n    ]);\n    // Handle clicking outside modal to close\n    const handleOverlayClick = (e)=>{\n        if (e.target === e.currentTarget) {\n            onClose();\n        }\n    };\n    // Format date for display\n    const formatDate = (date)=>{\n        const dateObj = typeof date === \"string\" ? new Date(date) : date;\n        return dateObj.toLocaleString(\"en-US\", {\n            year: \"numeric\",\n            month: \"long\",\n            day: \"numeric\",\n            hour: \"2-digit\",\n            minute: \"2-digit\",\n            second: \"2-digit\"\n        });\n    };\n    // Get operation color based on operation type\n    const getOperationColor = (operation)=>{\n        const op = operation.toLowerCase();\n        if (op.includes(\"create\") || op.includes(\"add\")) {\n            return \"bg-green-100 text-green-800 border-green-200\";\n        } else if (op.includes(\"update\") || op.includes(\"edit\") || op.includes(\"modify\")) {\n            return \"bg-blue-100 text-blue-800 border-blue-200\";\n        } else if (op.includes(\"delete\") || op.includes(\"remove\")) {\n            return \"bg-red-100 text-red-800 border-red-200\";\n        } else if (op.includes(\"cancel\") || op.includes(\"reject\")) {\n            return \"bg-orange-100 text-orange-800 border-orange-200\";\n        } else if (op.includes(\"approve\") || op.includes(\"complete\")) {\n            return \"bg-purple-100 text-purple-800 border-purple-200\";\n        } else if (op.includes(\"transfer\") || op.includes(\"move\")) {\n            return \"bg-indigo-100 text-indigo-800 border-indigo-200\";\n        } else if (op.includes(\"adjust\") || op.includes(\"modify\")) {\n            return \"bg-yellow-100 text-yellow-800 border-yellow-200\";\n        } else {\n            return \"bg-gray-100 text-gray-800 border-gray-200\";\n        }\n    };\n    // Format JSON data for display\n    const formatJsonData = (data)=>{\n        try {\n            return JSON.stringify(data, null, 2);\n        } catch (error) {\n            return \"Invalid JSON data\";\n        }\n    };\n    // Check if the log data contains delta changes\n    const hasDeltaChanges = (data)=>{\n        return data && data.changes && typeof data.changes === \"object\";\n    };\n    // Extract raw old data from delta changes\n    const extractOldData = (data)=>{\n        if (!hasDeltaChanges(data)) return null;\n        const oldData = {};\n        Object.entries(data.changes).forEach((param)=>{\n            let [field, change] = param;\n            if (change && typeof change === \"object\" && \"before\" in change) {\n                oldData[field] = change.before;\n            }\n        });\n        return Object.keys(oldData).length > 0 ? oldData : null;\n    };\n    // Extract raw new data from delta changes\n    const extractNewData = (data)=>{\n        if (!hasDeltaChanges(data)) return null;\n        const newData = {};\n        Object.entries(data.changes).forEach((param)=>{\n            let [field, change] = param;\n            if (change && typeof change === \"object\" && \"after\" in change) {\n                newData[field] = change.after;\n            }\n        });\n        return Object.keys(newData).length > 0 ? newData : null;\n    };\n    // Sophisticated change viewer that analyzes data types and shows meaningful differences\n    const renderSophisticatedChanges = (before, after)=>{\n        // Handle null/undefined cases\n        if (before === null && after === null) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-gray-500 italic\",\n                children: \"No change (both null)\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                lineNumber: 120,\n                columnNumber: 14\n            }, this);\n        }\n        if (before === null) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 text-sm font-medium text-green-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"w-2 h-2 bg-green-500 rounded-full\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 13\n                            }, this),\n                            \"Added\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-green-50 border border-green-200 rounded p-3\",\n                        children: renderValue(after)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                        lineNumber: 130,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                lineNumber: 125,\n                columnNumber: 9\n            }, this);\n        }\n        if (after === null) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 text-sm font-medium text-red-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"w-2 h-2 bg-red-500 rounded-full\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 13\n                            }, this),\n                            \"Deleted\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-red-50 border border-red-200 rounded p-3\",\n                        children: renderValue(before)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                        lineNumber: 144,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                lineNumber: 139,\n                columnNumber: 9\n            }, this);\n        }\n        // Both values exist - analyze types\n        const beforeType = Array.isArray(before) ? \"array\" : typeof before;\n        const afterType = Array.isArray(after) ? \"array\" : typeof after;\n        // Type changed\n        if (beforeType !== afterType) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm font-medium text-orange-700\",\n                        children: [\n                            \"Type Changed: \",\n                            beforeType,\n                            \" → \",\n                            afterType\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs font-medium text-red-700 mb-1\",\n                                        children: [\n                                            \"Before (\",\n                                            beforeType,\n                                            \")\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-red-50 border border-red-200 rounded p-3\",\n                                        children: renderValue(before)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs font-medium text-green-700 mb-1\",\n                                        children: [\n                                            \"After (\",\n                                            afterType,\n                                            \")\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-green-50 border border-green-200 rounded p-3\",\n                                        children: renderValue(after)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                        lineNumber: 160,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                lineNumber: 158,\n                columnNumber: 9\n            }, this);\n        }\n        // Same type - handle specific comparisons\n        if (beforeType === \"array\") {\n            return renderArrayChanges(before, after);\n        } else if (beforeType === \"object\") {\n            return renderObjectChanges(before, after);\n        } else {\n            // Primitive values\n            if (before === after) {\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-gray-500 italic\",\n                    children: \"No change\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                    lineNumber: 186,\n                    columnNumber: 16\n                }, this);\n            }\n            return renderPrimitiveChange(before, after);\n        }\n    };\n    // Helper function to render a value with appropriate formatting\n    const renderValue = (value)=>{\n        if (value === null) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"text-gray-500 italic\",\n            children: \"null\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n            lineNumber: 194,\n            columnNumber: 32\n        }, this);\n        if (value === undefined) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"text-gray-500 italic\",\n            children: \"undefined\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n            lineNumber: 195,\n            columnNumber: 37\n        }, this);\n        if (typeof value === \"string\") return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"text-blue-600\",\n            children: [\n                '\"',\n                value,\n                '\"'\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n            lineNumber: 196,\n            columnNumber: 43\n        }, this);\n        if (typeof value === \"number\") return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"text-purple-600\",\n            children: value\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n            lineNumber: 197,\n            columnNumber: 43\n        }, this);\n        if (typeof value === \"boolean\") return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"text-orange-600\",\n            children: String(value)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n            lineNumber: 198,\n            columnNumber: 44\n        }, this);\n        if (Array.isArray(value)) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"font-mono text-sm\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-gray-500\",\n                        children: \"[\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                        lineNumber: 202,\n                        columnNumber: 11\n                    }, this),\n                    value.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-gray-400 italic\",\n                        children: \"empty\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                        lineNumber: 204,\n                        columnNumber: 13\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-gray-600\",\n                        children: [\n                            value.length,\n                            \" items\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                        lineNumber: 206,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-gray-500\",\n                        children: \"]\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                        lineNumber: 208,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                lineNumber: 201,\n                columnNumber: 9\n            }, this);\n        }\n        if (typeof value === \"object\") {\n            const keys = Object.keys(value);\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"font-mono text-sm\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-gray-500\",\n                        children: \"{\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                        lineNumber: 216,\n                        columnNumber: 11\n                    }, this),\n                    keys.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-gray-400 italic\",\n                        children: \"empty\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                        lineNumber: 218,\n                        columnNumber: 13\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-gray-600\",\n                        children: [\n                            keys.length,\n                            \" properties\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                        lineNumber: 220,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-gray-500\",\n                        children: \"}\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                        lineNumber: 222,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                lineNumber: 215,\n                columnNumber: 9\n            }, this);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"text-gray-600\",\n            children: String(value)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n            lineNumber: 226,\n            columnNumber: 12\n        }, this);\n    };\n    // Render changes for primitive values\n    const renderPrimitiveChange = (before, after)=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 gap-3\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs font-medium text-red-700 mb-1\",\n                            children: \"Before\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                            lineNumber: 234,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-red-50 border border-red-200 rounded p-3\",\n                            children: renderValue(before)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                            lineNumber: 235,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                    lineNumber: 233,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs font-medium text-green-700 mb-1\",\n                            children: \"After\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                            lineNumber: 240,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-green-50 border border-green-200 rounded p-3\",\n                            children: renderValue(after)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                            lineNumber: 241,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                    lineNumber: 239,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n            lineNumber: 232,\n            columnNumber: 7\n        }, this);\n    };\n    // Check if an array contains business items (has id, name, price, quantity, etc.)\n    const isBusinessItemsArray = (arr)=>{\n        if (!arr || arr.length === 0) return false;\n        const firstItem = arr[0];\n        return typeof firstItem === \"object\" && firstItem !== null && (\"id\" in firstItem || \"name\" in firstItem || \"price\" in firstItem || \"quantity\" in firstItem);\n    };\n    // Render business item with key details highlighted\n    const renderBusinessItem = (item)=>{\n        if (!item || typeof item !== \"object\") {\n            return renderValue(item);\n        }\n        const { id, name, price, quantity, ...otherProps } = item;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-2\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-wrap gap-3\",\n                    children: [\n                        name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs text-gray-500\",\n                                    children: \"Name:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 271,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-medium text-gray-900\",\n                                    children: name\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 272,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                            lineNumber: 270,\n                            columnNumber: 13\n                        }, this),\n                        quantity !== undefined && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs text-gray-500\",\n                                    children: \"Qty:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 277,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-medium text-purple-600\",\n                                    children: quantity\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 278,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                            lineNumber: 276,\n                            columnNumber: 13\n                        }, this),\n                        price !== undefined && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs text-gray-500\",\n                                    children: \"Price:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 283,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-medium text-green-600\",\n                                    children: [\n                                        \"$\",\n                                        price\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 284,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                            lineNumber: 282,\n                            columnNumber: 13\n                        }, this),\n                        id !== undefined && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs text-gray-500\",\n                                    children: \"ID:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 289,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-mono text-xs text-gray-600\",\n                                    children: id\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 290,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                            lineNumber: 288,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                    lineNumber: 268,\n                    columnNumber: 9\n                }, this),\n                Object.keys(otherProps).length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-xs text-gray-500\",\n                    children: [\n                        \"+\",\n                        Object.keys(otherProps).length,\n                        \" other properties\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                    lineNumber: 295,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n            lineNumber: 267,\n            columnNumber: 7\n        }, this);\n    };\n    // Compare business items by ID or by content\n    const findMatchingItem = (item, array)=>{\n        if (!item || !array) return null;\n        // Try to match by ID first\n        if (item.id !== undefined) {\n            return array.find((arrItem)=>arrItem && arrItem.id === item.id);\n        }\n        // Fallback to exact match\n        return array.find((arrItem)=>JSON.stringify(arrItem) === JSON.stringify(item));\n    };\n    // Render changes for arrays - enhanced for business items\n    const renderArrayChanges = (before, after)=>{\n        const isBusinessItems = isBusinessItemsArray(before) || isBusinessItemsArray(after);\n        if (isBusinessItems) {\n            return renderBusinessItemsChanges(before, after);\n        }\n        // For non-business arrays, use simple comparison\n        const maxLength = Math.max(before.length, after.length);\n        const changes = [];\n        for(let i = 0; i < maxLength; i++){\n            const beforeItem = i < before.length ? before[i] : undefined;\n            const afterItem = i < after.length ? after[i] : undefined;\n            if (beforeItem === undefined) {\n                changes.push({\n                    type: \"added\",\n                    index: i,\n                    value: afterItem\n                });\n            } else if (afterItem === undefined) {\n                changes.push({\n                    type: \"removed\",\n                    index: i,\n                    value: beforeItem\n                });\n            } else if (JSON.stringify(beforeItem) !== JSON.stringify(afterItem)) {\n                changes.push({\n                    type: \"modified\",\n                    index: i,\n                    before: beforeItem,\n                    after: afterItem\n                });\n            }\n        }\n        if (changes.length === 0) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-gray-500 italic\",\n                children: \"No changes in array\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                lineNumber: 342,\n                columnNumber: 14\n            }, this);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-3\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm font-medium text-gray-700\",\n                    children: [\n                        \"Array Changes (\",\n                        before.length,\n                        \" → \",\n                        after.length,\n                        \" items)\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                    lineNumber: 347,\n                    columnNumber: 9\n                }, this),\n                changes.map((change, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border border-gray-200 rounded p-3\",\n                        children: [\n                            change.type === \"added\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 text-sm font-medium text-green-700\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"w-2 h-2 bg-green-500 rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                lineNumber: 355,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Added at index \",\n                                            change.index\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                        lineNumber: 354,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-green-50 border border-green-200 rounded p-2\",\n                                        children: renderValue(change.value)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                        lineNumber: 358,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                lineNumber: 353,\n                                columnNumber: 15\n                            }, this),\n                            change.type === \"removed\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 text-sm font-medium text-red-700\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"w-2 h-2 bg-red-500 rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                lineNumber: 366,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Removed from index \",\n                                            change.index\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                        lineNumber: 365,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-red-50 border border-red-200 rounded p-2\",\n                                        children: renderValue(change.value)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                        lineNumber: 369,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                lineNumber: 364,\n                                columnNumber: 15\n                            }, this),\n                            change.type === \"modified\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 text-sm font-medium text-blue-700\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"w-2 h-2 bg-blue-500 rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                lineNumber: 377,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Modified at index \",\n                                            change.index\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                        lineNumber: 376,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs font-medium text-red-700 mb-1\",\n                                                        children: \"Before\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                        lineNumber: 382,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-red-50 border border-red-200 rounded p-2\",\n                                                        children: renderValue(change.before)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                        lineNumber: 383,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                lineNumber: 381,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs font-medium text-green-700 mb-1\",\n                                                        children: \"After\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                        lineNumber: 388,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-green-50 border border-green-200 rounded p-2\",\n                                                        children: renderValue(change.after)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                        lineNumber: 389,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                lineNumber: 387,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                        lineNumber: 380,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                lineNumber: 375,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, idx, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                        lineNumber: 351,\n                        columnNumber: 11\n                    }, this))\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n            lineNumber: 346,\n            columnNumber: 7\n        }, this);\n    };\n    // Specialized renderer for business items (products, line items, etc.)\n    const renderBusinessItemsChanges = (before, after)=>{\n        const changes = [];\n        const processedAfterItems = new Set();\n        // Find removed and modified items\n        for (const beforeItem of before){\n            const matchingAfterItem = findMatchingItem(beforeItem, after);\n            if (!matchingAfterItem) {\n                changes.push({\n                    type: \"removed\",\n                    item: beforeItem\n                });\n            } else {\n                processedAfterItems.add(after.indexOf(matchingAfterItem));\n                if (JSON.stringify(beforeItem) !== JSON.stringify(matchingAfterItem)) {\n                    changes.push({\n                        type: \"modified\",\n                        before: beforeItem,\n                        after: matchingAfterItem\n                    });\n                }\n            }\n        }\n        // Find added items\n        for(let i = 0; i < after.length; i++){\n            if (!processedAfterItems.has(i)) {\n                const afterItem = after[i];\n                const matchingBeforeItem = findMatchingItem(afterItem, before);\n                if (!matchingBeforeItem) {\n                    changes.push({\n                        type: \"added\",\n                        item: afterItem\n                    });\n                }\n            }\n        }\n        if (changes.length === 0) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-gray-500 italic\",\n                children: \"No changes in items\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                lineNumber: 433,\n                columnNumber: 14\n            }, this);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-3\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm font-medium text-gray-700\",\n                    children: [\n                        \"Items Changes (\",\n                        before.length,\n                        \" → \",\n                        after.length,\n                        \" items)\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                    lineNumber: 438,\n                    columnNumber: 9\n                }, this),\n                changes.map((change, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border border-gray-200 rounded p-3\",\n                        children: [\n                            change.type === \"added\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 text-sm font-medium text-green-700\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"w-2 h-2 bg-green-500 rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                lineNumber: 446,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Added Item\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                        lineNumber: 445,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-green-50 border border-green-200 rounded p-3\",\n                                        children: renderBusinessItem(change.item)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                        lineNumber: 449,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                lineNumber: 444,\n                                columnNumber: 15\n                            }, this),\n                            change.type === \"removed\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 text-sm font-medium text-red-700\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"w-2 h-2 bg-red-500 rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                lineNumber: 457,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Removed Item\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                        lineNumber: 456,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-red-50 border border-red-200 rounded p-3\",\n                                        children: renderBusinessItem(change.item)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                        lineNumber: 460,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                lineNumber: 455,\n                                columnNumber: 15\n                            }, this),\n                            change.type === \"modified\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 text-sm font-medium text-blue-700\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"w-2 h-2 bg-blue-500 rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                lineNumber: 468,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Modified Item \",\n                                            change.before.name && '\"'.concat(change.before.name, '\"')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                        lineNumber: 467,\n                                        columnNumber: 17\n                                    }, this),\n                                    renderBusinessItemChanges(change.before, change.after)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                lineNumber: 466,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, idx, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                        lineNumber: 442,\n                        columnNumber: 11\n                    }, this))\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n            lineNumber: 437,\n            columnNumber: 7\n        }, this);\n    };\n    // Render changes for objects - show added, removed, and modified properties\n    const renderObjectChanges = (before, after)=>{\n        const beforeKeys = Object.keys(before);\n        const afterKeys = Object.keys(after);\n        const allKeys = Array.from(new Set([\n            ...beforeKeys,\n            ...afterKeys\n        ]));\n        const changes = [];\n        for (const key of allKeys){\n            const beforeValue = before[key];\n            const afterValue = after[key];\n            if (!beforeKeys.includes(key)) {\n                changes.push({\n                    type: \"added\",\n                    key,\n                    value: afterValue\n                });\n            } else if (!afterKeys.includes(key)) {\n                changes.push({\n                    type: \"removed\",\n                    key,\n                    value: beforeValue\n                });\n            } else if (JSON.stringify(beforeValue) !== JSON.stringify(afterValue)) {\n                changes.push({\n                    type: \"modified\",\n                    key,\n                    before: beforeValue,\n                    after: afterValue\n                });\n            }\n        }\n        if (changes.length === 0) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-gray-500 italic\",\n                children: \"No changes in object\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                lineNumber: 502,\n                columnNumber: 14\n            }, this);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-3\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm font-medium text-gray-700\",\n                    children: [\n                        \"Object Changes (\",\n                        beforeKeys.length,\n                        \" → \",\n                        afterKeys.length,\n                        \" properties)\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                    lineNumber: 507,\n                    columnNumber: 9\n                }, this),\n                changes.map((change, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border border-gray-200 rounded p-3\",\n                        children: [\n                            change.type === \"added\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 text-sm font-medium text-green-700\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"w-2 h-2 bg-green-500 rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                lineNumber: 515,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Added property: \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                className: \"bg-gray-100 px-1 rounded\",\n                                                children: change.key\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                lineNumber: 516,\n                                                columnNumber: 35\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                        lineNumber: 514,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-green-50 border border-green-200 rounded p-2\",\n                                        children: renderValue(change.value)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                        lineNumber: 518,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                lineNumber: 513,\n                                columnNumber: 15\n                            }, this),\n                            change.type === \"removed\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 text-sm font-medium text-red-700\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"w-2 h-2 bg-red-500 rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                lineNumber: 526,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Removed property: \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                className: \"bg-gray-100 px-1 rounded\",\n                                                children: change.key\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                lineNumber: 527,\n                                                columnNumber: 37\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                        lineNumber: 525,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-red-50 border border-red-200 rounded p-2\",\n                                        children: renderValue(change.value)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                        lineNumber: 529,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                lineNumber: 524,\n                                columnNumber: 15\n                            }, this),\n                            change.type === \"modified\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 text-sm font-medium text-blue-700\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"w-2 h-2 bg-blue-500 rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                lineNumber: 537,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Modified property: \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                className: \"bg-gray-100 px-1 rounded\",\n                                                children: change.key\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                lineNumber: 538,\n                                                columnNumber: 38\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                        lineNumber: 536,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs font-medium text-red-700 mb-1\",\n                                                        children: \"Before\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                        lineNumber: 542,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-red-50 border border-red-200 rounded p-2\",\n                                                        children: renderValue(change.before)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                        lineNumber: 543,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                lineNumber: 541,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs font-medium text-green-700 mb-1\",\n                                                        children: \"After\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                        lineNumber: 548,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-green-50 border border-green-200 rounded p-2\",\n                                                        children: renderValue(change.after)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                        lineNumber: 549,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                lineNumber: 547,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                        lineNumber: 540,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                lineNumber: 535,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, idx, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                        lineNumber: 511,\n                        columnNumber: 11\n                    }, this))\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n            lineNumber: 506,\n            columnNumber: 7\n        }, this);\n    };\n    // Render delta changes in a user-friendly format\n    const renderDeltaChanges = (changes)=>{\n        const changeEntries = Object.entries(changes);\n        if (changeEntries.length === 0) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-500 text-sm\",\n                    children: \"No changes detected\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                    lineNumber: 569,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                lineNumber: 568,\n                columnNumber: 9\n            }, this);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: changeEntries.map((param)=>{\n                let [field, change] = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border border-gray-200 rounded-lg p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                            className: \"font-medium text-gray-900 mb-4 capitalize\",\n                            children: field.replace(/([A-Z])/g, \" $1\").replace(/^./, (str)=>str.toUpperCase())\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                            lineNumber: 579,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700\",\n                                    children: \"� Change Analysis\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 585,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-500 mb-3\",\n                                    children: \"Intelligent analysis showing what was added, removed, or modified\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 588,\n                                    columnNumber: 17\n                                }, this),\n                                renderSophisticatedChanges(change.before, change.after)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                            lineNumber: 584,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, field, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                    lineNumber: 578,\n                    columnNumber: 13\n                }, this);\n            })\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n            lineNumber: 575,\n            columnNumber: 7\n        }, this);\n    };\n    if (!isOpen || !log) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n        onClick: handleOverlayClick,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg max-w-6xl w-full mx-4 max-h-[90vh] overflow-hidden flex flex-col\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 border-b border-gray-200 flex-shrink-0 flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-bold text-gray-900\",\n                                    children: \"Log Details\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 608,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600 mt-1\",\n                                    children: \"View detailed information about this log entry\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 611,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                            lineNumber: 607,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"p-2 rounded-full hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors\",\n                            title: \"Close\",\n                            \"aria-label\": \"Close modal\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCalendar_FiChevronDown_FiChevronRight_FiDatabase_FiFileText_FiTag_FiUser_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiX, {\n                                className: \"h-5 w-5 text-gray-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                lineNumber: 621,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                            lineNumber: 615,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                    lineNumber: 606,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 overflow-y-auto p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                    children: \"Basic Information\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 629,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2 flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCalendar_FiChevronDown_FiChevronRight_FiDatabase_FiFileText_FiTag_FiUser_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiTag, {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                            lineNumber: 635,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"Operation\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                    lineNumber: 634,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border \".concat(getOperationColor(log.operation)),\n                                                    children: log.operation\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                    lineNumber: 638,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                            lineNumber: 633,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2 flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCalendar_FiChevronDown_FiChevronRight_FiDatabase_FiFileText_FiTag_FiUser_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiDatabase, {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                            lineNumber: 646,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"Entity\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                    lineNumber: 645,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-900 font-medium\",\n                                                    children: log.entity\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                    lineNumber: 649,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                            lineNumber: 644,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2 flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCalendar_FiChevronDown_FiChevronRight_FiDatabase_FiFileText_FiTag_FiUser_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiUser, {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                            lineNumber: 655,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"User\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                    lineNumber: 654,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-900 font-medium\",\n                                                    children: userName || \"Unknown User\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                    lineNumber: 658,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-500 mt-1\",\n                                                    children: log.userUuid\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                    lineNumber: 661,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                            lineNumber: 653,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2 flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCalendar_FiChevronDown_FiChevronRight_FiDatabase_FiFileText_FiTag_FiUser_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiCalendar, {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                            lineNumber: 667,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"Timestamp\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                    lineNumber: 666,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-900\",\n                                                    children: formatDate(log.createdAt)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                    lineNumber: 670,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                            lineNumber: 665,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 631,\n                                    columnNumber: 13\n                                }, this),\n                                log.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2 flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCalendar_FiChevronDown_FiChevronRight_FiDatabase_FiFileText_FiTag_FiUser_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiFileText, {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                    lineNumber: 678,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Description\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                            lineNumber: 677,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-900 bg-gray-50 p-3 rounded-lg border\",\n                                            children: log.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                            lineNumber: 681,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 676,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                            lineNumber: 628,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                    children: log.data && hasDeltaChanges(log.data) ? \"Changes\" : \"Additional Data\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 690,\n                                    columnNumber: 13\n                                }, this),\n                                log.data ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: hasDeltaChanges(log.data) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-3\",\n                                                        children: [\n                                                            \"Field Changes (\",\n                                                            log.data.changeCount || 0,\n                                                            \" changes)\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                        lineNumber: 700,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    renderDeltaChanges(log.data.changes)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                lineNumber: 699,\n                                                columnNumber: 21\n                                            }, this),\n                                            Object.keys(log.data).some((key)=>![\n                                                    \"changes\",\n                                                    \"changedFields\",\n                                                    \"changeCount\"\n                                                ].includes(key)) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                        children: \"Additional Information\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                        lineNumber: 709,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gray-900 text-green-400 p-4 rounded-lg border overflow-x-auto\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                            className: \"text-sm font-mono whitespace-pre-wrap\",\n                                                            children: formatJsonData(Object.fromEntries(Object.entries(log.data).filter((param)=>{\n                                                                let [key] = param;\n                                                                return ![\n                                                                    \"changes\",\n                                                                    \"changedFields\",\n                                                                    \"changeCount\"\n                                                                ].includes(key);\n                                                            })))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                            lineNumber: 713,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                        lineNumber: 712,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                lineNumber: 708,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                        lineNumber: 697,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                children: \"JSON Data\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                lineNumber: 728,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-900 text-green-400 p-4 rounded-lg border overflow-x-auto\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                    className: \"text-sm font-mono whitespace-pre-wrap\",\n                                                    children: formatJsonData(log.data)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                    lineNumber: 732,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                lineNumber: 731,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                        lineNumber: 727,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 695,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCalendar_FiChevronDown_FiChevronRight_FiDatabase_FiFileText_FiTag_FiUser_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiDatabase, {\n                                            className: \"w-12 h-12 text-gray-300 mx-auto mb-3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                            lineNumber: 741,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-500 text-sm\",\n                                            children: \"No additional data available\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                            lineNumber: 742,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 740,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                            lineNumber: 689,\n                            columnNumber: 11\n                        }, this),\n                        log.data && hasDeltaChanges(log.data) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                    children: \"Raw Data\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 750,\n                                    columnNumber: 15\n                                }, this),\n                                extractOldData(log.data) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4 border border-gray-200 rounded-lg overflow-hidden\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowOldDataTab(!showOldDataTab),\n                                            className: \"w-full px-4 py-3 bg-gray-50 hover:bg-gray-100 flex items-center justify-between text-left transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        showOldDataTab ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCalendar_FiChevronDown_FiChevronRight_FiDatabase_FiFileText_FiTag_FiUser_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiChevronDown, {\n                                                            className: \"w-4 h-4 text-gray-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                            lineNumber: 761,\n                                                            columnNumber: 25\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCalendar_FiChevronDown_FiChevronRight_FiDatabase_FiFileText_FiTag_FiUser_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiChevronRight, {\n                                                            className: \"w-4 h-4 text-gray-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                            lineNumber: 763,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-gray-900\",\n                                                            children: \"Raw Old Data (Before Changes)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                            lineNumber: 765,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                    lineNumber: 759,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-gray-500 bg-red-100 px-2 py-1 rounded\",\n                                                    children: \"Original Values\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                    lineNumber: 767,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                            lineNumber: 755,\n                                            columnNumber: 19\n                                        }, this),\n                                        showOldDataTab && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-4 bg-gray-900 text-green-400 overflow-x-auto\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                className: \"text-sm font-mono whitespace-pre-wrap\",\n                                                children: formatJsonData(extractOldData(log.data) || {})\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                lineNumber: 773,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                            lineNumber: 772,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 754,\n                                    columnNumber: 17\n                                }, this),\n                                extractNewData(log.data) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4 border border-gray-200 rounded-lg overflow-hidden\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowNewDataTab(!showNewDataTab),\n                                            className: \"w-full px-4 py-3 bg-gray-50 hover:bg-gray-100 flex items-center justify-between text-left transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        showNewDataTab ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCalendar_FiChevronDown_FiChevronRight_FiDatabase_FiFileText_FiTag_FiUser_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiChevronDown, {\n                                                            className: \"w-4 h-4 text-gray-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                            lineNumber: 790,\n                                                            columnNumber: 25\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCalendar_FiChevronDown_FiChevronRight_FiDatabase_FiFileText_FiTag_FiUser_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiChevronRight, {\n                                                            className: \"w-4 h-4 text-gray-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                            lineNumber: 792,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-gray-900\",\n                                                            children: \"Raw New Data (After Changes)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                            lineNumber: 794,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                    lineNumber: 788,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-gray-500 bg-green-100 px-2 py-1 rounded\",\n                                                    children: \"Updated Values\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                    lineNumber: 796,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                            lineNumber: 784,\n                                            columnNumber: 19\n                                        }, this),\n                                        showNewDataTab && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-4 bg-gray-900 text-green-400 overflow-x-auto\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                className: \"text-sm font-mono whitespace-pre-wrap\",\n                                                children: formatJsonData(extractNewData(log.data) || {})\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                lineNumber: 802,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                            lineNumber: 801,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 783,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                            lineNumber: 749,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"pt-6 border-t border-gray-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"Log ID\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 814,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                    className: \"text-xs text-gray-600 bg-gray-100 px-2 py-1 rounded\",\n                                    children: log.id\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 817,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                            lineNumber: 813,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                    lineNumber: 626,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 border-t border-gray-200 flex-shrink-0 flex justify-end\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onClose,\n                        className: \"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-400 transition-colors\",\n                        children: \"Close\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                        lineNumber: 825,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                    lineNumber: 824,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n            lineNumber: 604,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n        lineNumber: 603,\n        columnNumber: 5\n    }, this);\n}\n_s(LogDetailsModal, \"3gNnT4s7QjbDbG3nIYQIGUhCzM8=\");\n_c = LogDetailsModal;\nvar _c;\n$RefreshReg$(_c, \"LogDetailsModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/logs/components/LogDetailsModal.tsx\n"));

/***/ })

});