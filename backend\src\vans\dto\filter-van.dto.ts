import { ApiProperty } from "@nestjs/swagger";
import { IsOptional, IsString, IsUUID } from "class-validator";

export class FilterVanDto {
  @ApiProperty({
    example: "d1e2f3a4-5678-4bcd-9e21-abcdef123456",
    description: "Warehouse UUID to filter by",
    required: false,
  })
  @IsUUID()
  @IsOptional()
  warehouseUuid?: string;

  @ApiProperty({
    example: "Van 001",
    description:
      "Van name to filter by (partial match). Empty string returns all vans.",
    required: false,
  })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiProperty({
    example: "ABC-123",
    description: "License plate to filter by (partial match)",
    required: false,
  })
  @IsString()
  @IsOptional()
  licensePlate?: string;

  @ApiProperty({
    example: "Ford Transit",
    description: "Model to filter by (partial match)",
    required: false,
  })
  @IsString()
  @IsOptional()
  model?: string;
}
