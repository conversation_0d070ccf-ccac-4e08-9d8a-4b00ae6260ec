import { ApiProperty } from "@nestjs/swagger";
import { IsString, IsOptional, IsUrl } from "class-validator";

export class UpdateCompanyDto {
  @ApiProperty({
    example: "Acme Corporation",
    description: "Company name",
    required: false,
  })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({
    example: "*********",
    description: "Tax identification number (NIF)",
    required: false,
  })
  @IsOptional()
  @IsString()
  nif?: string;

  @ApiProperty({
    example: "RC123456",
    description: "Register number (RC)",
    required: false,
  })
  @IsOptional()
  @IsString()
  rc?: string;

  @ApiProperty({
    example: "ART001",
    description: "Article number",
    required: false,
  })
  @IsOptional()
  @IsString()
  articleNumber?: string;

  @ApiProperty({
    example: "123 Main Street, City, Country",
    description: "Company address",
    required: false,
  })
  @IsOptional()
  @IsString()
  address?: string;

  @ApiProperty({
    example: "https://www.acme.com",
    description: "Company website URL",
    required: false,
  })
  @IsOptional()
  @IsUrl()
  website?: string;

  @ApiProperty({
    example: "+*********0",
    description: "Company phone number",
    required: false,
  })
  @IsOptional()
  @IsString()
  phoneNumber?: string;
}
