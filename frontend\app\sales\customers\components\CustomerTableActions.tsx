import React from 'react';
import { Eye, Edit, Trash, ShoppingCart } from 'lucide-react';
import { Customer } from '../customersApi';

interface CustomerTableActionsProps {
  customer: Customer;
  onViewDetails: (customer: Customer) => void;
  onCreateSale: (customer: Customer) => void;
  onEdit: (customer: Customer) => void;
  onDelete: (customer: Customer) => void;
  isUpdating: boolean;
  isDeleting: boolean;
}

export const CustomerTableActions: React.FC<CustomerTableActionsProps> = ({
  customer,
  onViewDetails,
  onCreateSale,
  onEdit,
  onDelete,
  isUpdating,
  isDeleting,
}) => (
  <div className="flex items-center justify-center gap-3">
    {/* View Details */}
    <button
      className="p-2 rounded-full hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-blue-400 transition-colors duration-200"
      title="View Details"
      aria-label="View Details"
      onClick={() => onViewDetails(customer)}
    >
      <Eye className="h-4 w-4 text-blue-600" />
    </button>

    {/* Create Sale */}
    <button
      className="p-2 rounded-full hover:bg-green-100 focus:outline-none focus:ring-2 focus:ring-green-400 transition-colors duration-200"
      title="Create Sale"
      aria-label="Create Sale"
      onClick={() => onCreateSale(customer)}
    >
      <ShoppingCart className="h-4 w-4 text-green-600" />
    </button>

    {/* Edit */}
    <button
      className="p-2 rounded-full hover:bg-yellow-100 focus:outline-none focus:ring-2 focus:ring-yellow-400 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
      title="Edit"
      aria-label="Edit"
      onClick={() => onEdit(customer)}
      disabled={isUpdating}
    >
      <Edit className="h-4 w-4 text-yellow-600" />
    </button>

    {/* Delete */}
    <button
      className="p-2 rounded-full hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-red-400 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
      title="Delete"
      aria-label="Delete"
      onClick={() => onDelete(customer)}
      disabled={isDeleting}
    >
      <Trash className="h-4 w-4 text-red-600" />
    </button>
  </div>
); 