// Customer API functions for the global Customer Modal

import { getAuthHeadersWithContentType } from '@/utils/authHeaders';
import type { Customer, CreateCustomerDto, CustomerFilter, PaginatedResponse } from '../types';

// Export types for external use
export type { CustomerFilter, Customer, CreateCustomerDto, PaginatedResponse };

// Filter customers with pagination
export async function filterCustomers(filter: CustomerFilter): Promise<PaginatedResponse<Customer>> {
  try {
    
    const response = await fetch('/api/customers/filter', {
      method: 'POST',
      headers: getAuthHeadersWithContentType(),
      body: JSON.stringify(filter),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    
    return {
      data: data.data || [],
      total: data.total || 0,
      page: data.page || 1,
      limit: data.limit || 20,
      hasNext: data.hasNext || false,
      hasPrev: data.hasPrev || false,
      totalPages: data.totalPages || Math.ceil((data.total || 0) / (data.limit || 20))
    };
  } catch (error: any) {
    console.error('[CustomerModal API] filterCustomers error:', error);
    throw error;
  }
}

// Create a new customer
export async function createCustomer(customerData: CreateCustomerDto): Promise<Customer> {
  try {
    
    const response = await fetch('/api/customers', {
      method: 'POST',
      headers: getAuthHeadersWithContentType(),
      body: JSON.stringify(customerData),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    
    return data;
  } catch (error: any) {
    console.error('[CustomerModal API] createCustomer error:', error);
    throw error;
  }
}

// Get customer by UUID
export async function getCustomer(uuid: string): Promise<Customer> {
  try {
    
    const response = await fetch(`/api/customers/${uuid}`, {
      method: 'GET',
      headers: getAuthHeadersWithContentType(),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    
    return data;
  } catch (error: any) {
    console.error('[CustomerModal API] getCustomer error:', error);
    throw error;
  }
} 