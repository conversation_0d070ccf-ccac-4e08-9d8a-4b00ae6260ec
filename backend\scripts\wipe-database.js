/**
 * Database Wipe Script
 * 
 * This script wipes all data from the database by truncating all tables.
 * It handles foreign key constraints by truncating tables in the correct order.
 * It automatically creates a backup before wiping for safety.
 * 
 * WARNING: This will permanently delete ALL data from the database!
 * 
 * USAGE:
 *   node scripts/wipe-database.js
 *
 * REQUIREMENTS:
 *   - Environment variables must be configured in .env file
 *   - YugabyteDB must be accessible
 *   - Database user must have TRUNCATE permissions
 *   - Backup API must be accessible for automatic backup creation
 */

// Load environment variables from .env file
require('dotenv').config({ path: require('path').resolve(__dirname, '../.env') });

const { Client } = require('pg');
const axios = require('axios');

// Database configuration
const config = {
  host: process.env.YUGABYTE_HOST,
  port: process.env.YUGABYTE_PORT,
  database: process.env.YUGABYTE_DATABASE,
  user: process.env.YUGABYTE_USER,
  password: process.env.YUGABYTE_PASSWORD,
  ssl: false, // Set to true if using SSL
};

// API configuration
const BACKUP_API_BASE_URL = process.env.BACKUP_API_BASE_URL || 'http://localhost:5000';

// Tables to truncate in order (respecting foreign key constraints)
const tablesToTruncate = [
  // Auth and user management tables
  'auth_audit',
  'rate_limit',
  'refresh_token',
  
  // Sales related tables
  'quote_item',
  'quote',
  'order_item',
  'order',
  'sale_item',
  'sale',
  
  // Customer related tables
  'customer_payment',
  'credit_adjustment',
  'customer',
  
  // Inventory and stock tables
  'stock_adjustment',
  'inventory_item',
  'storage',
  
  // Purchase related tables
  'purchase',
  
  // Route and logistics tables
  'route',
  'van',
  
  // Core business tables
  'product',
  'product_category',
  'supplier',
  'warehouse',
  'region',
  'company',
  
  // User account and settings
  'account_settings',
  'feature',
  'account_plan',
  
  // User management (keep these last as they're referenced by other tables)
  'role',
  'user',
];

async function validateBackupAPI() {
  """Validate that the backup API is accessible."""
  try {
    const response = await axios.get(`${BACKUP_API_BASE_URL}/health/`, { timeout: 10000 });
    if (response.status === 200) {
      const healthData = response.data;
      if (healthData.status === 'healthy') {
        console.log('✅ Backup API is healthy and accessible');
        return true;
      } else {
        console.log(`⚠️  Backup API status: ${healthData.status}`);
        return true;
      }
    } else {
      console.log(`❌ Backup API health check failed: ${response.status}`);
      return false;
    }
  } catch (error) {
    console.log(`❌ Cannot connect to backup API at ${BACKUP_API_BASE_URL}`);
    console.log('   Automatic backup creation will not be available');
    return false;
  }
}

async function createBackup(prefix = 'wipe') {
  """Create a database backup using the API."""
  try {
    console.log(`🔄 Creating backup with prefix: ${prefix}`);
    
    const response = await axios.post(`${BACKUP_API_BASE_URL}/backup/`, {
      prefix: prefix
    }, {
      headers: { 'Content-Type': 'application/json' },
      timeout: 30000
    });
    
    if (response.data.status === 'success') {
      const backupData = response.data.data;
      const backupName = backupData.backup_name;
      const timestamp = backupData.timestamp;
      const sizeBytes = backupData.size_bytes || 0;
      const sizeMb = sizeBytes / (1024 * 1024);
      
      console.log('✅ Backup created successfully!');
      console.log(`   [NAME] ${backupName}`);
      console.log(`   [TIME] ${timestamp}`);
      console.log(`   [SIZE] ${sizeMb.toFixed(2)} MB`);
      
      return backupName;
    } else {
      console.log(`❌ Backup creation failed: ${response.data}`);
      return null;
    }
  } catch (error) {
    console.log(`❌ Backup creation failed: ${error}`);
    return null;
  }
}

async function wipeDatabase() {
  const client = new Client(config);
  
  try {
    console.log('🗑️  Starting database wipe...\n');
    
    // Validate backup API connectivity
    const backupApiAvailable = await validateBackupAPI();
    
    // Create backup if API is available
    let backupName = null;
    if (backupApiAvailable) {
      backupName = await createBackup('wipe');
      if (!backupName) {
        console.log('⚠️  Backup creation failed, but continuing with wipe...');
      }
    } else {
      console.log('⚠️  Backup API not available, proceeding without backup...');
    }
    
    // Connect to database
    console.log('🔌 Connecting to database...');
    await client.connect();
    console.log('✅ Connected to database successfully\n');
    
    // Verify connection
    const result = await client.query('SELECT current_database(), current_user');
    console.log(`📊 Connected to: ${result.rows[0].current_database}`);
    console.log(`👤 User: ${result.rows[0].current_user}\n`);
    
    // Get confirmation from user
    console.log('⚠️  WARNING: This will permanently delete ALL data from the database!');
    console.log('📋 Tables that will be truncated:');
    tablesToTruncate.forEach((table, index) => {
      console.log(`   ${index + 1}. ${table}`);
    });
    console.log('\n❓ Are you sure you want to continue? (yes/no)');
    
    // For safety, we'll require explicit confirmation
    // In a real scenario, you might want to add a command line argument or interactive prompt
    console.log('\n🔒 For safety, please manually confirm by editing this script and setting confirmWipe = true');
    const confirmWipe = true; // Set to true to actually perform the wipe
    
    if (!confirmWipe) {
      console.log('❌ Database wipe cancelled. Set confirmWipe = true to proceed.');
      if (backupName) {
        console.log(`💾 Backup was created: ${backupName}`);
        console.log(`   You can restore it later if needed.`);
      }
      return;
    }
    
    console.log('🚀 Proceeding with database wipe...\n');
    
    // Disable foreign key checks temporarily (PostgreSQL/YugabyteDB approach)
    console.log('🔓 Disabling foreign key constraints...');
    await client.query('SET session_replication_role = replica;');
    console.log('✅ Foreign key constraints disabled\n');
    
    // Truncate all tables
    let successCount = 0;
    let errorCount = 0;
    
    for (const table of tablesToTruncate) {
      try {
        console.log(`🗑️  Truncating table: ${table}`);
        await client.query(`TRUNCATE TABLE "${table}" CASCADE`);
        console.log(`✅ Successfully truncated: ${table}`);
        successCount++;
      } catch (error) {
        console.error(`❌ Error truncating ${table}: ${error.message}`);
        errorCount++;
      }
    }
    
    // Re-enable foreign key checks
    console.log('\n🔒 Re-enabling foreign key constraints...');
    await client.query('SET session_replication_role = DEFAULT;');
    console.log('✅ Foreign key constraints re-enabled\n');
    
    // Summary
    console.log('📊 Wipe Summary:');
    console.log('================');
    console.log(`✅ Successfully truncated: ${successCount} tables`);
    if (errorCount > 0) {
      console.log(`❌ Failed to truncate: ${errorCount} tables`);
    }
    console.log(`📋 Total tables processed: ${tablesToTruncate.length}`);
    
    if (backupName) {
      console.log(`💾 Backup created: ${backupName}`);
      console.log(`   To restore: python scripts/migration_manager_ysqlsh.py restore-from ${backupName}`);
    }
    
    if (errorCount === 0) {
      console.log('\n🎉 Database wipe completed successfully!');
      console.log('💾 All data has been permanently deleted.');
    } else {
      console.log('\n⚠️  Database wipe completed with errors.');
      console.log('🔍 Please check the error messages above.');
    }
    
  } catch (error) {
    console.error('\n❌ Database wipe failed:');
    console.error(`   ${error.message}`);
    
    if (error.code === 'ECONNREFUSED') {
      console.error('\n🔧 Troubleshooting:');
      console.error('   1. Check if YugabyteDB is running');
      console.error('   2. Verify database connection settings in .env file');
      console.error('   3. Ensure database user has proper permissions');
    }
  } finally {
    // Always close the connection
    if (client) {
      await client.end();
      console.log('\n🔌 Database connection closed');
    }
  }
}

// Run the script
wipeDatabase().catch(console.error); 