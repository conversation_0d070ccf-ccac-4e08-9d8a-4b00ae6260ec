import { Injectable, NotFoundException } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository, DataSource } from "typeorm";
import { Warehouse } from "./warehouse.entity";
import { Role } from "../users/role.entity";
import { DEFAULT_ROLES } from "../users/default_roles";
import { WarehouseDto, toWarehouseDto } from "./dto/warehouse.dto";
import { CreateWarehouseDto } from "./dto/create-warehouse.dto";
import { UpdateWarehouseDto } from "./dto/update-warehouse.dto";
import { InventoryService } from "../inventory/inventory.service.typeorm";
import { StorageType } from "../inventory/storage_type.enum";

@Injectable()
export class WarehousesService {
  /**
   * Find warehouses by exact name match (non-deleted only).
   */
  async findByName(name: string): Promise<WarehouseDto[]> {
    const warehouses = await this.warehouseRepository.find({
      where: { name, isDeleted: false },
    });
    return warehouses.map(toWarehouseDto);
  }

  constructor(
    @InjectRepository(Warehouse)
    private warehouseRepository: Repository<Warehouse>,
    @InjectRepository(Role)
    private roleRepository: Repository<Role>,
    private inventoryService: InventoryService,
    private dataSource: DataSource,
  ) {}

  async create(data: CreateWarehouseDto): Promise<WarehouseDto> {
    console.log("[WarehousesService] Starting warehouse creation");
    console.log("[WarehousesService] Input data:", JSON.stringify(data, null, 2));
    
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Create warehouse first within transaction
      const warehouse = this.warehouseRepository.create({
        id: Warehouse.generateId(),
        ...data,
      });
      
      console.log("[WarehousesService] Generated warehouse UUID:", warehouse.id);
      console.log("[WarehousesService] Warehouse data before save:", JSON.stringify(warehouse, null, 2));
      
      const savedWarehouse = await queryRunner.manager.save(warehouse);

      console.log("[WarehousesService] Warehouse saved with UUID:", savedWarehouse.id);
      console.log("[WarehousesService] Creating warehouse:", warehouse.name);
      console.log("[WarehousesService] Warehouse UUID:", warehouse.id);
      console.log("[WarehousesService] User UUID:", warehouse.userUuid);

      // Create main storage for this warehouse within transaction
      const mainStorage = await this.inventoryService.createStorage({
        name: "Main Storage",
        warehouseUuid: warehouse.id,
        userUuid: data.userUuid,
        type: StorageType.WAREHOUSE,
      });

      console.log("[WarehousesService] Created main storage:", mainStorage.uuid);

      // Update warehouse with mainStorageUuid within transaction
      savedWarehouse.mainStorageUuid = mainStorage.uuid;
      const updatedWarehouse = await queryRunner.manager.save(savedWarehouse);

      console.log("[WarehousesService] Updated warehouse with main storage UUID:", mainStorage.uuid);

      // Create default roles for this warehouse within transaction
      const roles = DEFAULT_ROLES.map((role) => 
        this.roleRepository.create({
          id: Role.generateId(),
          name: role.name,
          permissions: role.permissions,
          warehouseUuid: warehouse.id,
          isDeleted: false,
        })
      );

      console.log(
        "[WarehousesService] Creating default roles for warehouse:",
        roles.map((r) => r.name),
      );

      const createdRoles = await queryRunner.manager.save(roles);
      console.log(
        "[WarehousesService] Successfully created roles:",
        createdRoles.map((r) => ({
          name: r.name,
          uuid: r.id,
        })),
      );

      // Verify admin role was created
      const adminRole = createdRoles.find((r) => r.name === "admin");
      if (adminRole) {
        console.log(
          "[WarehousesService] Admin role created with UUID:",
          adminRole.id,
        );
      } else {
        console.error("[WarehousesService] ERROR: Admin role was not created");
        throw new Error("Admin role was not created");
      }

      // Commit transaction if everything succeeded
      await queryRunner.commitTransaction();
      console.log("[WarehousesService] Warehouse creation transaction committed successfully");
      console.log("[WarehousesService] Returning warehouse with UUID:", updatedWarehouse.id);

      return toWarehouseDto(updatedWarehouse);
    } catch (error) {
      // Rollback transaction on any error
      await queryRunner.rollbackTransaction();
      console.error("[WarehousesService] Error during warehouse creation, transaction rolled back:", error);
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async findAllNonDeleted(
    page: number = 1,
    limit: number = 10,
    userUuid?: string,
  ) {
    const query: any = { isDeleted: false };
    if (userUuid) {
      query.userUuid = userUuid;
    }

    const skip = (page - 1) * limit;

    const [warehouses, total] = await Promise.all([
      this.warehouseRepository
        .find({ 
          where: query, 
          skip, 
          take: limit, 
          order: { createdAt: "DESC" } 
        }),
      this.warehouseRepository.count({ where: query }),
    ]);

    const totalPages = Math.ceil(total / limit);

    return {
      data: warehouses.map(toWarehouseDto),
      total,
      page,
      limit,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    };
  }

  async filterByName(
    name?: string,
    page: number = 1,
    limit: number = 10,
    userUuid?: string,
  ) {
    const skip = (page - 1) * limit;
    
    let whereClause: any = { isDeleted: false };
    if (userUuid) {
      whereClause.userUuid = userUuid;
    }

    let warehouses, total;
    
    if (name && name.trim() !== "") {
      // Case-insensitive partial match using ILIKE for PostgreSQL
      [warehouses, total] = await this.warehouseRepository
        .createQueryBuilder("warehouse")
        .where("warehouse.isDeleted = :isDeleted", { isDeleted: false })
        .andWhere(userUuid ? "warehouse.userUuid = :userUuid" : "1=1", { userUuid })
        .andWhere("warehouse.name ILIKE :name", { name: `%${name}%` })
        .skip(skip)
        .take(limit)
        .orderBy("warehouse.createdAt", "DESC")
        .getManyAndCount();
    } else {
      [warehouses, total] = await Promise.all([
        this.warehouseRepository
          .find({ 
            where: whereClause, 
            skip, 
            take: limit, 
            order: { createdAt: "DESC" } 
          }),
        this.warehouseRepository.count({ where: whereClause }),
      ]);
    }

    const totalPages = Math.ceil(total / limit);

    return {
      data: warehouses.map(toWarehouseDto),
      total,
      page,
      limit,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    };
  }

  async findAllRaw(userUuid?: string): Promise<WarehouseDto[]> {
    const query: any = {};
    if (userUuid) {
      query.userUuid = userUuid;
    }
    const warehouses = await this.warehouseRepository.find({ where: query });
    return warehouses.map(toWarehouseDto);
  }

  async findOne(uuid: string): Promise<WarehouseDto> {
    const warehouse = await this.warehouseRepository.findOne({
      where: { id: uuid, isDeleted: false },
    });
    if (!warehouse) throw new NotFoundException("Warehouse not found");
    return toWarehouseDto(warehouse);
  }

  async update(uuid: string, data: UpdateWarehouseDto): Promise<WarehouseDto> {
    const warehouse = await this.warehouseRepository.findOne({
      where: { id: uuid, isDeleted: false },
    });
    if (!warehouse) throw new NotFoundException("Warehouse not found");
    
    Object.assign(warehouse, data);
    const updatedWarehouse = await this.warehouseRepository.save(warehouse);
    return toWarehouseDto(updatedWarehouse);
  }

  async setName(uuid: string, name: string): Promise<WarehouseDto> {
    // Only update the name field
    const warehouse = await this.warehouseRepository.findOne({
      where: { id: uuid, isDeleted: false },
    });
    if (!warehouse) throw new NotFoundException("Warehouse not found");
    
    warehouse.name = name;
    const updatedWarehouse = await this.warehouseRepository.save(warehouse);
    return toWarehouseDto(updatedWarehouse);
  }

  async remove(uuid: string): Promise<void> {
    const warehouse = await this.warehouseRepository.findOne({
      where: { id: uuid, isDeleted: false },
    });
    if (!warehouse) throw new NotFoundException("Warehouse not found");
    
    warehouse.isDeleted = true;
    await this.warehouseRepository.save(warehouse);
  }

  async count(userUuid?: string): Promise<number> {
    const query: any = { isDeleted: false };
    if (userUuid) {
      query.userUuid = userUuid;
    }
    return this.warehouseRepository.count({ where: query });
  }
}
