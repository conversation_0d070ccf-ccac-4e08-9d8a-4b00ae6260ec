# Dido Distribution - Warehouse Management System

---

## API Integration & Proxying

- **API Prefix:** All frontend calls to backend endpoints must use the `/api/` prefix (e.g., `/api/users/by-warehouse/:warehouseUuid`).
- **NO DIRECT URLS:** Never use backend URLs or ports (like `localhost:8000`) directly in frontend code. Always use `/api/` paths to leverage Next.js proxying and maintain environment independence.
- **Proxy Setup:** The Next.js configuration (`next.config.js`) rewrites all `/api/:path*` requests to `http://localhost:8000/:path*` (the backend server). This avoids CORS issues and enables a consistent API path for frontend code.
- **Separation of Concerns:** Each module (e.g., users, customers, inventory) must keep backend API logic in a dedicated `api.ts` file (e.g., `app/settings/users/usersApi.ts`). Page components should import and use these functions, not call fetch/axios directly.

- **Why:** This makes the codebase more maintainable, testable, and consistent across modules.

---

Dido Distribution is a powerful, all-in-one warehouse management and product distribution software built to optimize your entire operation. From inventory control and sales processing to logistics and financial reporting, <PERSON><PERSON> provides the tools you need to manage your business with greater efficiency and insight. It's the perfect solution for distributors, wholesalers, and warehouse operators looking to scale their business and streamline workflows.

## Key Features

- **Authentication & Session Management**:
  - On initial app load, user and token are restored from localStorage.
  - Protected pages must be wrapped with the `ProtectedRoute` component to enforce authentication (redirects to `/auth` if not logged in).
  - Session validity is checked every 30 minutes; if the session is invalid, the user is logged out and redirected to `/auth`.
  - Usage:
    ```tsx
    import ProtectedRoute from '@/components/ProtectedRoute';
    // ...
    <ProtectedRoute>
      {/* protected page content */}
    </ProtectedRoute>
    ```
- **Top Task Bar**: Persistent bar at the top of the app displaying the current warehouse, user info, notifications, and quick actions
- **Point of Sale (POS)**: Fast and intuitive sales interface with real-time inventory updates
- **Inventory Management**: Track stock levels across multiple warehouses with real-time updates
  - **Stock Adjustments**: Creating adjustments now uses an API abstraction (`adjustmentsApi.ts`) and displays both toast and inline error messages if submission fails, improving user feedback.
- **Order Processing**: Manage sales and purchase orders efficiently
- **Customer & Supplier Management**: Maintain comprehensive records of business partners
- **Logistics & Routing**: Optimize delivery routes and manage fleet operations
- **Reporting & Analytics**: Gain insights with comprehensive reporting tools
- **Multi-warehouse Support**: Manage inventory across multiple locations
- **User Access Control**: Role-based permissions for secure access

## Technology Stack

- **Frontend**: Next.js 14 with TypeScript
- **Styling**: Tailwind CSS with custom theming
- **State Management**: React Context API
- **Form Handling**: React Hook Form with Zod validation
- **Data Fetching**: React Query
- **UI Components**: Headless UI and custom components
- **Icons**: Lucide React
- **Charts**: Recharts for data visualization

---

## Project Structure

> **Note:** For all warehouse- and user-scoped resources (such as inventory, products, storage, etc.), always inject `warehouseUuid` and `userUuid` from `AuthContext` in the parent/page component using `useAuth()`. Never take these values from user input. Debug logs are present in `app/inventory/stock-levels/page.tsx` to verify the UUIDs used in API calls.

## Components

### TopTaskBar

The `TopTaskBar` component appears at the top of main layouts (such as the dashboard) and provides:
- **Current Warehouse**: Shows the name of the currently selected warehouse (placeholder by default)
- **User Info**: Displays the current user's name and avatar (or initials)
- **Quick Actions**: Icons for notifications, settings, and help
- **Consistent Layout**: Stays fixed at the top for easy access to global actions

**Location:** `components/TopTaskBar/TopTaskBar.tsx`

### SideTaskBar

The `SideTaskBar` component provides a persistent, collapsible vertical sidebar for navigation throughout the application. It displays main navigation items (Dashboard, Inventory, Logistics, etc.) with icons, highlights the active route, and supports sub-items (but not sub-sub-items, to maintain a flat navigation hierarchy). The sidebar also includes a footer with user info and a logout option.

**Location:** `components/SideTaskBar/SideTaskBar.tsx`

- Appears on the left side of main pages (such as the dashboard)
- Navigation items and icons are defined in the component, but can be customized via props
- Responsive and collapsible for smaller screens

## Warhouse Name Display
The warhouse name shown in the TopTaskBar is dynamically sourced from the authenticated user's context (`user.warhouse`). It updates automatically on page reload if the user object contains a `warhouse` field. If not present, a default placeholder is shown.

frontend/
├── app/                    # App router directory
│   ├── dashboard/          # Dashboard module
│   ├── inventory/          # Inventory & Products module
│   ├── logistics/          # Logistics & Warehouses module
│   ├── purchasing/         # Purchasing & Suppliers module
│   ├── reports/            # Reports & Analytics module
│   ├── sales/              # Sales & CRM module
│   ├── settings/           # Settings & Administration module
│   ├── layout.tsx          # Root layout component
│   └── page.tsx            # Home page component (if one exists at app/page.tsx)
├── components/
│   ├── SideTaskBar/        # Vertical navigation bar
│   └── TopTaskBar/         # Top bar with warehouse, user info, quick actions
```
├── components/            # Reusable components
│   └── SideTaskBar/          # Collapsible sidebar navigation
│       ├── SideTaskBar.tsx    # Main component
│       └── SideTaskBar.module.css  # Styling
├── public/               # Static assets
├── styles/              # Global styles
├── types/               # TypeScript type definitions
└── lib/                 # Utility functions and shared logic
```

## Components

## Features

### Point of Sale (POS)

A modern, intuitive point of sale interface designed for speed and ease of use. The POS page features a responsive product grid, a real-time shopping cart, and a streamlined payment process.
- **Product Catalog**: Visually browse products in a grid layout.
- **Fast Product Search**: Quickly find products with a powerful search bar.
- **Shopping Cart**: Add products to the cart with instant updates to the subtotal, tax, and total.
- **Customer Management**: Easily add a customer to the sale.
- **Payment Validation**: A clear and simple "Validate & Finalize Sale" flow.
- **Real-time Inventory**: Product listings show current stock levels.

### Orders Management

Comprehensive order management system with:
- Order creation and tracking
- Order status updates (pending, processing, completed, cancelled)
- Order history and search functionality
- Bulk order processing
- Integration with inventory management
- Customer order history
- Export and reporting capabilities

### Customers

Customer relationship management features:
- Customer database with detailed profiles
- Purchase history and order tracking
- Customer segmentation and grouping
- Loyalty program integration
- Communication history
- Notes and tags for customer management
- Import/export customer data

### Dashboard

Overview dashboard with key metrics and activity feed:
- Displays important statistics in a clean, card-based layout
- Shows recent activity and notifications
- Responsive design that works on all screen sizes
- Integrated with the main navigation

### SideTaskBar

Collapsible sidebar navigation component with the following features:
- Expands on hover to show menu items with icons and labels
- Collapses to icon-only view when not in use
- Supports nested menu items with smooth animations
- Responsive design with mobile support

**Usage:**
```tsx
import { SideTaskBar } from '@/components/SideTaskBar/SideTaskBar';

// In your layout or page component
<SideTaskBar />
```

## Getting Started

1. Install dependencies:
```bash
npm install
# or
yarn install
# or
pnpm install
```

2. Run the development server:
```bash
npm run dev
# or
yarn dev
# or
pnpm dev
```

3. Open [http://localhost:3000](http://localhost:3000) in your browser.

## Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run type-check` - Run TypeScript type checking

## Technology Stack

- Next.js 14
- TypeScript
- Tailwind CSS
- ESLint
- Prettier

## Page Descriptions

### Dashboard
- **Overview**: Real-time dashboard showing key performance indicators (KPIs), recent activities, and alerts
- **Key Metrics**: Sales performance, inventory levels, pending orders, and delivery status
- **Quick Actions**: Quick access to common tasks like creating new orders or checking stock levels

### Sales Module
- **Point of Sale (POS)**: A feature-rich interface for handling sales transactions efficiently. It includes a visual product grid for easy selection, a dynamic shopping cart that calculates totals in real-time, and a straightforward process to validate payments and finalize sales. Designed for speed and accuracy in a busy retail or wholesale environment.
- **Orders**: Manage customer orders, track order status, and process returns
- **Customers**: Maintain customer profiles, view purchase history, and manage customer accounts
- **Returns**: Process product returns and manage refunds/exchanges

### Purchasing Module
- **Suppliers**: Manage supplier information and track supplier performance
- **Purchase Orders**: Create and track purchase orders with multiple line items
- **Goods Receipt**: Record incoming inventory against purchase orders
- **Supplier Returns**: Process returns to suppliers and manage credit notes

### Inventory Module
- **Products**: Comprehensive product catalog with variants, pricing, and barcode management
- **Product Categories**: Manage product categorization with full CRUD operations *(Note: User tracking fields missing - see docs/PRODUCT_CATEGORIES_IMPLEMENTATION.md)*
- **Stock Levels**: Real-time view of inventory across all warehouses
- **Stock Transfers**: Transfer inventory between locations with tracking
- **Stock Adjustments**: Record inventory adjustments and corrections
- **Stock Movements**: Audit trail of all inventory movements
- **Low Stock Alerts**: Automated notifications for items needing replenishment

### Logistics Module
- **Warehouses**: Manage multiple warehouse locations and their configurations
- **Vans**: Track delivery vehicles and their assignments
- **Van Stock**: Manage inventory loaded onto delivery vehicles
- **Van Loading**: Optimize loading process for delivery vehicles
- **Routes**: Plan and optimize delivery routes for efficiency

### Reports Module
- **Sales Reports**: Detailed sales analysis by product, category, customer, and time period
- **Inventory Reports**: Stock valuation, turnover rates, and aging reports
- **Van Performance**: Track delivery performance and efficiency metrics
- **Financial Reports**: Revenue, profit margins, and other financial KPIs

### Settings Module
- **Users**: Manage user accounts and permissions
- **Roles**: Define and assign role-based access controls
- **System**: Configure system settings and preferences
- **Data**: Manage data imports, exports, and backups

## UI Component Guidelines

- **Default Table Component:** Always use the `ItemsTable` component for listing items in any page. This ensures a consistent look, feel, and behavior across the application. Only use custom tables if `ItemsTable` cannot fulfill specific requirements.

---

## Navigation Structure

```

├── Dashboard
│   └── /dashboard
├── Sales
│   ├── POS   → /sales/pos
│   ├── Orders          → /sales/orders
│   ├── Customers             → /sales/customers
│   ├── Cash Register         → /sales/cash-register
│   ├── Invoices              → /sales/invoices
│   ├── Quotes                → /sales/quotes
│   └── Returns               → /sales/returns
├── Purchasing
│   ├── Suppliers             → /purchasing/suppliers
│   ├── Purchase Orders       → /purchasing/orders
│   ├── Goods Receipt         → /purchasing/goods-receipt
│   └── Returns               → /purchasing/returns
├── Inventory
│   ├── Products              → /inventory/products
│   ├── Stock Levels          → /inventory/stock/levels
│   ├── Stock Transfers       → /inventory/stock/transfers
│   ├── Stock Adjustments     → /inventory/stock/adjustments
│   ├── Stock Movements       → /inventory/stock/movements
│   └── Low Stock Alerts      → /inventory/stock/alerts
├── Logistics
│   ├── Warehouses            → /logistics/warehouses
│   ├── Vans                  → /logistics/vans
│   ├── Van Stock             → /logistics/van-stock
│   ├── Van Loading           → /logistics/van-loading
│   └── Routes                → /logistics/routes
├── Reports
│   ├── Sales                 → /reports/sales
│   ├── Inventory             → /reports/inventory
│   ├── Van Performance       → /reports/van-performance
│   └── Financial             → /reports/financial
└── Settings
    ├── Users                 → /settings/users
    ├── Roles                 → /settings/roles
    ├── System                → /settings/system
    └── Data                  → /settings/data
```

## Development Guidelines

1. Follow the TypeScript strict mode guidelines
2. Use functional components with hooks
3. Implement proper error boundaries
4. Write unit tests for critical components
5. Follow the established folder structure
6. Use proper naming conventions for files and components

## Adding New Modules

When adding new modules:

1. Create a new directory under `app/` for the module.
2. Inside the module directory, add:
   - `page.tsx` for the main page
   - `api.ts` for all backend API logic for this module
   - a `components/` subfolder for all UI components related to this module
3. Place all components for the module inside its `components/` subfolder (do not put components at the module root)
4. Create a `layout.tsx` if the module needs a custom layout
5. Update this README if the structure changes
6. Add proper TypeScript types in the `types/` directory

> **Module structure example:**
>
> ```
> app/
>   inventory/
>     page.tsx
>     api.ts
>     components/
>       AddStorageModal.tsx
>       StorageSelector.tsx
> ```

This structure is required for all modules to keep the codebase organized, maintainable, and consistent.

6. Include any shared utilities in the `lib/` directory