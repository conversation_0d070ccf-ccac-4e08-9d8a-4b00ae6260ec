// Centralized utility for auth endpoints only
// JWT tokens are only used for authentication endpoints (login, refresh, logout, security-status)

/**
 * Get authorization headers with <PERSON><PERSON><PERSON> token for auth endpoints only
 * @returns Headers object with Authorization Bearer token
 */
export function getAuthHeaders(): { Authorization: string } | {} {
  const token = localStorage.getItem('dido_token');
  console.log('getAuthHeaders - token exists:', !!token, 'token length:', token?.length);
  return token ? { Authorization: `Bearer ${token}` } : {};
}

/**
 * Get authorization headers with JW<PERSON> token for auth endpoints only
 * @returns Headers object with Authorization Bearer token and Content-Type
 */
export function getAuthHeadersWithContentType(): HeadersInit {
  const token = localStorage.getItem('dido_token');
  console.log('getAuthHeadersWithContentType - token exists:', !!token, 'token length:', token?.length);
  const headers: HeadersInit = {
    'Content-Type': 'application/json',
  };
  
  if (token) {
    headers['Authorization'] = `Bearer ${token}`;
  }
  
  return headers;
}

/**
 * Clear all authentication tokens from localStorage
 */
export function clearAuthTokens(): void {
  console.log('Clearing authentication tokens');
  localStorage.removeItem('dido_token');
  localStorage.removeItem('dido_refresh_token');
  localStorage.removeItem('dido_user');
  
  // Dispatch a custom event to notify components that tokens were cleared
  if (typeof window !== 'undefined') {
    window.dispatchEvent(new CustomEvent('tokensCleared'));
  }
}

/**
 * Enhanced fetch wrapper for auth endpoints only
 * @param url - The URL to fetch
 * @param options - Fetch options
 * @returns Promise with the response
 */
export async function authenticatedFetch(url: string, options: RequestInit = {}): Promise<Response> {
  // Only add auth headers for auth endpoints
  if (url.includes('/auth/')) {
    // Add auth headers if not already present
    if (!options.headers) {
      options.headers = getAuthHeadersWithContentType();
    } else if (!options.headers['Authorization']) {
      const authHeaders = getAuthHeadersWithContentType();
      options.headers = { ...options.headers, ...authHeaders };
    }
  } else {
    // For non-auth endpoints, only add Content-Type
    if (!options.headers) {
      options.headers = { 'Content-Type': 'application/json' };
    } else if (!options.headers['Content-Type']) {
      options.headers = { ...options.headers, 'Content-Type': 'application/json' };
    }
  }

  try {
    const response = await fetch(url, options);
    
    // If we get a 401 on auth endpoints, clear tokens and redirect to login
    if (response.status === 401 && url.includes('/auth/')) {
      console.log('Received 401 Unauthorized on auth endpoint, clearing tokens and redirecting to login');
      clearAuthTokens();
      
      // Redirect to login page only if not already on auth page
      if (typeof window !== 'undefined' && !window.location.pathname.includes('/auth')) {
        window.location.href = '/auth';
      }
    }
    
    return response;
  } catch (error) {
    console.error('Fetch error:', error);
    throw error;
  }
}

/**
 * Get authorization headers with JWT token for auth endpoints only
 * @returns Headers object with Authorization Bearer token
 */
export function getAxiosAuthHeaders(): { Authorization?: string } {
  const token = localStorage.getItem('dido_token');
  console.log('getAxiosAuthHeaders - token exists:', !!token, 'token length:', token?.length);
  return token ? { Authorization: `Bearer ${token}` } : {};
}

/**
 * Setup global fetch interceptor for automatic 401 handling on auth endpoints only
 * This should be called once when the app initializes
 */
export function setupGlobalFetchInterceptor(): void {
  if (typeof window === 'undefined') return; // Only run on client side
  
  // Store the original fetch
  const originalFetch = window.fetch;
  
  // Override fetch to add auth headers only for auth endpoints
  window.fetch = async (input: RequestInfo | URL, init?: RequestInit) => {
    const url = typeof input === 'string' ? input : input.toString();
    
    // Only add auth headers for auth endpoints
    if (url.includes('/auth/')) {
      const token = localStorage.getItem('dido_token');
      if (token) {
        const headers = new Headers(init?.headers);
        if (!headers.has('Authorization')) {
          headers.set('Authorization', `Bearer ${token}`);
        }
        init = { ...init, headers };
      }
    }
    
    try {
      const response = await originalFetch(input, init);
      
      // Handle 401 only for auth endpoints
      if (response.status === 401 && url.includes('/auth/')) {
        console.log('Global fetch interceptor: 401 on auth endpoint, clearing tokens');
        clearAuthTokens();
        
        // Redirect to login page only if not already on auth page
        if (!window.location.pathname.includes('/auth')) {
          window.location.href = '/auth';
        }
      }
      
      return response;
    } catch (error) {
      console.error('Global fetch interceptor error:', error);
      throw error;
    }
  };
}

/**
 * Setup axios interceptors for automatic token refresh on auth endpoints only
 * @param axiosInstance - The axios instance to configure
 * @param refreshAccessToken - Function to refresh the access token
 * @param logoutCallback - Optional callback for logout
 */
export function setupAxiosInterceptors(axiosInstance: any, refreshAccessToken: () => Promise<boolean>, logoutCallback?: () => void) {
  let isRefreshing = false;
  let failedQueue: Array<{ resolve: (value?: any) => void; reject: (reason?: any) => void }> = [];

  const processQueue = (error: any, token: string | null = null) => {
    failedQueue.forEach(({ resolve, reject }) => {
      if (error) {
        reject(error);
      } else {
        resolve(token);
      }
    });
    failedQueue = [];
  };

  // Request interceptor - only add auth headers for auth endpoints
  axiosInstance.interceptors.request.use(
    (config: any) => {
      const url = config.url || '';
      if (url.includes('/auth/')) {
        const token = localStorage.getItem('dido_token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
      }
      return config;
    },
    (error: any) => {
      return Promise.reject(error);
    }
  );

  // Response interceptor - handle 401 only for auth endpoints
  axiosInstance.interceptors.response.use(
    (response: any) => {
      return response;
    },
    async (error: any) => {
      const originalRequest = error.config;
      const url = originalRequest.url || '';

      if (error.response?.status === 401 && url.includes('/auth/') && !originalRequest._retry) {
        if (isRefreshing) {
          // If already refreshing, queue this request
          return new Promise((resolve, reject) => {
            failedQueue.push({ resolve, reject });
          }).then(() => {
            return axiosInstance(originalRequest);
          }).catch((err: any) => {
            return Promise.reject(err);
          });
        }

        originalRequest._retry = true;
        isRefreshing = true;

        try {
          const refreshSuccess = await refreshAccessToken();
          if (refreshSuccess) {
            // Update the original request with new token
            const newToken = localStorage.getItem('dido_token');
            if (newToken) {
              originalRequest.headers['Authorization'] = `Bearer ${newToken}`;
            }
            processQueue(null, newToken);
            return axiosInstance(originalRequest);
          } else {
            processQueue(error, null);
            // Use logout callback if provided, otherwise fallback to window.location
            if (logoutCallback) {
              logoutCallback();
            } else {
              console.warn('No logout callback provided, using window.location fallback');
              window.location.href = '/auth';
            }
            return Promise.reject(error);
          }
        } catch (refreshError) {
          processQueue(refreshError, null);
          // Use logout callback if provided, otherwise fallback to window.location
          if (logoutCallback) {
            logoutCallback();
          } else {
            console.warn('No logout callback provided, using window.location fallback');
            window.location.href = '/auth';
          }
          return Promise.reject(refreshError);
        } finally {
          isRefreshing = false;
        }
      }

      return Promise.reject(error);
    }
  );
} 