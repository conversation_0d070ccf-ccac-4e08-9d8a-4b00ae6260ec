import { Strategy } from "passport-local";
import { PassportStrategy } from "@nestjs/passport";
import { Injectable, UnauthorizedException } from "@nestjs/common";
import { UsersService } from "../users/users.service";
import * as bcrypt from "bcrypt";

@Injectable()
export class LocalStrategy extends PassportStrategy(Strategy, "local") {
  constructor(private usersService: UsersService) {
    super({ usernameField: "email" });
  }

  async validate(email: string, password: string): Promise<any> {
    console.log("🚀 UPDATED LOCAL STRATEGY CODE IS RUNNING! 🚀");
    console.log("=== LOCAL STRATEGY VALIDATION START ===");
    console.log("📧 Email received:", email);
    console.log(
      "🔐 Password received:",
      password
        ? `"${password}" (length: ${password.length})`
        : "undefined/null",
    );
    console.log("🔐 Password type:", typeof password);
    console.log("🔐 Password is empty string:", password === "");
    console.log("🔐 Password is falsy:", !password);

    // Find user by email
    console.log("🔍 Looking up user by email...");
    const user = await this.usersService.findByEmail(email);

    if (!user) {
      console.log("❌ USER NOT FOUND for email:", email);
      console.log(
        "🔥 THROWING EXCEPTION: User not found with this email address",
      );
      console.log("=== LOCAL STRATEGY VALIDATION FAILED ===");
      throw new UnauthorizedException("User not found with this email address");
    }

    console.log("✅ User found:", {
      uuid: user.uuid,
      email: user.email,
      name: user.name,
      hasPassword: !!user.password,
      passwordLength: user.password ? user.password.length : 0,
      userType: user.userType,
    });

    // Treat empty string as no password
    const hasNoPassword = !user.password;
    const providedNoPassword = !password || password === "";

    console.log("🔐 Password analysis:");
    console.log("  - User has password set:", !hasNoPassword);
    console.log("  - User provided password:", !providedNoPassword);
    console.log(
      "  - Password match scenario:",
      hasNoPassword && providedNoPassword
        ? "Both empty"
        : hasNoPassword
          ? "User has no password"
          : providedNoPassword
            ? "User provided no password"
            : "Both have password",
    );

    // If user has no password set, allow login without password validation
    if (hasNoPassword) {
      if (providedNoPassword) {
        console.log(
          "✅ SUCCESS: User has no password set and none provided - allowing login",
        );
        console.log("=== LOCAL STRATEGY VALIDATION SUCCESS ===");
        return user;
      } else {
        console.log(
          "❌ ERROR: User has no password set but password was provided",
        );
        console.log(
          "🔥 THROWING EXCEPTION: This account is set up for passwordless login. Please leave the password field empty or do not include it in your request.",
        );
        console.log("=== LOCAL STRATEGY VALIDATION FAILED ===");
        throw new UnauthorizedException(
          "This account is set up for passwordless login. Please leave the password field empty or do not include it in your request.",
        );
      }
    }

    // If user has a password set, validate it
    if (providedNoPassword) {
      console.log("❌ ERROR: User has password set but no password provided");
      console.log(
        "🔥 THROWING EXCEPTION: This account requires a password. Please provide your password to login.",
      );
      console.log("=== LOCAL STRATEGY VALIDATION FAILED ===");
      throw new UnauthorizedException(
        "This account requires a password. Please provide your password to login.",
      );
    }

    // Check if user is sending a bcrypt hash as password
    if (password.startsWith("$2b$") && password.length === 60) {
      console.log("❌ ERROR: User is sending bcrypt hash instead of plaintext");
      console.log(
        "🔥 THROWING EXCEPTION: Invalid password format. Please enter your plaintext password, not a hash.",
      );
      console.log("=== LOCAL STRATEGY VALIDATION FAILED ===");
      throw new UnauthorizedException(
        "Invalid password format. Please enter your plaintext password, not a hash.",
      );
    }

    console.log("🔐 Comparing provided password with stored hash...");
    const valid = await bcrypt.compare(password, user.password);
    console.log("🔐 Password comparison result:", valid);

    if (!valid) {
      console.log("❌ ERROR: Password verification failed");
      console.log(
        "🔥 THROWING EXCEPTION: Invalid password. Please check your password and try again.",
      );
      console.log("=== LOCAL STRATEGY VALIDATION FAILED ===");
      throw new UnauthorizedException(
        "Invalid password. Please check your password and try again.",
      );
    }

    console.log("✅ SUCCESS: Password verified successfully");
    console.log("=== LOCAL STRATEGY VALIDATION SUCCESS ===");
    return user;
  }
}
