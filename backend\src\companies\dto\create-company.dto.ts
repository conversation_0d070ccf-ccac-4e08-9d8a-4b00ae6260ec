import { ApiProperty } from "@nestjs/swagger";
import { IsString, IsUUID, IsOptional, IsUrl } from "class-validator";

export class CreateCompanyDto {
  @ApiProperty({
    example: "018ea2bb-6b8d-7e9a-8a2a-7e9a8a2a7e9a",
    description: "UUIDv7 of the user who owns this company (required)",
  })
  @IsUUID("all")
  userUuid: string;

  @ApiProperty({
    example: "Acme Corporation",
    description: "Company name",
  })
  @IsString()
  name: string;

  @ApiProperty({
    example: "*********",
    description: "Tax identification number (NIF)",
  })
  @IsString()
  nif: string;

  @ApiProperty({
    example: "RC123456",
    description: "Register number (RC)",
  })
  @IsString()
  rc: string;

  @ApiProperty({
    example: "ART001",
    description: "Article number",
  })
  @IsString()
  articleNumber: string;

  @ApiProperty({
    example: "123 Main Street, City, Country",
    description: "Company address",
  })
  @IsString()
  address: string;

  @ApiProperty({
    example: "https://www.acme.com",
    description: "Company website URL",
    required: false,
  })
  @IsOptional()
  @IsUrl()
  website?: string;

  @ApiProperty({
    example: "+*********0",
    description: "Company phone number",
  })
  @IsString()
  phoneNumber: string;
}
