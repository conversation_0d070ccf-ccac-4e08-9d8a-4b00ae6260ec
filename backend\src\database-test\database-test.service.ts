import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Client } from 'pg';

export interface DatabaseConnectionResult {
  success: boolean;
  message: string;
  details?: any;
  timestamp: string;
}

export interface DatabaseTestResult {
  yugabyte: DatabaseConnectionResult;
  overall: {
    success: boolean;
    message: string;
    timestamp: string;
  };
}

@Injectable()
export class DatabaseTestService {
  private readonly logger = new Logger(DatabaseTestService.name);

  constructor(private configService: ConfigService) {}

  async testDatabaseConnections(): Promise<DatabaseTestResult> {
    this.logger.log('Starting database connectivity tests...');

    const timestamp = new Date().toISOString();
    
    // Test YugabyteDB connection
    const yugabyteResult = await this.testYugabyteDBConnection();

    // Determine overall success
    const overallSuccess = yugabyteResult.success;
    const overallMessage = overallSuccess 
      ? 'Database connection successful'
      : 'Database connection failed';

    const result: DatabaseTestResult = {
      yugabyte: yugabyteResult,
      overall: {
        success: overallSuccess,
        message: overallMessage,
        timestamp
      }
    };

    this.logger.log(`Database connectivity test completed. Overall success: ${overallSuccess}`);
    return result;
  }

  private async testYugabyteDBConnection(): Promise<DatabaseConnectionResult> {
    const timestamp = new Date().toISOString();
    
    // Get YugabyteDB configuration from environment variables
    const host = this.configService.get<string>('YUGABYTE_HOST');
    const port = this.configService.get<number>('YUGABYTE_PORT');
    const database = this.configService.get<string>('YUGABYTE_DATABASE');
    const user = this.configService.get<string>('YUGABYTE_USER');
    const password = this.configService.get<string>('YUGABYTE_PASSWORD');

    // Check if all required environment variables are present
    if (!host || !port || !database || !user || !password) {
      const missingVars = [];
      if (!host) missingVars.push('YUGABYTE_HOST');
      if (!port) missingVars.push('YUGABYTE_PORT');
      if (!database) missingVars.push('YUGABYTE_DATABASE');
      if (!user) missingVars.push('YUGABYTE_USER');
      if (!password) missingVars.push('YUGABYTE_PASSWORD');

      return {
        success: false,
        message: `YugabyteDB environment variables not configured: ${missingVars.join(', ')}`,
        timestamp
      };
    }

    let client: Client | null = null;
    
    try {
      this.logger.log('Testing YugabyteDB connection...');
      
      client = new Client({
        host,
        port,
        database,
        user,
        password,
        connectionTimeoutMillis: 5000, // 5 second timeout
      });
      
      await client.connect();
      
      // Test the connection with a simple query
      const result = await client.query('SELECT version() as version, current_database() as database, current_user as user');
      
      return {
        success: true,
        message: `Successfully connected to YugabyteDB database: ${database}`,
        details: {
          database,
          host,
          port,
          user,
          version: result.rows[0]?.version,
          currentDatabase: result.rows[0]?.database,
          currentUser: result.rows[0]?.user
        },
        timestamp
      };
      
    } catch (error) {
      this.logger.error('YugabyteDB connection test failed:', error.message);
      
      return {
        success: false,
        message: `YugabyteDB connection failed: ${error.message}`,
        details: {
          error: error.message,
          host,
          port,
          database,
          user
        },
        timestamp
      };
      
    } finally {
      if (client) {
        await client.end();
      }
    }
  }
} 