import { useState, useCallback, useEffect, useRef, useMemo } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import type { Product, SaleItem } from '../types';
import type { Customer } from '@/components/CustomerModal';

// Simple UUID generator for draft sales
const generateDraftId = () => `draft_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

// Internal sale state interface
export interface POSSaleState {
  // Sale identifiers
  saleUuid: string | null;
  customerUuid: string | null;
  
  // Sale data
  items: SaleItem[];
  customer: Customer | null;
  
  // Pricing and totals
  subtotal: number;
  tax: number;
  taxRate: number;
  taxEnabled: boolean;
  total: number;
  
  // Payment
  paymentMethod: string;
  amountPaid: number;
  
  // Additional info
  notes: string;
  notesEnabled: boolean;
  
  // Status
  isDirty: boolean; // Has unsaved changes
  isSubmitting: boolean;
  isLoading: boolean; // Loading cart/sale data
  error: string | null;
  
  // Mode tracking
  mode: 'new' | 'edit' | 'load';
  originalSaleUuid?: string; // For edit mode
}

export interface UsePOSStateReturn {
  // State
  saleState: POSSaleState;
  
  // Sale management
  startNewSale: () => void;
  loadSaleForEdit: (saleUuid: string, saleData?: any) => void;
  loadSaleForContinue: (saleUuid: string, saleData?: any) => void;
  clearSale: () => void;
  
  // Customer management
  setCustomer: (customer: Customer | null) => void;
  
  // Item management
  addItem: (product: Product, quantity: number, unitPrice: number) => void;
  removeItem: (productUuid: string) => void;
  updateItemQuantity: (productUuid: string, quantity: number) => void;
  updateItemPrice: (productUuid: string, unitPrice: number) => void;
  clearItems: () => void;
  
  // Pricing controls
  setTaxEnabled: (enabled: boolean) => void;
  setTaxRate: (rate: number) => void;
  recalculateTotals: () => void;
  
  // Payment controls
  setPaymentMethod: (method: string) => void;
  setAmountPaid: (amount: number) => void;
  
  // Notes
  setNotes: (notes: string) => void;
  setNotesEnabled: (enabled: boolean) => void;
  
  // Status management
  setError: (error: string | null) => void;
  setSubmitting: (isSubmitting: boolean) => void;
  setLoading: (isLoading: boolean) => void;
  markClean: () => void;
  
  // Validation
  validateSale: () => { isValid: boolean; errors: string[] };
}

// Default state
const createDefaultState = (): POSSaleState => ({
  saleUuid: null,
  customerUuid: null,
  items: [],
  customer: null,
  subtotal: 0,
  tax: 0,
  taxRate: 0.1, // 10% default
  taxEnabled: false,
  total: 0,
  paymentMethod: 'cash',
  amountPaid: 0,
  notes: '',
  notesEnabled: false,
  isDirty: false,
  isSubmitting: false,
  isLoading: false,
  error: null,
  mode: 'new'
});

export function usePOSState(): UsePOSStateReturn {
  const { user } = useAuth();
  const [saleState, setSaleState] = useState<POSSaleState>(createDefaultState);
  const warehouseUuid = user?.warehouseUuid;
  const userUuid = user?.uuid;

  // Create a dependency that tracks item data changes
  const itemsDependency = useMemo(() => {
    return saleState.items.map(item => ({
      productUuid: item.productUuid,
      quantity: item.quantity,
      unitPrice: item.unitPrice,
      totalPrice: item.totalPrice
    }));
  }, [saleState.items]);

  // Calculate totals whenever items, tax settings change
  const recalculateTotals = useCallback(() => {
    setSaleState(prev => {
      const subtotal = prev.items.reduce((sum, item) => sum + item.totalPrice, 0);
      const tax = prev.taxEnabled ? subtotal * prev.taxRate : 0;
      const total = subtotal + tax;
      
      // Round all totals to 2 decimal places
      const roundedSubtotal = Math.round(subtotal * 100) / 100;
      const roundedTax = Math.round(tax * 100) / 100;
      const roundedTotal = Math.round(total * 100) / 100;
      
      return {
        ...prev,
        subtotal: roundedSubtotal,
        tax: roundedTax,
        total: roundedTotal,
        isDirty: true
      };
    });
  }, []);

  // Auto-recalculate when items or tax settings change
  useEffect(() => {
    recalculateTotals();
  }, [itemsDependency, saleState.taxEnabled, saleState.taxRate, recalculateTotals]);



  // Sale management functions
  const startNewSale = useCallback(() => {
    const newSaleUuid = generateDraftId();
    setSaleState({
      ...createDefaultState(),
      saleUuid: newSaleUuid,
      mode: 'new'
    });
  }, []);

  const loadSaleForEdit = useCallback((saleUuid: string, saleData?: any) => {
    
    // Transform and validate items from backend
    const transformItems = (items: any[]): SaleItem[] => {
      return items.map((item, index) => {
        // Handle both backend response formats (itemsSnapshot and items)
        const productUuid = item.productUuidString || item.productUuid || `unknown_${index}`;
        
        // Ensure productName is a string
        let productName = 'Unknown Product';
        if (typeof item.productName === 'string') {
          productName = item.productName;
        } else if (typeof item.name === 'string') {
          productName = item.name;
        } else if (item.productName && typeof item.productName === 'object' && item.productName.name) {
          productName = item.productName.name;
        } else if (item.name && typeof item.name === 'object' && item.name.name) {
          productName = item.name.name;
        }
        
        // Ensure all numeric values are valid numbers (handle both string and number types)
        const quantity = (() => {
          if (typeof item.quantity === 'number' && !isNaN(item.quantity)) return item.quantity;
          if (typeof item.quantity === 'string') {
            const parsed = parseFloat(item.quantity);
            return !isNaN(parsed) ? parsed : 0;
          }
          return 0;
        })();
        
        const unitPrice = (() => {
          if (typeof item.unitPrice === 'number' && !isNaN(item.unitPrice)) return item.unitPrice;
          if (typeof item.unitPrice === 'string') {
            const parsed = parseFloat(item.unitPrice);
            return !isNaN(parsed) ? parsed : 0;
          }
          return 0;
        })();
        
        // Use lineTotal from backend if available, otherwise calculate
        const totalPrice = (() => {
          if (typeof item.lineTotal === 'number' && !isNaN(item.lineTotal)) return item.lineTotal;
          if (typeof item.lineTotal === 'string') {
            const parsed = parseFloat(item.lineTotal);
            return !isNaN(parsed) ? parsed : quantity * unitPrice;
          }
          return quantity * unitPrice;
        })();
        
        return {
          productUuid,
          name: productName,
          quantity,
          unitPrice,
          totalPrice
        };
      });
    };

    setSaleState(prev => {
      const items = saleData?.itemsSnapshot || saleData?.items || [];
      
      const transformedItems = transformItems(items);
      
      // Ensure numeric values are valid
      const amountPaid = typeof saleData?.amountPaid === 'number' && !isNaN(saleData.amountPaid) ? saleData.amountPaid : 0;
      const taxRate = typeof saleData?.taxRate === 'number' && !isNaN(saleData.taxRate) ? saleData.taxRate : 0.1;
      
      return {
        ...prev,
        saleUuid,
        originalSaleUuid: saleUuid,
        mode: 'edit',
        isDirty: false,
        // Load sale data if provided
        ...(saleData && {
          customerUuid: saleData.customerUuidString || null,
          customer: saleData.customer || null,
          items: transformedItems,
          paymentMethod: saleData.paymentMethod || 'cash',
          amountPaid,
          taxEnabled: saleData.useTax || false,
          taxRate,
          notes: saleData.notes || ''
        })
      };
    });
  }, []);

  const loadSaleForContinue = useCallback((saleUuid: string, saleData?: any) => {
    
    // Transform and validate items from backend (same as edit mode)
    const transformItems = (items: any[]): SaleItem[] => {
      return items.map((item, index) => {
        // Handle both backend response formats (itemsSnapshot and items)
        const productUuid = item.productUuidString || item.productUuid || `unknown_${index}`;
        
        // Ensure productName is a string
        let productName = 'Unknown Product';
        if (typeof item.productName === 'string') {
          productName = item.productName;
        } else if (typeof item.name === 'string') {
          productName = item.name;
        } else if (item.productName && typeof item.productName === 'object' && item.productName.name) {
          productName = item.productName.name;
        } else if (item.name && typeof item.name === 'object' && item.name.name) {
          productName = item.name.name;
        }
        
        // Ensure all numeric values are valid numbers (handle both string and number types)
        const quantity = (() => {
          if (typeof item.quantity === 'number' && !isNaN(item.quantity)) return item.quantity;
          if (typeof item.quantity === 'string') {
            const parsed = parseFloat(item.quantity);
            return !isNaN(parsed) ? parsed : 0;
          }
          return 0;
        })();
        
        const unitPrice = (() => {
          if (typeof item.unitPrice === 'number' && !isNaN(item.unitPrice)) return item.unitPrice;
          if (typeof item.unitPrice === 'string') {
            const parsed = parseFloat(item.unitPrice);
            return !isNaN(parsed) ? parsed : 0;
          }
          return 0;
        })();
        
        // Use lineTotal from backend if available, otherwise calculate
        const totalPrice = (() => {
          if (typeof item.lineTotal === 'number' && !isNaN(item.lineTotal)) return item.lineTotal;
          if (typeof item.lineTotal === 'string') {
            const parsed = parseFloat(item.lineTotal);
            return !isNaN(parsed) ? parsed : quantity * unitPrice;
          }
          return quantity * unitPrice;
        })();
        
        return {
          productUuid,
          name: productName,
          quantity,
          unitPrice,
          totalPrice
        };
      });
    };

    setSaleState(prev => {
      const items = saleData?.itemsSnapshot || saleData?.items || [];
      
      const transformedItems = transformItems(items);
      
      // Ensure numeric values are valid
      const amountPaid = typeof saleData?.amountPaid === 'number' && !isNaN(saleData.amountPaid) ? saleData.amountPaid : 0;
      const taxRate = typeof saleData?.taxRate === 'number' && !isNaN(saleData.taxRate) ? saleData.taxRate : 0.1;
      
      return {
        ...prev,
        saleUuid,
        originalSaleUuid: saleUuid,
        mode: 'load',
        isDirty: false,
        // Load sale data if provided
        ...(saleData && {
          customerUuid: saleData.customerUuidString || null,
          customer: saleData.customer || null,
          items: transformedItems,
          paymentMethod: saleData.paymentMethod || 'cash',
          amountPaid,
          taxEnabled: saleData.useTax || false,
          taxRate,
          notes: saleData.notes || ''
        })
      };
    });
  }, []);

  const clearSale = useCallback(() => {
    setSaleState(createDefaultState());
  }, []);

  // Customer management
  const setCustomer = useCallback((customer: Customer | null) => {
    setSaleState(prev => ({
      ...prev,
      customer,
      customerUuid: customer?.uuid || null,
      isDirty: true
    }));
  }, []);

  // Item management
  const addItem = useCallback((product: Product, quantity: number, unitPrice: number) => {
    if (quantity <= 0 || unitPrice < 0) return;

    // Round quantity and unitPrice to 2 decimal places
    const roundedQuantity = Math.round(quantity * 100) / 100;
    const roundedUnitPrice = Math.round(unitPrice * 100) / 100;

    setSaleState(prev => {
      const existingItemIndex = prev.items.findIndex(item => item.productUuid === product.uuid);
      
      let newItems: SaleItem[];
      if (existingItemIndex >= 0) {
        // Update existing item
        newItems = [...prev.items];
        const existingItem = newItems[existingItemIndex];
        const newQuantity = Math.round((existingItem.quantity + roundedQuantity) * 100) / 100;
        const newTotalPrice = Math.round(newQuantity * roundedUnitPrice * 100) / 100;
        
        newItems[existingItemIndex] = {
          ...existingItem,
          quantity: newQuantity,
          unitPrice: roundedUnitPrice, // Update unit price in case it changed
          totalPrice: newTotalPrice
        };
      } else {
        // Add new item
        const totalPrice = Math.round(roundedQuantity * roundedUnitPrice * 100) / 100;
        const newItem: SaleItem = {
          productUuid: product.uuid,
          name: product.name,
          quantity: roundedQuantity,
          unitPrice: roundedUnitPrice,
          totalPrice: totalPrice
        };
        newItems = [...prev.items, newItem];
      }

      return {
        ...prev,
        items: newItems,
        isDirty: true
      };
    });
  }, []);

  const removeItem = useCallback((productUuid: string) => {
    setSaleState(prev => ({
      ...prev,
      items: prev.items.filter(item => item.productUuid !== productUuid),
      isDirty: true
    }));
  }, []);

  const updateItemQuantity = useCallback((productUuid: string, quantity: number) => {
    
    if (quantity <= 0) {
      removeItem(productUuid);
      return;
    }

    // Round quantity to 2 decimal places
    const roundedQuantity = Math.round(quantity * 100) / 100;

    setSaleState(prev => {
      const updatedItems = prev.items.map(item => 
        item.productUuid === productUuid 
          ? { 
              ...item, 
              quantity: roundedQuantity, 
              totalPrice: Math.round(roundedQuantity * item.unitPrice * 100) / 100 
            }
          : item
      );
      
      return {
        ...prev,
        items: updatedItems,
        isDirty: true
      };
    });
  }, [removeItem]);

  const updateItemPrice = useCallback((productUuid: string, unitPrice: number) => {
    
    if (unitPrice < 0) return;

    // Round unitPrice to 2 decimal places
    const roundedUnitPrice = Math.round(unitPrice * 100) / 100;

    setSaleState(prev => {
      const updatedItems = prev.items.map(item => 
        item.productUuid === productUuid 
          ? { 
              ...item, 
              unitPrice: roundedUnitPrice, 
              totalPrice: Math.round(item.quantity * roundedUnitPrice * 100) / 100 
            }
          : item
      );
      
      return {
        ...prev,
        items: updatedItems,
        isDirty: true
      };
    });
  }, []);

  const clearItems = useCallback(() => {
    setSaleState(prev => ({
      ...prev,
      items: [],
      isDirty: true
    }));
  }, []);

  // Pricing controls
  const setTaxEnabled = useCallback((enabled: boolean) => {
    setSaleState(prev => ({
      ...prev,
      taxEnabled: enabled,
      isDirty: true
    }));
  }, []);

  const setTaxRate = useCallback((rate: number) => {
    const safeRate = typeof rate === 'number' && !isNaN(rate) ? rate : 0.1;
    // Round tax rate to 2 decimal places
    const roundedRate = Math.round(safeRate * 100) / 100;
    setSaleState(prev => ({
      ...prev,
      taxRate: roundedRate,
      isDirty: true
    }));
  }, []);

  // Payment controls
  const setPaymentMethod = useCallback((method: string) => {
    setSaleState(prev => ({
      ...prev,
      paymentMethod: method,
      isDirty: true
    }));
  }, []);

  const setAmountPaid = useCallback((amount: number) => {
    // Round to 2 decimal places
    const roundedAmount = Math.round(Math.max(0, amount) * 100) / 100;
    setSaleState(prev => ({
      ...prev,
      amountPaid: roundedAmount,
      isDirty: true
    }));
  }, []);

  // Notes
  const setNotes = useCallback((notes: string) => {
    setSaleState(prev => ({
      ...prev,
      notes,
      isDirty: true
    }));
  }, []);

  const setNotesEnabled = useCallback((enabled: boolean) => {
    setSaleState(prev => ({
      ...prev,
      notesEnabled: enabled,
      isDirty: true
    }));
  }, []);

  // Status management
  const setError = useCallback((error: string | null) => {
    setSaleState(prev => ({
      ...prev,
      error
    }));
  }, []);

  const setSubmitting = useCallback((isSubmitting: boolean) => {
    setSaleState(prev => ({
      ...prev,
      isSubmitting
    }));
  }, []);

  const setLoading = useCallback((isLoading: boolean) => {
    setSaleState(prev => ({
      ...prev,
      isLoading
    }));
  }, []);

  const markClean = useCallback(() => {
    setSaleState(prev => ({
      ...prev,
      isDirty: false
    }));
  }, []);

  // Validation
  const validateSale = useCallback((): { isValid: boolean; errors: string[] } => {
    const errors: string[] = [];

    if (saleState.items.length === 0) {
      errors.push('Please add at least one item to the sale');
    }

    if (!saleState.paymentMethod) {
      errors.push('Please select a payment method');
    }

    if (saleState.total <= 0) {
      errors.push('Sale total must be greater than zero');
    }

    // Validate customer selection
    if (!saleState.customer) {
      errors.push('Please select a customer for this sale');
    }

    // Validate individual items
    saleState.items.forEach((item, index) => {
      if (item.quantity <= 0) {
        errors.push(`Item ${index + 1}: Quantity must be greater than zero`);
      }
      if (item.unitPrice < 0) {
        errors.push(`Item ${index + 1}: Unit price cannot be negative`);
      }
    });

    // Validate payment amount doesn't exceed total (for edit mode)
    if (saleState.mode === 'edit' && saleState.amountPaid > saleState.total) {
      errors.push(`Payment amount (${saleState.amountPaid.toFixed(2)}) cannot exceed total sale amount (${saleState.total.toFixed(2)}). Overpayment is not allowed.`);
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }, [saleState]);

  return {
    saleState,
    startNewSale,
    loadSaleForEdit,
    loadSaleForContinue,
    clearSale,
    setCustomer,
    addItem,
    removeItem,
    updateItemQuantity,
    updateItemPrice,
    clearItems,
    setTaxEnabled,
    setTaxRate,
    recalculateTotals,
    setPaymentMethod,
    setAmountPaid,
    setNotes,
    setNotesEnabled,
    setError,
    setSubmitting,
    setLoading,
    markClean,
    validateSale
  };
} 