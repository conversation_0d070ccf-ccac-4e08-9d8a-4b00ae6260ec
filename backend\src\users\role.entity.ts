import { <PERSON><PERSON><PERSON>, PrimaryColumn, Column, CreateDateColumn, UpdateDateColumn, Index } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Uuid7 } from '../utils/uuid7';

@Entity('roles')
export class Role {
  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the role",
  })
  @PrimaryColumn('uuid')
  id: string;

  @ApiProperty({ example: "Admin", description: "Role name" })
  @Column()
  name: string;

  @ApiProperty({ 
    example: ["read", "write", "delete"], 
    description: "Array of permissions" 
  })
  @Column('text', { array: true, default: [] })
  permissions: string[];

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the warehouse the role belongs to",
  })
  @Column('uuid')
  @Index()
  warehouseUuid: string;

  @ApiProperty({ example: false, description: "Soft delete flag" })
  @Column({ default: false })
  isDeleted: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Helper method to generate UUID
  static generateId(): string {
    return new Uuid7().toString();
  }

  // Helper method to generate UUID
  static fromBinary(binary: any): string {
    if (!binary) return null;
    try {
      return new Uuid7(binary).toString();
    } catch {
      return null;
    }
  }
} 