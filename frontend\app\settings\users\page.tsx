"use client";
import React, { useState, useMemo, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import ErrorToast from '@/components/ErrorToast';
import { fetchUsers, fetchRoles, addUser, setUserRole, updateUserProfile, deleteUser, assignUserVan, User, Role } from './usersApi';
import { listVans, VanDto } from '@/app/logistics/vans/vansApi';

import UserTable from './components/UserTable';
import AddUserModal, { AddUserForm } from './components/AddUserModal';
import EditUserModal, { EditUserForm } from './components/EditUserModal';
import DeleteUserDialog from './components/DeleteUserDialog';
import UserDetailsModal from './components/UserDetailsModal';

function extractErrorMessage(error: unknown): string {
  if (!error) return 'An unknown error occurred.';
  if (typeof error === 'string') return error;
  if (error instanceof Error) return error.message;
  if (typeof error === 'object' && 'message' in error && typeof (error as any).message === 'string') {
    return (error as any).message;
  }
  return 'An unexpected error occurred.';
}

const UserManagementPage = () => {
  const { user } = useAuth();
  const warehouseUuid = user?.warehouseUuid;
  const queryClient = useQueryClient();

  const [isAddOpen, setAddOpen] = useState(false);
  const [editUser, setEditUser] = useState<User | null>(null);
  const [deleteUserId, setDeleteUserId] = useState<string | null>(null);
  const [viewDetailsUserId, setViewDetailsUserId] = useState<string | null>(null);
  const [roleChanging, setRoleChanging] = useState<string | null>(null);
  const [vanChanging, setVanChanging] = useState<string | null>(null);
  const [optimisticRoleMap, setOptimisticRoleMap] = useState<Record<string, string>>({});
  const [optimisticVanMap, setOptimisticVanMap] = useState<Record<string, string>>({});

  const { data: users = [], isLoading: loadingUsers, error: usersError } = useQuery<User[], Error>({
    queryKey: ['users', warehouseUuid],
    queryFn: () => {
      if (!warehouseUuid) throw new Error("Warehouse UUID is not available.");
      return fetchUsers(warehouseUuid);
    },
    enabled: !!warehouseUuid,
  });

  const { data: roles = [], isLoading: loadingRoles, error: rolesError } = useQuery<Role[], Error>({
    queryKey: ['roles', warehouseUuid],
    queryFn: () => {
      if (!warehouseUuid) throw new Error("Warehouse UUID is not available.");
      return fetchRoles(warehouseUuid);
    },
    enabled: !!warehouseUuid,
  });

  const { data: vans = [], isLoading: loadingVans, error: vansError } = useQuery<VanDto[], Error>({
    queryKey: ['vans', warehouseUuid],
    queryFn: () => {
      if (!warehouseUuid) throw new Error("Warehouse UUID is not available.");
      return listVans({ warehouseUuid });
    },
    enabled: !!warehouseUuid,
  });

  const addUserMutation = useMutation<User, Error, AddUserForm & { userType: 'user' }>({
    mutationFn: async (data: AddUserForm & { userType: 'user' }) => {
      if (!warehouseUuid) {
        throw new Error("Warehouse information is not available.");
      }
      return await addUser({ ...data, warehouseUuid });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] });
      setAddOpen(false);
      toast.success('User added successfully!');
    },
    onError: (error) => {
      toast.error(<ErrorToast message={extractErrorMessage(error)} />);
    },
  });

  const setUserRoleMutation = useMutation<any, Error, { userUuid: string; roleUuid: string }>({
    mutationFn: (data: { userUuid: string; roleUuid: string }) => {
      if (!user) {
        throw new Error("User not authenticated.");
      }
      return setUserRole({ ...data, assignerUuid: user.uuid! });
    },
    onMutate: ({ userUuid, roleUuid }) => {
      setOptimisticRoleMap(prev => ({ ...prev, [userUuid]: roleUuid }));
      setRoleChanging(userUuid);
    },
    onSettled: (_data, _error, { userUuid }) => {
      setRoleChanging(null);
      setOptimisticRoleMap(prev => {
        const newMap = { ...prev };
        delete newMap[userUuid];
        return newMap;
      });
      queryClient.invalidateQueries({ queryKey: ['users'] });
    },
    onError: (error, { userUuid }) => {
      toast.error(<ErrorToast message={extractErrorMessage(error)} />);
      // Revert optimistic update
      setRoleChanging(null);
      setOptimisticRoleMap(prev => {
        const newMap = { ...prev };
        delete newMap[userUuid];
        return newMap;
      });
      queryClient.invalidateQueries({ queryKey: ['users'] });
    },
  });

  const assignVanMutation = useMutation<any, Error, { userUuid: string; vanUuid: string }>({
    mutationFn: (data: { userUuid: string; vanUuid: string }) => {
      return assignUserVan(data.userUuid, data.vanUuid);
    },
    onMutate: ({ userUuid, vanUuid }) => {
      setOptimisticVanMap(prev => ({ ...prev, [userUuid]: vanUuid }));
      setVanChanging(userUuid);
    },
    onSettled: (_data, _error, { userUuid }) => {
      setVanChanging(null);
      setOptimisticVanMap(prev => {
        const newMap = { ...prev };
        delete newMap[userUuid];
        return newMap;
      });
      queryClient.invalidateQueries({ queryKey: ['users'] });
    },
    onSuccess: () => {
      toast.success('Van assigned successfully!');
    },
    onError: (error, { userUuid }) => {
      toast.error(<ErrorToast message={extractErrorMessage(error)} />);
      // Revert optimistic update
      setVanChanging(null);
      setOptimisticVanMap(prev => {
        const newMap = { ...prev };
        delete newMap[userUuid];
        return newMap;
      });
      queryClient.invalidateQueries({ queryKey: ['users'] });
    },
  });

  const updateUserProfileMutation = useMutation<string, Error, { uuid: string } & EditUserForm>({
    mutationFn: async (data: { uuid: string } & EditUserForm) => {
      await updateUserProfile(data.uuid, { name: data.name, newPassword: data.newPassword || undefined });
      return data.uuid;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] });
      setEditUser(null);
      toast.success('User profile updated successfully!');
    },
    onError: (error) => {
      toast.error(<ErrorToast message={extractErrorMessage(error)} />);
    },
  });

  const deleteUserMutation = useMutation<void, Error, string>({
    mutationFn: (uuid: string) => deleteUser(uuid),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] });
      setDeleteUserId(null);
      toast.success('User deleted successfully.');
    },
    onError: (error) => {
      toast.error(<ErrorToast message={extractErrorMessage(error)} />);
    },
  });

  useEffect(() => {
    const handler = (e: KeyboardEvent) => {
      if (e.key === 'F2') setAddOpen(true);
    };
    window.addEventListener('keydown', handler);
    return () => window.removeEventListener('keydown', handler);
  }, []);

  return (
    <div className="p-4 sm:p-6 w-full">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-bold">User Management</h1>
        <button
          onClick={() => setAddOpen(true)}
          className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
        >
          Add User (F2)
        </button>
      </div>

      {loadingUsers ? (
        <p>Loading users...</p>
      ) : usersError ? (
        <p className="text-red-500">Error loading users: {usersError.message}</p>
      ) : (
        <UserTable
          users={users}
          roles={roles}
          vans={vans}
          onEdit={setEditUser}
          onDelete={setDeleteUserId}
          onViewDetails={setViewDetailsUserId}
          onAssignRole={(userUuid, roleUuid) => setUserRoleMutation.mutate({ userUuid, roleUuid })}
          onAssignVan={(userUuid, vanUuid) => assignVanMutation.mutate({ userUuid, vanUuid })}
          loadingRoles={loadingRoles}
          loadingVans={loadingVans}
          rolesError={rolesError}
          vansError={vansError}
          roleChanging={roleChanging}
          vanChanging={vanChanging}
          optimisticRoleMap={optimisticRoleMap}
          optimisticVanMap={optimisticVanMap}
        />
      )}

      <AddUserModal
        isOpen={isAddOpen}
        onClose={() => setAddOpen(false)}
        onSubmit={(data) => addUserMutation.mutate(data)}
        roles={roles}
        vans={vans}
        isLoading={addUserMutation.isPending}
      />

      <EditUserModal
        isOpen={!!editUser}
        onClose={() => setEditUser(null)}
        user={editUser}
        onSave={(uuid, data) => updateUserProfileMutation.mutate({ uuid, ...data })}
        roles={roles}
        vans={vans}
        isLoading={updateUserProfileMutation.isPending}
      />

      <DeleteUserDialog
        isOpen={!!deleteUserId}
        onClose={() => setDeleteUserId(null)}
        onConfirm={() => deleteUserMutation.mutate(deleteUserId!)}
        isLoading={deleteUserMutation.isPending}
      />

      <UserDetailsModal
        isOpen={!!viewDetailsUserId}
        onClose={() => setViewDetailsUserId(null)}
        userUuid={viewDetailsUserId}
      />
    </div>
  );
};

export default UserManagementPage;
