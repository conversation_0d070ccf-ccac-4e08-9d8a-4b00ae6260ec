.topTaskBar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 56px;
  padding: 0 2rem;
  background: #fff;
  border-bottom: 1px solid #e5e7eb;
  box-shadow: 0 2px 4px rgba(0,0,0,0.02);
  position: sticky;
  top: 0;
  z-index: 100;
}
.leftSection {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}
.warehouseLabel {
  font-size: 0.95rem;
  color: #888;
  margin-right: 0.25rem;
}
.warehouseName {
  font-weight: 600;
  color: #222;
}
.warehouseDropdownIcon {
  color: #888;
  margin-left: 0.25rem;
  font-size: 1rem;
}
.rightSection {
  display: flex;
  align-items: center;
  gap: 1.25rem;
}
.iconButton {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 1.3rem;
  color: #555;
  transition: color 0.15s;
  padding: 0.25rem;
}
.iconButton:hover {
  color: #1d4ed8;
}
.userSection {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-left: 1rem;
}
.avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #e5e7eb;
}
.avatarPlaceholder {
  width: 32px;
  height: 32px;
  background: #e5e7eb;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 1.1rem;
  color: #555;
}
.userName {
  font-size: 1rem;
  color: #222;
  font-weight: 500;
}

.userDropdown {
  position: relative;
  display: flex;
  align-items: center;
}

.userDropdownTrigger {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 0.375rem;
  transition: background-color 0.15s;
  color: inherit;
  font: inherit;
}

.userDropdownTrigger:hover {
  background-color: #f3f4f6;
}

.userDropdownIcon {
  color: #888;
  font-size: 0.875rem;
  transition: transform 0.15s;
}

.userDropdownIcon.rotated {
  transform: rotate(180deg);
}

.userDropdownMenu {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: 0.5rem;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  min-width: 160px;
  z-index: 1000;
}

.userDropdownItem {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  width: 100%;
  padding: 0.75rem 1rem;
  background: none;
  border: none;
  text-align: left;
  font-size: 0.875rem;
  color: #374151;
  cursor: pointer;
  transition: background-color 0.15s;
}

.userDropdownItem:hover {
  background-color: #f9fafb;
}

.userDropdownItem:first-child {
  border-radius: 0.5rem 0.5rem 0 0;
}

.userDropdownItem:last-child {
  border-radius: 0 0 0.5rem 0.5rem;
}

.userDropdownItemIcon {
  font-size: 1rem;
  color: #6b7280;
}

.warehouseDropdown {
  position: relative;
  display: flex;
  align-items: center;
}

.warehouseDropdownTrigger {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 0.375rem;
  transition: background-color 0.15s;
  color: inherit;
  font: inherit;
}

.warehouseDropdownTrigger:hover:not(:disabled) {
  background-color: #f3f4f6;
}

.warehouseDropdownTrigger:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.warehouseDropdownMenu {
  position: absolute;
  top: 100%;
  left: 0;
  margin-top: 0.5rem;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  min-width: 200px;
  z-index: 1000;
  max-height: 300px;
  overflow-y: auto;
}

.warehouseDropdownItem {
  display: block;
  width: 100%;
  padding: 0.75rem 1rem;
  background: none;
  border: none;
  text-align: left;
  font-size: 0.875rem;
  color: #374151;
  cursor: pointer;
  transition: background-color 0.15s;
}

.warehouseDropdownItem:hover {
  background-color: #f9fafb;
}

.warehouseDropdownItem.active {
  background-color: #dbeafe;
  color: #1d4ed8;
  font-weight: 500;
}

.warehouseDropdownItem:first-child {
  border-radius: 0.5rem 0.5rem 0 0;
}

.warehouseDropdownItem:last-child {
  border-radius: 0 0 0.5rem 0.5rem;
}

.warehouseDropdownDivider {
  height: 1px;
  background-color: #e5e7eb;
  margin: 0.25rem 0;
}

.addWarehouseButton {
  display: flex !important;
  align-items: center;
  gap: 0.5rem;
  color: #1d4ed8 !important;
  font-weight: 500;
}

.addWarehouseButton:hover {
  background-color: #dbeafe !important;
}

.warehouseDropdownItemIcon {
  font-size: 1rem;
  color: #6b7280;
}
