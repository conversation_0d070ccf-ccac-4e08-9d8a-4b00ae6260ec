import { <PERSON><PERSON><PERSON>, PrimaryColumn, Column, CreateDateColumn, UpdateDateColumn, Index } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Uuid7 } from '../utils/uuid7';
import { FeatureIdentifier } from './enums/feature.enum';

@Entity('features')
export class Feature {
  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the feature (primary key)",
  })
  @PrimaryColumn('uuid')
  id: string;

  @ApiProperty({
    example: "INVENTORY_TRACKING",
    description: "The identifier of the feature",
    enum: FeatureIdentifier,
  })
  @Column({ unique: true })
  @Index()
  identifier: FeatureIdentifier;

  @ApiProperty({
    example: "Inventory Tracking",
    description: "The display name of the feature",
  })
  @Column()
  name: string;

  @ApiProperty({ example: false, description: "Soft delete flag" })
  @Column({ default: false })
  isDeleted: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Helper method to generate UUID
  static generateId(): string {
    return new Uuid7().toString();
  }

  // Helper method to generate UUID
  static fromBinary(binary: any): string {
    if (!binary) return null;
    try {
      return new Uuid7(binary).toString();
    } catch {
      return null;
    }
  }
} 