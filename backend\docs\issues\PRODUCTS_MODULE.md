# Products Module Documentation

**Location:** `backend/src/products/`

## Purpose
Manages product catalog, including product information, pricing, and warehouse associations.

## Structure
```
products/
├── products.controller.ts   # Product CRUD operations
├── products.service.ts      # Product business logic
├── products.schema.ts       # Product data model
├── products.module.ts       # Module configuration
└── dto/                     # Data transfer objects
    ├── create-product.dto.ts
    ├── update-product.dto.ts
    ├── delete-product.dto.ts
    └── product-response.dto.ts
```

## Key Features
- Product CRUD operations
- Warehouse-specific product management
- Product category associations
- Multiple pricing tiers (retail, wholesale, etc.)
- SKU and barcode management
- Soft delete functionality
- Product filtering and search

## Endpoints

### Product Management
- `POST /products` - Create product
- `GET /products` - List products (with pagination and filtering)
- `GET /products/warehouse/:warehouseUuid` - List products by warehouse
- `GET /products/:uuid` - Get product by UUID
- `PUT /products/:uuid` - Update product
- `DELETE /products/:uuid` - Soft delete product
- `DELETE /products/all` - Hard delete all products

## Schema Fields

```typescript
{
  _id: Binary,                    // Primary key (UUIDv7)
  warehouseUuid: Binary,          // Warehouse reference (indexed)
  name: string,                   // Product name (required)
  description?: string,           // Product description
  sku?: string,                   // Stock keeping unit
  barcode?: string,               // Product barcode
  productCategoryUuid?: Binary,   // Category reference (indexed)
  retailPrice?: number,           // Retail price
  wholesalePrice?: number,        // Wholesale price
  midWholesalePrice?: number,     // Mid-wholesale price
  institutionalPrice?: number,    // Institutional price
  cost?: number,                  // Product cost
  isDeleted: boolean              // Soft delete flag (default: false)
}
```

## Virtual Properties
- `uuid` - String representation of _id
- `warehouseUuidString` - String representation of warehouseUuid
- `productCategoryUuidString` - String representation of productCategoryUuid

## DTOs

### Create Product DTO
```typescript
{
  warehouseUuid: string,          // Required
  name: string,                   // Required
  description?: string,           // Optional
  sku?: string,                   // Optional
  barcode?: string,               // Optional
  productCategoryUuid?: string,   // Optional
  retailPrice?: number,           // Optional
  wholesalePrice?: number,        // Optional
  midWholesalePrice?: number,     // Optional
  institutionalPrice?: number,    // Optional
  cost?: number                   // Optional
}
```

### Update Product DTO
```typescript
{
  warehouseUuid?: string,         // Optional
  name?: string,                  // Optional
  description?: string,           // Optional
  sku?: string,                   // Optional
  barcode?: string,               // Optional
  productCategoryUuid?: string,   // Optional
  retailPrice?: number,           // Optional
  wholesalePrice?: number,        // Optional
  midWholesalePrice?: number,     // Optional
  institutionalPrice?: number,    // Optional
  cost?: number                   // Optional
}
```

### Product Response DTO
```typescript
{
  uuid: string,                   // Product UUID
  warehouseUuidString: string,    // Warehouse UUID string
  name: string,                   // Product name
  description?: string,           // Product description
  sku?: string,                   // SKU
  barcode?: string,               // Barcode
  productCategoryUuidString?: string, // Category UUID string
  retailPrice?: number,           // Retail price
  wholesalePrice?: number,        // Wholesale price
  midWholesalePrice?: number,     // Mid-wholesale price
  institutionalPrice?: number,    // Institutional price
  cost?: number,                  // Product cost
  isDeleted: boolean              // Soft delete flag
}
```

## Business Logic

### Product Creation
1. **UUID Generation:** Automatically generates UUIDv7
2. **Warehouse Assignment:** Links product to specific warehouse
3. **Category Association:** Optional category assignment
4. **Price Management:** Supports multiple pricing tiers
5. **Validation:** Ensures required fields are present

### Product Listing
1. **Pagination:** Supports page and limit parameters
2. **Filtering:** Filter by warehouse, category, name
3. **Soft Delete:** Excludes deleted products by default
4. **Sorting:** Default sorting by creation date

### Product Updates
1. **Partial Updates:** Only update provided fields
2. **Validation:** Validates UUID format and existence
3. **Category Updates:** Can change product category
4. **Price Updates:** Can update any pricing tier

### Product Deletion
1. **Soft Delete:** Sets `isDeleted: true` by default
2. **Hard Delete:** Special endpoint for complete removal
3. **Cascade Protection:** Prevents deletion if referenced elsewhere

## Pricing Structure
- **Retail Price:** Standard customer price
- **Wholesale Price:** Bulk purchase price
- **Mid-Wholesale Price:** Medium quantity price
- **Institutional Price:** Large organization price
- **Cost:** Product acquisition cost

## Filtering Options
- **Warehouse:** Filter by specific warehouse
- **Category:** Filter by product category
- **Name:** Search by product name
- **SKU:** Search by SKU
- **Barcode:** Search by barcode

## Dependencies
- `@nestjs/mongoose` - MongoDB integration
- `../utils/uuid7` - UUID generation and conversion
- `../product-categories` - Category references

## Related Modules
- **Product Categories Module** - Category management
- **Inventory Module** - Stock management
- **Warehouses Module** - Warehouse references
- **Sales Module** - Product sales
- **Purchase Module** - Product purchases

## Issues Found

### 🔴 Critical Issues
1. **Missing Entity Fields**
   - Issue: No `createdBy`, `updatedBy` fields
   - Impact: No user tracking for creation/updates

2. **Missing Timestamps**
   - Issue: No `createdAt`, `updatedAt` fields
   - Impact: No audit trail for product changes

### 🟡 Medium Priority Issues
1. **Inconsistent Schema Patterns**
   - Issue: Schema doesn't follow entity standards
   - Impact: Inconsistent data model

2. **Limited Validation**
   - Issue: Basic validation only
   - Impact: Data integrity concerns

## Database Indexes
- `warehouseUuid` - For warehouse-specific queries
- `productCategoryUuid` - For category filtering
- `_id` - Primary key (automatic)

## Performance Considerations
- Indexed fields for efficient querying
- Pagination for large datasets
- Soft delete for data retention
- UUID-based lookups

## Security Considerations
- UUIDv7 for secure identifiers
- Warehouse scoping for data isolation
- Input validation for all fields
- Soft delete for data protection

## Future Improvements
1. Add missing entity fields (createdBy, updatedBy, timestamps)
2. Implement product image management
3. Add product variants support
4. Implement product search with full-text indexing
5. Add product import/export functionality
6. Implement product approval workflow
7. Add product analytics and reporting
8. Implement product versioning 