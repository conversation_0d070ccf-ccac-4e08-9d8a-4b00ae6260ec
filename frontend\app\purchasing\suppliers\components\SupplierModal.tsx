import React, { useEffect, useRef } from 'react';
import { CreateSupplierDto, UpdateSupplierDto, Supplier } from '../types';

interface SupplierModalProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (values: CreateSupplierDto | UpdateSupplierDto) => void;
  initialValues?: Partial<CreateSupplierDto & UpdateSupplierDto>;
  mode: 'add' | 'edit';
  loading: boolean;
  error?: string;
}

export default function SupplierModal({
  open,
  onClose,
  onSubmit,
  initialValues = {},
  mode,
  loading,
  error
}: SupplierModalProps) {
  const [values, setValues] = React.useState<Partial<CreateSupplierDto & UpdateSupplierDto>>(initialValues);
  const firstFieldRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (open) {
      setValues(initialValues);
      setTimeout(() => firstFieldRef.current?.focus(), 100);
    }
  }, [open, initialValues]);

  // Handle Escape key to close modal
  useEffect(() => {
    if (!open) return;
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === "Escape") onClose();
    };
    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [open, onClose]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    setValues(v => ({ 
      ...v, 
      [name]: type === 'number' ? (value === '' ? undefined : Number(value)) : value 
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(values as CreateSupplierDto | UpdateSupplierDto);
  };

  if (!open) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-30" role="dialog" aria-modal="true" aria-labelledby="supplier-modal-title">
      <div className="bg-white rounded shadow-lg w-full max-w-md p-6 relative max-h-[90vh] overflow-y-auto">
        <h2 className="text-xl font-bold mb-2" id="supplier-modal-title">{mode === 'add' ? 'Add Supplier' : 'Edit Supplier'}</h2>
        <p className="mb-4 text-gray-500">{mode === 'add' ? 'Enter details for the new supplier.' : 'Update supplier details.'}</p>
        {error && <div className="mb-3 text-red-600 text-sm">{error}</div>}
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block font-medium mb-1" htmlFor="name">Name <span className="text-red-500">*</span></label>
            <input 
              ref={firstFieldRef} 
              id="name" 
              name="name" 
              type="text" 
              required 
              className="w-full border rounded px-3 py-2" 
              value={values.name || ''} 
              onChange={handleChange} 
              placeholder="Supplier name" 
            />
          </div>
          
          <div>
            <label className="block font-medium mb-1" htmlFor="fiscalId">Fiscal ID</label>
            <input 
              id="fiscalId" 
              name="fiscalId" 
              type="text" 
              className="w-full border rounded px-3 py-2" 
              value={values.fiscalId || ''} 
              onChange={handleChange} 
              placeholder="Tax identification number" 
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block font-medium mb-1" htmlFor="rc">Registration Certificate</label>
              <input 
                id="rc" 
                name="rc" 
                type="text" 
                className="w-full border rounded px-3 py-2" 
                value={values.rc || ''} 
                onChange={handleChange} 
                placeholder="RC number" 
              />
            </div>
            <div>
              <label className="block font-medium mb-1" htmlFor="articleNumber">Article Number</label>
              <input 
                id="articleNumber" 
                name="articleNumber" 
                type="text" 
                className="w-full border rounded px-3 py-2" 
                value={values.articleNumber || ''} 
                onChange={handleChange} 
                placeholder="Article number" 
              />
            </div>
          </div>

          <div>
            <label className="block font-medium mb-1" htmlFor="email">Email</label>
            <input 
              id="email" 
              name="email" 
              type="email" 
              className="w-full border rounded px-3 py-2" 
              value={values.email || ''} 
              onChange={handleChange} 
              placeholder="Email address" 
            />
          </div>

          <div>
            <label className="block font-medium mb-1" htmlFor="phone">Phone</label>
            <input 
              id="phone" 
              name="phone" 
              type="text" 
              className="w-full border rounded px-3 py-2" 
              value={values.phone || ''} 
              onChange={handleChange} 
              placeholder="Phone number" 
            />
          </div>

          <div>
            <label className="block font-medium mb-1" htmlFor="address">Address</label>
            <textarea 
              id="address" 
              name="address" 
              className="w-full border rounded px-3 py-2" 
              value={values.address || ''} 
              onChange={handleChange} 
              placeholder="Full address" 
              rows={3}
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block font-medium mb-1" htmlFor="latitude">Latitude</label>
              <input 
                id="latitude" 
                name="latitude" 
                type="number" 
                step="any"
                className="w-full border rounded px-3 py-2" 
                value={values.latitude || ''} 
                onChange={handleChange} 
                placeholder="Latitude" 
              />
            </div>
            <div>
              <label className="block font-medium mb-1" htmlFor="longitude">Longitude</label>
              <input 
                id="longitude" 
                name="longitude" 
                type="number" 
                step="any"
                className="w-full border rounded px-3 py-2" 
                value={values.longitude || ''} 
                onChange={handleChange} 
                placeholder="Longitude" 
              />
            </div>
          </div>

          <div>
            <label className="block font-medium mb-1" htmlFor="notes">Notes</label>
            <textarea 
              id="notes" 
              name="notes" 
              className="w-full border rounded px-3 py-2" 
              value={values.notes || ''} 
              onChange={handleChange} 
              placeholder="Additional notes or remarks" 
              rows={3}
            />
          </div>

          <div className="flex gap-2 mt-6">
            <button 
              type="submit" 
              className="flex-1 bg-blue-600 text-white rounded px-4 py-2 font-semibold disabled:opacity-60" 
              disabled={loading}
            >
              {loading ? 'Saving...' : (mode === 'add' ? 'Add Supplier' : 'Save Changes')}
            </button>
            <button 
              type="button" 
              className="flex-1 bg-gray-200 text-gray-700 rounded px-4 py-2 font-semibold" 
              onClick={onClose} 
              disabled={loading}
            >
              Cancel
            </button>
          </div>
        </form>
        <button 
          className="absolute top-2 right-2 text-gray-400 hover:text-gray-700" 
          onClick={onClose} 
          aria-label="Close"
        >
          &times;
        </button>
      </div>
    </div>
  );
}
