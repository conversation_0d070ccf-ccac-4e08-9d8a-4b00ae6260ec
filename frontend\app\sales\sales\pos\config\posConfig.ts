// POS Configuration - Centralized configuration for POS page parameters

export interface POSConfig {
  // Default payment method
  defaultPaymentMethod: string;
  
  // URL parameters for loading sales
  urlParams: {
    editSale: string;
    loadSale: string;
    customerUuid: string;
    warehouseUuid: string;
  };
  
  // Keyboard navigation settings
  keyboard: {
    preserveSelectionAfterAdd: boolean;
    autoFocusSearchAfterAdd: boolean;
  };
  
  // Product display settings
  products: {
    itemsPerPage: number;
    maxSearchResults: number;
    showStockLevels: boolean;
  };
  
  // Cart settings
  cart: {
    allowEmptyCustomer: boolean;
    autoCalculateTax: boolean;
    taxRate: number;
  };
}

// Default configuration
export const defaultPOSConfig: POSConfig = {
  defaultPaymentMethod: 'cash',
  
  urlParams: {
    editSale: 'edit',
    loadSale: 'load',
    customerUuid: 'customer',
    warehouseUuid: 'warehouse',
  },
  
  keyboard: {
    preserveSelectionAfterAdd: true,
    autoFocusSearchAfterAdd: true,
  },
  
  products: {
    itemsPerPage: 13, // Reduced to 13 items per page
    maxSearchResults: 100,
    showStockLevels: true,
  },
  
  cart: {
    allowEmptyCustomer: true,
    autoCalculateTax: true,
    taxRate: 0.1, // 10% tax rate
  },
};

// Helper function to get URL parameters for POS
export function getPOSUrlParams(saleUuid?: string, customerUuid?: string, warehouseUuid?: string): string {
  const params = new URLSearchParams();
  
  if (saleUuid) {
    params.append(defaultPOSConfig.urlParams.editSale, saleUuid);
  }
  
  if (customerUuid) {
    params.append(defaultPOSConfig.urlParams.customerUuid, customerUuid);
  }
  
  if (warehouseUuid) {
    params.append(defaultPOSConfig.urlParams.warehouseUuid, warehouseUuid);
  }
  
  return params.toString();
}

// Helper function to build POS URL
export function buildPOSUrl(saleUuid?: string, customerUuid?: string, warehouseUuid?: string): string {
  const params = getPOSUrlParams(saleUuid, customerUuid, warehouseUuid);
  const baseUrl = '/sales';
  const urlParams = new URLSearchParams(params);
  
  // Always add view=pos to show POS mode
  urlParams.set('view', 'pos');
  
  return `${baseUrl}?${urlParams.toString()}`;
}

// Helper function to parse POS URL parameters
export function parsePOSUrlParams(searchParams: URLSearchParams): {
  editSaleUuid?: string;
  loadSaleUuid?: string;
  customerUuid?: string;
  warehouseUuid?: string;
} {
  return {
    editSaleUuid: searchParams.get(defaultPOSConfig.urlParams.editSale) || undefined,
    loadSaleUuid: searchParams.get(defaultPOSConfig.urlParams.loadSale) || undefined,
    customerUuid: searchParams.get(defaultPOSConfig.urlParams.customerUuid) || undefined,
    warehouseUuid: searchParams.get(defaultPOSConfig.urlParams.warehouseUuid) || undefined,
  };
} 