import { Injectable, NotFoundException } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";
import { Role } from "../users/role.entity";

@Injectable()
export class RolesService {
  constructor(
    @InjectRepository(Role)
    private roleRepository: Repository<Role>
  ) {}

  /**
   * Finds a role by name and warehouseUuid (UUIDv7 string).
   * Returns the role or throws if not found.
   */
  async findRoleByNameAndWarehouseUuid(name: string, warehouseUuid: string) {
    const role = await this.roleRepository.findOne({
      where: {
        name,
        warehouseUuid,
        isDeleted: false,
      },
    });
    if (!role) {
      throw new NotFoundException(`Role '${name}' not found for warehouse`);
    }
    return role;
  }
}
