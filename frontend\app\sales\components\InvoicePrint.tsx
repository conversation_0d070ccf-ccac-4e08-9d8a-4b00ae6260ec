"use client";
import React, { useEffect } from 'react';
import { Sale } from '../sales/salesApi';
import { <PERSON><PERSON>rin<PERSON>, FiX } from 'react-icons/fi';

interface CompanyInfo {
  uuid: string;
  name: string;
  nif: string;
  rc: string;
  articleNumber: string;
  address: string;
  phoneNumber: string;
  website?: string;
}

interface CustomerInfo {
  uuid: string;
  name: string;
  fiscalId: string;
  email?: string;
  phone?: string;
  address?: string;
  rc?: string;
  articleNumber?: string;
  customerType: 'retail' | 'wholesale' | 'mid-wholesale' | 'institutional';
}

interface InvoicePrintProps {
  sale: Sale;
  companyInfo: CompanyInfo;
  customerInfo: CustomerInfo;
  isOpen: boolean;
  onClose: () => void;
}

const InvoicePrint: React.FC<InvoicePrintProps> = ({
  sale,
  companyInfo,
  customerInfo,
  isOpen,
  onClose
}) => {

  // Debug tax information
  useEffect(() => {
    if (isOpen && sale) {
      // console.log('InvoicePrint - Sale data:', {
      //   uuid: sale.uuid,
      //   invoiceNumber: sale.invoiceNumber,
      //   useTax: sale.useTax,
      //   taxRate: sale.taxRate,
      //   taxAmount: sale.taxAmount,
      //   subtotal: sale.subtotal,
      //   totalAmount: sale.totalAmount
      // });
    }
  }, [isOpen, sale]);

  useEffect(() => {
    if (isOpen) {
      // Auto-focus first field when modal opens
      const handleKeyDown = (e: KeyboardEvent) => {
        if (e.key === "Escape") {
          onClose();
        }
      };
      
      window.addEventListener("keydown", handleKeyDown);
      
      return () => {
        window.removeEventListener("keydown", handleKeyDown);
      };
    }
  }, [isOpen, onClose]);

  const formatDate = (dateString: string | Date) => {
    return new Date(dateString).toLocaleDateString('fr-DZ', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('fr-DZ', {
      style: 'currency',
      currency: 'DZD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(amount);
  };

  const handlePrint = () => {
    try {
      // Hide the modal temporarily
      const modal = document.querySelector('.fixed.inset-0.bg-black.bg-opacity-50') as HTMLElement;
      if (modal) {
        modal.style.display = 'none';
      }

      // Create print styles
      const printStyles = `
        <style id="invoice-print-styles">
          @media print {
            body * { visibility: hidden; }
            #invoice-print-content, #invoice-print-content * { visibility: visible; }
            #invoice-print-content { position: absolute; left: 0; top: 0; width: 100%; }
            .no-print { display: none !important; }
          }
        </style>
      `;

      // Add print styles to head
      document.head.insertAdjacentHTML('beforeend', printStyles);

      // Create invoice content
      const invoiceContent = `
        <div id="invoice-print-content" style="display: none;">
          <div style="max-width: 800px; margin: 0 auto; font-family: Arial, sans-serif; padding: 20px;">
            <!-- Header -->
            <div style="text-align: center; margin-bottom: 30px; border-bottom: 2px solid #333; padding-bottom: 20px;">
              <div style="font-size: 24px; font-weight: bold; margin-bottom: 10px;">FACTURE</div>
              <div style="font-size: 18px; margin-bottom: 5px;">N° ${sale.invoiceNumber}</div>
              <div style="font-size: 14px; color: #666;">Date: ${formatDate(sale.invoiceDate || sale.createdAt)} | Paiement: ${(sale.paymentMethod || 'Non spécifié').replace('_', ' ')}</div>
            </div>

            <!-- Parties Information -->
            <div style="display: flex; justify-content: space-between; margin-bottom: 30px;">
              <div style="flex: 1; margin-right: 20px;">
                <div style="font-weight: bold; margin-bottom: 10px; font-size: 16px;">Vendeur:</div>
                <div style="font-size: 14px; line-height: 1.4;">
                  <p><strong>${companyInfo.name}</strong></p>
                  <p>NIF: ${companyInfo.nif}</p>
                  <p>RC: ${companyInfo.rc}</p>
                  <p>Art: ${companyInfo.articleNumber}</p>
                  <p>${companyInfo.address}</p>
                  <p>Tél: ${companyInfo.phoneNumber}</p>
                  ${companyInfo.website ? `<p>Site: ${companyInfo.website}</p>` : ''}
                </div>
              </div>
              
              <div style="flex: 1; margin-left: 20px;">
                <div style="font-weight: bold; margin-bottom: 10px; font-size: 16px;">Client:</div>
                <div style="font-size: 14px; line-height: 1.4;">
                  <p><strong>${customerInfo.name}</strong></p>
                  <p>NIF: ${customerInfo.fiscalId || 'Non spécifié'}</p>
                  ${customerInfo.rc ? `<p>RC: ${customerInfo.rc}</p>` : ''}
                  ${customerInfo.articleNumber ? `<p>Art: ${customerInfo.articleNumber}</p>` : ''}
                  ${customerInfo.address ? `<p>Adresse: ${customerInfo.address}</p>` : ''}
                  ${customerInfo.phone ? `<p>Tél: ${customerInfo.phone}</p>` : ''}
                  ${customerInfo.email ? `<p>Email: ${customerInfo.email}</p>` : ''}
                </div>
              </div>
            </div>

            <!-- Items Table -->
            <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">
              <thead>
                <tr>
                  <th style="border: 1px solid #ddd; padding: 8px; text-align: left; background-color: #f8f9fa; font-weight: bold; width: 5%;">N°</th>
                  <th style="border: 1px solid #ddd; padding: 8px; text-align: left; background-color: #f8f9fa; font-weight: bold; width: 45%;">Désignation</th>
                  <th style="border: 1px solid #ddd; padding: 8px; text-align: left; background-color: #f8f9fa; font-weight: bold; width: 10%;">Qté</th>
                  <th style="border: 1px solid #ddd; padding: 8px; text-align: left; background-color: #f8f9fa; font-weight: bold; width: 15%;">Prix Unit.</th>
                  <th style="border: 1px solid #ddd; padding: 8px; text-align: left; background-color: #f8f9fa; font-weight: bold; width: 15%;">Montant HT</th>
                  ${sale.useTax ? `<th style="border: 1px solid #ddd; padding: 8px; text-align: left; background-color: #f8f9fa; font-weight: bold; width: 10%;">TVA</th>` : ''}
                </tr>
              </thead>
              <tbody>
                ${(sale.itemsSnapshot || []).map((item, index) => `
                  <tr>
                    <td style="border: 1px solid #ddd; padding: 8px;">${index + 1}</td>
                    <td style="border: 1px solid #ddd; padding: 8px;">${item.name || 'Produit inconnu'}</td>
                    <td style="border: 1px solid #ddd; padding: 8px;">${item.quantity}</td>
                    <td style="border: 1px solid #ddd; padding: 8px;">${formatCurrency(item.unitPrice)}</td>
                    <td style="border: 1px solid #ddd; padding: 8px;">${formatCurrency(item.lineTotal || (item.quantity * item.unitPrice))}</td>
                    ${sale.useTax ? `<td style="border: 1px solid #ddd; padding: 8px;">${((sale.taxRate || 0) * 100).toFixed(0)}%</td>` : ''}
                  </tr>
                `).join('')}
              </tbody>
            </table>

            <!-- Totals -->
            <div style="margin-left: auto; width: 300px;">
              <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                <span style="font-weight: bold;">Total HT:</span>
                <span style="font-weight: bold;">${formatCurrency(sale.subtotal || 0)}</span>
              </div>
              ${sale.useTax ? `
                <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                  <span style="font-weight: bold;">TVA (${((sale.taxRate || 0) * 100).toFixed(0)}%):</span>
                  <span style="font-weight: bold;">${formatCurrency(sale.taxAmount || 0)}</span>
                </div>
              ` : ''}
              <div style="display: flex; justify-content: space-between; margin-bottom: 5px; border-top: 2px solid #333; padding-top: 5px; margin-top: 10px;">
                <span style="font-weight: bold;">Total TTC:</span>
                <span style="font-weight: bold;">${formatCurrency(sale.totalAmount || 0)}</span>
              </div>
              ${(sale.amountPaid || 0) > 0 ? `
                <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                  <span style="font-weight: bold;">Montant Payé:</span>
                  <span style="font-weight: bold;">${formatCurrency(sale.amountPaid || 0)}</span>
                </div>
              ` : ''}
              ${(sale.balanceDue || 0) > 0 ? `
                <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                  <span style="font-weight: bold;">Reste à Payer:</span>
                  <span style="font-weight: bold; color: #dc2626;">${formatCurrency(sale.balanceDue || 0)}</span>
                </div>
              ` : ''}
            </div>
          </div>
        </div>
      `;

      // Add invoice content to body
      document.body.insertAdjacentHTML('beforeend', invoiceContent);

      // Show print content
      const printContent = document.getElementById('invoice-print-content');
      if (printContent) {
        printContent.style.display = 'block';
      }

      // Print
      window.print();

      // Clean up after print
      const cleanup = () => {
        // Remove print styles
        const printStylesElement = document.getElementById('invoice-print-styles');
        if (printStylesElement) {
          printStylesElement.remove();
        }

        // Remove print content
        if (printContent) {
          printContent.remove();
        }

        // Show modal again
        if (modal) {
          modal.style.display = 'flex';
        }
      };

      // Clean up after print dialog closes
      setTimeout(cleanup, 1000);

      // Also listen for afterprint event
      const afterPrintHandler = () => {
        cleanup();
        window.removeEventListener('afterprint', afterPrintHandler);
      };
      window.addEventListener('afterprint', afterPrintHandler);

    } catch (error) {
      // console.error('Print error:', error);
      alert('There was an error printing the invoice. Please try again.');
    }
  };

  if (!isOpen) return null;

  // Validate required data
  if (!sale || !companyInfo || !customerInfo) {
    return null;
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">Imprimer la Facture</h2>
          <div className="flex gap-2">
            <button
              onClick={handlePrint}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <FiPrinter className="mr-2 h-4 w-4" />
              Imprimer
            </button>
            
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 rounded-full p-1"
            >
              <FiX className="h-6 w-6" />
            </button>
          </div>
        </div>
        
        <div className="p-6">
          <div className="bg-gray-50 rounded-lg p-6 mb-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Aperçu de la Facture</h3>
            <div className="space-y-4">
              <div className="flex justify-between">
                <span className="font-medium">Numéro de facture:</span>
                <span>{sale.invoiceNumber}</span>
              </div>
              <div className="flex justify-between">
                <span className="font-medium">Client:</span>
                <span>{customerInfo.name}</span>
              </div>
              <div className="flex justify-between">
                <span className="font-medium">Montant total:</span>
                <span className="font-semibold">{formatCurrency(sale.totalAmount)}</span>
              </div>
              <div className="flex justify-between">
                <span className="font-medium">Statut:</span>
                <span className={`px-2 py-1 text-xs font-semibold rounded-full ${
                  sale.status === 'paid' ? 'bg-green-100 text-green-800' :
                  sale.status === 'partially_paid' ? 'bg-yellow-100 text-yellow-800' :
                  sale.status === 'unpaid' ? 'bg-red-100 text-red-800' :
                  'bg-gray-100 text-gray-800'
                }`}>
                  {sale.status.replace('_', ' ')}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="font-medium">Articles:</span>
                <span>{sale.itemsSnapshot.length} produit(s)</span>
              </div>
            </div>
          </div>
          
          <div className="text-sm text-gray-600">
            <p className="mb-2">• La facture sera imprimée avec le format standard algérien</p>
            <p className="mb-2">• Inclut les informations complètes de l&apos;entreprise et du client</p>
            <p className="mb-2">• {sale.useTax ? `TVA appliquée (${(sale.taxRate * 100).toFixed(0)}%)` : 'Aucune TVA appliquée'}</p>
            <p>• Cliquez sur &quot;Imprimer&quot; pour générer la facture</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default InvoicePrint; 