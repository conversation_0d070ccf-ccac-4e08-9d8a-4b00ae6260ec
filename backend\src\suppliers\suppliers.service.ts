import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";
import { Supplier } from "./supplier.entity";
import { Warehouse } from "../warehouses/warehouse.entity";
import { Uuid7 } from "../utils/uuid7";

@Injectable()
export class SuppliersService {
  constructor(
    @InjectRepository(Supplier) private supplierRepository: Repository<Supplier>,
    @InjectRepository(Warehouse) private warehouseRepository: Repository<Warehouse>,
  ) {}

  async create(supplierData: any): Promise<Supplier> {
    const { 
      warehouseUuid, 
      userUuid, 
      name, 
      email, 
      phone, 
      address, 
      code, 
      description,
      fiscalId,
      rc,
      articleNumber,
      latitude,
      longitude,
      notes
    } = supplierData;

    if (!userUuid || !name) {
      throw new BadRequestException("userUuid and name are required");
    }

    // Check if warehouse exists
    const warehouse = await this.warehouseRepository.findOne({
      where: { id: warehouseUuid },
    });
    if (!warehouse) {
      throw new NotFoundException("Warehouse not found");
    }

    const supplier = new Supplier();
    supplier.id = Supplier.generateId();
    supplier.userUuid = userUuid;
    supplier.warehouseUuid = warehouseUuid;
    supplier.name = name;
    supplier.email = email;
    supplier.phone = phone;
    supplier.address = address;
    supplier.code = code;
    supplier.description = description;
    supplier.fiscalId = fiscalId;
    supplier.rc = rc;
    supplier.articleNumber = articleNumber;
    supplier.latitude = latitude;
    supplier.longitude = longitude;
    supplier.notes = notes;
    supplier.isDeleted = false;

    return await this.supplierRepository.save(supplier);
  }

  async findAll(warehouseUuid?: string, userUuid?: string): Promise<Supplier[]> {
    const queryBuilder = this.supplierRepository
      .createQueryBuilder('supplier')
      .where('supplier.isDeleted = :isDeleted', { isDeleted: false });

    if (warehouseUuid) {
      queryBuilder.andWhere('supplier.warehouseUuid = :warehouseUuid', { warehouseUuid });
    }

    if (userUuid) {
      queryBuilder.andWhere('supplier.userUuid = :userUuid', { userUuid });
    }

    return await queryBuilder.getMany();
  }

  async findOne(uuid: string, userUuid?: string): Promise<Supplier> {
    const whereClause: any = { id: uuid, isDeleted: false };
    if (userUuid) {
      whereClause.userUuid = userUuid;
    }

    const supplier = await this.supplierRepository.findOne({
      where: whereClause,
    });
    if (!supplier) {
      throw new NotFoundException("Supplier not found");
    }
    return supplier;
  }

  async update(uuid: string, updateData: any, userUuid?: string): Promise<Supplier> {
    const whereClause: any = { id: uuid, isDeleted: false };
    if (userUuid) {
      whereClause.userUuid = userUuid;
    }

    const supplier = await this.supplierRepository.findOne({
      where: whereClause,
    });
    if (!supplier) {
      throw new NotFoundException("Supplier not found");
    }

    // Update fields
    Object.assign(supplier, updateData);
    return await this.supplierRepository.save(supplier);
  }

  async remove(uuid: string, userUuid?: string): Promise<void> {
    const whereClause: any = { id: uuid, isDeleted: false };
    if (userUuid) {
      whereClause.userUuid = userUuid;
    }

    const result = await this.supplierRepository.update(
      whereClause,
      { isDeleted: true }
    );
    if (result.affected === 0) {
      throw new NotFoundException("Supplier not found");
    }
  }

  async filterSuppliers(filters: { 
    warehouseUuid?: string; 
    userUuid?: string;
    name?: string; 
    email?: string;
    phone?: string;
    code?: string;
    fiscalId?: string;
  }): Promise<Supplier[]> {
    const queryBuilder = this.supplierRepository
      .createQueryBuilder('supplier')
      .where('supplier.isDeleted = :isDeleted', { isDeleted: false });

    if (filters.warehouseUuid) {
      queryBuilder.andWhere('supplier.warehouseUuid = :warehouseUuid', { warehouseUuid: filters.warehouseUuid });
    }

    if (filters.userUuid) {
      queryBuilder.andWhere('supplier.userUuid = :userUuid', { userUuid: filters.userUuid });
    }

    if (filters.name) {
      queryBuilder.andWhere('supplier.name ILIKE :name', { name: `%${filters.name}%` });
    }

    if (filters.email) {
      queryBuilder.andWhere('supplier.email ILIKE :email', { email: `%${filters.email}%` });
    }

    if (filters.phone) {
      queryBuilder.andWhere('supplier.phone ILIKE :phone', { phone: `%${filters.phone}%` });
    }

    if (filters.code) {
      queryBuilder.andWhere('supplier.code ILIKE :code', { code: `%${filters.code}%` });
    }

    if (filters.fiscalId) {
      queryBuilder.andWhere('supplier.fiscalId ILIKE :fiscalId', { fiscalId: `%${filters.fiscalId}%` });
    }

    return await queryBuilder.getMany();
  }
} 