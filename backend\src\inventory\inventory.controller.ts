import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Query,
  Patch,
  Delete,
  UseGuards,
  ParseUUIDPipe,
} from "@nestjs/common";
import { InventoryItemFilterDto } from "./dto/inventory-item-filter.dto";
import { ListStorageDto } from "./dto/list-storage.dto";
import { InventoryService } from "./inventory.service.typeorm";
import { StockAdjustmentService } from "./stock-adjustment.service";
import { Storage } from "./storage.entity";
import { InventoryItem } from "./inventory-item.entity";
import { CreateStorageDto } from "./dto/create-storage.dto";
import { CreateInventoryItemDto } from "./dto/create-inventory-item.dto";
import { StorageDto } from "./dto/storage.dto";
import { UpdateStorageNameDto } from "./dto/update-storage-name.dto";
import { InventoryItemDto } from "./dto/inventory-item.dto";
import { CreateStockAdjustmentDto } from "./dto/create-stock-adjustment.dto";

import { StockAdjustmentResponseDto } from "./dto/stock-adjustment-response.dto";
import { ApiOperation, ApiBody, ApiOkResponse, ApiTags, ApiQuery } from "@nestjs/swagger";
import { StockLevelDto } from "./dto/stock-level.dto";
@ApiTags("inventory")
@Controller("inventory")
export class InventoryController {
  constructor(
    private readonly inventoryService: InventoryService,
    private readonly stockAdjustmentService: StockAdjustmentService,
  ) {}

  // Storage endpoints
  @Post("storage")
  @ApiOperation({ summary: "Create a new storage location" })
  @ApiBody({ type: CreateStorageDto })
  @ApiOkResponse({
    description: "Returns the created storage location.",
    type: StorageDto,
  })
  async createStorage(@Body() dto: CreateStorageDto) {
    return this.inventoryService.createStorage(dto);
  }

  @Get("storage")
  @ApiOperation({
    summary:
      "List all storage locations for a warehouse (warehouseUuid required)",
  })
  @ApiOkResponse({
    description: "Returns the list of storage locations.",
    type: [StorageDto],
  })
  async listStorages(@Query() query: ListStorageDto) {
    return this.inventoryService.listStorages(query.warehouseUuid);
  }

  @Get("storage/list-raw")
  @ApiOperation({
    summary:
      "List all storage locations (no warehouseUuid filter, admin/debug only)",
  })
  @ApiOkResponse({
    description: "Returns all storage locations, ignoring warehouseUuid.",
    type: [StorageDto],
  })
  async listAllStorages() {
    return this.inventoryService.listAllStorages();
  }

  @Delete("storage/all")
  @ApiOperation({
    summary: "Soft-delete ALL storage locations (admin/debug only, dangerous)",
  })
  @ApiOkResponse({
    description: "Returns the number of storages soft-deleted.",
  })
  async deleteAllStorages() {
    return this.inventoryService.deleteAllStorages();
  }

  @Get("storage/:uuid")
  @ApiOperation({ summary: "Get a storage location by UUID" })
  @ApiOkResponse({
    description: "Returns the storage location with the specified UUID.",
    type: StorageDto,
  })
  async getStorageByUuid(@Param("uuid", ParseUUIDPipe) uuid: string) {
    return this.inventoryService.getStorageByUuid(uuid);
  }

  @Post("storage/:uuid/soft-delete")
  @ApiOperation({
    summary: "Soft delete a storage location (sets isDeleted=true)",
  })
  @ApiOkResponse({
    description: "Returns the soft-deleted storage location.",
    type: StorageDto,
  })
  async softDeleteStorage(@Param("uuid", ParseUUIDPipe) uuid: string) {
    return this.inventoryService.softDeleteStorage(uuid);
  }

  @Patch("storage/:uuid/name")
  @ApiOperation({ summary: "Update the name of a storage location" })
  @ApiBody({ type: UpdateStorageNameDto })
  @ApiOkResponse({
    description: "Returns the updated storage location.",
    type: StorageDto,
  })
  async updateStorageName(
    @Param("uuid", ParseUUIDPipe) uuid: string,
    @Body() dto: UpdateStorageNameDto,
  ) {
    return this.inventoryService.updateStorageName(uuid, dto);
  }

  // InventoryItem endpoints
  @Post("item")
  @ApiOperation({ summary: "Create a new inventory item" })
  @ApiBody({ type: CreateInventoryItemDto })
  @ApiOkResponse({
    description: "Returns the created inventory item.",
    type: InventoryItemDto,
  })
  async createInventoryItem(
    @Body() dto: CreateInventoryItemDto,
  ): Promise<InventoryItemDto> {
    return this.inventoryService.createInventoryItem(dto);
  }

  @Get("item")
  @ApiOperation({ summary: "List inventory items" })
  @ApiOkResponse({
    description: "Returns the list of inventory items.",
    type: [InventoryItemDto],
  })
  async listInventoryItems(
    @Query() filter: InventoryItemFilterDto,
  ): Promise<InventoryItemDto[]> {
    return this.inventoryService.listInventoryItems(filter);
  }

  // Stock Adjustment endpoint
  @Post("stock-adjustment")
  @ApiOperation({
    summary: "Create a stock adjustment and atomically update inventory",
  })
  @ApiBody({ type: CreateStockAdjustmentDto })
  @ApiOkResponse({
    description: "Returns the created stock adjustment.",
    type: StockAdjustmentResponseDto,
  })
  async createStockAdjustment(@Body() dto: CreateStockAdjustmentDto) {
    return this.stockAdjustmentService.createStockAdjustment(dto);
  }

  @Get("stock-adjustment")
  @ApiOperation({ summary: "List all stock adjustments" })
  @ApiOkResponse({
    description: "Returns all stock adjustments.",
    type: [StockAdjustmentResponseDto],
  })
  async listStockAdjustments(): Promise<StockAdjustmentResponseDto[]> {
    return this.stockAdjustmentService.listStockAdjustments();
  }

  @Get("stock-adjustment/by-storage/:storageUuid")
  @ApiOperation({ summary: "List stock adjustments by storageUuid with pagination" })
  @ApiQuery({
    name: "page",
    required: false,
    type: Number,
    description: "Page number (default: 1)",
  })
  @ApiQuery({
    name: "limit",
    required: false,
    type: Number,
    description: "Items per page (default: 10)",
  })
  @ApiOkResponse({
    description: "Returns paginated stock adjustments for the given storageUuid.",
    schema: {
      type: 'object',
      properties: {
        data: {
          type: 'array',
          items: { $ref: '#/components/schemas/StockAdjustmentResponseDto' }
        },
        total: { type: 'number' },
        page: { type: 'number' },
        limit: { type: 'number' },
        totalPages: { type: 'number' }
      }
    }
  })
  async listStockAdjustmentsByStorage(
    @Param("storageUuid", ParseUUIDPipe) storageUuid: string,
    @Query("page") page?: number,
    @Query("limit") limit?: number,
  ) {
    const pageNum = page || 1;
    const limitNum = limit || 10;
    
    return this.stockAdjustmentService.listStockAdjustmentsByStorage(
      storageUuid, 
      pageNum, 
      limitNum
    );
  }

  // Get stock levels for a list of products in a storage
  @Get("stock/levels")
  @ApiOperation({
    summary:
      "Get current stock levels for a list of products in a storage location",
  })
  @ApiOkResponse({
    description:
      "Returns stock levels for the given products in the specified storage.",
    type: [StockLevelDto],
  })
  async getStockLevels(
    @Query("storageUuid", ParseUUIDPipe) storageUuid: string,
    @Query("productUuids") productUuids: string,
  ): Promise<StockLevelDto[]> {
    const uuids = productUuids ? productUuids.split(",") : [];
    return this.stockAdjustmentService.getStockLevels(storageUuid, uuids);
  }

  // NEW: Get stock levels for all products in a storage (more efficient)
  @Get("stock/levels/:storageUuid")
  @ApiOperation({
    summary: "Get current stock levels for all products in a storage location",
  })
  @ApiOkResponse({
    description:
      "Returns stock levels for all products in the specified storage.",
    type: [StockLevelDto],
  })
  async getStockLevelsForStorage(
    @Param("storageUuid", ParseUUIDPipe) storageUuid: string,
  ): Promise<StockLevelDto[]> {
    return this.stockAdjustmentService.getStockLevelsForStorage(storageUuid);
  }
}
