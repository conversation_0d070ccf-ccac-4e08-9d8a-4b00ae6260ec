"use client";

import React, { useState, useEffect } from "react";
import { useAuth } from "@/contexts/AuthContext";
import ProtectedRoute from "@/components/ProtectedRoute";
import RouteMap from "./components/RouteMap";
import ComputeRouteModal from "./components/ComputeRouteModal";
import RouteList from "./components/RouteList";
import { Customer } from "../../sales/customers/customersApi";
import { Route, computeRoute, getRoutes } from "./api";
import { RegionDto, listRegions } from "../regions/api";
import { getAuthHeadersWithContentType } from "@/utils/authHeaders";
import { 
  Route as RouteIcon, 
  MapPin, 
  Filter, 
  Plus, 
  RefreshCw, 
  Loader2,
  AlertCircle,
  CheckCircle,
  X
} from "lucide-react";
import toast from "react-hot-toast";

export default function RoutesPage() {
  const { user } = useAuth();
  const warehouseUuid = user?.warehouseUuid;
  const userUuid = user?.uuid;

  // State management
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [regions, setRegions] = useState<RegionDto[]>([]);
  const [selectedCustomers, setSelectedCustomers] = useState<Customer[]>([]);
  const [currentRoute, setCurrentRoute] = useState<Route | null>(null);
  const [routes, setRoutes] = useState<Route[]>([]);
  const [selectedRegion, setSelectedRegion] = useState<string>("");
  const [isLoading, setIsLoading] = useState(true);
  const [isComputing, setIsComputing] = useState(false);
  const [showComputeModal, setShowComputeModal] = useState(false);
  const [error, setError] = useState("");

  // Load initial data
  useEffect(() => {
    if (warehouseUuid) {
      loadInitialData();
    }
  }, [warehouseUuid]);

  const loadInitialData = async () => {
    if (!warehouseUuid) return;

    setIsLoading(true);
    setError("");

    try {
      // Load regions
      const regionsResponse = await listRegions({}, { warehouseUuid });
      setRegions(regionsResponse.data);

      // Load routes
      const routesResponse = await getRoutes({}, { warehouseUuid });
      setRoutes(routesResponse.data);

      // Load customers (all customers for this warehouse)
      await loadCustomers();
    } catch (err) {
      console.error("Failed to load initial data:", err);
      setError("Failed to load data. Please try again.");
      toast.error("Failed to load data");
    } finally {
      setIsLoading(false);
    }
  };

  const loadCustomers = async (regionUuid?: string) => {
    if (!warehouseUuid) return;

    try {
      const response = await fetch(`/api/customers?warehouseUuid=${warehouseUuid}${regionUuid ? `&regionUuid=${regionUuid}` : ""}`, {
        headers: getAuthHeadersWithContentType(),
      });
      if (!response.ok) throw new Error("Failed to load customers");
      
      const data = await response.json();
      const customersWithCoordinates = data.data.filter((customer: Customer) => 
        customer.latitude && customer.longitude && !customer.isDeleted
      );
      setCustomers(customersWithCoordinates);
    } catch (err) {
      console.error("Failed to load customers:", err);
      toast.error("Failed to load customers");
    }
  };

  const handleRegionChange = async (regionUuid: string) => {
    setSelectedRegion(regionUuid);
    setSelectedCustomers([]); // Clear selected customers when region changes
    setCurrentRoute(null); // Clear current route
    
    if (regionUuid) {
      await loadCustomers(regionUuid);
    } else {
      await loadCustomers(); // Load all customers
    }
  };

  const handleCustomerSelect = (customer: Customer) => {
    setSelectedCustomers(prev => {
      const isSelected = prev.some(c => c.uuid === customer.uuid);
      if (isSelected) {
        return prev.filter(c => c.uuid !== customer.uuid);
      } else {
        return [...prev, customer];
      }
    });
  };

  const handleCustomerDeselect = (customer: Customer) => {
    setSelectedCustomers(prev => prev.filter(c => c.uuid !== customer.uuid));
  };

  const handleComputeRoute = async (data: any) => {
    if (!warehouseUuid) return;

    setIsComputing(true);
    try {
      const newRoute = await computeRoute({
        ...data,
        warehouseUuid
      });
      
      setCurrentRoute(newRoute);
      setRoutes(prev => [newRoute, ...prev]);
      setSelectedCustomers([]); // Clear selection after route creation
      
      toast.success("Route computed successfully!");
    } catch (err) {
      console.error("Failed to compute route:", err);
      toast.error("Failed to compute route");
      throw err;
    } finally {
      setIsComputing(false);
    }
  };

  const handleRouteEdit = (route: Route) => {
    setCurrentRoute(route);
    // TODO: Implement route editing functionality
    toast.info("Route editing functionality coming soon");
  };

  const handleRouteSave = async (route: Route) => {
    // TODO: Implement route saving functionality
    toast.info("Route saving functionality coming soon");
  };

  const handleRouteCancel = () => {
    // TODO: Implement route cancel functionality
    toast.info("Route cancel functionality coming soon");
  };

  const handleRouteSelect = (route: Route) => {
    setCurrentRoute(route);
  };

  const handleRouteDelete = async (route: Route) => {
    if (confirm(`Are you sure you want to delete the route "${route.name}"?`)) {
      try {
        // TODO: Implement route deletion
        toast.info("Route deletion functionality coming soon");
      } catch (err) {
        toast.error("Failed to delete route");
      }
    }
  };

  const handleRefresh = () => {
    loadInitialData();
  };

  if (!warehouseUuid) {
    return (
      <ProtectedRoute>
        <div className="p-6">
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div className="flex items-center space-x-2">
              <AlertCircle className="w-5 h-5 text-yellow-600" />
              <span className="text-yellow-700">No warehouse assigned to user</span>
            </div>
          </div>
        </div>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute>
      <div className="p-6 h-screen flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
              <RouteIcon className="w-5 h-5 text-blue-600" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Route Management</h1>
              <p className="text-gray-600">Plan and optimize delivery routes</p>
            </div>
          </div>
          
          <div className="flex items-center space-x-3">
            <button
              onClick={handleRefresh}
              disabled={isLoading}
              className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors disabled:opacity-50 flex items-center space-x-2"
            >
              {isLoading ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : (
                <RefreshCw className="w-4 h-4" />
              )}
              <span>Refresh</span>
            </button>
            
            <button
              onClick={() => {
                console.log('Compute Route button clicked');
                console.log('Selected customers:', selectedCustomers.length);
                setShowComputeModal(true);
              }}
              disabled={selectedCustomers.length < 2 || isComputing}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              <Plus className="w-4 h-4" />
              <span>Compute Route ({selectedCustomers.length})</span>
            </button>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <Filter className="w-4 h-4 text-gray-500" />
              <span className="text-sm font-medium text-gray-700">Filters:</span>
            </div>
            
            <div className="flex items-center space-x-2">
              <label htmlFor="regionFilter" className="text-sm text-gray-600">
                Region:
              </label>
              <select
                id="regionFilter"
                value={selectedRegion}
                onChange={(e) => handleRegionChange(e.target.value)}
                className="px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">All Regions</option>
                {regions.map((region) => (
                  <option key={region.uuid} value={region.uuid}>
                    {region.name}
                  </option>
                ))}
              </select>
            </div>
            
            <div className="flex items-center space-x-2">
              <MapPin className="w-4 h-4 text-gray-500" />
              <span className="text-sm text-gray-600">
                {customers.length} customers available
              </span>
            </div>
            
            {selectedCustomers.length > 0 && (
              <div className="flex items-center space-x-2">
                <CheckCircle className="w-4 h-4 text-green-500" />
                <span className="text-sm text-green-600">
                  {selectedCustomers.length} selected
                </span>
                <button
                  onClick={() => setSelectedCustomers([])}
                  className="p-1 rounded hover:bg-gray-100"
                  title="Clear selection"
                >
                  <X className="w-3 h-3 text-gray-500" />
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Error Display */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <AlertCircle className="w-5 h-5 text-red-600" />
                <span className="text-red-700">{error}</span>
              </div>
              <button
                onClick={() => setError("")}
                className="p-1 rounded hover:bg-red-100"
              >
                <X className="w-4 h-4 text-red-600" />
              </button>
            </div>
          </div>
        )}

        {/* Main Content Area */}
        <div className="flex-1 flex space-x-6">
          {/* Map Container */}
          <div className="flex-1 bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            {isLoading ? (
              <div className="h-full flex items-center justify-center">
                <div className="flex items-center space-x-3">
                  <Loader2 className="w-6 h-6 animate-spin text-blue-600" />
                  <span className="text-gray-600">Loading map...</span>
                </div>
              </div>
            ) : (
              <RouteMap
                customers={customers}
                selectedCustomers={selectedCustomers}
                route={currentRoute || undefined}
                onCustomerSelect={handleCustomerSelect}
                onCustomerDeselect={handleCustomerDeselect}
                onRouteEdit={handleRouteEdit}
                onRouteSave={handleRouteSave}
                onRouteCancel={handleRouteCancel}
                isEditing={false}
              />
            )}
          </div>

          {/* Route List Sidebar */}
          <div className="w-80 flex-shrink-0">
            <RouteList
              routes={routes}
              currentRoute={currentRoute}
              onRouteSelect={handleRouteSelect}
              onRouteDelete={handleRouteDelete}
            />
          </div>
        </div>

        {/* Compute Route Modal */}
        <ComputeRouteModal
          isOpen={showComputeModal}
          onClose={() => setShowComputeModal(false)}
          selectedCustomers={selectedCustomers}
          onComputeRoute={handleComputeRoute}
          warehouseUuid={warehouseUuid}
        />
      </div>
      

    </ProtectedRoute>
  );
}
