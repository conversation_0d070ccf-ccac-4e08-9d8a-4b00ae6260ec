import React from 'react';

/**
 * A presentational error toast component for displaying error messages with a distinct style.
 * Use this in a toast system by rendering <ErrorToast message={...} />
 */
export default function ErrorToast({ message }: { message: string }) {
  return (
    <div className="custom-error-toast">
      <span role="img" aria-label="Error" style={{ marginRight: 8 }}>❌</span>
      {message}
    </div>
  );
}
