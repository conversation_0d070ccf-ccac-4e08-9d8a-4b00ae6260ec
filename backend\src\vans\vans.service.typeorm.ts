import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository, DataSource } from "typeorm";
import { Van } from "./van.entity";
import { Storage } from "../inventory/storage.entity";
import { Warehouse } from "../warehouses/warehouse.entity";
import { StorageType } from "../inventory/storage_type.enum";
import { CreateVanDto } from "./dto/create-van.dto";
import { UpdateVanDto } from "./dto/update-van.dto";
import { FilterVanDto } from "./dto/filter-van.dto";
import { VanDto } from "./dto/van.dto";

@Injectable()
export class VansService {
  constructor(
    @InjectRepository(Van)
    private vanRepository: Repository<Van>,
    @InjectRepository(Storage)
    private storageRepository: Repository<Storage>,
    @InjectRepository(Warehouse)
    private warehouseRepository: Repository<Warehouse>,
    private dataSource: DataSource,
  ) {}

  // Map Van entity to VanDto
  private toVanDto(van: Van): VanDto {
    return {
      uuid: van.id,
      name: van.name,
      warehouseUuid: van.warehouseUuid,
      storageUuid: van.storageUuid,
      licensePlate: van.licensePlate,
      model: van.model,
      year: van.year,
      createdAt: van.createdAt,
      updatedAt: van.updatedAt,
      isDeleted: van.isDeleted,
    };
  }

  // Create Van with automatic truck storage creation
  async createVan(dto: CreateVanDto): Promise<VanDto> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Verify warehouse exists
      const warehouse = await this.warehouseRepository.findOne({
        where: {
          id: dto.warehouseUuid,
          isDeleted: false,
        },
      });

      if (!warehouse) {
        throw new NotFoundException("Warehouse not found");
      }

      // Create storage first (truck type)
      const storage = new Storage();
      storage.id = Storage.generateId();
      storage.name = `${dto.name} Storage`;
      storage.type = StorageType.TRUCK;
      storage.warehouseUuid = dto.warehouseUuid;
      storage.userUuid = dto.warehouseUuid; // Using warehouseUuid as userUuid for now
      storage.isDeleted = false;

      const savedStorage = await queryRunner.manager.save(storage);

      // Create van with reference to the storage
      const van = new Van();
      van.id = Van.generateId();
      van.name = dto.name;
      van.warehouseUuid = dto.warehouseUuid;
      van.storageUuid = savedStorage.id;
      van.licensePlate = dto.licensePlate;
      van.model = dto.model;
      van.year = dto.year;
      van.isDeleted = false;

      const savedVan = await queryRunner.manager.save(van);

      await queryRunner.commitTransaction();
      return this.toVanDto(savedVan);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  // List vans with filtering
  async listVans(filter: FilterVanDto): Promise<VanDto[]> {
    const queryBuilder = this.vanRepository
      .createQueryBuilder("van")
      .where("van.isDeleted = :isDeleted", { isDeleted: false });

    // Apply filters
    if (filter.warehouseUuid) {
      queryBuilder.andWhere("van.warehouseUuid = :warehouseUuid", {
        warehouseUuid: filter.warehouseUuid,
      });
    }

    if (filter.name && filter.name.trim() !== "") {
      queryBuilder.andWhere("van.name ILIKE :name", {
        name: `%${filter.name}%`,
      });
    }

    if (filter.licensePlate && filter.licensePlate.trim() !== "") {
      queryBuilder.andWhere("van.licensePlate ILIKE :licensePlate", {
        licensePlate: `%${filter.licensePlate}%`,
      });
    }

    if (filter.model && filter.model.trim() !== "") {
      queryBuilder.andWhere("van.model ILIKE :model", {
        model: `%${filter.model}%`,
      });
    }

    const vans = await queryBuilder.getMany();
    return vans.map((van) => this.toVanDto(van));
  }

  // Raw list (no filtering, for admin/debug)
  async listAllVans(): Promise<VanDto[]> {
    const vans = await this.vanRepository.find({
      where: { isDeleted: false },
    });
    return vans.map((van) => this.toVanDto(van));
  }

  // Get van by UUID
  async getVanByUuid(uuid: string): Promise<VanDto> {
    const van = await this.vanRepository.findOne({
      where: {
        id: uuid,
        isDeleted: false,
      },
    });

    if (!van) {
      throw new NotFoundException("Van not found");
    }

    return this.toVanDto(van);
  }

  // Update van (single endpoint)
  async updateVan(uuid: string, dto: UpdateVanDto): Promise<VanDto> {
    const updateData: any = {};

    if (dto.name !== undefined) updateData.name = dto.name;
    if (dto.licensePlate !== undefined)
      updateData.licensePlate = dto.licensePlate;
    if (dto.model !== undefined) updateData.model = dto.model;
    if (dto.year !== undefined) updateData.year = dto.year;

    const result = await this.vanRepository.update(
      { id: uuid, isDeleted: false },
      updateData,
    );

    if (result.affected === 0) {
      throw new NotFoundException("Van not found");
    }

    const van = await this.vanRepository.findOne({
      where: { id: uuid },
    });

    return this.toVanDto(van);
  }

  // Soft delete van
  async softDeleteVan(uuid: string): Promise<VanDto> {
    const result = await this.vanRepository.update(
      { id: uuid, isDeleted: false },
      { isDeleted: true },
    );

    if (result.affected === 0) {
      throw new NotFoundException("Van not found or already deleted");
    }

    const van = await this.vanRepository.findOne({
      where: { id: uuid },
    });

    return this.toVanDto(van);
  }

  // Delete all vans (soft delete, for admin/debug)
  async deleteAllVans(): Promise<{ deletedCount: number }> {
    const result = await this.vanRepository.update(
      { isDeleted: false },
      { isDeleted: true },
    );
    return { deletedCount: result.affected || 0 };
  }
} 