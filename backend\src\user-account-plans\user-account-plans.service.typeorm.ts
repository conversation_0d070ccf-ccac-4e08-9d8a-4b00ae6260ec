import { Injectable, NotFoundException } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";
import { AccountPlan } from "./account-plan.entity";
import { Feature } from "./feature.entity";
import { AccountPlanIdentifier } from "./enums/account-plan.enum";
import { FeatureIdentifier } from "./enums/feature.enum";
import { AccountPlanDto } from "./dto/account-plan.dto";
import { FeatureDto } from "./dto/feature.dto";

@Injectable()
export class UserAccountPlansService {
  constructor(
    @InjectRepository(AccountPlan)
    private accountPlanRepository: Repository<AccountPlan>,
    @InjectRepository(Feature)
    private featureRepository: Repository<Feature>,
  ) {}

  /**
   * Get all account plans
   */
  async getAllAccountPlans(): Promise<AccountPlanDto[]> {
    const plans = await this.accountPlanRepository.find({
      where: { isDeleted: false },
      order: { name: 'ASC' },
    });
    return plans.map((plan) => this.toAccountPlanDto(plan));
  }

  /**
   * Get account plan by identifier
   */
  async getAccountPlanByIdentifier(
    identifier: AccountPlanIdentifier,
  ): Promise<AccountPlanDto> {
    const plan = await this.accountPlanRepository.findOne({
      where: { identifier, isDeleted: false },
    });
    if (!plan) {
      throw new NotFoundException(
        `Account plan with identifier '${identifier}' not found`,
      );
    }
    return this.toAccountPlanDto(plan);
  }

  /**
   * Get all features
   */
  async getAllFeatures(): Promise<FeatureDto[]> {
    const features = await this.featureRepository.find({
      where: { isDeleted: false },
      order: { name: 'ASC' },
    });
    return features.map((feature) => this.toFeatureDto(feature));
  }

  /**
   * Get feature by identifier
   */
  async getFeatureByIdentifier(identifier: FeatureIdentifier): Promise<FeatureDto> {
    const feature = await this.featureRepository.findOne({
      where: { identifier, isDeleted: false },
    });
    if (!feature) {
      throw new NotFoundException(
        `Feature with identifier '${identifier}' not found`,
      );
    }
    return this.toFeatureDto(feature);
  }

  /**
   * Check if an account plan includes a specific feature
   */
  async hasFeature(
    planIdentifier: AccountPlanIdentifier,
    featureIdentifier: FeatureIdentifier,
  ): Promise<boolean> {
    const plan = await this.accountPlanRepository.findOne({
      where: { identifier: planIdentifier, isDeleted: false },
    });
    if (!plan) {
      return false;
    }
    return plan.features.includes(featureIdentifier);
  }

  /**
   * Get all features for a specific account plan
   */
  async getFeaturesForPlan(planIdentifier: AccountPlanIdentifier): Promise<FeatureDto[]> {
    const plan = await this.accountPlanRepository.findOne({
      where: { identifier: planIdentifier, isDeleted: false },
    });
    if (!plan) {
      throw new NotFoundException(
        `Account plan with identifier '${planIdentifier}' not found`,
      );
    }

    const features = await this.featureRepository.find({
      where: {
        identifier: plan.features as any,
        isDeleted: false,
      },
    });

    return features.map((feature) => this.toFeatureDto(feature));
  }

  /**
   * Create a new account plan
   */
  async createAccountPlan(data: {
    identifier: AccountPlanIdentifier;
    name: string;
    features: FeatureIdentifier[];
  }): Promise<AccountPlanDto> {
    const plan = this.accountPlanRepository.create({
      id: AccountPlan.generateId(),
      ...data,
      features: data.features,
    });

    const savedPlan = await this.accountPlanRepository.save(plan);
    return this.toAccountPlanDto(savedPlan);
  }

  /**
   * Create a new feature
   */
  async createFeature(data: {
    identifier: FeatureIdentifier;
    name: string;
  }): Promise<FeatureDto> {
    const feature = this.featureRepository.create({
      id: Feature.generateId(),
      ...data,
    });

    const savedFeature = await this.featureRepository.save(feature);
    return this.toFeatureDto(savedFeature);
  }

  /**
   * Update an account plan
   */
  async updateAccountPlan(
    identifier: AccountPlanIdentifier,
    data: Partial<{
      name: string;
      features: FeatureIdentifier[];
    }>,
  ): Promise<AccountPlanDto> {
    const plan = await this.accountPlanRepository.findOne({
      where: { identifier, isDeleted: false },
    });
    if (!plan) {
      throw new NotFoundException(
        `Account plan with identifier '${identifier}' not found`,
      );
    }

    Object.assign(plan, data);
    const updatedPlan = await this.accountPlanRepository.save(plan);
    return this.toAccountPlanDto(updatedPlan);
  }

  /**
   * Update a feature
   */
  async updateFeature(
    identifier: FeatureIdentifier,
    data: Partial<{ name: string }>,
  ): Promise<FeatureDto> {
    const feature = await this.featureRepository.findOne({
      where: { identifier, isDeleted: false },
    });
    if (!feature) {
      throw new NotFoundException(
        `Feature with identifier '${identifier}' not found`,
      );
    }

    Object.assign(feature, data);
    const updatedFeature = await this.featureRepository.save(feature);
    return this.toFeatureDto(updatedFeature);
  }

  /**
   * Soft delete an account plan
   */
  async deleteAccountPlan(identifier: AccountPlanIdentifier): Promise<void> {
    const plan = await this.accountPlanRepository.findOne({
      where: { identifier, isDeleted: false },
    });
    if (!plan) {
      throw new NotFoundException(
        `Account plan with identifier '${identifier}' not found`,
      );
    }

    plan.isDeleted = true;
    await this.accountPlanRepository.save(plan);
  }

  /**
   * Soft delete a feature
   */
  async deleteFeature(identifier: FeatureIdentifier): Promise<void> {
    const feature = await this.featureRepository.findOne({
      where: { identifier, isDeleted: false },
    });
    if (!feature) {
      throw new NotFoundException(
        `Feature with identifier '${identifier}' not found`,
      );
    }

    feature.isDeleted = true;
    await this.featureRepository.save(feature);
  }

  /**
   * Convert AccountPlan entity to AccountPlanDto
   */
  private toAccountPlanDto(plan: AccountPlan): AccountPlanDto {
    return {
      identifier: plan.identifier,
      name: plan.name,
      features: plan.features as FeatureIdentifier[],
    };
  }

  /**
   * Convert Feature entity to FeatureDto
   */
  private toFeatureDto(feature: Feature): FeatureDto {
    return {
      identifier: feature.identifier,
      name: feature.name,
    };
  }
} 