/*!*****************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[7].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[7].use[3]!./components/SideTaskBar/SideTaskBar.module.css ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************/
.SideTaskBar_taskBar__ZuXqF {
  --transition: all 0.2s ease-in-out;
  --radius: var(--radius-medium);

  display: flex;
  flex-direction: row;
  width: auto;
  height: 100vh;
  background: var(--color-surface);
  color: var(--color-textPrimary);
  flex-shrink: 0;
  box-shadow: var(--shadow-small);
}

/* Primary Navigation Column */
.SideTaskBar_primaryNavContainer__Qowr7 {
  display: flex;
  flex-direction: column;
  width: 7.5rem;
  flex-shrink: 0;
  background: var(--color-surface);
  border-right: 1px solid var(--color-border);
  overflow-y: auto;
}

/* Secondary Navigation Column (Sub-items) */
.SideTaskBar_secondaryNavContainer___jRyu {
  display: flex;
  flex-direction: column;
  width: 9.5rem;
  flex-shrink: 0;
  background: var(--color-background);
  border-right: 1px solid var(--color-border);
  overflow-y: auto;
}

.SideTaskBar_secondaryHeader__VpQ4T {
  padding: 1.75rem 1rem 1.5rem;
  background: var(--color-background);
  position: sticky;
  top: 0;
  z-index: 10;
}

.SideTaskBar_activeItemName__UiHBG {
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--color-primary);
  margin: 0 0 0.5rem;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.SideTaskBar_headerDivider__QZqDx {
  height: 1px;
  background-color: var(--color-border);
  margin: 0.25rem 0 0.5rem;
}

/* Always visible elements - Removed opacity/transform/transition from base styles */
.SideTaskBar_logoText__LjLra,
.SideTaskBar_label__CpV92,
.SideTaskBar_chevron__n2F5R,
.SideTaskBar_profileInfo__8Iu_V,
.SideTaskBar_logoutButton__Lid2R {
  opacity: 1;
  transform: translateX(0);
  transition: none; /* No transitions on visibility/position for these elements */
  transition-delay: 0s;
}

.SideTaskBar_logo__mi68V {
  display: flex;
  align-items: center;
  padding: 1rem 0.5rem;
  gap: 0.75rem;
  border-bottom: 1px solid var(--color-border);
  background: var(--color-surface);
  z-index: 10;
}

.SideTaskBar_logoIcon__Qf1TA {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  background: var(--color-primary);
  color: white;
  border-radius: var(--radius);
  font-size: 1rem;
  font-weight: 700;
  flex-shrink: 0;
}

.SideTaskBar_logoText__LjLra {
  font-size: 1.25rem;
  font-weight: 700;
  white-space: nowrap;
  color: var(--text);
}

/* Main Navigation (Primary Column) */
.SideTaskBar_primaryNav__0gyIV,
.SideTaskBar_secondaryNav__ZCNaL { /* Both navs share similar flex and scrollbar properties */
  flex: 1;
  overflow-y: auto;
  padding: 0.75rem 0.25rem;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  scrollbar-width: thin;
  scrollbar-color: var(--color-border) transparent;
}

.SideTaskBar_secondaryNav__ZCNaL {
  flex: 1;
  overflow-y: auto;
  padding: 0 0.25rem 0.5rem;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

/* Scrollbar styles for both primary and secondary navs */
.SideTaskBar_primaryNav__0gyIV::-webkit-scrollbar,
.SideTaskBar_secondaryNav__ZCNaL::-webkit-scrollbar {
  width: 4px;
}

.SideTaskBar_primaryNav__0gyIV::-webkit-scrollbar-track,
.SideTaskBar_secondaryNav__ZCNaL::-webkit-scrollbar-track {
  background: transparent;
}

.SideTaskBar_primaryNav__0gyIV::-webkit-scrollbar-thumb,
.SideTaskBar_secondaryNav__ZCNaL::-webkit-scrollbar-thumb {
  background-color: var(--color-border);
  border-radius: 20px;
}

.SideTaskBar_primaryNav__0gyIV::-webkit-scrollbar-thumb:hover,
.SideTaskBar_secondaryNav__ZCNaL::-webkit-scrollbar-thumb:hover {
  background: var(--color-primary);
}

.SideTaskBar_navItem__3TQDi {
  position: relative;
}

.SideTaskBar_navButton__OqbEj { /* Styles for primary items in the first column */
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: 0.75rem 0.5rem;
  border-radius: var(--radius);
  color: var(--color-textSecondary);
  transition: var(--transition);
  background: transparent;
  border: none;
  cursor: pointer;
  text-align: center;
  gap: 0.25rem;
  position: relative;
  font-size: 0.75rem;
  font-weight: 500;
  overflow: hidden;
  margin: 0.125rem 0;
  min-height: 4rem;
}

.SideTaskBar_navButton__OqbEj::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 3px;
  background: var(--primary);
  transform: scaleY(0);
  transform-origin: center;
  transition: var(--transition);
  border-radius: 0 4px 4px 0;
}

.SideTaskBar_navButton__OqbEj:hover {
  background: var(--color-hover);
  color: var(--color-textPrimary);
}

.SideTaskBar_navButton__OqbEj.SideTaskBar_active__osF8h {
  color: var(--color-primary);
  background: var(--color-active);
  font-weight: 600;
}

.SideTaskBar_navButton__OqbEj.SideTaskBar_active__osF8h .SideTaskBar_icon__geV4U {
  color: var(--color-primary);
}

.SideTaskBar_navButton__OqbEj.SideTaskBar_active__osF8h::before {
  transform: scaleY(1);
}

.SideTaskBar_icon__geV4U {
  font-size: 1.25rem;
  width: 1.5rem;
  height: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  color: var(--text-muted);
  margin-bottom: 0.25rem;
}

.SideTaskBar_label__CpV92 {
  white-space: nowrap;
  text-align: center;
  line-height: 1.2;
  font-size: 0.7rem;
  padding: 0 0.25rem;
}

.SideTaskBar_chevron__n2F5R {
  margin-left: auto;
  transition: transform 0.2s ease; /* Keep transition for rotation */
  font-size: 0.9em;
}

.SideTaskBar_rotated__bXAyV {
  transform: rotate(90deg);
}

/* Secondary Navigation Items (Sub-items in the second column) */
.SideTaskBar_subNavItem__CSQh1 { /* Wrapper for sub-item and its nested sub-menu */
  position: relative;
}

.SideTaskBar_subNavLink__xOTPC { /* Styles for items in the secondary column */
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: 0.75rem 0.5rem;
  min-height: 4rem;
  border-radius: var(--radius);
  color: var(--color-textSecondary);
  transition: var(--transition);
  background: transparent;
  border: none;
  cursor: pointer;
  text-align: center;
  gap: 0.75rem;
  position: relative;
  font-size: 0.9375rem;
  font-weight: 500;
  overflow: hidden;
  margin: 0.125rem 0;
}

.SideTaskBar_subNavLink__xOTPC::before { /* Active indicator for secondary items */
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 3px;
  background: var(--color-primary);
  transform: scaleY(0);
  transform-origin: center;
  transition: var(--transition);
  border-radius: 0 4px 4px 0;
}

.SideTaskBar_subNavLink__xOTPC:hover {
  background: var(--color-hover);
  color: var(--color-textPrimary);
}

.SideTaskBar_subNavLink__xOTPC.SideTaskBar_active__osF8h {
  color: var(--color-primary);
  background: var(--color-active);
  font-weight: 600;
}

.SideTaskBar_subNavLink__xOTPC.SideTaskBar_active__osF8h .SideTaskBar_subIcon__x1GtK {
  color: var(--color-primary);
}

.SideTaskBar_subNavLink__xOTPC.SideTaskBar_active__osF8h::before {
  transform: scaleY(1);
}

.SideTaskBar_subIcon__x1GtK { /* Icon for secondary nav items */
  font-size: 1.1rem; /* Same as main icon for consistency */
  width: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  color: var(--color-textSecondary);
}






/* Footer for Primary Nav (Always expanded) */
.SideTaskBar_footer__rwkcH {
  margin-top: auto;
  padding: 0.5rem;
  border-top: 1px solid var(--color-border);
  background: var(--color-surface);
  position: sticky;
  bottom: 0;
  z-index: 20;
}

.SideTaskBar_profile__Gt5jk {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
  text-align: center;
}

.SideTaskBar_avatar__fxU37 {
  width: 1.5rem;
  height: 1.5rem;
  border-radius: 50%;
  background: var(--color-hover);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  font-size: 0.8rem;
}

.SideTaskBar_profileInfo__8Iu_V {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.1rem;
  width: 100%;
}

.SideTaskBar_userName__Px1id {
  font-size: 0.6rem;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
}

.SideTaskBar_logoutButton__Lid2R {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.25rem;
  background: none;
  border: none;
  color: var(--color-textSecondary);
  font-size: 0.6rem;
  cursor: pointer;
  padding: 0.1rem 0.2rem;
  border-radius: 0.2rem;
  transition: var(--transition);
  width: 100%;
}

.SideTaskBar_logoutButton__Lid2R:hover {
  background: var(--color-hover);
  color: var(--color-primary);
}

/* Ensure the main nav content doesn't get hidden behind the footer */
.SideTaskBar_primaryNav__0gyIV {
  padding-bottom: 4rem; /* Make space for the footer */
  overflow-y: auto;
}

/* Animations (only for nested sub-menus within the second column) */
@keyframes SideTaskBar_fadeIn__kFMdU { /* Kept for reference but not used for profile/footer */
  from {
    opacity: 0;
    transform: translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/*!***************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[7].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[7].use[3]!./components/TopTaskBar/TopTaskBar.module.css ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************/
.TopTaskBar_topTaskBar___x_Yz {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 56px;
  padding: 0 2rem;
  background: #fff;
  border-bottom: 1px solid #e5e7eb;
  box-shadow: 0 2px 4px rgba(0,0,0,0.02);
  position: sticky;
  top: 0;
  z-index: 100;
}
.TopTaskBar_leftSection__hIOxd {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}
.TopTaskBar_warehouseLabel__2kDbh {
  font-size: 0.95rem;
  color: #888;
  margin-right: 0.25rem;
}
.TopTaskBar_warehouseName__IXp8P {
  font-weight: 600;
  color: #222;
}
.TopTaskBar_warehouseDropdownIcon__GVPt8 {
  color: #888;
  margin-left: 0.25rem;
  font-size: 1rem;
}
.TopTaskBar_rightSection__GGbPI {
  display: flex;
  align-items: center;
  gap: 1.25rem;
}
.TopTaskBar_iconButton__IzaF5 {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 1.3rem;
  color: #555;
  transition: color 0.15s;
  padding: 0.25rem;
}
.TopTaskBar_iconButton__IzaF5:hover {
  color: #1d4ed8;
}
.TopTaskBar_userSection__3P7UN {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-left: 1rem;
}
.TopTaskBar_avatar__mFCZT {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  -o-object-fit: cover;
     object-fit: cover;
  border: 2px solid #e5e7eb;
}
.TopTaskBar_avatarPlaceholder__ksvJj {
  width: 32px;
  height: 32px;
  background: #e5e7eb;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 1.1rem;
  color: #555;
}
.TopTaskBar_userName__e1K6O {
  font-size: 1rem;
  color: #222;
  font-weight: 500;
}

.TopTaskBar_userDropdown__TQ84M {
  position: relative;
  display: flex;
  align-items: center;
}

.TopTaskBar_userDropdownTrigger__rjZD2 {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 0.375rem;
  transition: background-color 0.15s;
  color: inherit;
  font: inherit;
}

.TopTaskBar_userDropdownTrigger__rjZD2:hover {
  background-color: #f3f4f6;
}

.TopTaskBar_userDropdownIcon__82OWh {
  color: #888;
  font-size: 0.875rem;
  transition: transform 0.15s;
}

.TopTaskBar_userDropdownIcon__82OWh.TopTaskBar_rotated__MXnGL {
  transform: rotate(180deg);
}

.TopTaskBar_userDropdownMenu__HytG8 {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: 0.5rem;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  min-width: 160px;
  z-index: 1000;
}

.TopTaskBar_userDropdownItem__MXr6I {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  width: 100%;
  padding: 0.75rem 1rem;
  background: none;
  border: none;
  text-align: left;
  font-size: 0.875rem;
  color: #374151;
  cursor: pointer;
  transition: background-color 0.15s;
}

.TopTaskBar_userDropdownItem__MXr6I:hover {
  background-color: #f9fafb;
}

.TopTaskBar_userDropdownItem__MXr6I:first-child {
  border-radius: 0.5rem 0.5rem 0 0;
}

.TopTaskBar_userDropdownItem__MXr6I:last-child {
  border-radius: 0 0 0.5rem 0.5rem;
}

.TopTaskBar_userDropdownItemIcon__e3Q5g {
  font-size: 1rem;
  color: #6b7280;
}

.TopTaskBar_warehouseDropdown__mIvQS {
  position: relative;
  display: flex;
  align-items: center;
}

.TopTaskBar_warehouseDropdownTrigger__DKecL {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 0.375rem;
  transition: background-color 0.15s;
  color: inherit;
  font: inherit;
}

.TopTaskBar_warehouseDropdownTrigger__DKecL:hover:not(:disabled) {
  background-color: #f3f4f6;
}

.TopTaskBar_warehouseDropdownTrigger__DKecL:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.TopTaskBar_warehouseDropdownMenu__28tBS {
  position: absolute;
  top: 100%;
  left: 0;
  margin-top: 0.5rem;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  min-width: 200px;
  z-index: 1000;
  max-height: 300px;
  overflow-y: auto;
}

.TopTaskBar_warehouseDropdownItem__blY_r {
  display: block;
  width: 100%;
  padding: 0.75rem 1rem;
  background: none;
  border: none;
  text-align: left;
  font-size: 0.875rem;
  color: #374151;
  cursor: pointer;
  transition: background-color 0.15s;
}

.TopTaskBar_warehouseDropdownItem__blY_r:hover {
  background-color: #f9fafb;
}

.TopTaskBar_warehouseDropdownItem__blY_r.TopTaskBar_active__xDw6c {
  background-color: #dbeafe;
  color: #1d4ed8;
  font-weight: 500;
}

.TopTaskBar_warehouseDropdownItem__blY_r:first-child {
  border-radius: 0.5rem 0.5rem 0 0;
}

.TopTaskBar_warehouseDropdownItem__blY_r:last-child {
  border-radius: 0 0 0.5rem 0.5rem;
}

.TopTaskBar_warehouseDropdownDivider__dVfTz {
  height: 1px;
  background-color: #e5e7eb;
  margin: 0.25rem 0;
}

.TopTaskBar_addWarehouseButton__p9qFH {
  display: flex !important;
  align-items: center;
  gap: 0.5rem;
  color: #1d4ed8 !important;
  font-weight: 500;
}

.TopTaskBar_addWarehouseButton__p9qFH:hover {
  background-color: #dbeafe !important;
}

.TopTaskBar_warehouseDropdownItemIcon__P3eXZ {
  font-size: 1rem;
  color: #6b7280;
}

