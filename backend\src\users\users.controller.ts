import {
  <PERSON>,
  Get,
  Post,
  Patch,
  Body,
  Param,
  Delete,
  BadRequestException,
  NotFoundException,
  ParseUUIDPipe,
} from "@nestjs/common";
import { Uuid7 } from "../utils/uuid7";
import { UsersService } from "./users.service";
import { UserRolesService } from "./user_roles.service";
import { ApiTags, ApiResponse, ApiBody, ApiOperation } from "@nestjs/swagger";
import { CreateUserDto } from "./dto/create-user.dto";
import { ChangePasswordDto } from "./dto/change-password.dto";
import { UpdateUserDto } from "./dto/update-user.dto";
import { UpdateUserAndPasswordDto } from "./dto/update-user-and-password.dto";
import { CreateRoleDto } from "./dto/create-role.dto";
import { AssignRoleDto } from "./dto/assign-role.dto";
import { AssignVanDto } from "./dto/assign-van.dto";
import { UpdateRoleDto } from "./dto/update-role.dto";
import { UpdateWarhouseDto } from "./dto/update-warhouse.dto";
import { Permission } from "./user_permissions";

@ApiTags("users")
@Controller("users")
export class UsersController {
  @Delete("all")
  async deleteAllUsers() {
    return this.usersService.deleteAllUsers();
  }
  constructor(
    private readonly usersService: UsersService,
    private readonly userRolesService: UserRolesService,
  ) {}

  @Post()
  @ApiBody({ type: CreateUserDto })
  @ApiResponse({
    status: 201,
    description:
      "User created. Only the first user for a warehouse can be a super user. All others are type user.",
    schema: {
      example: {
        uuid: "uuid-v7-string",
        warehouseUuidString: "uuid-v7-string",
        email: "<EMAIL>",
        name: "John Doe",
        roleUuid: "018ea2bb-6b8d-7e9a-8a2a-7e9a8a2a7e9a",
        userType: "super", // or 'user'
        isDeleted: false,
      },
    },
  })
  async create(@Body() body: CreateUserDto) {
    return this.usersService.create(
      body.warehouseUuid,
      body.email,
      body.name,
      body.password,
      body.userType,
      body.roleUuid,
    );
  }

  @Get("list")
  @ApiResponse({
    status: 200,
    description: "List all users (excluding deleted).",
  })
  async findAll() {
    return this.usersService.findAll();
  }

  @Get("by-warehouse/:warehouseUuid")
  @ApiResponse({
    status: 200,
    description: "List all users by warehouse UUID (excluding deleted).",
  })
  async findByWarehouseUuid(@Param("warehouseUuid") warehouseUuid: string) {
    return this.usersService.findByWarehouseUuid(warehouseUuid);
  }

  @Get("list-raw")
  @ApiResponse({
    status: 200,
    description: "List all users (including deleted).",
  })
  async findAllRaw() {
    return this.usersService.findAllRaw();
  }

  @Get(":uuid")
  @ApiResponse({ status: 200, description: "Get user by UUID." })
  async findOne(@Param("uuid", ParseUUIDPipe) uuid: string) {
    return this.usersService.findOne(uuid);
  }

  @Patch(":uuid/password")
  @ApiOperation({ summary: "Change user password" })
  @ApiBody({ type: ChangePasswordDto })
  @ApiResponse({ status: 200, description: "Password changed successfully." })
  async changePassword(
    @Param("uuid", ParseUUIDPipe) uuid: string,
    @Body() body: ChangePasswordDto,
  ) {
    return this.usersService.changePassword(
      uuid,
      body.oldPassword,
      body.newPassword,
    );
  }

  @Patch(":uuid/name")
  @ApiOperation({ summary: "Change user name" })
  @ApiBody({ type: UpdateUserDto })
  @ApiResponse({ status: 200, description: "User name updated successfully." })
  async changeName(@Param("uuid", ParseUUIDPipe) uuid: string, @Body() body: UpdateUserDto) {
    return this.usersService.changeName(uuid, body.name);
  }

  // NOTE: Password is optional. Only provide 'newPassword' if you want to change the password.
  @Patch(":uuid/update-profile")
  @ApiOperation({ summary: "Change user name and/or password" })
  @ApiBody({ type: UpdateUserAndPasswordDto })
  @ApiResponse({
    status: 200,
    description: "User profile updated successfully.",
  })
  async updateProfile(
    @Param("uuid", ParseUUIDPipe) uuid: string,
    @Body() body: UpdateUserAndPasswordDto,
  ) {
    return this.usersService.updateProfile(uuid, body);
  }

  @Delete(":uuid")
  @ApiOperation({ summary: "Delete user by UUID (soft delete)" })
  @ApiResponse({ status: 200, description: "User soft deleted." })
  async deleteUser(@Param("uuid", ParseUUIDPipe) uuid: string) {
    return this.usersService.deleteUser(uuid);
  }

  @Patch(":uuid/warehouse")
  @ApiOperation({ summary: "Change user warehouse UUID" })
  @ApiBody({ type: UpdateWarhouseDto })
  @ApiResponse({
    status: 200,
    description: "Warehouse UUID updated successfully.",
  })
  async changeWarehouse(
    @Param("uuid", ParseUUIDPipe) uuid: string,
    @Body() body: UpdateWarhouseDto,
  ) {
    return this.usersService.changeWarhouse(uuid, body.warehouseUuid);
  }

  @Post("roles")
  @ApiOperation({ summary: "Create a new role" })
  @ApiBody({ type: CreateRoleDto })
  @ApiResponse({ status: 201, description: "Role created." })
  async createRole(@Body() body: CreateRoleDto) {
    return this.userRolesService.createRole(
      body.name,
      body.permissions,
      body.warehouseUuid,
    );
  }

  @Get("roles/by-warehouse/:warehouseUuid")
  @ApiOperation({
    summary: "List all roles for a warehouse (excluding deleted)",
  })
  @ApiResponse({
    status: 200,
    description: "List of roles for the given warehouse.",
  })
  async listRolesByWarehouse(@Param("warehouseUuid") warehouseUuid: string) {
    return this.userRolesService.listRolesByWarehouse(warehouseUuid);
  }

  @Get("roles/list")
  @ApiOperation({ summary: "List all roles (excluding deleted)" })
  @ApiResponse({
    status: 200,
    description: "List of roles (excluding deleted).",
  })
  async listRoles() {
    // No UUID validation, this endpoint has no parameters
    return this.userRolesService.listRoles();
  }

  @Get("roles/list-raw")
  @ApiOperation({ summary: "List all roles (including deleted)" })
  @ApiResponse({
    status: 200,
    description: "List of roles (including deleted).",
  })
  async listRolesRaw() {
    return this.userRolesService.listRolesRaw();
  }

  @Get("roles/permissions")
  @ApiOperation({ summary: "Get list of all possible permissions" })
  @ApiResponse({
    status: 200,
    description: "List of all available permissions.",
  })
  getAllPermissions() {
    return Object.values(Permission);
  }

  @Get("roles/:uuid")
  @ApiOperation({ summary: "Get role by UUID" })
  @ApiResponse({ status: 200, description: "Role details." })
  async getRoleByUuid(@Param("uuid", ParseUUIDPipe) uuid: string) {
    // Always use Buffer for service lookup (Uuid7.toBinary())
    return this.userRolesService.getRoleByUuid(uuid);
  }

  @Patch("roles/:uuid")
  @ApiOperation({ summary: "Update role by UUID" })
  @ApiBody({ type: UpdateRoleDto })
  @ApiResponse({ status: 200, description: "Role updated." })
  async updateRoleByUuid(
    @Param("uuid", ParseUUIDPipe) uuid: string,
    @Body() body: UpdateRoleDto,
  ) {
    return this.userRolesService.updateRoleByUuid(uuid, body);
  }

  @Delete("roles/:uuid")
  @ApiOperation({ summary: "Delete role by UUID (soft delete)" })
  @ApiResponse({ status: 200, description: "Role soft deleted." })
  async deleteRoleByUuid(@Param("uuid", ParseUUIDPipe) uuid: string) {
    return this.userRolesService.deleteRoleByUuid(uuid);
  }

  @Post(":uuid/assign-role")
  @ApiOperation({ summary: "Assign role to user (by UUID)" })
  @ApiBody({ type: AssignRoleDto })
  @ApiResponse({ status: 200, description: "Role assigned." })
  async assignRole(@Param("uuid", ParseUUIDPipe) uuid: string, @Body() body: AssignRoleDto) {
    return this.userRolesService.assignRoleToUser(
      uuid,
      body.roleUuid,
      body.assignerUuid,
    );
  }

  @Patch(":uuid/assign-van")
  @ApiOperation({ summary: "Assign van to user (only mobile sale agents)" })
  @ApiBody({ type: AssignVanDto })
  @ApiResponse({ status: 200, description: "Van assigned." })
  async assignVan(@Param("uuid", ParseUUIDPipe) uuid: string, @Body() body: AssignVanDto) {
    return this.usersService.assignVanToUser(uuid, body.vanUuid);
  }

  @Get(":uuid/role")
  @ApiOperation({ summary: "Get role by user UUID" })
  @ApiResponse({ status: 200, description: "Role for given user UUID." })
  async getRoleByUser(@Param("uuid", ParseUUIDPipe) uuid: string) {
    // Find user and fetch role by roleUuid
    const user = await this.usersService.findOne(uuid);
    if (!user || !user.roleUuidString)
      throw new NotFoundException("User or user role not found");
    return this.userRolesService.getRoleByUuid(user.roleUuidString);
  }

  @Post(":uuid/assign-admin-role")
  @ApiOperation({ 
    summary: "Assign admin role to user (requires existing admin authorization)",
    description: "This endpoint allows existing admins to assign admin role to users who have no role assigned. This is a security-sensitive operation."
  })
  @ApiBody({ 
    type: AssignRoleDto,
    description: "Requires assignerUuid of an existing admin user"
  })
  @ApiResponse({ status: 200, description: "Admin role assigned successfully." })
  @ApiResponse({ status: 403, description: "Unauthorized - only existing admins can assign admin roles." })
  async assignAdminRoleToUser(@Param("uuid", ParseUUIDPipe) uuid: string, @Body() body: AssignRoleDto) {
    // This endpoint requires the assigner to be an existing admin
    // The userRolesService.assignRoleToUser method already has proper authorization checks
    return this.usersService.assignAdminRoleToUser(uuid);
  }

  @Get("users-without-roles")
  @ApiOperation({ 
    summary: "List users without roles (admin only)",
    description: "Returns a list of users who exist but have no role assigned. This helps identify users who need role assignment."
  })
  @ApiResponse({ status: 200, description: "List of users without roles." })
  async getUsersWithoutRoles() {
    return this.usersService.findUsersWithoutRoles();
  }

  @Get("debug/warehouse-consistency")
  @ApiResponse({
    status: 200,
    description: "Debug endpoint to check warehouse UUID consistency between users and warehouses.",
  })
  async debugWarehouseUuidConsistency() {
    return this.usersService.debugWarehouseUuidConsistency();
  }
}
