import { ApiProperty } from "@nestjs/swagger";

export class AccountSettingsResponseDto {
  @ApiProperty({
    example: "018ea2bb-6b8d-7e9a-8a2a-7e9a8a2a7e9a",
    description: "Unique identifier for the account settings (UUIDv7)",
  })
  uuid: string;

  @ApiProperty({
    example: "018ea2bb-6b8d-7e9a-8a2a-7e9a8a2a7e9a",
    description: "UUIDv7 of the user who owns these account settings",
  })
  userUuidString: string;

  @ApiProperty({
    example: "en",
    description: "Preferred language for the user interface",
  })
  preferredLanguage: string;

  @ApiProperty({
    example: "light",
    description: "Preferred theme (light/dark)",
  })
  preferredTheme: string;

  @ApiProperty({
    example: "cash",
    description: "Preferred payment method for sales",
  })
  preferredPaymentMethod: string;

  @ApiProperty({
    example: "standard",
    description: "Invoice format preference",
  })
  invoiceFormat: string;

  @ApiProperty({
    example: 20.0,
    description: "Preferred tax rate percentage",
  })
  preferredTaxRate: number;

  @ApiProperty({
    example: true,
    description: "Flag to indicate if tax should be used by default",
  })
  preferredUseTax: boolean;

  @ApiProperty({
    example: "basic",
    description: "User account plan type",
  })
  userAccountPlan: string;

  @ApiProperty({
    example: false,
    description: "Soft delete flag",
  })
  isDeleted: boolean;

  @ApiProperty({
    example: "2024-01-01T00:00:00.000Z",
    description: "Creation timestamp",
  })
  createdAt: Date;

  @ApiProperty({
    example: "2024-01-01T00:00:00.000Z",
    description: "Last update timestamp",
  })
  updatedAt: Date;
}
