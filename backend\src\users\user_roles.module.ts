import { Module, forwardRef } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { Role } from "./role.entity";
import { User } from "./user.entity";
import { UserRolesService } from "./user_roles.service";

@Module({
  imports: [
    TypeOrmModule.forFeature([Role, User]),
    // Add forwardRef if you have circular dependency (optional)
  ],
  providers: [UserRolesService],
  exports: [UserRolesService],
})
export class UserRolesModule {}
