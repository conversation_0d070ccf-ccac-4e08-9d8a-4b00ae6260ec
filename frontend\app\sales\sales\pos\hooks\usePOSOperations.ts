import { useCallback } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'sonner';
import { 
  createSale, 
  getSale, 
  updateSale,
  createCustomer,
  filterCustomers
} from '../posApi';
import type { POSSaleState } from './usePOSState';
import type { Customer, CreateCustomerDto } from '@/components/CustomerModal';

export interface UsePOSOperationsReturn {
  // Sale operations
  saveSale: (saleState: POSSaleState) => Promise<string>; // Returns sale UUID
  loadSale: (saleUuid: string) => Promise<any>;
  
  // Customer operations
  createNewCustomer: (customerData: CreateCustomerDto) => Promise<Customer>;
  getDefaultCustomer: () => Promise<Customer>;
  searchCustomers: (warehouseUuid: string) => Promise<Customer[]>;
  
  // Validation
  validateStock: (items: any[]) => Promise<boolean>;
}

export function usePOSOperations(): UsePOSOperationsReturn {
  const { user } = useAuth();
  const warehouseUuid = user?.warehouseUuid;
  const userUuid = user?.uuid;

  // Save current sale to backend
  const saveSale = useCallback(async (saleState: POSSaleState): Promise<string> => {
    
    if (!warehouseUuid || !userUuid) {
      throw new Error('User or warehouse not available');
    }

    try {
      let saleUuid: string;

      if (saleState.mode === 'edit' && saleState.originalSaleUuid) {
        // Edit existing sale - update sale details first
        saleUuid = saleState.originalSaleUuid;
        
        // Check if we have items to send
        if (!saleState.items || saleState.items.length === 0) {
          throw new Error('Cannot update sale: No items found in sale state');
        }
        
        // Validate and ensure items have consistent data before sending
        const validatedItems = saleState.items.map(item => {
          const calculatedTotal = Math.round(item.quantity * item.unitPrice * 100) / 100;
          const difference = Math.abs(item.totalPrice - calculatedTotal);
          
          // If there's a significant difference, use the calculated value
          if (difference > 0.01) {
            return {
              ...item,
              totalPrice: calculatedTotal
            };
          }
          
          return item;
        });
        
        // For edit mode, we need to send all the updated information including items
        const updatePayload: any = {
          ...(saleState.customerUuid && { customerUuid: saleState.customerUuid }),
          paymentMethod: saleState.paymentMethod,
          useTax: saleState.taxEnabled,
          taxRate: saleState.taxRate,
          // Include validated items to ensure backend recalculates totals correctly
          items: validatedItems
        };
        
        // Only include amountPaid if it's provided and doesn't exceed total
        if (saleState.amountPaid >= 0) {
          // Validate that amountPaid doesn't exceed the frontend-calculated total
          if (saleState.amountPaid > saleState.total) {
            throw new Error(`Payment amount (${saleState.amountPaid}) cannot exceed total sale amount (${saleState.total}). Overpayment is not allowed.`);
          }
          updatePayload.amountPaid = saleState.amountPaid;
        }

        // Update sale with all information including items
        const updatedSale = await updateSale(saleUuid, { ...updatePayload, userUuid });
        
      } else {
        // Create new sale
        if (!saleState.customer) {
          throw new Error('Customer is required to create a sale');
        }
        
        // Validate and ensure items have consistent data before sending
        const validatedItems = saleState.items.map(item => {
          const calculatedTotal = Math.round(item.quantity * item.unitPrice * 100) / 100;
          const difference = Math.abs(item.totalPrice - calculatedTotal);
          
          // If there's a significant difference, use the calculated value
          if (difference > 0.01) {
            return {
              ...item,
              totalPrice: calculatedTotal
            };
          }
          
          return item;
        });
        
        // Validate that amountPaid doesn't exceed the frontend-calculated total for new sales
        if (saleState.amountPaid > saleState.total) {
          throw new Error(`Payment amount (${saleState.amountPaid}) cannot exceed total sale amount (${saleState.total}). Overpayment is not allowed.`);
        }
        
        const salePayload = {
          customerUuid: saleState.customer.uuid,
          warehouseUuid,
          userUuid,
          items: validatedItems,
          paymentMethod: saleState.paymentMethod,
          amountPaid: saleState.amountPaid,
          useTax: saleState.taxEnabled,
          taxRate: saleState.taxRate
        };

        const createdSale = await createSale(salePayload);
        saleUuid = createdSale.uuid;
      }

      return saleUuid;
    } catch (error) {
      throw error;
    }
  }, [warehouseUuid, userUuid]);

  // Load sale data from backend
  const loadSale = useCallback(async (saleUuid: string) => {
    try {
      const saleData = await getSale(saleUuid);
      
      // Load customer data if needed
      if (saleData.customerUuidString && warehouseUuid) {
        try {
          const customersResponse = await filterCustomers({ warehouseUuid });
          const customer = customersResponse.data.find((c: any) => c.uuid === saleData.customerUuidString);
          if (customer) {
            saleData.customer = customer;
          }
        } catch (error) {
          console.error('Failed to load customer:', error);
        }
      }
      
      return saleData;
    } catch (error) {
      console.error('Error loading sale:', error);
      throw error;
    }
  }, [warehouseUuid]);

  // Create new customer
  const createNewCustomer = useCallback(async (customerData: CreateCustomerDto): Promise<Customer> => {
    if (!warehouseUuid) {
      throw new Error('Warehouse not available');
    }

    try {
      const customerDataWithWarehouse = {
        ...customerData,
        warehouseUuid
      };
      
      const newCustomer = await createCustomer(customerDataWithWarehouse);
      return newCustomer as Customer;
    } catch (error) {
      console.error('Error creating customer:', error);
      throw error;
    }
  }, [warehouseUuid]);

  // Get default customer (create a simple fallback for now)
  const getDefaultCustomer = useCallback(async (): Promise<Customer> => {
    if (!warehouseUuid) {
      throw new Error('Warehouse not available');
    }

    // For now, create a simple "Walk-in Customer" if no customer is selected
    const defaultCustomerData: CreateCustomerDto = {
      name: 'Walk-in Customer',
      warehouseUuid,
      creditLimit: 0
    };

    try {
      // Try to find existing walk-in customer first
      const customersResponse = await filterCustomers({ 
        warehouseUuid, 
        name: 'Walk-in Customer' 
      });
      
      if (customersResponse.data.length > 0) {
        return customersResponse.data[0] as Customer;
      }
      
      // Create new walk-in customer if not found
      return await createNewCustomer(defaultCustomerData);
    } catch (error) {
      console.error('Error getting default customer:', error);
      throw error;
    }
  }, [warehouseUuid, createNewCustomer]);

  // Search customers
  const searchCustomers = useCallback(async (warehouseUuid: string): Promise<Customer[]> => {
    try {
      const response = await filterCustomers({ warehouseUuid });
      return response.data as Customer[];
    } catch (error) {
      console.error('Error searching customers:', error);
      throw error;
    }
  }, []);

  // Validate stock levels (simplified for now)
  const validateStock = useCallback(async (items: any[]): Promise<boolean> => {
    // For now, just return true - implement actual stock validation later if needed
    // This would check current stock levels against sale quantities
    return true;
  }, []);

  return {
    saveSale,
    loadSale,
    createNewCustomer,
    getDefaultCustomer,
    searchCustomers,
    validateStock
  };
} 