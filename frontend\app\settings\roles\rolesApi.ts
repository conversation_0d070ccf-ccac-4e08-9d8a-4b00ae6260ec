// All API calls for roles management are defined here.
// Uses the /api/ prefix so requests are proxied to the backend (see next.config.js)
import { getAuthHeadersWithContentType } from '@/utils/authHeaders';

export type Role = {
  uuid: string;
  warehouseUuid: string;
  name: string;
  permissions: string[];
  isDeleted: boolean;
};

export type AddRoleInput = {
  name: string;
  warehouseUuid: string;
  permissions: string[];
};

export type UpdateRoleInput = {
  name?: string;
  permissions?: string[];
};

// Fetch all roles for a warehouse (excluding deleted)
// GET /users/roles/by-warehouse/:warehouseUuid
export const fetchRoles = async (warehouseUuid: string): Promise<Role[]> => {
  if (!warehouseUuid) return [];
  const res = await fetch(`/api/users/roles/by-warehouse/${warehouseUuid}`, {
    headers: getAuthHeadersWithContentType(),
  });
  if (!res.ok) throw new Error('Failed to fetch roles');
  const roles = await res.json();
  console.log('Raw roles from backend:', roles);
  return roles;
};

// Fetch all available permissions
// GET /users/roles/permissions
export const fetchPermissions = async (): Promise<string[]> => {
  const res = await fetch('/api/users/roles/permissions', {
    headers: getAuthHeadersWithContentType(),
  });
  if (!res.ok) throw new Error('Failed to fetch permissions');
  const permissions = await res.json();
  console.log('Raw permissions from backend:', permissions);
  return permissions;
};

// For role creation
export const addRole = async (data: AddRoleInput) => {
  const res = await fetch('/api/users/roles', {
    method: 'POST',
    headers: getAuthHeadersWithContentType(),
    body: JSON.stringify(data),
  });
  if (!res.ok) {
    let message = 'Failed to add role';
    try {
      const error = await res.json();
      if (error && error.message) message = error.message;
    } catch {}
    throw new Error(message);
  }
  return res.json();
};

// Update role details
export const updateRole = async (uuid: string, data: UpdateRoleInput) => {
  const res = await fetch(`/api/users/roles/${encodeURIComponent(uuid)}`, {
    method: 'PATCH',
    headers: getAuthHeadersWithContentType(),
    body: JSON.stringify(data),
  });
  if (!res.ok) {
    let message = 'Failed to update role';
    try {
      const error = await res.json();
      if (error && error.message) message = error.message;
    } catch {}
    throw new Error(message);
  }
  return res.json();
};

// Soft delete role
export const deleteRole = async (uuid: string) => {
  const res = await fetch(`/api/users/roles/${encodeURIComponent(uuid)}`, {
    method: 'DELETE',
    headers: getAuthHeadersWithContentType(),
  });
  if (!res.ok) {
    let message = 'Failed to delete role';
    try {
      const error = await res.json();
      if (error && error.message) message = error.message;
    } catch {}
    throw new Error(message);
  }
  return res.json();
};
