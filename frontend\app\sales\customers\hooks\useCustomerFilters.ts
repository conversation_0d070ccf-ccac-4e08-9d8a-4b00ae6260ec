import { useState, useEffect, useMemo, useCallback } from 'react';
import { FilterCustomerDto } from '../customersApi';

// Local useDebounce implementation
const useDebounce = (value: string, delay: number) => {
  const [debouncedValue, setDebouncedValue] = useState(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => clearTimeout(handler);
  }, [value, delay]);

  return debouncedValue;
};

/**
 * Enhanced customer filters hook with credit filters and sorting
 */
export const useCustomerFilters = (warehouseUuid?: string) => {
  // Search and basic filters
  const [searchTerm, setSearchTerm] = useState("");
  const [customerTypeFilter, setCustomerTypeFilter] = useState("");
  const [regionFilter, setRegionFilter] = useState("");
  
  // Credit filters
  const [creditFilter, setCreditFilter] = useState<'all' | 'positive' | 'negative' | 'zero' | 'custom'>('all');
  const [minCredit, setMinCredit] = useState("");
  const [maxCredit, setMaxCredit] = useState("");
  
  // Pagination and sorting
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [sortBy, setSortBy] = useState<'name' | 'email' | 'customerType' | 'currentCredit' | 'createdAt' | 'updatedAt'>('createdAt');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  
  // UI state
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);

  // Debounced values
  const debouncedSearchTerm = useDebounce(searchTerm, 300);
  const debouncedMinCredit = useDebounce(minCredit, 500);
  const debouncedMaxCredit = useDebounce(maxCredit, 500);

  // Build filter object
  const filter: FilterCustomerDto = useMemo(() => {
    const filterObj: FilterCustomerDto = {};
    
    if (warehouseUuid) {
      filterObj.warehouseUuid = warehouseUuid;
    }
    
    if (debouncedSearchTerm.trim()) {
      filterObj.name = debouncedSearchTerm.trim();
    }
    
    if (customerTypeFilter) {
      filterObj.customerType = customerTypeFilter as any;
    }
    
    if (regionFilter) {
      filterObj.regionUuid = regionFilter;
    }
    
    // Credit filters
    switch (creditFilter) {
      case 'positive':
        filterObj.minCredit = 0.01;
        break;
      case 'negative':
        filterObj.maxCredit = -0.01;
        break;
      case 'zero':
        filterObj.currentCredit = 0;
        break;
      case 'custom':
        if (debouncedMinCredit && !isNaN(parseFloat(debouncedMinCredit))) {
          filterObj.minCredit = parseFloat(debouncedMinCredit);
        }
        if (debouncedMaxCredit && !isNaN(parseFloat(debouncedMaxCredit))) {
          filterObj.maxCredit = parseFloat(debouncedMaxCredit);
        }
        break;
    }
    
    // Add sorting parameters
    filterObj.sortBy = sortBy;
    filterObj.sortOrder = sortOrder;
    
    return filterObj;
  }, [warehouseUuid, debouncedSearchTerm, customerTypeFilter, regionFilter, creditFilter, debouncedMinCredit, debouncedMaxCredit, sortBy, sortOrder]);

  // Reset page when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [debouncedSearchTerm, customerTypeFilter, regionFilter, creditFilter, debouncedMinCredit, debouncedMaxCredit]);

  // Clear all filters
  const clearAllFilters = useCallback(() => {
    setSearchTerm("");
    setCustomerTypeFilter("");
    setRegionFilter("");
    setCreditFilter('all');
    setMinCredit("");
    setMaxCredit("");
    setCurrentPage(1);
  }, []);

  // Check if filters are active
  const hasActiveFilters = useMemo(() => {
    return !!(
      searchTerm ||
      customerTypeFilter ||
      regionFilter ||
      creditFilter !== 'all' ||
      minCredit ||
      maxCredit
    );
  }, [searchTerm, customerTypeFilter, regionFilter, creditFilter, minCredit, maxCredit]);

  return {
    // Search and basic filters
    searchTerm,
    setSearchTerm,
    customerTypeFilter,
    setCustomerTypeFilter,
    regionFilter,
    setRegionFilter,
    
    // Credit filters
    creditFilter,
    setCreditFilter,
    minCredit,
    setMinCredit,
    maxCredit,
    setMaxCredit,
    
    // Pagination and sorting
    currentPage,
    setCurrentPage,
    pageSize,
    setPageSize,
    sortBy,
    setSortBy,
    sortOrder,
    setSortOrder,
    
    // UI state
    showAdvancedFilters,
    setShowAdvancedFilters,
    
    // Computed values
    filter,
    clearAllFilters,
    hasActiveFilters,
  };
}; 