import { Injectable, CanActivate, ExecutionContext } from "@nestjs/common";
import { UnifiedStrategy } from "./unified.strategy";

@Injectable()
export class UnifiedGuard implements CanActivate {
  constructor(private readonly unifiedStrategy: UnifiedStrategy) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const { identifier, password } = request.body;

    try {
      const user = await this.unifiedStrategy.validate(
        identifier,
        password || "",
      );

      request.user = user;
      return true;
    } catch (error) {
      throw error; // This will be caught by our exception filter
    }
  }
}
