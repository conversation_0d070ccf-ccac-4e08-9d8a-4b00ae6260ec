import { uuidv7 } from "uuidv7";

// Manual UUID string to buffer conversion
export function uuidStringToBuffer(uuid: string): Buffer {
  if (typeof uuid !== "string" || !uuid) {
    throw new Error(
      `uuidStringToBuffer: Expected a non-empty UUID string, got: ${uuid}`,
    );
  }
  return Buffer.from(uuid.replace(/-/g, ""), "hex");
}

/**
 * A wrapper class for UUIDv7 to handle UUID generation and conversion.
 */
export class Uuid7 {
  #bytes: Buffer; // Private field to store the raw 16 bytes

  /**
   * Creates an instance of Uuid7.
   * - If no value is provided, a new UUIDv7 is generated.
   * - If a string is provided, it's converted to <PERSON>uffer.
   * - If a Buffer is provided, it's used directly.
   * @param {string | Buffer} [value] - Optional initial value.
   */
  constructor(value?: string | Buffer) {
    if (!value) {
      const uuid = uuidv7();
      this.#bytes = uuidStringToBuffer(uuid);
    } else if (typeof value === "string") {
      this.#bytes = this.fromString(value);
    } else if (Buffer.isBuffer(value)) {
      this.#bytes = this.fromBuffer(value);
    } else {
      throw new Error("Unsupported type for Uuid7 constructor.");
    }

    // Additional validation
    this.validateBytes();
  }

  private validateBytes(): void {
    if (!this.#bytes || this.#bytes.length !== 16) {
      throw new Error("Invalid UUID: must be 16 bytes");
    }
  }

  private fromString(value: string): Buffer {
    const uuidRegex =
      /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/;
    if (!uuidRegex.test(value)) {
      console.error(`Uuid7: Invalid UUID string format: ${value}`);
      console.error(`Uuid7: Regex test failed for value: ${value}`);
      throw new Error(`Invalid UUID string format: ${value}`);
    }
    return uuidStringToBuffer(value);
  }

  private fromBuffer(value: Buffer): Buffer {
    if (value.length !== 16) {
      throw new Error("Buffer must be 16 bytes long for UUID");
    }
    return Buffer.from(value); // Create a copy to avoid external modifications
  }

  /**
   * A static factory method to generate a new Uuid7 instance.
   * @returns {Uuid7}
   */
  static generate(): Uuid7 {
    return new Uuid7();
  }

  /**
   * A static factory method to create a Uuid7 instance from a string.
   * @param {string} uuidString - The UUID string.
   * @returns {Uuid7}
   */
  static fromString(uuidString: string): Uuid7 {
    return new Uuid7(uuidString);
  }

  /**
   * Returns the standard hyphenated string representation of the UUID.
   * @returns {string}
   */
  toString(): string {
    if (!this.#bytes || this.#bytes.length !== 16) {
      // This validation is also in the constructor, but serves as a safeguard here.
      throw new Error(
        "Uuid7: Cannot stringify, internal bytes are invalid or not 16 bytes long.",
      );
    }
    // Use the local hex utility (defined below in this file) to format the UUID string.
    // This bypasses the strict validation of the 'uuid' package's stringify method.
    return (
      hex(this.#bytes.subarray(0, 4)) +
      "-" +
      hex(this.#bytes.subarray(4, 6)) +
      "-" +
      hex(this.#bytes.subarray(6, 8)) +
      "-" +
      hex(this.#bytes.subarray(8, 10)) +
      "-" +
      hex(this.#bytes.subarray(10, 16))
    );
  }

  /**
   * Used by JSON.stringify. Automatically converts the object to its
   * string representation when included in a JSON response.
   * @returns {string}
   */
  toJSON(): string {
    return this.toString();
  }

  /**
   * Checks for equality with another Uuid7 instance.
   * @param {Uuid7} other
   * @returns {boolean}
   */
  equals(other: unknown): boolean {
    return other instanceof Uuid7 && this.toString() === other.toString();
  }
}

// DEPRECATED: Use the 'uuidv7' package or a standards-compliant library for UUIDv7 generation.
// This implementation is a simplified polyfill and does not fully comply with the latest UUIDv7 specification.
// Retained here for legacy/testing purposes only.

function hex(bytes: Uint8Array): string {
  return Array.from(bytes)
    .map((b) => b.toString(16).padStart(2, "0"))
    .join("");
}

/**
 * DEPRECATED: Generates a UUIDv7 string (timestamp-based, draft).
 * Use the 'uuidv7' package for production.
 */
export function uuid7(): string {
  // NOTE: This uses Date.now() which has only millisecond resolution.
  const now = BigInt(Date.now());
  const timeHigh = Number((now >> 16n) & 0xffffffffn);
  const timeLow = Number(now & 0xffffn);
  const rand = crypto.getRandomValues(new Uint8Array(10));

  // Compose UUID v7 fields (not fully compliant, for demonstration only)
  const b = new Uint8Array(16);
  b[0] = (timeHigh >>> 24) & 0xff;
  b[1] = (timeHigh >>> 16) & 0xff;
  b[2] = (timeHigh >>> 8) & 0xff;
  b[3] = timeHigh & 0xff;
  b[4] = (timeLow >>> 8) & 0xff;
  b[5] = timeLow & 0xff;
  b[6] = 0x70 | ((rand[0] & 0xf0) >> 4); // version 7
  b[7] = ((rand[0] & 0x0f) << 4) | ((rand[1] & 0xf0) >> 4);
  b.set(rand.slice(2), 8);

  // Set variant (10xx)
  b[8] = (b[8] & 0x3f) | 0x80;

  // Format as UUID string
  return (
    hex(b.slice(0, 4)) +
    "-" +
    hex(b.slice(4, 6)) +
    "-" +
    hex(b.slice(6, 8)) +
    "-" +
    hex(b.slice(8, 10)) +
    "-" +
    hex(b.slice(10, 16))
  );
}
