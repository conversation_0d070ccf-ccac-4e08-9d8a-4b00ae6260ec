import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { getAllProductCategoriesRaw } from '@/app/inventory/products/productCategoriesApi';
import type { ProductCategory } from '@/app/inventory/products/productCategoriesApi';

interface CategoryFilterProps {
  selectedCategory: string;
  onCategoryChange: (category: string) => void;
  disabled?: boolean;
}

export function CategoryFilter({ selectedCategory, onCategoryChange, disabled = false }: CategoryFilterProps) {
  const { user } = useAuth();
  const warehouseUuid = user?.warehouseUuid;
  
  const [categories, setCategories] = useState<ProductCategory[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load categories when component mounts
  useEffect(() => {
    const loadCategories = async () => {
      if (!warehouseUuid) return;
      
      setIsLoading(true);
      setError(null);
      
      try {
        const categoriesData = await getAllProductCategoriesRaw(warehouseUuid);
        setCategories(categoriesData);
      } catch (err) {
        console.error('Failed to load categories:', err);
        setError('Failed to load categories');
      } finally {
        setIsLoading(false);
      }
    };

    loadCategories();
  }, [warehouseUuid]);

  const handleCategoryChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    const categoryUuid = event.target.value;
    onCategoryChange(categoryUuid);
  };

  const selectedCategoryName = categories.find(cat => cat.uuid === selectedCategory)?.name || 'All Categories';

  return (
    <div className="flex flex-col">
      <div className="flex items-center space-x-2">
        <label className="text-sm font-medium text-gray-700 whitespace-nowrap flex-shrink-0">
          Category:
        </label>
        <div className="relative flex-1 min-w-0">
          <select
            value={selectedCategory}
            onChange={handleCategoryChange}
            disabled={disabled || isLoading}
            className={`w-full pl-3 pr-10 py-2.5 text-base border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 placeholder-gray-400 hover:border-gray-400 ${
              disabled || isLoading ? 'bg-gray-100 cursor-not-allowed opacity-50' : 'bg-white'
            }`}
          >
            <option value="">All Categories</option>
            {categories.map((category) => (
              <option key={category.uuid} value={category.uuid}>
                {category.name}
              </option>
            ))}
          </select>
          
          {isLoading && (
            <div className="absolute inset-y-0 right-0 flex items-center pr-3">
              <svg className="w-4 h-4 text-gray-400 animate-spin" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v8z"></path>
              </svg>
            </div>
          )}
        </div>
      </div>
      
      {/* Add spacing to match SearchBar's search hints */}
      <div className="text-xs text-gray-500 mt-1 flex items-center space-x-3">
        <span className="flex items-center space-x-1">
          <span className="w-1 h-1 bg-gray-400 rounded-full"></span>
          <span>Filter by</span>
        </span>
        <span className="flex items-center space-x-1">
          <span className="w-1 h-1 bg-gray-400 rounded-full"></span>
          <span>Category</span>
        </span>
      </div>
      
      {error && (
        <div className="text-xs text-red-600 mt-1">
          {error}
        </div>
      )}
    </div>
  );
} 