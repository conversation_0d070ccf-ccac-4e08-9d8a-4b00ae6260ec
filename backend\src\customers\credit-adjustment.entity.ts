import { Entity, PrimaryColumn, Column, CreateDateColumn, UpdateDateColumn, Index } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Uuid7 } from '../utils/uuid7';

export enum CreditAdjustmentType {
  SALE = "sale",
  PAYMENT = "payment",
  REFUND = "refund",
  MANUAL = "manual",
  CORRECTION = "correction",
}

@Entity('credit_adjustments')
export class CreditAdjustment {
  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the credit adjustment (primary key)",
  })
  @PrimaryColumn('uuid')
  id: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the customer",
  })
  @Column('uuid')
  @Index()
  customerUuid: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the user who made the adjustment",
  })
  @Column('uuid')
  @Index()
  userUuid: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the warehouse",
  })
  @Column('uuid')
  @Index()
  warehouseUuid: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the related sale (optional)",
    required: false,
  })
  @Column('uuid', { nullable: true })
  @Index()
  saleUuid?: string;

  @ApiProperty({
    example: "sale",
    description: "Type of credit adjustment",
    enum: Object.values(CreditAdjustmentType),
  })
  @Column({
    type: 'enum',
    enum: CreditAdjustmentType,
  })
  adjustmentType: CreditAdjustmentType;

  @ApiProperty({
    example: 100.0,
    description:
      "Amount adjusted (positive for credit increase, negative for decrease)",
  })
  @Column('decimal', { precision: 10, scale: 2 })
  amountAdjusted: number;

  @ApiProperty({
    example: 50.0,
    description: "Customer credit balance before this adjustment",
  })
  @Column('decimal', { precision: 10, scale: 2 })
  previousBalance: number;

  @ApiProperty({
    example: 150.0,
    description: "Customer credit balance after this adjustment",
  })
  @Column('decimal', { precision: 10, scale: 2 })
  newBalance: number;

  @ApiProperty({
    example: "Sale payment received",
    description: "Reason for the credit adjustment",
  })
  @Column()
  reason: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Helper method to generate UUID
  static generateId(): string {
    return new Uuid7().toString();
  }

  // Helper method to generate UUID
  static fromBinary(binary: any): string {
    if (!binary) return null;
    try {
      return new Uuid7(binary).toString();
    } catch {
      return null;
    }
  }
} 