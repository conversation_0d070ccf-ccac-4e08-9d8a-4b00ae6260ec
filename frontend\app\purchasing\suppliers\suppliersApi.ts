// API abstraction for suppliers module
import axios from 'axios';
import { getAxiosAuthHeaders } from '@/utils/authHeaders';
import { Supplier, CreateSupplierDto, UpdateSupplierDto } from './types';

const BASE_URL = '/api/suppliers';

// Get all suppliers (returns all non-deleted suppliers)
export async function getSuppliers() {
  const res = await axios.get<Supplier[]>(BASE_URL, {
    headers: getAxiosAuthHeaders(),
  });
  return res.data;
}

// Filter suppliers by warehouseUuid and/or name
export async function filterSuppliers(params: { warehouseUuid?: string; name?: string }) {
  const res = await axios.get<Supplier[]>(`${BASE_URL}/filter`, { 
    params,
    headers: getAxiosAuthHeaders(),
  });
  return res.data;
}

// Get supplier by UUID
export async function getSupplier(uuid: string) {
  const res = await axios.get<Supplier>(`${BASE_URL}/${uuid}`, {
    headers: getAxiosAuthHeaders(),
  });
  return res.data;
}

// Create supplier
export async function createSupplier(data: CreateSupplierDto & { warehouseUuid: string }) {
  const res = await axios.post<Supplier>(BASE_URL, data, {
    headers: getAxiosAuthHeaders(),
  });
  return res.data;
}

// Update supplier
export async function updateSupplier(uuid: string, data: UpdateSupplierDto & { warehouseUuid: string }) {
  const res = await axios.patch<Supplier>(`${BASE_URL}/${uuid}`, data, {
    headers: getAxiosAuthHeaders(),
  });
  return res.data;
}

// Delete supplier (soft delete)
export async function deleteSupplier(uuid: string) {
  const res = await axios.delete(`${BASE_URL}/${uuid}`, {
    headers: getAxiosAuthHeaders(),
  });
  return res.data;
}
