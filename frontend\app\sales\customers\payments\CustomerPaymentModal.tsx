// CustomerPaymentModal.tsx - Modal for adding/editing customer payments
// Follows UI Guidelines: Modal UX Conventions (All Resource Modals)
// - Escape key closes modal
// - First field auto-focused when modal opens
// - Form validation and error handling
// - Convention: inject warehouseUuid and userUuid from parent component (AuthContext)

import React, { useState, useEffect, useRef } from 'react';
import { useAuth } from "@/contexts/AuthContext";
import { useCustomers } from '../useCustomers';
import { useCustomerPaymentActions, useCustomerCredit } from '../useCustomerPayments';
import { 
  CustomerPayment, 
  CreateCustomerPaymentDto, 
  UpdateCustomerPaymentDto,
  PaymentMethod,
  PaymentStatus,
  formatPaymentMethod,
  formatCurrency,
  formatCustomerType
} from '../customerPaymentsApi';
import { X, DollarSign, AlertCircle } from 'lucide-react';
import { toast } from 'sonner';

interface CustomerPaymentModalProps {
  isOpen: boolean;
  onClose: () => void;
  initialData?: CustomerPayment | null;
  error?: string | null;
  isLoading?: boolean;
}

const CustomerPaymentModal: React.FC<CustomerPaymentModalProps> = ({
  isOpen,
  onClose,
  initialData,
  error: externalError,
  isLoading: externalLoading = false,
}) => {
  const { user } = useAuth();
  const warehouseUuid = user?.warehouseUuid;
  const userUuid = user?.uuid;
  
  const modalRef = useRef<HTMLDivElement>(null);
  const firstInputRef = useRef<HTMLSelectElement>(null);
  
  // Local state
  const [formData, setFormData] = useState({
    customerUuid: '',
    paymentMethod: 'cash' as PaymentMethod,
    amount: '',
    description: '',
    referenceNumber: '',
    saleUuid: '',
    status: 'pending' as PaymentStatus,
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Hooks
  const { createPayment, updatePayment } = useCustomerPaymentActions();
  
  // Fetch customers for selection (backend limit is 100)
  const { data: customersResponse } = useCustomers(
    { page: 1, limit: 100 },
    { warehouseUuid: warehouseUuid || undefined }
  );
  const customers = customersResponse?.data || [];

  // Fetch customer credit information when a customer is selected
  const { data: customerCredit } = useCustomerCredit(formData.customerUuid || undefined);

  // Find selected customer for additional display info
  const selectedCustomer = customers.find(c => c.uuid === formData.customerUuid);

  const isEditMode = !!initialData;

  // Initialize form data
  useEffect(() => {
    if (isOpen) {
      if (initialData) {
        // Edit mode - populate with existing data
        setFormData({
          customerUuid: initialData.customerUuid,
          paymentMethod: initialData.paymentMethod,
          amount: initialData.amount.toString(),
          description: initialData.description || '',
          referenceNumber: initialData.referenceNumber || '',
          saleUuid: initialData.saleUuid || '',
          status: initialData.status,
        });
      } else {
        // Add mode - reset form
        setFormData({
          customerUuid: '',
          paymentMethod: 'cash',
          amount: '',
          description: '',
          referenceNumber: '',
          saleUuid: '',
          status: 'pending',
        });
      }
      setErrors({});
    }
  }, [isOpen, initialData]);

  // Handle modal focus and escape key
  useEffect(() => {
    if (!isOpen) return;

    // Auto-focus first field when modal opens
    const timer = setTimeout(() => {
      firstInputRef.current?.focus();
    }, 100);

    // Handle escape key
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => {
      clearTimeout(timer);
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [isOpen, onClose]);

  // Form validation
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.customerUuid.trim()) {
      newErrors.customerUuid = 'Customer is required';
    }

    if (!formData.amount.trim()) {
      newErrors.amount = 'Amount is required';
    } else {
      const amount = parseFloat(formData.amount);
      if (isNaN(amount) || amount <= 0) {
        newErrors.amount = 'Amount must be a positive number';
      }
    }

    if (!formData.paymentMethod) {
      newErrors.paymentMethod = 'Payment method is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    if (!warehouseUuid || !userUuid) {
      toast.error('User information not available');
      return;
    }

    setIsSubmitting(true);

    try {
      if (isEditMode && initialData) {
        // Update existing payment
        const updateDto: UpdateCustomerPaymentDto = {
          amount: parseFloat(formData.amount),
          description: formData.description.trim() || undefined,
          referenceNumber: formData.referenceNumber.trim() || undefined,
          status: formData.status,
          userUuid,
        };

        await updatePayment({
          uuid: initialData.uuid,
          data: updateDto
        });
        
        toast.success('Payment updated successfully');
      } else {
        // Create new payment
        const createDto: CreateCustomerPaymentDto = {
          customerUuid: formData.customerUuid,
          userUuid,
          warehouseUuid,
          paymentMethod: formData.paymentMethod,
          amount: parseFloat(formData.amount),
          description: formData.description.trim() || undefined,
          referenceNumber: formData.referenceNumber.trim() || undefined,
          saleUuid: formData.saleUuid.trim() || undefined,
        };

        await createPayment(createDto);
        
        toast.success('Payment created successfully');
      }

      onClose();
    } catch (error: any) {
      console.error('Failed to save payment:', error);
      const errorMessage = error.response?.data?.message || error.message || 'Failed to save payment';
      toast.error(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle input changes
  const handleInputChange = (field: keyof typeof formData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  if (!isOpen) return null;

  const loading = isSubmitting || externalLoading;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div
        ref={modalRef}
        className="bg-white rounded-lg shadow-xl w-full max-w-md mx-4"
        role="dialog"
        aria-modal="true"
        aria-labelledby="payment-modal-title"
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <DollarSign className="h-6 w-6 text-green-600" />
            <h2 id="payment-modal-title" className="text-lg font-semibold text-gray-900">
              {isEditMode ? 'Edit Payment' : 'Add New Payment'}
            </h2>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 rounded-full p-1"
            disabled={loading}
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-4">
          {/* Error display */}
          {externalError && (
            <div className="bg-red-50 border border-red-200 rounded-md p-3">
              <div className="flex items-center">
                <AlertCircle className="h-4 w-4 text-red-400 mr-2" />
                <span className="text-sm text-red-600">{externalError}</span>
              </div>
            </div>
          )}

          {/* Customer Selection */}
          <div>
            <label htmlFor="customerUuid" className="block text-sm font-medium text-gray-700 mb-1">
              Customer *
            </label>
            <select
              ref={firstInputRef}
              id="customerUuid"
              value={formData.customerUuid}
              onChange={(e) => handleInputChange('customerUuid', e.target.value)}
              disabled={loading || isEditMode} // Don't allow changing customer in edit mode
              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                errors.customerUuid ? 'border-red-300' : 'border-gray-300'
              } ${loading || isEditMode ? 'bg-gray-100' : ''}`}
            >
              <option value="">Select a customer</option>
              {customers.map((customer) => (
                <option key={customer.uuid} value={customer.uuid}>
                  {customer.name} - {formatCustomerType(customer.customerType)} - Credit: {formatCurrency(customer.currentCredit)}
                </option>
              ))}
            </select>
            {errors.customerUuid && (
              <p className="mt-1 text-sm text-red-600">{errors.customerUuid}</p>
            )}
            
            {/* Customer Details Card */}
            {selectedCustomer && (
              <div className="mt-3 p-3 bg-blue-50 border border-blue-200 rounded-md">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium text-blue-900">{selectedCustomer.name}</h4>
                    <p className="text-sm text-blue-700">
                      {formatCustomerType(selectedCustomer.customerType)} • {selectedCustomer.fiscalId}
                    </p>
                    {selectedCustomer.email && (
                      <p className="text-sm text-blue-600">{selectedCustomer.email}</p>
                    )}
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium text-blue-900">Current Credit</p>
                    <p className={`text-lg font-bold ${
                      selectedCustomer.currentCredit >= 0 ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {formatCurrency(selectedCustomer.currentCredit)}
                    </p>
                    {customerCredit && customerCredit.currentCredit !== selectedCustomer.currentCredit && (
                      <p className="text-xs text-blue-600">
                        Live: {formatCurrency(customerCredit.currentCredit)}
                      </p>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Amount */}
          <div>
            <label htmlFor="amount" className="block text-sm font-medium text-gray-700 mb-1">
              Amount *
            </label>
            <input
              type="number"
              step="0.01"
              id="amount"
              value={formData.amount}
              onChange={(e) => handleInputChange('amount', e.target.value)}
              disabled={loading}
              placeholder="0.00"
              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                errors.amount ? 'border-red-300' : 'border-gray-300'
              }`}
            />
            {errors.amount && (
              <p className="mt-1 text-sm text-red-600">{errors.amount}</p>
            )}
          </div>

          {/* Payment Method */}
          <div>
            <label htmlFor="paymentMethod" className="block text-sm font-medium text-gray-700 mb-1">
              Payment Method *
            </label>
            <select
              id="paymentMethod"
              value={formData.paymentMethod}
              onChange={(e) => handleInputChange('paymentMethod', e.target.value)}
              disabled={loading}
              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                errors.paymentMethod ? 'border-red-300' : 'border-gray-300'
              }`}
            >
              <option value="cash">Cash</option>
              <option value="credit_card">Credit Card</option>
              <option value="debit_card">Debit Card</option>
              <option value="bank_transfer">Bank Transfer</option>
              <option value="check">Check</option>
              <option value="mobile_payment">Mobile Payment</option>
            </select>
            {errors.paymentMethod && (
              <p className="mt-1 text-sm text-red-600">{errors.paymentMethod}</p>
            )}
          </div>

          {/* Description */}
          <div>
            <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
              Description
            </label>
            <textarea
              id="description"
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              disabled={loading}
              placeholder="Enter payment description..."
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Reference Number */}
          <div>
            <label htmlFor="referenceNumber" className="block text-sm font-medium text-gray-700 mb-1">
              Reference Number
            </label>
            <input
              type="text"
              id="referenceNumber"
              value={formData.referenceNumber}
              onChange={(e) => handleInputChange('referenceNumber', e.target.value)}
              disabled={loading}
              placeholder="Transaction reference, check number, etc."
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Sale UUID (optional) */}
          <div>
            <label htmlFor="saleUuid" className="block text-sm font-medium text-gray-700 mb-1">
              Sale UUID (Optional)
            </label>
            <input
              type="text"
              id="saleUuid"
              value={formData.saleUuid}
              onChange={(e) => handleInputChange('saleUuid', e.target.value)}
              disabled={loading}
              placeholder="Link to specific sale/invoice"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Status (only in edit mode) */}
          {isEditMode && (
            <div>
              <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">
                Status
              </label>
              <select
                id="status"
                value={formData.status}
                onChange={(e) => handleInputChange('status', e.target.value as PaymentStatus)}
                disabled={loading}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="pending">Pending</option>
                <option value="completed">Completed</option>
                <option value="failed">Failed</option>
                <option value="cancelled">Cancelled</option>
                <option value="refunded">Refunded</option>
              </select>
            </div>
          )}

          {/* Buttons */}
          <div className="flex space-x-3 pt-4">
            <button
              type="submit"
              disabled={loading}
              className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
            >
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  {isEditMode ? 'Updating...' : 'Creating...'}
                </>
              ) : (
                isEditMode ? 'Update Payment' : 'Create Payment'
              )}
            </button>
            <button
              type="button"
              onClick={onClose}
              disabled={loading}
              className="flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Cancel
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default CustomerPaymentModal; 