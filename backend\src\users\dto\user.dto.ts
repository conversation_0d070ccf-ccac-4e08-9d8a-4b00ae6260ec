// DTO utility for User output
import { User } from "../user.entity";
import { mapEntityIdToUuid, mapUuidFieldToString } from "../../common/dto-utils";

export interface UserDto {
  uuid: string; // from virtual
  warehouseUuidString: string; // from virtual
  vanUuidString?: string; // from virtual
  email?: string;
  name: string;
  roleUuidString?: string; // UUID of the role as string (virtual)
  userType: "super" | "user";
  isDeleted: boolean;
  /**
   * @internal
   * Only for internal use (e.g., authentication). Do NOT expose to frontend APIs!
   */
  password?: string;
  // add other fields as needed, but do NOT include _id, warehouseUuid (Buffer)
}

export function toUserDto(user: any): UserDto {
  // Accepts a TypeORM entity or plain object
  // Always include warehouseUuidString, even if undefined
  return {
    uuid: mapEntityIdToUuid(user),
    warehouseUuidString: mapUuidFieldToString(user, 'warehouseUuid'),
    vanUuidString: mapUuidFieldToString(user, 'vanUuid'),
    email: user.email ?? undefined,
    name: user.name,
    roleUuidString: mapUuidFieldToString(user, 'roleUuid'),
    userType: user.userType,
    isDeleted: user.isDeleted,
    password: user.password, // Only present if queried, not exposed to frontend
  };
}

export function toUserDtoArray(users: any[]): UserDto[] {
  return users.map(toUserDto);
}
