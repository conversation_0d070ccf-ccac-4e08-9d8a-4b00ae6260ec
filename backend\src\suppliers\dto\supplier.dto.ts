import { ApiProperty } from "@nestjs/swagger";

export class SupplierDto {
  @ApiProperty({ example: "018e6b7c-9c2a-7b73-bb6d-0d1e8e2f4b1a" })
  uuid: string;

  @ApiProperty({
    example: "018e6b7c-9c2a-7b73-bb6d-0d1e8e2f4b1a",
    required: false,
  })
  warehouseUuid?: string;

  @ApiProperty({
    example: "018e6b7c-9c2a-7b73-bb6d-0d1e8e2f4b1a",
    description: "The UUID of the user who created/owns this supplier",
  })
  userUuid: string;

  @ApiProperty({ example: "Acme Supplies Ltd." })
  name: string;

  @ApiProperty({
    example: "12345678901",
    description: "Fiscal identification number (tax ID)",
    required: false,
  })
  fiscalId?: string;

  @ApiProperty({
    example: "RC123456789",
    description: "Registration Certificate number",
    required: false,
  })
  rc?: string;

  @ApiProperty({
    example: "ART-001-2024",
    description: "Article Number assigned by the supplier",
    required: false,
  })
  articleNumber?: string;

  @ApiProperty({
    example: "SUP-001",
    description: "Supplier code",
    required: false,
  })
  code?: string;

  @ApiProperty({
    example: "Premium supplier for electronics",
    description: "Supplier description",
    required: false,
  })
  description?: string;

  @ApiProperty({ example: "<EMAIL>", required: false })
  email?: string;

  @ApiProperty({ example: "+1234567890", required: false })
  phone?: string;

  @ApiProperty({ example: "123 Main St, City, Country", required: false })
  address?: string;

  @ApiProperty({
    example: 40.7128,
    description: "Latitude coordinate for supplier location",
    required: false,
  })
  latitude?: number;

  @ApiProperty({
    example: -74.006,
    description: "Longitude coordinate for supplier location",
    required: false,
  })
  longitude?: number;

  @ApiProperty({ example: "Supplier notes or remarks.", required: false })
  notes?: string;

  @ApiProperty({ example: false, description: "Soft delete flag" })
  isDeleted: boolean;

  @ApiProperty({ example: "2025-06-28T18:26:05.697Z" })
  createdAt: string;

  @ApiProperty({ example: "2025-06-28T18:26:05.697Z" })
  updatedAt: string;
}

export function toSupplierDto(entity: any): SupplierDto {
  return {
    uuid: entity.id || entity.uuid,
    warehouseUuid: entity.warehouseUuid,
    userUuid: entity.userUuid,
    name: entity.name,
    fiscalId: entity.fiscalId,
    rc: entity.rc,
    articleNumber: entity.articleNumber,
    code: entity.code,
    description: entity.description,
    email: entity.email,
    phone: entity.phone,
    address: entity.address,
    latitude: entity.latitude,
    longitude: entity.longitude,
    notes: entity.notes,
    isDeleted: entity.isDeleted,
    createdAt: entity.createdAt,
    updatedAt: entity.updatedAt,
  };
}

export function toSupplierDtoArray(entities: any[]): SupplierDto[] {
  return entities.map(toSupplierDto);
}
