import { Test, TestingModule } from "@nestjs/testing";
import { ProductsService } from "../src/products/products.service.typeorm";
import { getRepositoryToken } from "@nestjs/typeorm";
import { Product } from "../src/products/product.entity";
import { Uuid7 } from "../src/utils/uuid7";

describe("ProductsService - Update", () => {
  let service: ProductsService;
  let mockProductRepository: any;

  beforeEach(async () => {
    // Create mock for Product repository
    mockProductRepository = {
      findOne: jest.fn(),
      save: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ProductsService,
        {
          provide: getRepositoryToken(Product),
          useValue: mockProductRepository,
        },
      ],
    }).compile();

    service = module.get<ProductsService>(ProductsService);
  });

  describe("update", () => {
    it("should update product with valid productCategoryUuid", async () => {
      // Arrange
      const productUuid = new Uuid7().toString();
      const productCategoryUuid = new Uuid7().toString();
      const updateData = {
        name: "Updated Product",
        productCategoryUuid: productCategoryUuid,
      };

      const mockExistingProduct = {
        id: productUuid,
        name: "Original Product",
        productCategoryUuid: "old-category-uuid",
      };

      const mockUpdatedProduct = {
        id: productUuid,
        name: "Updated Product",
        productCategoryUuid: productCategoryUuid,
      };

      mockProductRepository.findOne.mockResolvedValue(mockExistingProduct);
      mockProductRepository.save.mockResolvedValue(mockUpdatedProduct);

      // Act
      const result = await service.update(productUuid, updateData);

      // Assert
      expect(mockProductRepository.findOne).toHaveBeenCalledWith({
        where: { id: productUuid, isDeleted: false },
      });
      expect(mockProductRepository.save).toHaveBeenCalledWith(
        expect.objectContaining({
          id: productUuid,
          name: "Updated Product",
          productCategoryUuid: productCategoryUuid,
        }),
      );
      expect(result).toEqual(mockUpdatedProduct);
    });

    it("should throw error for invalid product UUID", async () => {
      // Arrange
      const invalidProductUuid = "invalid-uuid";
      const updateData = { name: "Updated Product" };

      // Act & Assert
      await expect(
        service.update(invalidProductUuid, updateData),
      ).rejects.toThrow("Product with UUID invalid-uuid not found");
    });

    it("should throw error for invalid productCategoryUuid", async () => {
      // Arrange
      const productUuid = new Uuid7().toString();
      const invalidProductCategoryUuid = "invalid-uuid";
      const updateData = {
        name: "Updated Product",
        productCategoryUuid: invalidProductCategoryUuid,
      };

      // Act & Assert
      await expect(service.update(productUuid, updateData)).rejects.toThrow(
        `Product with UUID ${productUuid} not found`,
      );
    });

    it("should handle update without productCategoryUuid", async () => {
      // Arrange
      const productUuid = new Uuid7().toString();
      const updateData = {
        name: "Updated Product",
        price: 25.99,
      };

      const mockExistingProduct = {
        id: productUuid,
        name: "Original Product",
        price: 10.99,
      };

      const mockUpdatedProduct = {
        id: productUuid,
        name: "Updated Product",
        price: 25.99,
      };

      mockProductRepository.findOne.mockResolvedValue(mockExistingProduct);
      mockProductRepository.save.mockResolvedValue(mockUpdatedProduct);

      // Act
      const result = await service.update(productUuid, updateData);

      // Assert
      expect(mockProductRepository.findOne).toHaveBeenCalledWith({
        where: { id: productUuid, isDeleted: false },
      });
      expect(mockProductRepository.save).toHaveBeenCalledWith(
        expect.objectContaining({
          id: productUuid,
          name: "Updated Product",
          price: 25.99,
        }),
      );
      expect(result).toEqual(mockUpdatedProduct);
    });

    it("should handle database errors gracefully", async () => {
      // Arrange
      const productUuid = new Uuid7().toString();
      const updateData = { name: "Updated Product" };

      mockProductRepository.findOne.mockRejectedValue(
        new Error("Database error"),
      );

      // Act & Assert
      await expect(service.update(productUuid, updateData)).rejects.toThrow(
        "Database error",
      );
    });
  });
});
