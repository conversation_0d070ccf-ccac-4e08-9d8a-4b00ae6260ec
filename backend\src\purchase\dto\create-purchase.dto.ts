import {
  IsString,
  IsNotEmpty,
  IsOptional,
  IsArray,
  ValidateNested,
  IsNumber,
  IsUUID,
} from "class-validator";
import { Type } from "class-transformer";
import { ApiProperty } from "@nestjs/swagger";

export class PurchaseItemDto {
  @ApiProperty({
    example: "uuid-v7-string",
    description: "UUID of the product",
  })
  @IsString()
  @IsNotEmpty()
  @IsUUID("all")
  productUuid: string;

  @ApiProperty({ example: "Product Name", description: "Name of the product" })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({ example: 5, description: "Quantity purchased" })
  @IsNumber()
  quantity: number;

  @ApiProperty({ example: 9.99, description: "Unit price of the product" })
  @IsNumber()
  unitPrice: number;

  @ApiProperty({
    example: 49.95,
    description: "Line total (quantity * unit price)",
  })
  @IsNumber()
  lineTotal: number;
}

export class CreatePurchaseDto {
  @ApiProperty({
    example: "uuid-v7-string",
    description: "UUID of the user creating the purchase",
  })
  @IsString()
  @IsNotEmpty()
  @IsUUID("all")
  userUuid: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "UUID of the warehouse",
  })
  @IsString()
  @IsNotEmpty()
  @IsUUID("all")
  warehouseUuid: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "UUID of the supplier",
    required: false,
  })
  @IsOptional()
  @IsString()
  @IsUUID("all")
  supplierUuid?: string;

  @ApiProperty({
    type: [PurchaseItemDto],
    description: "Array of items in the purchase",
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => PurchaseItemDto)
  items: PurchaseItemDto[];
}
