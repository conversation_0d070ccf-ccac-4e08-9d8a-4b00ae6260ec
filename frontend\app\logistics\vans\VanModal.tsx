import React, { useRef, useEffect, useMemo } from "react";
import Van<PERSON><PERSON>, { VanFormValues } from "./VanForm";
import { VanD<PERSON> } from "./vansApi";

interface VanModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (values: VanFormValues) => Promise<void>;
  van?: VanDto | null;
  isLoading?: boolean;
  error?: string | null;
}

const VanModal: React.FC<VanModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
  van = null,
  isLoading = false,
  error = null,
}) => {
  const modalRef = useRef<HTMLDivElement>(null);

  // Modal accessibility and keyboard navigation
  useEffect(() => {
    if (!isOpen) return;

    // Focus the first input when modal opens
    const focusFirstInput = () => {
      const input = modalRef.current?.querySelector('input, textarea, select') as HTMLElement;
      if (input) {
        input.focus();
      }
    };

    // Handle escape key and focus trap
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === "Escape") {
        onClose();
        return;
      }

      // Focus trap
      if (e.key === "Tab" && modalRef.current) {
        const focusableElements = modalRef.current.querySelectorAll<HTMLElement>(
          'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
        );
        const firstElement = focusableElements[0];
        const lastElement = focusableElements[focusableElements.length - 1];

        if (!e.shiftKey && document.activeElement === lastElement) {
          e.preventDefault();
          firstElement.focus();
        } else if (e.shiftKey && document.activeElement === firstElement) {
          e.preventDefault();
          lastElement.focus();
        }
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    
    // Focus first input after modal animation
    setTimeout(focusFirstInput, 100);

    return () => {
      window.removeEventListener("keydown", handleKeyDown);
    };
  }, [isOpen, onClose]);

  const initialValues: VanFormValues = useMemo(() => van
    ? {
        name: van.name,
        licensePlate: van.licensePlate || "",
        model: van.model || "",
        year: van.year || undefined,
      }
    : {
        name: "",
        licensePlate: "",
        model: "",
        year: undefined,
      }, [van]);

  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-50 animate-fadeIn"
      role="dialog"
      aria-modal="true"
      aria-labelledby="van-modal-title"
      onClick={(e) => {
        // Close modal when clicking outside
        if (e.target === e.currentTarget) {
          onClose();
        }
      }}
    >
      <div
        ref={modalRef}
        className="bg-white rounded-lg shadow-xl p-6 w-full max-w-md mx-4 max-h-[90vh] overflow-y-auto"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Modal Header */}
        <div className="mb-4">
          <h2 id="van-modal-title" className="text-xl font-semibold text-gray-900">
            {van ? "Edit Van" : "Add Van"}
          </h2>
          <p className="text-sm text-gray-600 mt-1">
            {van ? "Update the van information below." : "Fill in the van details below. Only name is required."}
          </p>
        </div>

        {/* Van Form */}
        <VanForm
          initialValues={initialValues}
          onSubmit={onSubmit}
          onCancel={onClose}
          isLoading={isLoading}
          error={error}
        />
      </div>
    </div>
  );
};

export default VanModal; 