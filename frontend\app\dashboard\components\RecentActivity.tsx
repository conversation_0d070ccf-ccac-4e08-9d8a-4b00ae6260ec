import React from 'react';
import { SecurityEvent } from '../dashboardApi';

interface RecentActivityProps {
  events: SecurityEvent[];
  isLoading?: boolean;
}

const getEventIcon = (eventType: string) => {
  switch (eventType) {
    case 'LOGIN_SUCCESS':
      return '✅';
    case 'LOGIN_FAILURE':
      return '❌';
    case 'LOGOUT':
      return '🚪';
    case 'ACCOUNT_LOCKED':
      return '🔒';
    case 'ACCOUNT_UNLOCKED':
      return '🔓';
    case 'PASSWORD_CHANGE':
      return '🔑';
    case 'GOOGLE_LOGIN_SUCCESS':
      return '🌐';
    case 'GOOGLE_LOGIN_FAILURE':
      return '🌐❌';
    case 'RATE_LIMIT_EXCEEDED':
      return '⏰';
    case 'SUSPICIOUS_ACTIVITY':
      return '⚠️';
    default:
      return '📝';
  }
};

const getEventColor = (eventType: string, success: boolean) => {
  if (!success) return 'text-red-600 bg-red-50';
  
  switch (eventType) {
    case 'LOGIN_SUCCESS':
    case 'GOOGLE_LOGIN_SUCCESS':
      return 'text-green-600 bg-green-50';
    case 'LOGOUT':
    case 'PASSWORD_CHANGE':
      return 'text-blue-600 bg-blue-50';
    case 'ACCOUNT_UNLOCKED':
      return 'text-green-600 bg-green-50';
    case 'ACCOUNT_LOCKED':
    case 'RATE_LIMIT_EXCEEDED':
    case 'SUSPICIOUS_ACTIVITY':
      return 'text-orange-600 bg-orange-50';
    default:
      return 'text-gray-600 bg-gray-50';
  }
};

const formatEventType = (eventType: string) => {
  return eventType
    .toLowerCase()
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
};

const formatTimestamp = (timestamp: string) => {
  const date = new Date(timestamp);
  const now = new Date();
  const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);
  
  if (diffInHours < 1) {
    const diffInMinutes = Math.floor(diffInHours * 60);
    return `${diffInMinutes} minute${diffInMinutes !== 1 ? 's' : ''} ago`;
  } else if (diffInHours < 24) {
    const hours = Math.floor(diffInHours);
    return `${hours} hour${hours !== 1 ? 's' : ''} ago`;
  } else {
    return date.toLocaleDateString();
  }
};

export const RecentActivity: React.FC<RecentActivityProps> = ({ events, isLoading }) => {
  if (isLoading) {
    return (
      <div className="bg-white rounded-xl shadow-lg border border-gray-200 p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Recent Activity</h2>
        <div className="space-y-4">
          {[1, 2, 3].map((i) => (
            <div key={i} className="flex items-center space-x-3 animate-pulse">
              <div className="w-8 h-8 bg-gray-200 rounded-full"></div>
              <div className="flex-1">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (!events || events.length === 0) {
    return (
      <div className="bg-white rounded-xl shadow-lg border border-gray-200 p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Recent Activity</h2>
        <div className="text-center py-8">
          <div className="text-gray-400 text-6xl mb-4">📝</div>
          <p className="text-gray-500">No recent activity to display</p>
          <p className="text-sm text-gray-400 mt-1">Security events and system activities will appear here</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-xl shadow-lg border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-xl font-semibold text-gray-900">Recent Activity</h2>
        <span className="text-sm text-gray-500">
          {events.length} event{events.length !== 1 ? 's' : ''}
        </span>
      </div>
      
      <div className="space-y-3">
        {events.slice(0, 10).map((event, index) => (
          <div key={index} className="flex items-start space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors">
            <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm ${getEventColor(event.eventType, event.success)}`}>
              {getEventIcon(event.eventType)}
            </div>
            <div className="flex-1 min-w-0">
              <div className="flex items-center justify-between">
                <p className="text-sm font-medium text-gray-900">
                  {formatEventType(event.eventType)}
                </p>
                <span className="text-xs text-gray-500">
                  {formatTimestamp(event.timestamp)}
                </span>
              </div>
              {event.ipAddress && (
                <p className="text-xs text-gray-500 mt-1">
                  IP: {event.ipAddress}
                </p>
              )}
            </div>
          </div>
        ))}
      </div>
      
      {events.length > 10 && (
        <div className="mt-4 pt-4 border-t border-gray-200">
          <p className="text-sm text-gray-500 text-center">
            Showing 10 of {events.length} events
          </p>
        </div>
      )}
    </div>
  );
}; 