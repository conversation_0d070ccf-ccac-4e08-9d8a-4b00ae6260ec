import 'package:flutter/foundation.dart';
import 'api_service.dart';
import '../models/user.dart';
import '../models/role.dart';

/// Service to handle user-related API calls
class UserService {
  factory UserService() => _instance;
  UserService._internal();
  static final UserService _instance = UserService._internal();

  final ApiService _apiService = ApiService();

  /// Fetch user by UUID
  Future<User?> getUserByUuid(String uuid) async {
    try {
      debugPrint('🔵 USER SERVICE: Fetching user with UUID: $uuid');
      final response = await _apiService.get('/users/$uuid');
      
      if (response.statusCode == 200) {
        debugPrint('🟢 USER SERVICE: Successfully fetched user data');
        debugPrint('🟢 USER SERVICE: User data: ${response.data}');
        
        // Transform backend user data to mobile user data
        final transformedUser = _transformBackendUserToMobileUser(response.data);
        return User.from<PERSON>son(transformedUser);
      }
      
      debugPrint('🔴 USER SERVICE: Failed to fetch user - Status: ${response.statusCode}');
      return null;
    } catch (e) {
      debugPrint('🔴 USER SERVICE: Error fetching user: $e');
      return null;
    }
  }

  /// Fetch user role by user UUID
  Future<Role?> getUserRole(String userUuid) async {
    try {
      debugPrint('🔵 USER SERVICE: Fetching role for user UUID: $userUuid');
      final response = await _apiService.get('/users/$userUuid/role');
      
      if (response.statusCode == 200) {
        debugPrint('🟢 USER SERVICE: Successfully fetched user role');
        debugPrint('🟢 USER SERVICE: Role data: ${response.data}');
        
        // Transform backend role data to mobile role data
        final transformedRole = _transformBackendRoleToMobileRole(response.data);
        return Role.fromJson(transformedRole);
      }
      
      debugPrint('🔴 USER SERVICE: Failed to fetch user role - Status: ${response.statusCode}');
      return null;
    } catch (e) {
      debugPrint('🔴 USER SERVICE: Error fetching user role: $e');
      return null;
    }
  }

  /// Fetch role by UUID
  Future<Role?> getRoleByUuid(String roleUuid) async {
    try {
      debugPrint('🔵 USER SERVICE: Fetching role with UUID: $roleUuid');
      final response = await _apiService.get('/users/roles/$roleUuid');
      
      if (response.statusCode == 200) {
        debugPrint('🟢 USER SERVICE: Successfully fetched role data');
        debugPrint('🟢 USER SERVICE: Role data: ${response.data}');
        
        // Transform backend role data to mobile role data
        final transformedRole = _transformBackendRoleToMobileRole(response.data);
        return Role.fromJson(transformedRole);
      }
      
      debugPrint('🔴 USER SERVICE: Failed to fetch role - Status: ${response.statusCode}');
      return null;
    } catch (e) {
      debugPrint('🔴 USER SERVICE: Error fetching role: $e');
      return null;
    }
  }

  /// Get all available permissions
  Future<List<String>> getAllPermissions() async {
    try {
      debugPrint('🔵 USER SERVICE: Fetching all available permissions');
      final response = await _apiService.get('/users/roles/permissions');
      
      if (response.statusCode == 200) {
        debugPrint('🟢 USER SERVICE: Successfully fetched permissions');
        debugPrint('🟢 USER SERVICE: Permissions: ${response.data}');
        
        // Backend returns array of permission strings
        return List<String>.from(response.data);
      }
      
      debugPrint('🔴 USER SERVICE: Failed to fetch permissions - Status: ${response.statusCode}');
      return [];
    } catch (e) {
      debugPrint('🔴 USER SERVICE: Error fetching permissions: $e');
      return [];
    }
  }

  /// Transform backend user data to mobile user format
  Map<String, dynamic> _transformBackendUserToMobileUser(Map<String, dynamic> backendUser) {
    // Backend returns: { uuid, warehouseUuidString, email, name, roleUuid, userType, isDeleted }
    // Mobile expects: { uuid, email, firstName, lastName, phone, isActive, roles, warehouseUuid, vanUuid, createdAt, updatedAt }
    
    // Split name into firstName and lastName
    final String fullName = backendUser['name'] ?? '';
    final nameParts = fullName.split(' ');
    final firstName = nameParts.isNotEmpty ? nameParts.first : '';
    final lastName = nameParts.length > 1 ? nameParts.skip(1).join(' ') : '';
    
    return {
      'uuid': backendUser['uuid'],
      'email': backendUser['email'],
      'firstName': firstName,
      'lastName': lastName,
      'phone': null, // Backend doesn't provide phone
      'isActive': !(backendUser['isDeleted'] ?? false),
      'roles': [], // Will be populated separately
      'warehouseUuid': backendUser['warehouseUuidString'],
      'vanUuid': null, // Backend may provide this in the future
      'createdAt': DateTime.now().toIso8601String(), // Placeholder
      'updatedAt': DateTime.now().toIso8601String(), // Placeholder
    };
  }

  /// Transform backend role data to mobile role format
  Map<String, dynamic> _transformBackendRoleToMobileRole(Map<String, dynamic> backendRole) {
    // Backend returns: { uuid, name, permissions, warehouseUuid, isDeleted, createdAt, updatedAt }
    // Mobile expects: { uuid, name, permissions, warehouseUuid, isDeleted, createdAt, updatedAt }
    
    return {
      'uuid': backendRole['uuid'],
      'name': backendRole['name'],
      'permissions': List<String>.from(backendRole['permissions'] ?? []),
      'warehouseUuid': backendRole['warehouseUuid'],
      'isDeleted': backendRole['isDeleted'] ?? false,
      'createdAt': backendRole['createdAt'] != null 
          ? DateTime.parse(backendRole['createdAt']).toIso8601String()
          : DateTime.now().toIso8601String(),
      'updatedAt': backendRole['updatedAt'] != null 
          ? DateTime.parse(backendRole['updatedAt']).toIso8601String()
          : DateTime.now().toIso8601String(),
    };
  }

  /// Enhanced user data fetch with role information
  Future<User?> getUserWithRole(String userUuid) async {
    try {
      debugPrint('🔵 USER SERVICE: Fetching user with role for UUID: $userUuid');
      
      // Fetch user data first
      final user = await getUserByUuid(userUuid);
      if (user == null) {
        debugPrint('🔴 USER SERVICE: Failed to fetch user data');
        return null;
      }
      
      // Use the correct backend endpoint: /users/:uuid/role
      // This endpoint is specifically designed to get the role for a user
      debugPrint('🔵 USER SERVICE: Fetching role using /users/$userUuid/role endpoint');
      
      final role = await getUserRole(userUuid);
      if (role == null) {
        debugPrint('🔴 USER SERVICE: Failed to fetch user role from /users/$userUuid/role');
        return user; // Return user without role data
      }
      
      // Create user with role data
      final userWithRole = User(
        uuid: user.uuid,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        phone: user.phone,
        isActive: user.isActive,
        roles: [role], // Add the fetched role
        warehouseUuid: user.warehouseUuid,
        vanUuid: user.vanUuid,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
      );
      
      debugPrint('🟢 USER SERVICE: Successfully created user with role');
      debugPrint('🟢 USER SERVICE: User role: ${role.name}');
      debugPrint('🟢 USER SERVICE: User permissions: ${role.permissions}');
      
      return userWithRole;
    } catch (e) {
      debugPrint('🔴 USER SERVICE: Error fetching user with role: $e');
      return null;
    }
  }
} 