import { <PERSON><PERSON>ty, PrimaryColumn, Column, CreateDateColumn, UpdateDateColumn, Index } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Uuid7 } from '../utils/uuid7';

@Entity('inventory_items')
export class InventoryItem {
  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the inventory item (primary key)",
  })
  @PrimaryColumn('uuid')
  id: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the product",
  })
  @Column('uuid')
  @Index()
  productUuid: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the storage location",
  })
  @Column('uuid')
  @Index()
  storageUuid: string;

  @ApiProperty({
    example: 100,
    description: "Current quantity in stock",
  })
  @Column('integer', { default: 0 })
  quantity: number;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Helper method to generate UUID
  static generateId(): string {
    return new Uuid7().toString();
  }

  // Helper method to generate UUID
  static fromBinary(binary: any): string {
    if (!binary) return null;
    try {
      return new Uuid7(binary).toString();
    } catch {
      return null;
    }
  }
} 