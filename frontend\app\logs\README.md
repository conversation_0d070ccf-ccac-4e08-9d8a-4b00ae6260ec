# Delta Diff Viewer for Logs

This enhanced logs system now includes a clean and intuitive diff viewer that shows changes between before and after values in a user-friendly format.

## Features

### 1. Before/After Comparison
- Shows changes inline with visual indicators
- Removed content appears with strikethrough and red background
- Added content appears with green background and bold text
- Unchanged content appears in normal gray text
- Color-coded legend at the top for clarity

### 2. Smart Data Handling
- Automatically detects delta format in log data
- Handles complex objects and arrays
- Properly formats JSON with indentation
- Gracefully handles null values for creation/deletion

## Usage

### Backend Integration
The backend now uses `LogsUtilityService` to create delta logs:

```typescript
// Instead of storing full objects
await this.logsUtilityService.logUpdate(
  userUuid,
  'sale_123',
  'Sale updated',
  originalSale,  // before
  updatedSale,   // after
  { additionalMetadata: 'value' }
);
```

### Frontend Display
The `LogDetailsModal` automatically detects delta format and renders appropriately:

```typescript
// Delta format detection
const hasDeltaChanges = (data: Record<string, any>) => {
  return data && data.changes && typeof data.changes === 'object';
};
```

## Data Format

### Delta Log Structure
```json
{
  "changes": {
    "fieldName": {
      "before": "old value",
      "after": "new value"
    }
  },
  "changedFields": ["fieldName"],
  "changeCount": 1,
  "additionalMetadata": "..."
}
```

### Example Change Types

#### Simple Value Change
```json
{
  "status": {
    "before": "pending",
    "after": "completed"
  }
}
```

#### Object Change
```json
{
  "metadata": {
    "before": { "version": "1.0", "source": "web" },
    "after": { "version": "1.1", "source": "mobile", "notes": "Updated" }
  }
}
```

#### Creation (null to value)
```json
{
  "name": {
    "before": null,
    "after": "New Product"
  }
}
```

#### Deletion (value to null)
```json
{
  "name": {
    "before": "Deleted Product",
    "after": null
  }
}
```

## Testing

Visit `/logs/test-diff` to see the diff viewer in action with sample data including:
- Simple value changes
- Complex object modifications
- Array updates
- Creation scenarios
- Deletion scenarios

## Benefits

1. **Reduced Storage**: Only changed fields are stored, not full objects
2. **Better UX**: Clean, intuitive visual representation of what changed
3. **Easy to Read**: Inline comparison makes changes immediately obvious
4. **Audit Trail**: Complete change history without bloat
5. **Performance**: Smaller log entries mean faster queries
6. **Space Efficient**: Single-panel view saves screen real estate

## Backward Compatibility

The system gracefully handles both old and new log formats:
- New delta logs use the enhanced diff viewer
- Legacy logs fall back to the original JSON display
- No migration required for existing data
