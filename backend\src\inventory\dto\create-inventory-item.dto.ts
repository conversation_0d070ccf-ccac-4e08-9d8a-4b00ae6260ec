import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty, <PERSON>UUI<PERSON>, IsInt, Min } from "class-validator";

export class CreateInventoryItemDto {
  @ApiProperty({ example: "a3b1c2d4-5678-4e9f-8b12-123456789abc" })
  @IsUUID()
  @IsNotEmpty()
  productUuid: string;

  @ApiProperty({ example: "b2c3d4e5-6789-4f0a-9c23-987654321def" })
  @IsUUID()
  @IsNotEmpty()
  storageUuid: string;

  @ApiProperty({ example: 100 })
  @IsInt()
  @Min(0)
  quantity: number;
}
