"use client";

// CustomerMap Component
// Interactive world map component using react-leaflet to display customer locations
// Shows customer markers with detailed popups containing customer information

import React, { useEffect, useRef } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Popup, useMap } from "react-leaflet";
import { Icon, divIcon } from "leaflet";
import { Customer } from "../customersApi";
import { MapPin, Mail, Phone, MapIcon, Building, User, CreditCard, Hash, Users, Store, ShoppingBag, Building2 } from "lucide-react";
import "leaflet/dist/leaflet.css";
import "./map.css";

// Fix for default markers in react-leaflet
const defaultIcon = new Icon({
  iconUrl: "https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png",
  iconRetinaUrl: "https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png",
  shadowUrl: "https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png",
  iconSize: [25, 41],
  iconAnchor: [12, 41],
  popupAnchor: [1, -34],
  shadowSize: [41, 41]
});

// Custom marker icons based on customer type with appropriate client icons
const getCustomerIcon = (customerType: string) => {
  const customerConfig = {
    retail: { color: '#3B82F6', icon: '👤' },      // Blue with person icon
    wholesale: { color: '#10B981', icon: '🏪' },   // Green with shop icon
    'mid-wholesale': { color: '#F59E0B', icon: '🛒' }, // Yellow with shopping cart icon
    institutional: { color: '#8B5CF6', icon: '🏢' } // Purple with building icon
  };
  
  const config = customerConfig[customerType as keyof typeof customerConfig] || { color: '#6B7280', icon: '👤' };
  
  return divIcon({
    html: `
      <div style="
        background-color: ${config.color};
        width: 32px;
        height: 32px;
        border-radius: 50%;
        border: 3px solid white;
        box-shadow: 0 3px 6px rgba(0,0,0,0.2);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 16px;
        position: relative;
      ">
        <span style="
          filter: drop-shadow(0 1px 1px rgba(0,0,0,0.3));
        ">${config.icon}</span>
      </div>
    `,
    className: 'custom-marker',
    iconSize: [32, 32],
    iconAnchor: [16, 16],
    popupAnchor: [0, -16]
  });
};

// Component to fit map bounds to show all markers and handle resize
const FitBounds: React.FC<{ customers: Customer[]; isExpanded: boolean }> = ({ customers, isExpanded }) => {
  const map = useMap();
  
  useEffect(() => {
    // Invalidate size when expansion state changes
    setTimeout(() => {
      map.invalidateSize();
    }, 100);
  }, [isExpanded, map]);
  
  useEffect(() => {
    if (customers.length > 0) {
      const bounds = customers.map(customer => [
        customer.latitude!,
        customer.longitude!
      ] as [number, number]);
      
      if (bounds.length === 1) {
        // If only one customer, center on that location
        map.setView(bounds[0], 10);
      } else {
        // Fit bounds to show all customers
        map.fitBounds(bounds, { padding: [20, 20] });
      }
    }
  }, [customers, map]);
  
  return null;
};

// Customer type badge component
const CustomerTypeBadge: React.FC<{ type: string }> = ({ type }) => {
  const colors = {
    retail: 'bg-blue-100 text-blue-800',
    wholesale: 'bg-green-100 text-green-800',
    'mid-wholesale': 'bg-yellow-100 text-yellow-800',
    institutional: 'bg-purple-100 text-purple-800'
  };
  
  const colorClass = colors[type as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  
  return (
    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${colorClass}`}>
      {type.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
    </span>
  );
};

interface CustomerMapProps {
  customers: Customer[];
  isExpanded?: boolean;
}

const CustomerMap: React.FC<CustomerMapProps> = ({ customers, isExpanded = false }) => {
  const mapRef = useRef<any>(null);

  // Force map resize when expansion state changes
  useEffect(() => {
    if (mapRef.current) {
      setTimeout(() => {
        mapRef.current.invalidateSize();
      }, 150);
    }
  }, [isExpanded]);

  return (
    <div className="w-full h-full relative">
      <MapContainer
        ref={mapRef}
        center={[0, 0]}
        zoom={2}
        style={{ height: "100%", width: "100%" }}
        className="rounded-lg"
      >
        <TileLayer
          url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
          attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        />
        
        <FitBounds customers={customers} isExpanded={isExpanded} />
        
        {customers.map((customer) => (
          <Marker
            key={customer.uuid}
            position={[customer.latitude!, customer.longitude!]}
            icon={getCustomerIcon(customer.customerType)}
          >
            <Popup className="customer-popup" maxWidth={320}>
              <div className="p-3 min-w-[280px]">
                {/* Header */}
                <div className="flex items-start justify-between mb-3">
                  <div className="flex-1">
                    <h3 className="font-semibold text-lg text-gray-900 mb-1">
                      {customer.name}
                    </h3>
                    <CustomerTypeBadge type={customer.customerType} />
                  </div>
                  <div className="ml-3">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                      customer.customerType === 'retail' ? 'bg-blue-100' :
                      customer.customerType === 'wholesale' ? 'bg-green-100' :
                      customer.customerType === 'mid-wholesale' ? 'bg-yellow-100' :
                      'bg-purple-100'
                    }`}>
                      {customer.customerType === 'retail' && <User className="w-4 h-4 text-blue-600" />}
                      {customer.customerType === 'wholesale' && <Store className="w-4 h-4 text-green-600" />}
                      {customer.customerType === 'mid-wholesale' && <ShoppingBag className="w-4 h-4 text-yellow-600" />}
                      {customer.customerType === 'institutional' && <Building2 className="w-4 h-4 text-purple-600" />}
                    </div>
                  </div>
                </div>
                
                {/* Customer Details */}
                <div className="space-y-2 text-sm">
                  {customer.fiscalId && (
                    <div className="flex items-center space-x-2 text-sm text-gray-600">
                      <span className="font-medium">Fiscal ID:</span>
                      <span>{customer.fiscalId}</span>
                    </div>
                  )}

                  {customer.rc && (
                    <div className="flex items-center space-x-2 text-sm text-gray-600">
                      <span className="font-medium">RC:</span>
                      <span>{customer.rc}</span>
                    </div>
                  )}

                  {customer.articleNumber && (
                    <div className="flex items-center space-x-2 text-sm text-gray-600">
                      <span className="font-medium">Article Number:</span>
                      <span>{customer.articleNumber}</span>
                    </div>
                  )}
                  
                  {customer.email && (
                    <div className="flex items-center space-x-2">
                      <Mail className="w-4 h-4 text-gray-400" />
                      <span className="text-gray-600">Email:</span>
                      <span className="font-medium">{customer.email}</span>
                    </div>
                  )}
                  
                  {customer.phone && (
                    <div className="flex items-center space-x-2">
                      <Phone className="w-4 h-4 text-gray-400" />
                      <span className="text-gray-600">Phone:</span>
                      <span className="font-medium">{customer.phone}</span>
                    </div>
                  )}
                  
                  {customer.address && (
                    <div className="flex items-start space-x-2">
                      <MapIcon className="w-4 h-4 text-gray-400 mt-0.5" />
                      <span className="text-gray-600">Address:</span>
                      <span className="font-medium flex-1">{customer.address}</span>
                    </div>
                  )}
                  
                  
                </div>
                
                {/* Location Coordinates */}
                <div className="mt-3 pt-3 border-t border-gray-200">
                  <div className="flex items-center justify-between text-xs text-gray-500">
                    <span>Coordinates:</span>
                    <span>{
                      (typeof customer.latitude === 'number' && !isNaN(customer.latitude))
                        ? customer.latitude.toFixed(6)
                        : (Number(customer.latitude) ? Number(customer.latitude).toFixed(6) : 'N/A')
                    }, {
                      (typeof customer.longitude === 'number' && !isNaN(customer.longitude))
                        ? customer.longitude.toFixed(6)
                        : (Number(customer.longitude) ? Number(customer.longitude).toFixed(6) : 'N/A')
                    }</span>
                  </div>
                </div>
              </div>
            </Popup>
          </Marker>
        ))}
      </MapContainer>
      
      {/* Legend */}
      <div className="absolute top-4 right-4 bg-white rounded-lg shadow-lg border border-gray-200 p-3 z-[1000]">
        <h4 className="font-medium text-sm text-gray-900 mb-2">Customer Types</h4>
        <div className="space-y-1">
          {[
            { type: 'retail', label: 'Retail', color: '#3B82F6', icon: '👤' },
            { type: 'wholesale', label: 'Wholesale', color: '#10B981', icon: '🏪' },
            { type: 'mid-wholesale', label: 'Mid-Wholesale', color: '#F59E0B', icon: '🛒' },
            { type: 'institutional', label: 'Institutional', color: '#8B5CF6', icon: '🏢' }
          ].map(({ type, label, color, icon }) => (
            <div key={type} className="flex items-center space-x-2">
              <div
                className="w-5 h-5 rounded-full border-2 border-white flex items-center justify-center text-xs shadow-sm"
                style={{ backgroundColor: color }}
              >
                <span className="filter drop-shadow-sm">{icon}</span>
              </div>
              <span className="text-xs text-gray-700">{label}</span>
            </div>
          ))}
        </div>
      </div>
      
      {/* Customer Count */}
      <div className="absolute bottom-4 left-4 bg-white rounded-lg shadow-lg border border-gray-200 p-3 z-[1000]">
        <div className="flex items-center space-x-2">
          <MapPin className="w-4 h-4 text-blue-600" />
          <span className="text-sm font-medium text-gray-900">
            {customers.length} customer{customers.length !== 1 ? 's' : ''} shown
          </span>
        </div>
      </div>
    </div>
  );
};

export default CustomerMap; 