# =============================================================================
# Environment Configuration Template
# =============================================================================
# This file contains environment variables template for the mobile app
# Copy this file to .env and fill in the real values

# Backend API Configuration
API_BASE_URL=http://localhost:8000
API_TIMEOUT=30000

# Google OAuth Configuration for Mobile
# Get this from Google Cloud Console > Credentials > Android OAuth 2.0 Client ID
GOOGLE_CLIENT_ID=YOUR_ANDROID_CLIENT_ID_HERE
# Note: Mobile apps don't need client secrets - only backend servers do

# Development/Debug Settings
DEBUG_MODE=true
LOG_LEVEL=debug

# =============================================================================
# SETUP INSTRUCTIONS
# =============================================================================
#
# 1. COPY THIS FILE:
#    - Copy from: config_template.env
#    - Copy to:   .env
#
# 2. REPLACE ALL PLACEHOLDER VALUES:
#    - Get real values from Firebase Console
#    - Update API_BASE_URL if backend is hosted elsewhere
#    - Set DEBUG_MODE=false for production
#
# 3. SECURITY NOTES:
#    - The real .env file is already added to .gitignore
#    - Never commit sensitive API keys or secrets
#    - Only commit this template file
#
# 4. USAGE IN FLUTTER:
#    - Add flutter_dotenv package to pubspec.yaml
#    - Load environment variables in main.dart
#    - Access with: dotenv.env['API_BASE_URL']
#
# ============================================================================= 