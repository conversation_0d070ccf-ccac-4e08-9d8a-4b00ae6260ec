"use client";
import React from "react";
import ItemsTable, { ItemsTableColumn } from "@/components/itemsTable/ItemsTable";
import { useAuth } from "@/contexts/AuthContext";
import { useStockLevelsData } from "./hooks/useStockLevelsData";
import { Search, Filter, X, Warehouse } from "lucide-react";

// Convention: warehouseUuid and userUuid are injected from AuthContext (see CODE_GENERATION.md)
// Always use warehouseUuid and userUuid from AuthContext, never from user input.
// This page shows all products with their stock levels for a selected storage.
// It uses the ItemsTable component for UI consistency and includes advanced filtering.

interface StockLevelWithProductExtended {
  productUuid: string;
  productName: string;
  quantity: number;
  product: any;
  sku?: string;
  barcode?: string;
  category?: string;
  price?: number;
  productCategoryUuidString?: string;
  productDescription?: string;
  productSku?: string;
  productPrice?: number;
  productCost?: number;
}

const StockLevelsPage: React.FC = () => {
  const { user } = useAuth();
  const warehouseUuid = user?.warehouseUuid;

  // Use the custom hook for data management
  const {
    stockLevels,
    storages,
    categories,
    total,
    totalPages,
    hasNext,
    hasPrev,
    selectedStorage,
    filter,
    pagination,
    isLoading,
    isLoadingStorages,
    isLoadingCategories,
    error,
    updateFilter,
    clearFilters,
    handlePageChange,
    handleStorageChange,
    hasActiveFilters
  } = useStockLevelsData();

  // Set initial storage if none selected and storages are available
  React.useEffect(() => {
    if (storages.length > 0 && !selectedStorage) {
      handleStorageChange(storages[0].uuid);
    }
  }, [storages, selectedStorage, handleStorageChange]);

  const columns: ItemsTableColumn<StockLevelWithProductExtended>[] = [
    { 
      key: "productName", 
      header: "Product Name",
      render: (_: any, row: StockLevelWithProductExtended) => (
        <div>
          <div className="font-semibold text-sm">{row.productName}</div>
          {row.sku && <div className="text-xs text-gray-500">SKU: {row.sku}</div>}
        </div>
      )
    },
    { 
      key: "quantity", 
      header: "Stock Level",
      render: (_: any, row: StockLevelWithProductExtended) => (
        <span className={`font-semibold text-sm ${row.quantity === 0 ? 'text-red-600' : row.quantity < 10 ? 'text-yellow-600' : 'text-green-600'}`}> 
          {row.quantity}
        </span>
      )
    },
    { 
      key: "category", 
      header: "Category",
      render: (_: any, row: StockLevelWithProductExtended) => (
        <span className="text-sm text-gray-600">{row.category || "-"}</span>
      )
    },
    { 
      key: "barcode", 
      header: "Barcode",
      render: (_: any, row: StockLevelWithProductExtended) => (
        <span className="text-sm text-gray-600">{row.barcode || "-"}</span>
      )
    },
    { 
      key: "price", 
      header: "Price",
      render: (_: any, row: StockLevelWithProductExtended) => (
        <span className="text-sm text-gray-600">
          {row.price ? `$${row.price.toFixed(2)}` : "-"}
        </span>
      )
    },
  ];

  if (!warehouseUuid) {
    return <div className="text-center py-8">Please select a warehouse to view stock levels.</div>;
  }

  return (
    <div className="space-y-2">
      {/* Storage Selector */}
      <div className="bg-white p-4 rounded-lg border border-gray-200">
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <Warehouse className="text-gray-500" size={20} />
            <label htmlFor="storage-select" className="text-sm font-medium text-gray-700 whitespace-nowrap">
              Storage Location:
            </label>
          </div>
          <div className="flex-1">
            <select
              id="storage-select"
              value={selectedStorage}
              onChange={(e) => handleStorageChange(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              disabled={isLoadingStorages || storages.length === 0}
            >
              {isLoadingStorages ? (
                <option value="">Loading storage locations...</option>
              ) : storages.length === 0 ? (
                <option value="">No storage locations available</option>
              ) : (
                <>
                   {storages.map(storage => (
                     <option key={storage.uuid} value={storage.uuid}>
                       {storage.name}
                     </option>
                   ))}
                </>
              )}
            </select>
          </div>
          {selectedStorage && storages.find(s => s.uuid === selectedStorage) && (
            <div className="text-sm text-gray-600 bg-blue-50 px-3 py-2 rounded-md border border-blue-200">
              <span className="font-medium">Current:</span> {storages.find(s => s.uuid === selectedStorage)?.name}
            </div>
          )}
        </div>
      </div>

      {/* Search Bar and Filters - unified */}
      <div className="bg-white p-4 rounded-lg border border-gray-200">
        <div className="flex flex-col md:flex-row md:items-end gap-2 md:gap-4">
          {/* Search Input */}
          <div className="flex flex-col flex-1 min-w-[180px]">
            <label className="text-xs font-medium text-gray-700 mb-0.5">Search</label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
              <input
                type="text"
                placeholder="Name, SKU, or barcode..."
                value={filter.searchQuery}
                onChange={(e) => updateFilter({ searchQuery: e.target.value })}
                className="w-full pl-9 pr-2 py-2 border border-gray-300 rounded focus:outline-none text-sm"
              />
            </div>
          </div>
          {/* Category Filter */}
          <div className="flex flex-col min-w-[120px]">
            <label className="text-xs font-medium text-gray-700 mb-0.5">Category</label>
            <select
              value={filter.filterProductCategoryUuid}
              onChange={(e) => updateFilter({ filterProductCategoryUuid: e.target.value })}
              className="min-w-[100px] px-2 py-1 border border-gray-300 rounded focus:outline-none text-xs"
            >
              <option value="">All</option>
              {categories.map(category => (
                <option key={category.uuid} value={category.uuid}>
                  {category.name}
                </option>
              ))}
            </select>
          </div>
          {/* Min Price Filter */}
          <div className="flex flex-col min-w-[90px]">
            <label className="text-xs font-medium text-gray-700 mb-0.5">Min Price</label>
            <input
              type="number"
              step="0.01"
              min="0"
              value={filter.filterMinPrice}
              onChange={(e) => {
                const value = e.target.value;
                if (value === '' || (parseFloat(value) >= 0 && !isNaN(parseFloat(value)))) {
                  updateFilter({ filterMinPrice: value });
                }
              }}
              className="w-full px-2 py-1 border border-gray-300 rounded focus:outline-none text-xs"
              placeholder="0.00"
            />
          </div>
          {/* Max Price Filter */}
          <div className="flex flex-col min-w-[90px]">
            <label className="text-xs font-medium text-gray-700 mb-0.5">Max Price</label>
            <input
              type="number"
              step="0.01"
              min="0"
              value={filter.filterMaxPrice}
              onChange={(e) => {
                const value = e.target.value;
                if (value === '' || (parseFloat(value) >= 0 && !isNaN(parseFloat(value)))) {
                  updateFilter({ filterMaxPrice: value });
                }
              }}
              className="w-full px-2 py-1 border border-gray-300 rounded focus:outline-none text-xs"
              placeholder="0.00"
            />
          </div>
          {/* Clear Button (optional, only show if any filter is active) */}
          {hasActiveFilters && (
            <button
              onClick={clearFilters}
              className="flex items-center gap-1 px-2 py-1 text-xs bg-red-100 hover:bg-red-200 text-red-700 rounded transition-colors mt-5 md:mt-0"
            >
              <X size={14} />
              Clear
            </button>
          )}
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
          {error instanceof Error ? error.message : String(error)}
        </div>
      )}

      {/* Stock Levels Table with built-in pagination */}
      <ItemsTable
        columns={columns}
        data={stockLevels}
        isLoading={isLoading}
        noDataText={
          isLoading 
            ? "Loading stock levels..." 
            : selectedStorage 
              ? "No products found for this storage with the current filters."
              : "Please select a storage location to view stock levels."
        }
        containerClassName="bg-white rounded-xl shadow-lg border border-gray-200 w-full"
        pagination={
          totalPages > 1 ? {
            currentPage: pagination.page,
            totalPages: totalPages,
            onPageChange: handlePageChange,
            totalItems: total,
            itemsPerPage: pagination.limit
          } : undefined
        }
      />
    </div>
  );
};

export default StockLevelsPage;
