import { ApiProperty } from "@nestjs/swagger";

export class StockAdjustmentResponseDto {
  @ApiProperty({ description: "Stock adjustment UUID", format: "uuid" })
  uuid: string;
  @ApiProperty({ description: "User UUID", format: "uuid" })
  userUuid: string;
  @ApiProperty({ description: "Warehouse UUID", format: "uuid" })
  warehouseUuid: string;
  @ApiProperty({ description: "Storage UUID", format: "uuid" })
  storageUuid: string;
  @ApiProperty({ description: "Product UUID", format: "uuid" })
  productUuid: string;
  @ApiProperty({ description: "Adjusted quantity (non-zero)" })
  quantityAdjusted: number;
  @ApiProperty({ description: "Reason for adjustment", required: false })
  reason?: string;
  @ApiProperty({
    description: "Creation date",
    type: String,
    format: "date-time",
  })
  createdAt: Date;
}
