import 'package:dio/dio.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:logger/logger.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import '../constants/app_constants.dart';
import '../errors/app_error.dart';
import '../storage/secure_storage_service.dart';

class ApiClient {
  factory ApiClient() => _instance;
  ApiClient._internal();
  static final ApiClient _instance = ApiClient._internal();

  late Dio _dio;
  final Logger _logger = Logger();
  final SecureStorageService _secureStorage = SecureStorageService();

  Dio get dio => _dio;

  void initialize({String? baseUrl}) {
    // Get API configuration from environment variables or use defaults
    final apiBaseUrl = baseUrl ?? 
                      dotenv.env['API_BASE_URL'] ?? 
                      AppConstants.apiBaseUrl;
    
    final apiTimeout = int.tryParse(dotenv.env['API_TIMEOUT'] ?? '') ?? 
                      AppConstants.apiTimeout;
    
    _logger.i('Initializing API client with base URL: $apiBaseUrl');
    _logger.i('API timeout set to: ${apiTimeout}ms');
    
    _dio = Dio(BaseOptions(
      baseUrl: apiBaseUrl,
      connectTimeout: Duration(milliseconds: apiTimeout),
      receiveTimeout: Duration(milliseconds: apiTimeout),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    ));

    _setupInterceptors();
  }

  void _setupInterceptors() {
    // Request interceptor - Add authentication token
    _dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) async {
          // Add authentication token if available
          final token = await _secureStorage.getAccessToken();
          if (token != null) {
            options.headers['Authorization'] = 'Bearer $token';
          }

          _logger.d('Request: ${options.method} ${options.path}');
          _logger.d('Headers: ${options.headers}');
          if (options.data != null) {
            _logger.d('Data: ${options.data}');
          }

          handler.next(options);
        },
        onResponse: (response, handler) {
          _logger.d('Response: ${response.statusCode} ${response.requestOptions.path}');
          _logger.d('Data: ${response.data}');
          handler.next(response);
        },
        onError: (error, handler) async {
          _logger.e('Error: ${error.response?.statusCode} ${error.requestOptions.path}');
          _logger.e('Error data: ${error.response?.data}');

          // Handle token refresh for 401 errors
          if (error.response?.statusCode == 401) {
            final refreshed = await _refreshToken();
            if (refreshed) {
              // Retry the original request
              final response = await _dio.fetch(error.requestOptions);
              return handler.resolve(response);
            } else {
              // Clear tokens and redirect to login
              await _secureStorage.clearTokens();
              handler.next(error);
            }
          } else {
            handler.next(error);
          }
        },
      ),
    );

    // Add logging interceptor in debug mode
    if (AppConstants.defaultAnalytics) {
      _dio.interceptors.add(LogInterceptor(
        requestBody: true,
        responseBody: true,
        responseHeader: false,
        logPrint: _logger.d,
      ));
    }
  }

  Future<bool> _refreshToken() async {
    try {
      final refreshToken = await _secureStorage.getRefreshToken();
      if (refreshToken == null) return false;

      final response = await _dio.post(
        ApiEndpoints.refreshToken,
        data: {'refresh_token': refreshToken},
        options: Options(
          headers: {'Authorization': null}, // Remove auth header for refresh
        ),
      );

      if (response.statusCode == 200) {
        final data = response.data;
        await _secureStorage.saveAccessToken(data['access_token']);
        if (data['refresh_token'] != null) {
          await _secureStorage.saveRefreshToken(data['refresh_token']);
        }
        return true;
      }
    } catch (e) {
      _logger.e('Token refresh failed: $e');
    }
    return false;
  }

  // Generic GET request
  Future<Response<T>> get<T>(
    String path, {
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    try {
      return await _dio.get<T>(
        path,
        queryParameters: queryParameters,
        options: options,
      );
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  // Generic POST request
  Future<Response<T>> post<T>(
    String path, {
    data,
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    try {
      return await _dio.post<T>(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
      );
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  // Generic PUT request
  Future<Response<T>> put<T>(
    String path, {
    data,
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    try {
      return await _dio.put<T>(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
      );
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  // Generic PATCH request
  Future<Response<T>> patch<T>(
    String path, {
    data,
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    try {
      return await _dio.patch<T>(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
      );
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  // Generic DELETE request
  Future<Response<T>> delete<T>(
    String path, {
    data,
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    try {
      return await _dio.delete<T>(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
      );
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  // File upload
  Future<Response<T>> upload<T>(
    String path,
    FormData formData, {
    Options? options,
    ProgressCallback? onSendProgress,
  }) async {
    try {
      return await _dio.post<T>(
        path,
        data: formData,
        options: options,
        onSendProgress: onSendProgress,
      );
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  // File download
  Future<Response> download(
    String urlPath,
    String savePath, {
    ProgressCallback? onReceiveProgress,
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    try {
      return await _dio.download(
        urlPath,
        savePath,
        queryParameters: queryParameters,
        options: options,
        onReceiveProgress: onReceiveProgress,
      );
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  AppError _handleDioError(DioException error) {
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return const NetworkError(
          message: 'Connection timeout. Please check your internet connection.',
          code: 'TIMEOUT_ERROR',
        );
      case DioExceptionType.badResponse:
        final statusCode = error.response?.statusCode;
        final message = error.response?.data?['message'] ?? 'Unknown server error';
        
        switch (statusCode) {
          case 400:
            return ValidationError(
              message: message,
              code: 'BAD_REQUEST',
              data: error.response?.data,
            );
          case 401:
            return const AuthenticationError(
              message: 'Authentication failed. Please login again.',
              code: 'UNAUTHORIZED',
            );
          case 403:
            return const PermissionError(
              message: 'Permission denied. You don\'t have access to this resource.',
              code: 'FORBIDDEN',
            );
          case 404:
            return const DataError(
              message: 'Requested resource not found.',
              code: 'NOT_FOUND',
            );
          case 422:
            return ValidationError(
              message: message,
              code: 'VALIDATION_ERROR',
              data: error.response?.data,
            );
          case 500:
            return const ServerError(
              message: 'Internal server error. Please try again later.',
              code: 'INTERNAL_SERVER_ERROR',
            );
          default:
            return ServerError(
              message: message,
              code: 'SERVER_ERROR_$statusCode',
              data: error.response?.data,
            );
        }
      case DioExceptionType.cancel:
        return const NetworkError(
          message: 'Request was cancelled.',
          code: 'REQUEST_CANCELLED',
        );
      case DioExceptionType.connectionError:
        return const NetworkError(
          message: 'Connection error. Please check your internet connection.',
          code: 'CONNECTION_ERROR',
        );
      case DioExceptionType.badCertificate:
        return const NetworkError(
          message: 'Certificate error. Please check your connection security.',
          code: 'CERTIFICATE_ERROR',
        );
      case DioExceptionType.unknown:
      default:
        return UnknownError(
          message: 'An unexpected error occurred: ${error.message}',
          code: 'UNKNOWN_ERROR',
          data: error,
        );
    }
  }

  // Set base URL dynamically
  void setBaseUrl(String baseUrl) {
    _dio.options.baseUrl = baseUrl;
  }

  // Add custom header
  void addHeader(String key, String value) {
    _dio.options.headers[key] = value;
  }

  // Remove header
  void removeHeader(String key) {
    _dio.options.headers.remove(key);
  }

  // Clear all custom headers
  void clearHeaders() {
    _dio.options.headers.clear();
    _dio.options.headers.addAll({
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    });
  }
} 