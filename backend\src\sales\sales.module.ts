import { Module } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { Sale } from "./sale.entity";
import { SaleItem } from "./sale-item.entity";
import { Order } from "./order.entity";
import { OrderItem } from "./order-item.entity";
import { Quote } from "./quote.entity";
import { QuoteItem } from "./quote-item.entity";

import { SalesController } from "./sales.controller";
import { OrdersController } from "./orders.controller";
import { QuoteController } from "./quote.controller";
import { WarehousesModule } from "../warehouses/warehouses.module";
import { SalesService } from "./sales.service.typeorm";
import { OrdersService } from "./orders.service.typeorm";
import { QuoteService } from "./quote.service.typeorm";
import { UsersModule } from "../users/users.module";
import { InventoryModule } from "../inventory/inventory.module";
import { CustomersModule } from "../customers/customers.module";
import { LogsModule } from "../logs/logs.module";

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Sale,
      SaleItem,
      Order,
      OrderItem,
      Quote,
      QuoteItem,
    ]),
    WarehousesModule,
    UsersModule,
    InventoryModule,
    CustomersModule,
    LogsModule,
  ],
  controllers: [SalesController, OrdersController, QuoteController],
  providers: [SalesService, OrdersService, QuoteService],
  exports: [TypeOrmModule, SalesService, OrdersService, QuoteService],
})
export class SalesModule {}
