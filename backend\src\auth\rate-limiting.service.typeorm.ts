import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, LessThan } from 'typeorm';
import { RateLimit } from './rate-limit.entity';

export interface RateLimitConfig {
  maxAttempts: number;
  windowMs: number;
  lockoutDurationMs: number;
}

export interface RateLimitEntry {
  identifier: string; // email, IP, or user ID
  attempts: number;
  firstAttempt: Date;
  lastAttempt: Date;
  lockedUntil?: Date;
  lockoutReason?: string;
}

@Injectable()
export class RateLimitingService {
  private readonly logger = new Logger(RateLimitingService.name);
  
  // Default rate limiting configuration
  private readonly defaultConfig: RateLimitConfig = {
    maxAttempts: 5,
    windowMs: 15 * 60 * 1000, // 15 minutes
    lockoutDurationMs: 30 * 60 * 1000, // 30 minutes
  };

  constructor(
    @InjectRepository(RateLimit) private rateLimitRepository: Repository<RateLimit>,
  ) {}

  async checkRateLimit(identifier: string, config?: Partial<RateLimitConfig>): Promise<{ allowed: boolean; remainingAttempts: number; lockedUntil?: Date }> {
    const finalConfig = { ...this.defaultConfig, ...config };
    
    try {
      // Clean up expired entries
      await this.cleanupExpiredEntries();
      
      // Get or create rate limit entry
      let entry = await this.rateLimitRepository.findOne({
        where: { identifier }
      });
      
      if (!entry) {
        entry = new RateLimit();
        entry.identifier = identifier;
        entry.attempts = 0;
        entry.firstAttempt = new Date();
        entry.lastAttempt = new Date();
      }

      // Check if account is locked
      if (entry.lockedUntil && entry.lockedUntil > new Date()) {
        this.logger.warn(`Rate limit exceeded for ${identifier}, locked until ${entry.lockedUntil}`);
        return {
          allowed: false,
          remainingAttempts: 0,
          lockedUntil: entry.lockedUntil,
        };
      }

      // Check if lockout period has expired
      if (entry.lockedUntil && entry.lockedUntil <= new Date()) {
        // Reset attempts after lockout period
        entry.attempts = 0;
        entry.lockedUntil = undefined;
        entry.lockoutReason = undefined;
        this.logger.log(`Rate limit reset for ${identifier} after lockout period`);
      }

      // Check if we're still within the time window
      const windowStart = new Date(Date.now() - finalConfig.windowMs);
      if (entry.firstAttempt < windowStart) {
        // Reset if outside window
        entry.attempts = 0;
        entry.firstAttempt = new Date();
        this.logger.log(`Rate limit window reset for ${identifier}`);
      }

      const remainingAttempts = Math.max(0, finalConfig.maxAttempts - entry.attempts);
      
      return {
        allowed: remainingAttempts > 0,
        remainingAttempts,
        lockedUntil: entry.lockedUntil,
      };
    } catch (error) {
      this.logger.error('Error checking rate limit:', error);
      // Allow request if rate limiting fails
      return { allowed: true, remainingAttempts: finalConfig.maxAttempts };
    }
  }

  async recordFailedAttempt(identifier: string, config?: Partial<RateLimitConfig>): Promise<{ locked: boolean; lockedUntil?: Date }> {
    const finalConfig = { ...this.defaultConfig, ...config };
    
    try {
      let entry = await this.rateLimitRepository.findOne({
        where: { identifier }
      });
      
      if (!entry) {
        entry = new RateLimit();
        entry.identifier = identifier;
        entry.attempts = 0;
        entry.firstAttempt = new Date();
        entry.lastAttempt = new Date();
      }

      // Check if we need to reset the window
      const windowStart = new Date(Date.now() - finalConfig.windowMs);
      if (entry.firstAttempt < windowStart) {
        entry.attempts = 0;
        entry.firstAttempt = new Date();
      }

      entry.attempts += 1;
      entry.lastAttempt = new Date();

      // Check if we should lock the account
      if (entry.attempts >= finalConfig.maxAttempts) {
        entry.lockedUntil = new Date(Date.now() + finalConfig.lockoutDurationMs);
        entry.lockoutReason = 'Too many failed login attempts';
        
        this.logger.warn(`Account locked for ${identifier} due to ${entry.attempts} failed attempts`);
        
        await this.rateLimitRepository.save(entry);
        return { locked: true, lockedUntil: entry.lockedUntil };
      }

      await this.rateLimitRepository.save(entry);
      return { locked: false };
    } catch (error) {
      this.logger.error('Error recording failed attempt:', error);
      return { locked: false };
    }
  }

  async recordSuccessfulAttempt(identifier: string): Promise<void> {
    try {
      // Reset rate limiting on successful login
      await this.rateLimitRepository.delete({ identifier });
      this.logger.log(`Rate limit reset for ${identifier} after successful login`);
    } catch (error) {
      this.logger.error('Error recording successful attempt:', error);
    }
  }

  async unlockAccount(identifier: string): Promise<void> {
    try {
      await this.rateLimitRepository.update(
        { identifier },
        { 
          lockedUntil: null,
          lockoutReason: null,
          attempts: 0
        }
      );
      
      this.logger.log(`Account unlocked for ${identifier}`);
    } catch (error) {
      this.logger.error('Error unlocking account:', error);
    }
  }

  async getRateLimitStatus(identifier: string): Promise<RateLimitEntry | null> {
    try {
      const entry = await this.rateLimitRepository.findOne({
        where: { identifier }
      });
      return entry as unknown as RateLimitEntry | null;
    } catch (error) {
      this.logger.error('Error getting rate limit status:', error);
      return null;
    }
  }

  private async cleanupExpiredEntries(): Promise<void> {
    try {
      const cutoffTime = new Date(Date.now() - 24 * 60 * 60 * 1000); // 24 hours ago
      
      await this.rateLimitRepository.delete({
        lastAttempt: LessThan(cutoffTime),
        lockedUntil: null,
      });
    } catch (error) {
      this.logger.error('Error cleaning up expired rate limit entries:', error);
    }
  }

  // IP-based rate limiting
  async checkIpRateLimit(ipAddress: string): Promise<{ allowed: boolean; remainingAttempts: number }> {
    const ipConfig: RateLimitConfig = {
      maxAttempts: 10, // More lenient for IP-based
      windowMs: 5 * 60 * 1000, // 5 minutes
      lockoutDurationMs: 15 * 60 * 1000, // 15 minutes
    };
    
    const result = await this.checkRateLimit(`ip:${ipAddress}`, ipConfig);
    return {
      allowed: result.allowed,
      remainingAttempts: result.remainingAttempts,
    };
  }

  async recordIpFailedAttempt(ipAddress: string): Promise<void> {
    const ipConfig: RateLimitConfig = {
      maxAttempts: 10,
      windowMs: 5 * 60 * 1000,
      lockoutDurationMs: 15 * 60 * 1000,
    };
    
    await this.recordFailedAttempt(`ip:${ipAddress}`, ipConfig);
  }
} 