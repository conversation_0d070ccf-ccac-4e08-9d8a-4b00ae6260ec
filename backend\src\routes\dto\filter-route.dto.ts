import { IsOptional, IsString, IsUUID } from "class-validator";
import { ApiProperty } from "@nestjs/swagger";

export class FilterRouteDto {
  @IsUUID("all")
  @IsOptional()
  @ApiProperty({
    example: "uuid-v7-string",
    description: "Filter by warehouse UUID",
    required: false,
  })
  warehouseUuid?: string;

  @IsString()
  @IsOptional()
  @ApiProperty({
    example: "Morning",
    description: "Filter by route name (partial match)",
    required: false,
  })
  name?: string;

  @IsString()
  @IsOptional()
  @ApiProperty({
    example: "delivery",
    description: "Filter by route description (partial match)",
    required: false,
  })
  description?: string;
}
