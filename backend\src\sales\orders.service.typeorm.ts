import {
  Injectable,
  NotFoundException,
  BadRequestException,
  Inject,
  forwardRef,
} from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository, Like, ILike, Between, MoreThanOrEqual, LessThanOrEqual, In } from "typeorm";
import { UsersService } from "../users/users.service";
import { Order, OrderStatus, OrderPriority } from "./order.entity";
import { OrderItem } from "./order-item.entity";
import { Quote, QuoteStatus } from "./quote.entity";
import { QuoteService } from "./quote.service.typeorm";
import { Customer } from "../customers/customer.entity";
import { Product } from "../products/product.entity";
import {
  EMPTY_STRING_FILTER,
  EMPTY_UUID_FILTER,
  MIN_DATE_FILTER,
  MAX_DATE_FILTER,
  ORDER_NUMBER_PREFIX,
  DEFAULT_ORDER_PRIORITY,
  DEFAULT_TAX_RATE,
  DEFAULT_USE_TAX,
  DEFAULT_DELIVERY_WINDOW_DAYS,
} from "./order.constants";
import { Uuid7 } from "../utils/uuid7";
import {
  OrderResponseDto,
  toOrderResponseDto,
  OrdersListResponseDto,
} from "./dto/order-response.dto";
import { CreateOrderDto } from "./dto/create-order.dto";
import { UpdateOrderDto } from "./dto/update-order.dto";

type ProductWithUuid = { uuid: string; name: string };

@Injectable()
export class OrdersService {
  constructor(
    @InjectRepository(Order)
    private orderRepository: Repository<Order>,
    @InjectRepository(OrderItem)
    private orderItemRepository: Repository<OrderItem>,
    @InjectRepository(Quote)
    private quoteRepository: Repository<Quote>,
    @InjectRepository(Customer)
    private customerRepository: Repository<Customer>,
    @InjectRepository(Product)
    private productRepository: Repository<Product>,
    private usersService: UsersService,
    @Inject(forwardRef(() => QuoteService)) private quoteService: QuoteService,
  ) {}

  /**
   * Create a new Order with the provided input.
   */
  async create(createOrderDto: CreateOrderDto): Promise<OrderResponseDto> {
    const now = new Date();

    // Validate required fields
    if (
      !createOrderDto.userUuid ||
      !createOrderDto.warehouseUuid ||
      !createOrderDto.customerUuid
    ) {
      throw new BadRequestException(
        "userUuid, warehouseUuid, and customerUuid are required to create an order",
      );
    }

    // Check user exists
    const user = await this.usersService.findOne(createOrderDto.userUuid);
    if (!user) throw new NotFoundException("User not found");

    // Check warehouse exists (this would need to be implemented based on your warehouse service)
    // For now, we'll assume it exists

    // Check customer exists
    const customer = await this.customerRepository.findOne({
      where: { id: createOrderDto.customerUuid, isDeleted: false }
    });
    if (!customer) throw new NotFoundException("Customer not found");

    // If quote is provided, validate it exists
    let quote: Quote | null = null;
    if (createOrderDto.quoteUuid) {
      quote = await this.quoteRepository.findOne({
        where: { id: createOrderDto.quoteUuid, isDeleted: false }
      });
      if (!quote) throw new NotFoundException("Quote not found");
    }

    // Validate and transform items
    const validatedItems = await this.validateAndTransformItems(createOrderDto.items || []);

    // Calculate totals
    const subtotal = this.roundToTwoDecimals(
      validatedItems.reduce((sum, item) => sum + item.lineTotal, 0)
    );
    const useTax = createOrderDto.useTax ?? DEFAULT_USE_TAX;
    const taxRate = createOrderDto.taxRate ?? DEFAULT_TAX_RATE;
    const taxAmount = useTax ? this.calculateTaxAmount(subtotal, taxRate) : 0;
    const totalAmount = this.roundToTwoDecimals(subtotal + taxAmount);

    // Generate order number
    const timestamp = now.getTime();
    const orderNumber = `${ORDER_NUMBER_PREFIX}${timestamp}`;

    // Create order entity
    const order = this.orderRepository.create({
      id: Order.generateId(),
      orderNumber,
      customerUuid: createOrderDto.customerUuid,
      customerName: customer.name,
      customerFiscalId: customer.fiscalId,
      customerRc: customer.rc,
      customerArticleNumber: customer.articleNumber,
      warehouseUuid: createOrderDto.warehouseUuid,
      quoteUuid: createOrderDto.quoteUuid || null,
      subtotal,
      useTax,
      taxRate,
      taxAmount,
      totalAmount,
      orderDate: now,
      requestedDeliveryDate: createOrderDto.requestedDeliveryDate || 
        new Date(now.getTime() + DEFAULT_DELIVERY_WINDOW_DAYS * 24 * 60 * 60 * 1000),
      status: createOrderDto.status || OrderStatus.DRAFT,
      priority: createOrderDto.priority || OrderPriority.NORMAL,
      notes: createOrderDto.notes,
      createdBy: createOrderDto.userUuid,
      updatedBy: createOrderDto.userUuid,
      isDeleted: false,
    });

    // Save order
    const savedOrder = await this.orderRepository.save(order);

    // Create order items
    const orderItems = validatedItems.map(item => 
      this.orderItemRepository.create({
        id: OrderItem.generateId(),
        orderUuid: savedOrder.id,
        productUuid: item.productUuid,
        name: item.name,
        quantity: item.quantity,
        unitPrice: item.unitPrice,
        lineTotal: item.lineTotal,
        taxAmount: item.taxAmount || 0,
        notes: item.notes,
      })
    );

    await this.orderItemRepository.save(orderItems);

    // If order was created from quote, mark quote as converted
    if (quote && createOrderDto.quoteUuid) {
      await this.quoteService.markAsConverted(createOrderDto.quoteUuid, savedOrder.id);
    }

    return this.findOne(savedOrder.id);
  }

  /**
   * Create order from quote
   */
  async createFromQuote(
    quoteUuid: string,
    userUuid: string,
  ): Promise<OrderResponseDto> {
    const quote = await this.quoteRepository.findOne({
      where: { id: quoteUuid, isDeleted: false },
      relations: ['quoteItems'],
    });

    if (!quote) {
      throw new NotFoundException("Quote not found");
    }

    if (quote.status === QuoteStatus.CONVERTED) {
      throw new BadRequestException("Quote has already been converted to an order");
    }

    // Get quote items
    const quoteItems = await this.quoteService.getQuoteItems(quoteUuid);

    // Create order DTO
    const createOrderDto: CreateOrderDto = {
      userUuid,
      warehouseUuid: quote.warehouseUuid || '', // This would need to be set based on your business logic
      customerUuid: quote.customerUuid,
      items: quoteItems.map(item => ({
        productUuid: item.productUuid,
        quantity: item.quantity,
        unitPrice: item.unitPrice,
        notes: item.notes,
      })),
      quoteUuid,
      status: OrderStatus.DRAFT,
      priority: OrderPriority.NORMAL,
      notes: `Order created from quote ${quote.quoteNumber}`,
    };

    return this.create(createOrderDto);
  }

  /**
   * Calculate tax amount based on subtotal and tax rate
   */
  private calculateTaxAmount(subtotal: number, taxRate: number): number {
    return this.roundToTwoDecimals(subtotal * taxRate);
  }

  /**
   * Recalculate order totals based on items
   */
  private recalculateOrderTotals(order: Order): void {
    // This would need to be implemented based on the items
    // For now, we'll keep the existing totals
  }

  /**
   * Utility function to round numbers to 2 decimal places
   */
  private roundToTwoDecimals(value: number): number {
    return Math.round(value * 100) / 100;
  }

  /**
   * Find all orders with filtering and pagination
   */
  async findAll({
    customerUuid = EMPTY_UUID_FILTER,
    warehouseUuid = EMPTY_UUID_FILTER,
    status = EMPTY_STRING_FILTER,
    priority = EMPTY_STRING_FILTER,
    createdFrom = MIN_DATE_FILTER,
    createdTo = MAX_DATE_FILTER,
    page = 1,
    limit = 10,
  }: {
    customerUuid?: string;
    warehouseUuid?: string;
    status?: string;
    priority?: string;
    createdFrom?: Date;
    createdTo?: Date;
    page?: number;
    limit?: number;
  } = {}): Promise<OrdersListResponseDto> {
    const queryBuilder = this.orderRepository
      .createQueryBuilder('order')
      .leftJoinAndSelect('order.orderItems', 'orderItems')
      .where('order.isDeleted = :isDeleted', { isDeleted: false });

    // Apply filters
    if (customerUuid !== EMPTY_UUID_FILTER) {
      queryBuilder.andWhere('order.customerUuid = :customerUuid', { customerUuid });
    }

    if (warehouseUuid !== EMPTY_UUID_FILTER) {
      queryBuilder.andWhere('order.warehouseUuid = :warehouseUuid', { warehouseUuid });
    }

    if (status !== EMPTY_STRING_FILTER) {
      queryBuilder.andWhere('order.status = :status', { status });
    }

    if (priority !== EMPTY_STRING_FILTER) {
      queryBuilder.andWhere('order.priority = :priority', { priority });
    }

    if (createdFrom !== MIN_DATE_FILTER) {
      queryBuilder.andWhere('order.createdAt >= :createdFrom', { createdFrom });
    }

    if (createdTo !== MAX_DATE_FILTER) {
      queryBuilder.andWhere('order.createdAt <= :createdTo', { createdTo });
    }

    // Apply pagination
    const skip = (page - 1) * limit;
    queryBuilder.skip(skip).take(limit);

    // Order by creation date (newest first)
    queryBuilder.orderBy('order.createdAt', 'DESC');

    const [orders, total] = await queryBuilder.getManyAndCount();

    // Transform to response DTOs
    const orderDtos = await this.enrichOrdersData(orders);

    return new OrdersListResponseDto(
      orderDtos,
      total,
      page,
      limit,
      Math.ceil(total / limit)
    );
  }

  /**
   * Find orders by warehouse
   */
  async findByWarehouse(warehouseUuid: string): Promise<OrderResponseDto[]> {
    const orders = await this.orderRepository.find({
      where: { warehouseUuid, isDeleted: false },
      order: { createdAt: 'DESC' },
    });

    return this.enrichOrdersData(orders);
  }

  /**
   * Find one order by UUID
   */
  async findOne(uuid: string): Promise<OrderResponseDto> {
    const order = await this.orderRepository.findOne({
      where: { id: uuid, isDeleted: false },
      relations: ['orderItems'],
    });

    if (!order) {
      throw new NotFoundException("Order not found");
    }

    const enrichedOrders = await this.enrichOrdersData([order]);
    return enrichedOrders[0];
  }

  /**
   * Enrich orders data with additional information
   */
  private async enrichOrdersData(
    orders: Order[],
  ): Promise<OrderResponseDto[]> {
    // Extract unique UUIDs for batch fetching
    const customerUuids = [...new Set(orders.map(o => o.customerUuid))];
    const createdByUuids = [...new Set(orders.map(o => o.createdBy))];
    const updatedByUuids = [...new Set(orders.map(o => o.updatedBy))];
    const quoteUuids = [...new Set(orders.map(o => o.quoteUuid).filter(Boolean))];

    // Batch fetch related data
    const [customers, users, quotes] = await Promise.all([
      customerUuids.length > 0
        ? this.customerRepository.find({ where: { id: In(customerUuids) } })
        : [],
      createdByUuids.length > 0 || updatedByUuids.length > 0
        ? Promise.all([
            ...createdByUuids.map(uuid => this.usersService.findOne(uuid)),
            ...updatedByUuids.map(uuid => this.usersService.findOne(uuid)),
          ])
        : [],
      quoteUuids.length > 0
        ? this.quoteRepository.find({ where: { id: In(quoteUuids) } })
        : [],
    ]);

    // Create lookup maps with proper typing
    const customerMap = new Map<string, any>();
    customers.forEach(c => customerMap.set(c.id, c));
    
    const userMap = new Map<string, any>();
    users.forEach((user: any) => {
      if (user) userMap.set(user.id, user);
    });
    
    const quoteMap = new Map<string, any>();
    quotes.forEach(q => quoteMap.set(q.id, q));

    // Transform orders to DTOs
    return orders.map(order => {
      const customer = customerMap.get(order.customerUuid);
      const createdByUser = userMap.get(order.createdBy);
      const updatedByUser = userMap.get(order.updatedBy);
      const quote = order.quoteUuid ? quoteMap.get(order.quoteUuid) : null;

      // Create the base order response DTO
      const orderResponse = toOrderResponseDto(order);
      
      // Add enriched data as additional properties (these will be handled by the DTO)
      return {
        ...orderResponse,
        // Add any additional enriched data here if needed
        customer: customer ? {
          uuid: customer.id,
          name: customer.name,
          fiscalId: customer.fiscalId,
          rc: customer.rc,
          articleNumber: customer.articleNumber,
        } : null,
        createdByUser: createdByUser ? {
          uuid: createdByUser.id,
          name: createdByUser.name,
          email: createdByUser.email,
        } : null,
        updatedByUser: updatedByUser ? {
          uuid: updatedByUser.id,
          name: updatedByUser.name,
          email: updatedByUser.email,
        } : null,
        quote: quote ? {
          uuid: quote.id,
          quoteNumber: quote.quoteNumber,
          status: quote.status,
        } : null,
      };
    });
  }

  /**
   * Update an order
   */
  async update(
    uuid: string,
    updateOrderDto: UpdateOrderDto,
  ): Promise<OrderResponseDto> {
    const order = await this.orderRepository.findOne({
      where: { id: uuid, isDeleted: false },
    });

    if (!order) {
      throw new NotFoundException("Order not found");
    }

    // Update order fields
    Object.assign(order, updateOrderDto);
    order.updatedAt = new Date();

    const updatedOrder = await this.orderRepository.save(order);
    return this.findOne(updatedOrder.id);
  }

  /**
   * Update order status
   */
  async updateStatus(
    uuid: string,
    status: OrderStatus,
  ): Promise<OrderResponseDto> {
    const order = await this.orderRepository.findOne({
      where: { id: uuid, isDeleted: false },
    });

    if (!order) {
      throw new NotFoundException("Order not found");
    }

    order.status = status;
    order.updatedAt = new Date();

    const updatedOrder = await this.orderRepository.save(order);
    return this.findOne(updatedOrder.id);
  }

  /**
   * Remove an order (soft delete)
   */
  async remove(uuid: string): Promise<{ message: string }> {
    const order = await this.orderRepository.findOne({
      where: { id: uuid, isDeleted: false },
    });

    if (!order) {
      throw new NotFoundException("Order not found");
    }

    order.isDeleted = true;
    order.updatedAt = new Date();
    await this.orderRepository.save(order);

    return { message: "Order deleted successfully" };
  }

  /**
   * Add products to an order
   */
  async addProducts(uuid: string, items: any[]): Promise<OrderResponseDto> {
    const order = await this.orderRepository.findOne({
      where: { id: uuid, isDeleted: false },
    });

    if (!order) {
      throw new NotFoundException("Order not found");
    }

    // Validate and transform items
    const validatedItems = await this.validateAndTransformItems(items);

    // Create new order items
    const orderItems = validatedItems.map(item => 
      this.orderItemRepository.create({
        id: OrderItem.generateId(),
        orderUuid: order.id,
        productUuid: item.productUuid,
        name: item.name,
        quantity: item.quantity,
        unitPrice: item.unitPrice,
        lineTotal: item.lineTotal,
        taxAmount: item.taxAmount || 0,
        notes: item.notes,
      })
    );

    await this.orderItemRepository.save(orderItems);

    // Recalculate totals
    const allItems = await this.orderItemRepository.find({
      where: { orderUuid: order.id },
    });

    const subtotal = this.roundToTwoDecimals(
      allItems.reduce((sum, item) => sum + item.lineTotal, 0)
    );
    const taxAmount = order.useTax ? this.calculateTaxAmount(subtotal, order.taxRate) : 0;
    const totalAmount = this.roundToTwoDecimals(subtotal + taxAmount);

    order.subtotal = subtotal;
    order.taxAmount = taxAmount;
    order.totalAmount = totalAmount;
    order.updatedAt = new Date();

    await this.orderRepository.save(order);

    return this.findOne(order.id);
  }

  /**
   * Validate and transform items
   */
  private async validateAndTransformItems(items: any[]): Promise<any[]> {
    const productUuids = items.map(item => item.productUuid);
    const products = await this.productRepository.find({
      where: { id: In(productUuids), isDeleted: false },
    });

    const productMap = new Map(products.map(p => [p.id, p]));

    return items.map(item => {
      const product = productMap.get(item.productUuid);
      if (!product) {
        throw new BadRequestException(`Product with UUID ${item.productUuid} not found`);
      }

      const quantity = parseFloat(item.quantity);
      const unitPrice = parseFloat(item.unitPrice);
      const lineTotal = this.roundToTwoDecimals(quantity * unitPrice);

      return {
        productUuid: item.productUuid,
        name: product.name,
        quantity,
        unitPrice,
        lineTotal,
        taxAmount: item.taxAmount || 0,
        notes: item.notes,
      };
    });
  }

  /**
   * Cancel an order
   */
  async cancelOrder(uuid: string): Promise<OrderResponseDto> {
    const order = await this.orderRepository.findOne({
      where: { id: uuid, isDeleted: false },
    });

    if (!order) {
      throw new NotFoundException("Order not found");
    }

    if (order.status === OrderStatus.CANCELLED) {
      throw new BadRequestException("Order is already cancelled");
    }

    order.status = OrderStatus.CANCELLED;
    order.updatedAt = new Date();

    await this.orderRepository.save(order);

    return this.findOne(order.id);
  }
} 