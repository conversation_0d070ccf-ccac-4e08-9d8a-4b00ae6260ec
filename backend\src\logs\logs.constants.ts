export const LOG_OPERATIONS = {
  CREATE: 'create',
  UPDATE: 'update',
  DELETE: 'delete',
} as const;

export const LOG_ENTITIES = {
  SALE: 'sale',
  PURCHASE: 'purchase',
  REGION: 'region',
  ROUTE: 'route',
  PRODUCT: 'product',
  VAN: 'van',
  WAREHOUSE: 'warehouse',
  STOCK_ADJUSTMENT: 'stock_adjustment',
  SUPPLIER: 'supplier',
  ORDER: 'order',
  CUSTOMER: 'customer',
  USER: 'user',
  COMPANY: 'company',
  INVENTORY_ITEM: 'inventory_item',
  PRODUCT_CATEGORY: 'product_category',
  ACCOUNT_SETTINGS: 'account_settings',
  CREDIT_ADJUSTMENT: 'credit_adjustment',
  PAYMENT: 'payment',
  LOG: 'log',
  AUTH_AUDIT: 'auth_audit',
  USER_ACCOUNT_PLAN: 'user_account_plan',
  FEATURE: 'feature',
} as const;

export type LogOperation = typeof LOG_OPERATIONS[keyof typeof LOG_OPERATIONS];
export type LogEntity = typeof LOG_ENTITIES[keyof typeof LOG_ENTITIES];

export const LOG_OPERATIONS_ARRAY = Object.values(LOG_OPERATIONS);
export const LOG_ENTITIES_ARRAY = Object.values(LOG_ENTITIES); 