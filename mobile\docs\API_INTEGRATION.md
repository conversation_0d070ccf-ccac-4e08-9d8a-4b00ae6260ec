# API Integration Guide

## Overview

This document outlines the API integration strategy for the Dido Distribution mobile application. The mobile app integrates with the same backend API as the web frontend, ensuring data consistency and feature parity across platforms.

## 🌐 API Configuration

### Base Configuration
```dart
class ApiConfig {
  static const String baseUrl = 'http://localhost:8000';
  static const Duration timeout = Duration(seconds: 30);
  static const String apiVersion = 'v1';
  
  // Environment-specific URLs
  static const String devUrl = 'http://localhost:8000';
  static const String stagingUrl = 'https://staging-api.dido-distribution.com';
  static const String prodUrl = 'https://api.dido-distribution.com';
}
```

### HTTP Client Setup
```dart
class ApiClient {
  late final Dio _dio;
  
  ApiClient() {
    _dio = Dio(BaseOptions(
      baseUrl: ApiConfig.baseUrl,
      connectTimeout: ApiConfig.timeout,
      receiveTimeout: ApiConfig.timeout,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    ));
    
    _setupInterceptors();
  }
  
  void _setupInterceptors() {
    _dio.interceptors.addAll([
      AuthInterceptor(),
      LoggingInterceptor(),
      ErrorInterceptor(),
      RetryInterceptor(),
    ]);
  }
}
```

## 🔐 Authentication Integration

### JWT Token Management
```dart
class AuthInterceptor extends Interceptor {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    final token = AuthService.instance.getToken();
    if (token != null) {
      options.headers['Authorization'] = 'Bearer $token';
    }
    handler.next(options);
  }
  
  @override
  void onError(DioError err, ErrorInterceptorHandler handler) {
    if (err.response?.statusCode == 401) {
      // Handle token refresh or logout
      AuthService.instance.refreshToken();
    }
    handler.next(err);
  }
}
```

### Authentication Service
```dart
class AuthService {
  static final AuthService instance = AuthService._internal();
  AuthService._internal();
  
  Future<AuthResult> login(String username, String password) async {
    final response = await ApiClient().post('/auth/login', data: {
      'username': username,
      'password': password,
    });
    
    if (response.statusCode == 200) {
      final authData = AuthResponse.fromJson(response.data);
      await _storeTokens(authData);
      return AuthResult.success(authData.user);
    }
    
    return AuthResult.failure('Login failed');
  }
  
  Future<void> logout() async {
    await ApiClient().post('/auth/logout');
    await _clearTokens();
  }
  
  Future<bool> refreshToken() async {
    final refreshToken = await _getRefreshToken();
    if (refreshToken == null) return false;
    
    try {
      final response = await ApiClient().post('/auth/refresh', data: {
        'refreshToken': refreshToken,
      });
      
      final authData = AuthResponse.fromJson(response.data);
      await _storeTokens(authData);
      return true;
    } catch (e) {
      await _clearTokens();
      return false;
    }
  }
}
```

## 📊 API Service Pattern

### Base API Service
```dart
abstract class BaseApiService<T> {
  final ApiClient _client;
  final String _endpoint;
  
  BaseApiService(this._client, this._endpoint);
  
  Future<List<T>> getAll({Map<String, dynamic>? queryParams}) async {
    final response = await _client.get(_endpoint, queryParameters: queryParams);
    return (response.data as List).map((json) => fromJson(json)).toList();
  }
  
  Future<T> getById(String id) async {
    final response = await _client.get('$_endpoint/$id');
    return fromJson(response.data);
  }
  
  Future<T> create(T item) async {
    final response = await _client.post(_endpoint, data: toJson(item));
    return fromJson(response.data);
  }
  
  Future<T> update(String id, T item) async {
    final response = await _client.put('$_endpoint/$id', data: toJson(item));
    return fromJson(response.data);
  }
  
  Future<void> delete(String id) async {
    await _client.delete('$_endpoint/$id');
  }
  
  T fromJson(Map<String, dynamic> json);
  Map<String, dynamic> toJson(T item);
}
```

### Feature-Specific Services

#### Product Service
```dart
class ProductService extends BaseApiService<Product> {
  ProductService(ApiClient client) : super(client, '/products');
  
  Future<List<Product>> getByWarehouse(String warehouseUuid) async {
    return await getAll(queryParams: {'warehouseUuid': warehouseUuid});
  }
  
  Future<List<Product>> searchProducts(String query) async {
    return await getAll(queryParams: {'search': query});
  }
  
  @override
  Product fromJson(Map<String, dynamic> json) => Product.fromJson(json);
  
  @override
  Map<String, dynamic> toJson(Product item) => item.toJson();
}
```

#### Inventory Service
```dart
class InventoryService extends BaseApiService<InventoryItem> {
  InventoryService(ApiClient client) : super(client, '/inventory');
  
  Future<List<StockLevel>> getStockLevels({
    String? warehouseUuid,
    String? storageUuid,
  }) async {
    final params = <String, dynamic>{};
    if (warehouseUuid != null) params['warehouseUuid'] = warehouseUuid;
    if (storageUuid != null) params['storageUuid'] = storageUuid;
    
    final response = await _client.get('/stock-levels', queryParameters: params);
    return (response.data as List).map((json) => StockLevel.fromJson(json)).toList();
  }
  
  Future<void> createStockAdjustment(StockAdjustment adjustment) async {
    await _client.post('/stock-adjustments', data: adjustment.toJson());
  }
  
  @override
  InventoryItem fromJson(Map<String, dynamic> json) => InventoryItem.fromJson(json);
  
  @override
  Map<String, dynamic> toJson(InventoryItem item) => item.toJson();
}
```

#### Sales Service
```dart
class SalesService extends BaseApiService<Sale> {
  SalesService(ApiClient client) : super(client, '/sales');
  
  Future<Sale> createSale(SaleRequest request) async {
    final response = await _client.post('/sales', data: request.toJson());
    return Sale.fromJson(response.data);
  }
  
  Future<List<Sale>> getSalesByDateRange(DateTime start, DateTime end) async {
    return await getAll(queryParams: {
      'startDate': start.toIso8601String(),
      'endDate': end.toIso8601String(),
    });
  }
  
  @override
  Sale fromJson(Map<String, dynamic> json) => Sale.fromJson(json);
  
  @override
  Map<String, dynamic> toJson(Sale item) => item.toJson();
}
```

## 🔄 Error Handling

### Error Interceptor
```dart
class ErrorInterceptor extends Interceptor {
  @override
  void onError(DioError err, ErrorInterceptorHandler handler) {
    final apiError = _mapError(err);
    
    // Log error for debugging
    logger.error('API Error: ${apiError.message}', err);
    
    // Handle specific error types
    switch (apiError.type) {
      case ApiErrorType.network:
        _handleNetworkError(apiError);
        break;
      case ApiErrorType.unauthorized:
        _handleUnauthorizedError(apiError);
        break;
      case ApiErrorType.validation:
        _handleValidationError(apiError);
        break;
      case ApiErrorType.server:
        _handleServerError(apiError);
        break;
    }
    
    handler.next(err);
  }
  
  ApiError _mapError(DioError error) {
    switch (error.type) {
      case DioErrorType.connectTimeout:
      case DioErrorType.sendTimeout:
      case DioErrorType.receiveTimeout:
        return ApiError(
          type: ApiErrorType.network,
          message: 'Connection timeout',
          statusCode: 0,
        );
      case DioErrorType.response:
        return ApiError(
          type: _getErrorType(error.response?.statusCode),
          message: error.response?.data['message'] ?? 'Unknown error',
          statusCode: error.response?.statusCode ?? 0,
        );
      default:
        return ApiError(
          type: ApiErrorType.unknown,
          message: 'Unknown error occurred',
          statusCode: 0,
        );
    }
  }
}
```

### Error Types
```dart
enum ApiErrorType {
  network,
  unauthorized,
  validation,
  server,
  unknown,
}

class ApiError {
  final ApiErrorType type;
  final String message;
  final int statusCode;
  final Map<String, dynamic>? details;
  
  ApiError({
    required this.type,
    required this.message,
    required this.statusCode,
    this.details,
  });
}
```

## 📱 Offline Support

### Cache Strategy
```dart
class CacheInterceptor extends Interceptor {
  final CacheManager _cacheManager;
  
  CacheInterceptor(this._cacheManager);
  
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    if (_shouldCache(options)) {
      final cachedResponse = _cacheManager.get(options.uri.toString());
      if (cachedResponse != null && !_isExpired(cachedResponse)) {
        handler.resolve(cachedResponse);
        return;
      }
    }
    handler.next(options);
  }
  
  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    if (_shouldCache(response.requestOptions)) {
      _cacheManager.put(
        response.requestOptions.uri.toString(),
        response,
        Duration(minutes: 30),
      );
    }
    handler.next(response);
  }
}
```

### Offline Queue
```dart
class OfflineQueue {
  final List<QueuedRequest> _queue = [];
  final ApiClient _apiClient;
  
  OfflineQueue(this._apiClient);
  
  Future<void> enqueue(QueuedRequest request) async {
    _queue.add(request);
    await _persistQueue();
  }
  
  Future<void> processQueue() async {
    if (!await _isOnline()) return;
    
    final failedRequests = <QueuedRequest>[];
    
    for (final request in _queue) {
      try {
        await _executeRequest(request);
      } catch (e) {
        failedRequests.add(request);
      }
    }
    
    _queue.clear();
    _queue.addAll(failedRequests);
    await _persistQueue();
  }
  
  Future<void> _executeRequest(QueuedRequest request) async {
    switch (request.method) {
      case HttpMethod.post:
        await _apiClient.post(request.endpoint, data: request.data);
        break;
      case HttpMethod.put:
        await _apiClient.put(request.endpoint, data: request.data);
        break;
      case HttpMethod.delete:
        await _apiClient.delete(request.endpoint);
        break;
    }
  }
}
```

## 🔄 Real-time Updates

### WebSocket Integration
```dart
class WebSocketService {
  late WebSocketChannel _channel;
  final StreamController<WebSocketEvent> _eventController = StreamController.broadcast();
  
  Stream<WebSocketEvent> get events => _eventController.stream;
  
  Future<void> connect() async {
    final token = await AuthService.instance.getToken();
    final uri = Uri.parse('ws://localhost:8000/ws?token=$token');
    
    _channel = WebSocketChannel.connect(uri);
    
    _channel.stream.listen(
      (data) {
        final event = WebSocketEvent.fromJson(jsonDecode(data));
        _eventController.add(event);
      },
      onError: (error) => _handleWebSocketError(error),
      onDone: () => _handleWebSocketClose(),
    );
  }
  
  void subscribe(String event) {
    _channel.sink.add(jsonEncode({
      'type': 'subscribe',
      'event': event,
    }));
  }
  
  void unsubscribe(String event) {
    _channel.sink.add(jsonEncode({
      'type': 'unsubscribe',
      'event': event,
    }));
  }
  
  void disconnect() {
    _channel.sink.close();
    _eventController.close();
  }
}
```

### Event Handling
```dart
class EventHandler {
  final WebSocketService _webSocketService;
  
  EventHandler(this._webSocketService);
  
  void setupEventHandlers() {
    _webSocketService.events.listen((event) {
      switch (event.type) {
        case 'stock_update':
          _handleStockUpdate(event);
          break;
        case 'sale_created':
          _handleSaleCreated(event);
          break;
        case 'inventory_adjustment':
          _handleInventoryAdjustment(event);
          break;
        default:
          logger.warn('Unknown event type: ${event.type}');
      }
    });
  }
  
  void _handleStockUpdate(WebSocketEvent event) {
    final stockUpdate = StockUpdate.fromJson(event.data);
    // Update local cache and notify UI
    InventoryProvider.instance.updateStock(stockUpdate);
  }
}
```

## 📊 Data Synchronization

### Sync Manager
```dart
class SyncManager {
  final List<SyncableService> _services = [];
  final StreamController<SyncStatus> _statusController = StreamController.broadcast();
  
  Stream<SyncStatus> get status => _statusController.stream;
  
  void registerService(SyncableService service) {
    _services.add(service);
  }
  
  Future<void> syncAll() async {
    _statusController.add(SyncStatus.syncing);
    
    try {
      for (final service in _services) {
        await service.sync();
      }
      _statusController.add(SyncStatus.completed);
    } catch (e) {
      _statusController.add(SyncStatus.failed);
      throw e;
    }
  }
  
  Future<void> syncFeature(String feature) async {
    final service = _services.firstWhere(
      (s) => s.featureName == feature,
      orElse: () => throw Exception('Service not found: $feature'),
    );
    
    await service.sync();
  }
}
```

### Conflict Resolution
```dart
class ConflictResolver {
  Future<T> resolveConflict<T>(
    T localData,
    T remoteData,
    ConflictStrategy strategy,
  ) async {
    switch (strategy) {
      case ConflictStrategy.localWins:
        return localData;
      case ConflictStrategy.remoteWins:
        return remoteData;
      case ConflictStrategy.lastModifiedWins:
        return _getLastModified(localData, remoteData);
      case ConflictStrategy.userChoice:
        return await _showConflictDialog(localData, remoteData);
      case ConflictStrategy.merge:
        return _mergeData(localData, remoteData);
    }
  }
}
```

## 🧪 Testing API Integration

### Mock API Service
```dart
class MockApiService extends BaseApiService<Product> {
  final List<Product> _mockProducts = [];
  
  MockApiService() : super(MockApiClient(), '/products');
  
  @override
  Future<List<Product>> getAll({Map<String, dynamic>? queryParams}) async {
    await Future.delayed(Duration(milliseconds: 500)); // Simulate network delay
    return _mockProducts;
  }
  
  @override
  Future<Product> getById(String id) async {
    await Future.delayed(Duration(milliseconds: 300));
    return _mockProducts.firstWhere((p) => p.id == id);
  }
  
  @override
  Product fromJson(Map<String, dynamic> json) => Product.fromJson(json);
  
  @override
  Map<String, dynamic> toJson(Product item) => item.toJson();
}
```

### Integration Tests
```dart
void main() {
  group('API Integration Tests', () {
    late ApiClient apiClient;
    late ProductService productService;
    
    setUp(() {
      apiClient = ApiClient();
      productService = ProductService(apiClient);
    });
    
    test('should fetch products successfully', () async {
      final products = await productService.getAll();
      expect(products, isNotEmpty);
      expect(products.first, isA<Product>());
    });
    
    test('should handle network errors gracefully', () async {
      // Mock network failure
      when(apiClient.get(any)).thenThrow(DioError(
        requestOptions: RequestOptions(path: '/products'),
        type: DioErrorType.connectTimeout,
      ));
      
      expect(
        () => productService.getAll(),
        throwsA(isA<ApiError>()),
      );
    });
  });
}
```

## 📈 Performance Optimization

### Request Batching
```dart
class BatchProcessor {
  final Map<String, List<BatchRequest>> _batches = {};
  Timer? _batchTimer;
  
  void addRequest(BatchRequest request) {
    final key = '${request.method}_${request.endpoint}';
    _batches[key] ??= [];
    _batches[key]!.add(request);
    
    _scheduleBatch();
  }
  
  void _scheduleBatch() {
    _batchTimer?.cancel();
    _batchTimer = Timer(Duration(milliseconds: 100), _processBatches);
  }
  
  Future<void> _processBatches() async {
    for (final entry in _batches.entries) {
      if (entry.value.isNotEmpty) {
        await _processBatch(entry.key, entry.value);
      }
    }
    _batches.clear();
  }
}
```

### Response Caching
```dart
class ResponseCache {
  final Map<String, CachedResponse> _cache = {};
  
  void put(String key, Response response, Duration ttl) {
    _cache[key] = CachedResponse(
      response: response,
      expiry: DateTime.now().add(ttl),
    );
  }
  
  Response? get(String key) {
    final cached = _cache[key];
    if (cached == null || cached.isExpired) {
      _cache.remove(key);
      return null;
    }
    return cached.response;
  }
  
  void clear() {
    _cache.clear();
  }
}
```

## 🔐 Security Considerations

### Certificate Pinning
```dart
class SecurityConfig {
  static List<String> get certificateHashes => [
    'sha256/AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=',
    'sha256/BBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBB=',
  ];
  
  static void setupCertificatePinning(Dio dio) {
    (dio.httpClientAdapter as DefaultHttpClientAdapter).onHttpClientCreate = (client) {
      client.badCertificateCallback = (cert, host, port) {
        return _validateCertificate(cert, host);
      };
      return client;
    };
  }
  
  static bool _validateCertificate(X509Certificate cert, String host) {
    final certHash = _getCertificateHash(cert);
    return certificateHashes.contains(certHash);
  }
}
```

### Request Signing
```dart
class RequestSigner {
  final String _secretKey;
  
  RequestSigner(this._secretKey);
  
  void signRequest(RequestOptions options) {
    final timestamp = DateTime.now().millisecondsSinceEpoch.toString();
    final signature = _generateSignature(options, timestamp);
    
    options.headers['X-Timestamp'] = timestamp;
    options.headers['X-Signature'] = signature;
  }
  
  String _generateSignature(RequestOptions options, String timestamp) {
    final payload = '${options.method}${options.path}$timestamp';
    return _hmacSha256(payload, _secretKey);
  }
}
```

---

This comprehensive API integration guide ensures secure, efficient, and robust communication between the mobile application and the backend services. 