import { useState, useMemo } from 'react';
import { useQuery } from '@tanstack/react-query';
import { useAuth } from '@/contexts/AuthContext';
import { getSales, FilterSaleDto, PaginationQueryDto } from '../salesApi';

export const useSalesData = () => {
  const { user, token } = useAuth();
  const warehouseUuid = user?.warehouseUuid;
  
  // Sales List state
  const [filter, setFilter] = useState<FilterSaleDto>({});
  const [pagination, setPagination] = useState<PaginationQueryDto>({ page: 1, limit: 10 });
  const [showFilters, setShowFilters] = useState(true);

  // Memoize the filter and pagination objects to prevent infinite loops
  const memoizedFilter = useMemo(() => filter, [JSON.stringify(filter)]);
  const memoizedPagination = useMemo(() => pagination, [JSON.stringify(pagination)]);

  // Memoize the query key dependencies to prevent infinite loops
  const queryKey = useMemo(() => ['sales', warehouseUuid, memoizedFilter, memoizedPagination], [warehouseUuid, memoizedFilter, memoizedPagination]);

  // Query for sales data
  const { data: salesResponse, isLoading, error, refetch } = useQuery({
    queryKey,
    queryFn: () => {
      // Validate warehouseUuid before making the request
      if (!warehouseUuid || warehouseUuid.trim() === '') {
        throw new Error('Warehouse UUID is required');
      }
      
      // Always include warehouseUuid in the filter
      const filterWithWarehouse = {
        ...memoizedFilter,
        warehouseUuid: warehouseUuid.trim()
      };
      
      return getSales(memoizedPagination, filterWithWarehouse);
    },
    enabled: !!warehouseUuid && warehouseUuid.trim() !== '' && !!token,
  });

  const sales = salesResponse?.data || [];
  const total = salesResponse?.total || 0;
  const totalPages = salesResponse?.totalPages || 0;




  // Filter handlers
  const updateFilter = (newFilter: Partial<FilterSaleDto>) => {
    setFilter(prev => ({ ...prev, ...newFilter }));
    setPagination(prev => ({ ...prev, page: 1 })); // Reset to first page when filtering
  };

  const clearFilters = () => {
    setFilter({});
    setPagination({ page: 1, limit: 10 });
  };

  // Pagination handlers
  const handlePageChange = (page: number) => {
    // Validate page number
    if (page && !isNaN(page) && page > 0) {
      setPagination(prev => ({ ...prev, page }));
    } else {
      console.warn('Invalid page number:', page);
    }
  };

  const toggleFilters = () => {
    setShowFilters(prev => !prev);
  };

  return {
    // Data
    sales,
    total,
    totalPages,
    
    // State
    filter,
    pagination,
    showFilters,
    isLoading,
    error,
    
    // Actions
    updateFilter,
    clearFilters,
    handlePageChange,
    toggleFilters,
    refetch,
    
    // Computed
    warehouseUuid
  };
}; 