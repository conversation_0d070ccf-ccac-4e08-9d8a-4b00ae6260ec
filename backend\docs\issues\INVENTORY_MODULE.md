# Inventory Module Documentation

**Location:** `backend/src/inventory/`

## Purpose
Manages inventory items, storage locations, and stock adjustments across warehouses.

## Structure
```
inventory/
├── inventory.controller.ts      # Inventory CRUD operations
├── inventory.service.ts         # Inventory business logic
├── inventory.module.ts          # Module configuration
├── inventoryItem.schema.ts      # Inventory item data model
├── storage.schema.ts            # Storage location data model
├── stockAdjustment.schema.ts    # Stock adjustment data model
├── storage_type.enum.ts         # Storage type definitions
├── examples/                    # Example data
└── dto/                         # Data transfer objects
```

## Key Features
- Inventory item management
- Storage location management
- Stock adjustments and movements
- Real-time stock tracking
- Warehouse-specific inventory
- Product-storage associations
- Stock history tracking

## Endpoints

### Inventory Items
- `POST /inventory/items` - Create inventory item
- `GET /inventory/items` - List inventory items (with filtering)
- `GET /inventory/items/:uuid` - Get inventory item by UUID
- `PUT /inventory/items/:uuid` - Update inventory item
- `DELETE /inventory/items/:uuid` - Soft delete inventory item

### Storage Locations
- `POST /inventory/storage` - Create storage location
- `GET /inventory/storage` - List storage locations
- `GET /inventory/storage/:uuid` - Get storage by UUID
- `PUT /inventory/storage/:uuid` - Update storage
- `DELETE /inventory/storage/:uuid` - Soft delete storage

### Stock Adjustments
- `POST /inventory/stock-adjustments` - Create stock adjustment
- `GET /inventory/stock-adjustments` - List stock adjustments

## Schema Fields

### Inventory Item Schema
```typescript
{
  _id: Binary,                    // Primary key (UUIDv7)
  productUuid: Binary,            // Product reference (indexed)
  storageUuid: Binary,            // Storage reference (indexed)
  quantity: number,               // Current quantity (required)
  createdAt: Date,                // Creation timestamp
  updatedAt: Date                 // Update timestamp
}
```

### Storage Schema
```typescript
{
  _id: Binary,                    // Primary key (UUIDv7)
  name: string,                   // Storage name (required)
  type: StorageType,              // Storage type (warehouse/van/etc.)
  warehouseUuid: Binary,          // Warehouse reference (indexed)
  userUuid: Binary,               // Owner user reference (indexed)
  isDeleted: boolean,             // Soft delete flag
  createdAt: Date,                // Creation timestamp
  updatedAt: Date                 // Update timestamp
}
```

### Stock Adjustment Schema
```typescript
{
  _id: Binary,                    // Primary key (UUIDv7)
  userUuid: Binary,               // User reference (required)
  warehouseUuid: Binary,          // Warehouse reference (required)
  storageUuid: Binary,            // Storage reference (required)
  productUuid: Binary,            // Product reference (required)
  quantityAdjusted: number,       // Adjustment quantity (non-zero)
  reason: string,                 // Adjustment reason
  createdAt: Date                 // Creation timestamp
}
```

## Virtual Properties

### Inventory Item Virtuals
- `uuid` - String representation of _id
- `productUuidString` - String representation of productUuid
- `storageUuidString` - String representation of storageUuid

### Storage Virtuals
- `uuid` - String representation of _id
- `warehouseUuidString` - String representation of warehouseUuid
- `userUuidString` - String representation of userUuid

### Stock Adjustment Virtuals
- `uuid` - String representation of _id
- `userUuidStr` - String representation of userUuid
- `warehouseUuidStr` - String representation of warehouseUuid
- `storageUuidStr` - String representation of storageUuid
- `productUuidStr` - String representation of productUuid

## Storage Types
```typescript
enum StorageType {
  WAREHOUSE = 'warehouse',
  VAN = 'van',
  TRUCK = 'truck',
  SHELF = 'shelf',
  BIN = 'bin',
  OTHER = 'other'
}
```

## DTOs

### Create Inventory Item DTO
```typescript
{
  productUuid: string,            // Required
  storageUuid: string,            // Required
  quantity: number                // Required
}
```

### Create Storage DTO
```typescript
{
  name: string,                   // Required
  type: StorageType,              // Required
  warehouseUuid: string,          // Required
  userUuid: string                // Required
}
```

### Create Stock Adjustment DTO
```typescript
{
  userUuid: string,               // Required
  warehouseUuid: string,          // Required
  storageUuid: string,            // Required
  productUuid: string,            // Required
  quantityAdjusted: number,       // Required (non-zero)
  reason?: string                 // Optional
}
```

## Business Logic

### Inventory Item Management
1. **Creation:** Links product to storage location with quantity
2. **Updates:** Modifies quantity or location
3. **Validation:** Ensures product and storage exist
4. **Tracking:** Maintains creation and update timestamps

### Storage Management
1. **Types:** Supports various storage types (warehouse, van, etc.)
2. **Ownership:** Links to specific user and warehouse
3. **Organization:** Hierarchical storage structure
4. **Soft Delete:** Preserves storage history

### Stock Adjustments
1. **Tracking:** Records all quantity changes
2. **Audit Trail:** Links adjustments to users
3. **Validation:** Prevents zero quantity adjustments
4. **Reasoning:** Requires reason for adjustments

## Stock Computation
- **Real-time:** Calculates current stock from adjustments
- **Historical:** Maintains adjustment history
- **Accuracy:** Ensures data consistency
- **Performance:** Optimized for large datasets

## Issues Found

### 🔴 Critical Issues
1. **`.lean()` Usage Violations**
   - Location: `inventory.service.ts` lines 94, 106, 465, 498, 534
   - Issue: Multiple instances of `.lean()` usage
   - Impact: Disables schema virtuals, violates UUID guidelines

2. **Missing Entity Fields**
   - Issue: No `createdBy`, `updatedBy` fields in some schemas
   - Impact: No user tracking for creation/updates

3. **Missing Soft Delete**
   - Issue: No `isDeleted` field in InventoryItem
   - Impact: No soft delete functionality for inventory items

### 🟡 Medium Priority Issues
1. **Inconsistent Schema Patterns**
   - Issue: Some schemas don't follow entity standards
   - Impact: Inconsistent data model

2. **Limited Validation**
   - Issue: Basic validation only
   - Impact: Data integrity concerns

## Dependencies
- `@nestjs/mongoose` - MongoDB integration
- `../utils/uuid7` - UUID generation and conversion
- `../products` - Product references
- `../warehouses` - Warehouse references
- `../users` - User references

## Related Modules
- **Products Module** - Product data
- **Warehouses Module** - Warehouse data
- **Users Module** - User data
- **Stock Computation Module** - Stock calculations
- **Sales Module** - Stock consumption
- **Purchase Module** - Stock additions

## Database Indexes
- `productUuid` - For product-specific queries
- `storageUuid` - For storage-specific queries
- `warehouseUuid` - For warehouse-specific queries
- `userUuid` - For user-specific queries
- `_id` - Primary key (automatic)

## Performance Considerations
- Indexed fields for efficient querying
- Stock computation optimization
- Large dataset handling
- Real-time updates

## Security Considerations
- UUIDv7 for secure identifiers
- Warehouse scoping for data isolation
- User tracking for all operations
- Input validation for all fields

## Future Improvements
1. Fix `.lean()` usage violations
2. Add missing entity fields
3. Implement stock alerts
4. Add inventory forecasting
5. Implement barcode scanning
6. Add inventory reports
7. Implement stock transfers
8. Add inventory analytics 