import axios from "axios";
import { getAllProductCategoriesRaw, ProductCategory } from "../products/productCategoriesApi";
import { getAxiosAuthHeaders } from '@/utils/authHeaders';

export interface StockLevel {
  productUuid: string;
  quantity: number;
}

export interface Product {
  uuid: string;
  name: string;
  description?: string;
  sku?: string;
  barcode?: string;
  category?: string;
  price?: number;
  cost?: number;
  productCategoryUuidString?: string; // Backend returns this field name
}

export interface Storage {
  uuid: string;
  name: string;
  warehouseUuid: string;
  userUuid: string;
  createdAt: string;
  updatedAt: string;
  isDeleted: boolean;
  type: string;
}

export interface StockLevelWithProduct extends StockLevel {
  productName: string;
  productDescription?: string;
  productSku?: string;
  productPrice?: number;
  productCost?: number;
}

export interface ProductFilterOptions {
  name?: string;
  category?: string;
  sku?: string;
  barcode?: string;
  minPrice?: number;
  maxPrice?: number;
  productCategoryUuid?: string;
}

const INVENTORY_API = "/api/inventory";
const PRODUCTS_API = "/api/products";

// Get stock levels for specific products in a storage
export async function getStockLevels(storageUuid: string, productUuids: string[]): Promise<StockLevel[]> {
  if (!storageUuid || !productUuids.length) return [];
  
  // Split into chunks to avoid URL length limits
  const chunkSize = 50;
  const chunks = [];
  for (let i = 0; i < productUuids.length; i += chunkSize) {
    chunks.push(productUuids.slice(i, i + chunkSize));
  }
  
  const results = await Promise.all(
    chunks.map(chunk => 
      axios.get(`${INVENTORY_API}/stock/levels`, {
        params: {
          storageUuid,
          productUuids: chunk.join(',')
        },
        headers: getAxiosAuthHeaders(),
      })
    )
  );
  
  return results.flatMap(response => response.data);
}

// Get stock levels for all products in a storage (more efficient endpoint)
export async function getStockLevelsForStorage(storageUuid: string): Promise<StockLevel[]> {
  if (!storageUuid) return [];
  
  const response = await axios.get(`${INVENTORY_API}/stock/levels/${storageUuid}`, {
    headers: getAxiosAuthHeaders(),
  });
  return response.data;
}

// Get all products for a warehouse (no pagination)
export async function getAllProductsByWarehouse(warehouseUuid: string): Promise<Product[]> {
  if (!warehouseUuid) return [];
  
  const response = await axios.get(`${PRODUCTS_API}/by-warehouse/${warehouseUuid}`, {
    headers: getAxiosAuthHeaders(),
  });
  return response.data;
}



// Search products by query
export async function searchProducts(
  warehouseUuid: string,
  query: string,
  page: number = 1,
  limit: number = 20
): Promise<{ data: Product[], total: number, page: number, limit: number, hasNext: boolean, hasPrev: boolean }> {
  if (!warehouseUuid || !query) return { data: [], total: 0, page: 1, limit, hasNext: false, hasPrev: false };
  
  const params = new URLSearchParams({
    warehouseUuid,
    search: query, // changed from 'query' to 'search'
    page: page.toString(),
    limit: limit.toString()
  });
  
  const response = await axios.get(`${PRODUCTS_API}/filter?${params.toString()}`, {
    headers: getAxiosAuthHeaders(),
  });
  return response.data;
}

// Get product categories for a warehouse (using the proper API)
export async function getProductCategories(warehouseUuid: string): Promise<ProductCategory[]> {
  try {
    return await getAllProductCategoriesRaw(warehouseUuid);
  } catch (error) {
    console.error('Error fetching product categories:', error);
    return [];
  }
}

// Get storage locations for a warehouse
export async function getStoragesByWarehouse(warehouseUuid: string): Promise<Storage[]> {
  if (!warehouseUuid) return [];
  
  const response = await axios.get(`${INVENTORY_API}/storage`, {
    params: { warehouseUuid },
    headers: getAxiosAuthHeaders(),
  });
  return response.data;
}

// Combine stock levels with product information
export async function getStockLevelsWithProducts(
  storageUuid: string,
  warehouseUuid: string,
  filters?: ProductFilterOptions
): Promise<StockLevelWithProduct[]> {
  if (!storageUuid || !warehouseUuid) return [];
  
  try {
    // Get stock levels for all products in the storage
    const stockLevels = await getStockLevelsForStorage(storageUuid);
    
    if (stockLevels.length === 0) return [];
    
    // Get product details for all products with stock
    const productUuids = stockLevels.map(sl => sl.productUuid);
    
    // Use the new filter endpoint if filters are provided
    let products: Product[] = [];
    if (filters && Object.keys(filters).some(key => filters[key as keyof ProductFilterOptions])) {
      // Use advanced filtering
      const result = await getProductsWithAdvancedFilters(warehouseUuid, filters, 1, 1000); // Get all filtered products
      products = result.data;
    } else {
      // Get all products for the warehouse
      products = await getAllProductsByWarehouse(warehouseUuid);
    }
    
    // Filter products to only include those with stock levels
    const productsWithStock = products.filter(product => 
      productUuids.includes(product.uuid)
    );
    
    // Combine stock levels with product information
    return stockLevels.map(stockLevel => {
      const product = productsWithStock.find(p => p.uuid === stockLevel.productUuid);
      return {
        ...stockLevel,
        productName: product?.name || 'Unknown Product',
        productDescription: product?.description,
        productSku: product?.sku,
        productPrice: product?.price,
        productCost: product?.cost
      };
    });
  } catch (error) {
    console.error('Error getting stock levels with products:', error);
    return [];
  }
} 

// Get products with advanced filtering (including category filtering)
export async function getProductsWithAdvancedFilters(
  warehouseUuid: string,
  filters: ProductFilterOptions,
  page: number = 1,
  limit: number = 20
): Promise<{ data: Product[], total: number, page: number, limit: number, hasNext: boolean, hasPrev: boolean }> {
  if (!warehouseUuid) return { data: [], total: 0, page: 1, limit, hasNext: false, hasPrev: false };
  
  console.log('getProductsWithAdvancedFilters called with:', { warehouseUuid, filters, page, limit });
  
  const params = new URLSearchParams({
    warehouseUuid,
    page: page.toString(),
    limit: limit.toString()
  });
  
  // Add filter parameters if they exist
  if (filters.name) params.append('name', filters.name);
  if (filters.productCategoryUuid) params.append('productCategoryUuid', filters.productCategoryUuid);
  if (filters.sku) params.append('sku', filters.sku);
  if (filters.barcode) params.append('barcode', filters.barcode);
  if (filters.minPrice !== undefined) params.append('minPrice', filters.minPrice.toString());
  if (filters.maxPrice !== undefined) params.append('maxPrice', filters.maxPrice.toString());
  
  const url = `/api/products/filter/advanced?${params.toString()}`;
  console.log('Making request to:', url);
  
  const response = await axios.get(url, {
    headers: getAxiosAuthHeaders(),
  });
  return response.data;
} 