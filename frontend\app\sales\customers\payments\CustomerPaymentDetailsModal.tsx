// CustomerPaymentDetailsModal.tsx - Modal for viewing customer payment details
// Follows UI Guidelines: Modal UX Conventions (All Resource Modals)
// - Escape key closes modal
// - Displays comprehensive payment information

import React, { useEffect, useRef } from 'react';
import { 
  CustomerPayment,
  formatCurrency,
  formatPaymentMethod,
  formatPaymentStatus,
  getStatusColor,
  formatCustomerType
} from '../customerPaymentsApi';
import { 
  useCustomerWithCredit, 
  useCustomerSales 
} from '../useCustomerPayments';
import { X, DollarSign, Calendar, User, CreditCard, FileText, Hash, Building, Receipt, Users, ShoppingCart } from 'lucide-react';

interface CustomerPaymentDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  payment: CustomerPayment | null;
}

const CustomerPaymentDetailsModal: React.FC<CustomerPaymentDetailsModalProps> = ({
  isOpen,
  onClose,
  payment,
}) => {
  const modalRef = useRef<HTMLDivElement>(null);

  // Fetch customer details and sales history for context
  const { data: customerDetails } = useCustomerWithCredit(payment?.customerUuid);
  const { data: customerSales } = useCustomerSales(payment?.customerUuid, 1, 5); // Get recent sales

  // Handle modal focus and escape key
  useEffect(() => {
    if (!isOpen) return;

    // Handle escape key
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, onClose]);

  if (!isOpen || !payment) return null;

  // Format date and time
  const formatDateTime = (dateString: string | Date) => {
    const date = new Date(dateString);
    return {
      date: date.toLocaleDateString(),
      time: date.toLocaleTimeString(),
    };
  };

  const createdDate = formatDateTime(payment.createdAt);
  const updatedDate = formatDateTime(payment.updatedAt);
  const processedDate = payment.processedAt ? formatDateTime(payment.processedAt) : null;

  const statusColorClass = getStatusColor(payment.status);

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div
        ref={modalRef}
        className="bg-white rounded-lg shadow-xl w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto"
        role="dialog"
        aria-modal="true"
        aria-labelledby="payment-details-modal-title"
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 sticky top-0 bg-white">
          <div className="flex items-center space-x-3">
            <DollarSign className="h-6 w-6 text-green-600" />
            <div>
              <h2 id="payment-details-modal-title" className="text-lg font-semibold text-gray-900">
                Payment Details
              </h2>
              <p className="text-sm text-gray-600">
                {formatCurrency(payment.amount)} • {formatPaymentMethod(payment.paymentMethod)}
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 rounded-full p-1"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Status Banner */}
          <div className={`px-4 py-3 rounded-lg border ${statusColorClass}`}>
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <span className="font-medium">Status: {formatPaymentStatus(payment.status)}</span>
              </div>
              {processedDate && (
                <div className="text-sm">
                  Processed: {processedDate.date} at {processedDate.time}
                </div>
              )}
            </div>
          </div>

          {/* Payment Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Left Column */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900 border-b border-gray-200 pb-2">
                Payment Information
              </h3>

              {/* Amount */}
              <div className="flex items-center space-x-3">
                <DollarSign className="h-5 w-5 text-green-600" />
                <div>
                  <p className="text-sm text-gray-600">Amount</p>
                  <p className="text-lg font-semibold text-green-600">
                    {formatCurrency(payment.amount)}
                  </p>
                </div>
              </div>

              {/* Payment Method */}
              <div className="flex items-center space-x-3">
                <CreditCard className="h-5 w-5 text-blue-600" />
                <div>
                  <p className="text-sm text-gray-600">Payment Method</p>
                  <p className="font-medium">
                    {formatPaymentMethod(payment.paymentMethod)}
                  </p>
                </div>
              </div>

              {/* Reference Number */}
              {payment.referenceNumber && (
                <div className="flex items-center space-x-3">
                  <Hash className="h-5 w-5 text-gray-600" />
                  <div>
                    <p className="text-sm text-gray-600">Reference Number</p>
                    <p className="font-medium font-mono text-sm">
                      {payment.referenceNumber}
                    </p>
                  </div>
                </div>
              )}

              {/* Sale Invoice */}
              {payment.saleInvoiceNumber && (
                <div className="flex items-center space-x-3">
                  <Receipt className="h-5 w-5 text-purple-600" />
                  <div>
                    <p className="text-sm text-gray-600">Related Invoice</p>
                    <p className="font-medium">
                      {payment.saleInvoiceNumber}
                    </p>
                  </div>
                </div>
              )}
            </div>

            {/* Right Column */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900 border-b border-gray-200 pb-2">
                Related Information
              </h3>

              {/* Customer */}
              <div className="space-y-3">
                <div className="flex items-center space-x-3">
                  <User className="h-5 w-5 text-blue-600" />
                  <div className="flex-1">
                    <p className="text-sm text-gray-600">Customer</p>
                    <p className="font-medium">
                      {payment.customerName || customerDetails?.name || 'Unknown Customer'}
                    </p>
                    {customerDetails && (
                      <div className="mt-2 p-3 bg-gray-50 rounded-md">
                        <div className="grid grid-cols-2 gap-3 text-sm">
                          <div>
                            <span className="text-gray-600">Type:</span>
                            <span className="ml-1 font-medium">{formatCustomerType(customerDetails.customerType)}</span>
                          </div>
                          <div>
                            <span className="text-gray-600">Credit Balance:</span>
                            <span className={`ml-1 font-semibold ${
                              customerDetails.currentCredit >= 0 ? 'text-green-600' : 'text-red-600'
                            }`}>
                              {formatCurrency(customerDetails.currentCredit)}
                            </span>
                          </div>
                          {customerDetails.fiscalId && (
                            <div>
                              <span className="text-gray-600">Fiscal ID:</span>
                              <span className="ml-1 font-medium">{customerDetails.fiscalId}</span>
                            </div>
                          )}
                          {customerDetails.email && (
                            <div>
                              <span className="text-gray-600">Email:</span>
                              <span className="ml-1 font-medium">{customerDetails.email}</span>
                            </div>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* User */}
              <div className="flex items-center space-x-3">
                <User className="h-5 w-5 text-orange-600" />
                <div>
                  <p className="text-sm text-gray-600">Processed By</p>
                  <p className="font-medium">
                    {payment.userName || 'Unknown User'}
                  </p>
                </div>
              </div>

              {/* Warehouse */}
              <div className="flex items-center space-x-3">
                <Building className="h-5 w-5 text-gray-600" />
                <div>
                  <p className="text-sm text-gray-600">Warehouse</p>
                  <p className="font-medium">
                    {payment.warehouseName || 'Unknown Warehouse'}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Description */}
          {payment.description && (
            <div>
              <h3 className="text-lg font-medium text-gray-900 border-b border-gray-200 pb-2 mb-3">
                Description
              </h3>
              <div className="flex items-start space-x-3">
                <FileText className="h-5 w-5 text-gray-600 mt-0.5" />
                <div className="bg-gray-50 p-3 rounded-md flex-1">
                  <p className="text-gray-700 whitespace-pre-wrap">
                    {payment.description}
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Customer Sales Context */}
          {customerSales && customerSales.data.length > 0 && (
            <div>
              <h3 className="text-lg font-medium text-gray-900 border-b border-gray-200 pb-2 mb-3 flex items-center">
                <ShoppingCart className="h-5 w-5 mr-2" />
                Recent Customer Sales
              </h3>
              <div className="space-y-2">
                {customerSales.data.slice(0, 3).map((sale) => (
                  <div key={sale.uuid} className="p-3 bg-gray-50 rounded-md">
                    <div className="flex justify-between items-start">
                      <div>
                        <p className="font-medium text-sm">{sale.invoiceNumber}</p>
                        <p className="text-xs text-gray-600">
                          {new Date(sale.invoiceDate).toLocaleDateString()}
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="font-medium text-sm">
                          {formatCurrency(sale.totalAmount)}
                        </p>
                        <p className="text-xs text-gray-600">
                          Paid: {formatCurrency(sale.amountPaid)}
                        </p>
                        <p className={`text-xs font-medium ${
                          sale.balanceDue > 0 ? 'text-red-600' : 'text-green-600'
                        }`}>
                          {sale.balanceDue > 0 ? 
                            `Due: ${formatCurrency(sale.balanceDue)}` : 
                            'Paid'
                          }
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
                {customerSales.total > 3 && (
                  <p className="text-xs text-gray-500 text-center">
                    And {customerSales.total - 3} more sales...
                  </p>
                )}
              </div>
            </div>
          )}

          {/* System Information */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 border-b border-gray-200 pb-2 mb-3">
              System Information
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Payment UUID */}
              <div className="flex items-center space-x-3">
                <Hash className="h-5 w-5 text-gray-600" />
                <div>
                  <p className="text-sm text-gray-600">Payment ID</p>
                  <p className="font-mono text-sm text-gray-800">
                    {payment.uuid}
                  </p>
                </div>
              </div>

              {/* Customer UUID */}
              <div className="flex items-center space-x-3">
                <User className="h-5 w-5 text-gray-600" />
                <div>
                  <p className="text-sm text-gray-600">Customer ID</p>
                  <p className="font-mono text-sm text-gray-800">
                    {payment.customerUuid}
                  </p>
                </div>
              </div>

              {/* Sale UUID */}
              {payment.saleUuid && (
                <div className="flex items-center space-x-3">
                  <Receipt className="h-5 w-5 text-gray-600" />
                  <div>
                    <p className="text-sm text-gray-600">Sale ID</p>
                    <p className="font-mono text-sm text-gray-800">
                      {payment.saleUuid}
                    </p>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Timestamps */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 border-b border-gray-200 pb-2 mb-3">
              Timeline
            </h3>
            <div className="space-y-3">
              {/* Created */}
              <div className="flex items-center space-x-3">
                <Calendar className="h-5 w-5 text-green-600" />
                <div>
                  <p className="text-sm text-gray-600">Created</p>
                  <p className="font-medium">
                    {createdDate.date} at {createdDate.time}
                  </p>
                </div>
              </div>

              {/* Last Updated */}
              {payment.updatedAt !== payment.createdAt && (
                <div className="flex items-center space-x-3">
                  <Calendar className="h-5 w-5 text-blue-600" />
                  <div>
                    <p className="text-sm text-gray-600">Last Updated</p>
                    <p className="font-medium">
                      {updatedDate.date} at {updatedDate.time}
                    </p>
                  </div>
                </div>
              )}

              {/* Processed */}
              {processedDate && (
                <div className="flex items-center space-x-3">
                  <Calendar className="h-5 w-5 text-purple-600" />
                  <div>
                    <p className="text-sm text-gray-600">Processed</p>
                    <p className="font-medium">
                      {processedDate.date} at {processedDate.time}
                    </p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex justify-end p-6 border-t border-gray-200 bg-gray-50">
          <button
            onClick={onClose}
            className="bg-gray-600 text-white py-2 px-6 rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default CustomerPaymentDetailsModal; 