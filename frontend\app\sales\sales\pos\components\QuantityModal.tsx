import React, { useEffect } from 'react';
import { posStyles } from '../styles/posStyles';
import { formatCurrency } from '../utils/posHelpers';
import type { QuantityModalProps } from '../types';

export function QuantityModal({
  isOpen,
  product,
  quantity,
  price,
  onQuantityChange,
  onPriceChange,
  onConfirm,
  onCancel,
  quantityInputRef,
  priceInputRef,
}: QuantityModalProps) {
  // Handle escape key to close modal and auto-focus quantity field
  useEffect(() => {
    if (!isOpen) return;
    
    // Auto-focus quantity field when modal opens
    const focusQuantityField = () => {
      if (quantityInputRef?.current) {
        quantityInputRef.current.focus();
        quantityInputRef.current.select();
      }
    };
    
    // Small delay to ensure modal is fully rendered
    const focusTimeout = setTimeout(focusQuantityField, 100);
    
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onCancel();
      }
    };
    
    window.addEventListener('keydown', handleKeyDown);
    
    return () => {
      clearTimeout(focusTimeout);
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [isOpen, onCancel, quantityInputRef]);

  if (!isOpen || !product) return null;

  const handleQuantityKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      // If price field exists, focus it, otherwise confirm
      if (priceInputRef?.current) {
        priceInputRef.current.focus();
        priceInputRef.current.select();
      } else {
        onConfirm();
      }
    } else if (e.key === 'Escape') {
      e.preventDefault();
      onCancel();
    }
  };

  const handlePriceKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      onConfirm();
    } else if (e.key === 'Escape') {
      e.preventDefault();
      onCancel();
    }
  };

  const quantityNum = parseFloat(quantity) || 0;
  const priceNum = parseFloat(price) || 0;
  const totalPrice = quantityNum * priceNum;

  return (
    <div className={posStyles.modal.overlay}>
      <div className="bg-white rounded-lg shadow-lg max-w-lg w-full mx-4 max-h-[90vh] overflow-y-auto">
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-bold text-gray-900">
            Add to Sale
          </h3>
        </div>
        
        <div className="p-6">
          <div className="space-y-6">
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <div className="font-semibold text-gray-900 text-lg">
                {product.name}
              </div>
              {product.price && (
                <div className="text-green-600 font-bold mt-1">
                  List Price: {formatCurrency(product.price)}
                </div>
              )}
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700">
                  Quantity
                </label>
                <input
                  ref={quantityInputRef}
                  type="number"
                  value={quantity}
                  onChange={(e) => onQuantityChange(e.target.value)}
                  min="0.01"
                  step="0.01"
                  className="w-full p-3 text-center border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-lg"
                  onFocus={(e) => e.target.select()}
                  onKeyDown={handleQuantityKeyDown}
                />
              </div>
              
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700">
                  Unit Price ($)
                </label>
                <input
                  ref={priceInputRef}
                  type="number"
                  value={price}
                  onChange={(e) => onPriceChange(e.target.value)}
                  min="0"
                  step="0.01"
                  className="w-full p-3 text-center border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-lg"
                  onFocus={(e) => e.target.select()}
                  onKeyDown={handlePriceKeyDown}
                />
              </div>
            </div>
            
            {totalPrice > 0 && (
              <div className="text-center p-4 bg-blue-50 rounded-lg">
                <div className="text-sm text-gray-600">Line Total</div>
                <div className="text-2xl font-bold text-blue-600">
                  {formatCurrency(totalPrice)}
                </div>
              </div>
            )}
          </div>
        </div>
        
        <div className="p-6 border-t border-gray-200 flex gap-3">
          <button
            onClick={onCancel}
            className="flex-1 bg-gray-300 text-gray-700 py-3 px-4 rounded-lg hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500"
          >
            Cancel
          </button>
          <button
            onClick={onConfirm}
            className="flex-1 bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            disabled={quantityNum <= 0 || priceNum < 0}
          >
            Add to Sale
          </button>
        </div>
      </div>
    </div>
  );
} 