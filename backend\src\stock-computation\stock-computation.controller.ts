import { Controller, Get, Param, Query, UseGuards } from "@nestjs/common";
import { StockComputationService } from "./stock-computation.service";
import { ApiTags, ApiOperation, ApiParam, ApiQuery } from "@nestjs/swagger";
@ApiTags("Stock Computation")
@Controller("stock-computation")
export class StockComputationController {
  constructor(
    private readonly stockComputationService: StockComputationService,
  ) {}

  @Get("product/:productUuid")
  @ApiOperation({ summary: "Recompute stock for a specific product" })
  @ApiParam({
    name: "productUuid",
    description: "Product UUID (string or hex)",
  })
  async getProductStock(@Param("productUuid") productUuid: string) {
    const stock =
      await this.stockComputationService.computeProductStock(productUuid);
    return { productUuid, stock };
  }

  @Get("storage/:storageUuid")
  @ApiOperation({
    summary: "Recompute all product stocks in a storage location",
  })
  @ApiParam({
    name: "storageUuid",
    description: "Storage UUID (string or hex)",
  })
  async getStorageStocks(@Param("storageUuid") storageUuid: string) {
    const stocks =
      await this.stockComputationService.computeAllStorageStocks(storageUuid);
    return { storageUuid, stocks };
  }

  @Get("warehouse/:warehouseUuid")
  @ApiOperation({ summary: "Recompute all product stocks in a warehouse" })
  @ApiParam({
    name: "warehouseUuid",
    description: "Warehouse UUID (string or hex)",
  })
  async getWarehouseStocks(@Param("warehouseUuid") warehouseUuid: string) {
    const stocks =
      await this.stockComputationService.computeAllWarehouseStocks(
        warehouseUuid,
      );
    return { warehouseUuid, stocks };
  }
}
