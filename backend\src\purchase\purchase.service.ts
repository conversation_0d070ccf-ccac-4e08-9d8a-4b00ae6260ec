import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";
import { Purchase, PaymentMethods, PurchaseStatus } from "./purchase.entity";
import { CreatePurchaseDto } from "./dto/create-purchase.dto";
import { FilterPurchaseDto } from "./dto/filter-purchase.dto";
import {
  PurchaseDto,
  toPurchaseDto,
  toPurchaseDtoArray,
} from "./dto/purchase.dto";
import { Warehouse } from "../warehouses/warehouse.entity";
import { User } from "../users/user.entity";
import { Supplier } from "../suppliers/supplier.entity";
import { Uuid7 } from "../utils/uuid7";

@Injectable()
export class PurchaseService {
  constructor(
    @InjectRepository(Purchase) private purchaseRepository: Repository<Purchase>,
    @InjectRepository(Warehouse) private warehouseRepository: Repository<Warehouse>,
    @InjectRepository(User) private userRepository: Repository<User>,
    @InjectRepository(Supplier) private supplierRepository: Repository<Supplier>,
  ) {}

  async create(dto: CreatePurchaseDto): Promise<PurchaseDto> {
    const now = new Date();
    const { userUuid, warehouseUuid, supplierUuid, items } = dto;

    if (!userUuid || !warehouseUuid) {
      throw new BadRequestException(
        "userUuid and warehouseUuid are required to create a purchase",
      );
    }

    // Check user exists
    const user = await this.userRepository.findOne({ where: { id: userUuid } });
    if (!user) throw new NotFoundException("User not found");

    // Check warehouse exists
    const warehouse = await this.warehouseRepository.findOne({
      where: { id: warehouseUuid },
    });
    if (!warehouse) throw new NotFoundException("Warehouse not found");

    // Check supplier exists if provided
    if (supplierUuid) {
      const supplier = await this.supplierRepository.findOne({
        where: { id: supplierUuid, isDeleted: false },
      });
      if (!supplier) throw new NotFoundException("Supplier not found");
    }

    // Calculate totals
    let totalAmount = 0;
    let itemsSnapshot = Array.isArray(items)
      ? items.map((item) => {
          totalAmount += item.lineTotal;
          return {
            ...item,
            quantity: item.quantity,
            unitPrice: item.unitPrice,
            lineTotal: item.lineTotal,
          };
        })
      : [];

    const purchase = new Purchase();
    purchase.id = Purchase.generateId();
    purchase.userUuid = userUuid;
    purchase.warehouseUuid = warehouseUuid;
    purchase.supplierUuid = supplierUuid;
    purchase.itemsSnapshot = itemsSnapshot;
    purchase.totalAmount = totalAmount;
    purchase.amountPaid = 0;
    purchase.balanceDue = totalAmount;
    purchase.paymentMethod = PaymentMethods.CASH;
    purchase.paymentDate = now;
    purchase.invoiceDate = now;
    purchase.dueDate = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);
    purchase.status = PurchaseStatus.UNPAID;
    purchase.isDeleted = false;

    const saved = await this.purchaseRepository.save(purchase);
    return toPurchaseDto(saved);
  }

  async addProducts(uuid: string, items: any[], userUuid: string): Promise<PurchaseDto> {
    const purchase = await this.purchaseRepository.findOne({
      where: { id: uuid, isDeleted: false },
    });
    if (!purchase) throw new NotFoundException("Purchase not found");

    // Validate that the authenticated user owns this purchase
    if (purchase.userUuid !== userUuid) {
      throw new BadRequestException("You can only modify purchases that you created");
    }

    purchase.itemsSnapshot = items;
    purchase.totalAmount = items.reduce((sum, item) => sum + item.lineTotal, 0);
    purchase.balanceDue = purchase.totalAmount - purchase.amountPaid;

    const updated = await this.purchaseRepository.save(purchase);
    return toPurchaseDto(updated);
  }

  async setPayment(
    uuid: string,
    paymentMethod: PaymentMethods,
    amountPaid: number,
    userUuid: string,
  ): Promise<PurchaseDto> {
    const purchase = await this.purchaseRepository.findOne({
      where: { id: uuid, isDeleted: false },
    });
    if (!purchase) throw new NotFoundException("Purchase not found");

    // Validate that the authenticated user owns this purchase
    if (purchase.userUuid !== userUuid) {
      throw new BadRequestException("You can only modify payments for purchases that you created");
    }

    purchase.paymentMethod = paymentMethod;
    purchase.amountPaid = amountPaid;
    purchase.balanceDue = purchase.totalAmount - amountPaid;
    purchase.status =
      purchase.balanceDue <= 0
        ? PurchaseStatus.PAID
        : PurchaseStatus.PARTIALLY_PAID;

    const updated = await this.purchaseRepository.save(purchase);
    return toPurchaseDto(updated);
  }

  async findAll(query: any, userUuid?: string): Promise<PurchaseDto[]> {
    const queryBuilder = this.purchaseRepository
      .createQueryBuilder('purchase')
      .where('purchase.isDeleted = :isDeleted', { isDeleted: false });

    // Only filter by userUuid if provided
    if (userUuid) {
      queryBuilder.andWhere('purchase.userUuid = :userUuid', { userUuid });
    }

    // Handle query parameters
    if (query.warehouseUuid) {
      queryBuilder.andWhere('purchase.warehouseUuid = :warehouseUuid', { 
        warehouseUuid: query.warehouseUuid 
      });
    }
    if (query.supplierUuid) {
      queryBuilder.andWhere('purchase.supplierUuid = :supplierUuid', { 
        supplierUuid: query.supplierUuid 
      });
    }
    if (query.status) {
      queryBuilder.andWhere('purchase.status = :status', { status: query.status });
    }
    if (query.createdFrom || query.createdTo) {
      if (query.createdFrom) {
        queryBuilder.andWhere('purchase.createdAt >= :createdFrom', { 
          createdFrom: new Date(query.createdFrom) 
        });
      }
      if (query.createdTo) {
        queryBuilder.andWhere('purchase.createdAt <= :createdTo', { 
          createdTo: new Date(query.createdTo) 
        });
      }
    }

    const purchases = await queryBuilder.getMany();
    return toPurchaseDtoArray(purchases);
  }

  async filterPurchases(dto: FilterPurchaseDto, userUuid?: string): Promise<PurchaseDto[]> {
    const queryBuilder = this.purchaseRepository
      .createQueryBuilder('purchase')
      .where('purchase.isDeleted = :isDeleted', { isDeleted: false });

    // Only filter by userUuid if provided
    if (userUuid) {
      queryBuilder.andWhere('purchase.userUuid = :userUuid', { userUuid });
    }

    if (dto.warehouseUuid) {
      queryBuilder.andWhere('purchase.warehouseUuid = :warehouseUuid', { 
        warehouseUuid: dto.warehouseUuid 
      });
    }
    if (dto.supplierUuid) {
      queryBuilder.andWhere('purchase.supplierUuid = :supplierUuid', { 
        supplierUuid: dto.supplierUuid 
      });
    }
    if (dto.status) {
      queryBuilder.andWhere('purchase.status = :status', { status: dto.status });
    }
    if (dto.createdFrom || dto.createdTo) {
      if (dto.createdFrom) {
        queryBuilder.andWhere('purchase.createdAt >= :createdFrom', { 
          createdFrom: new Date(dto.createdFrom) 
        });
      }
      if (dto.createdTo) {
        queryBuilder.andWhere('purchase.createdAt <= :createdTo', { 
          createdTo: new Date(dto.createdTo) 
        });
      }
    }

    const purchases = await queryBuilder.getMany();
    return toPurchaseDtoArray(purchases);
  }

  async findOne(uuid: string, userUuid?: string): Promise<PurchaseDto> {
    const purchase = await this.purchaseRepository.findOne({
      where: { id: uuid, isDeleted: false },
    });
    if (!purchase) throw new NotFoundException("Purchase not found");
    
    // Only validate ownership if userUuid is provided
    if (userUuid && purchase.userUuid !== userUuid) {
      throw new BadRequestException("You can only view purchases that you created");
    }
    
    return toPurchaseDto(purchase);
  }

  async remove(uuid: string, userUuid: string): Promise<void> {
    const purchase = await this.purchaseRepository.findOne({
      where: { id: uuid, isDeleted: false },
    });
    if (!purchase) throw new NotFoundException("Purchase not found");

    // Validate that the authenticated user owns this purchase
    if (purchase.userUuid !== userUuid) {
      throw new BadRequestException("You can only delete purchases that you created");
    }

    const result = await this.purchaseRepository.update(
      { id: uuid, isDeleted: false },
      { isDeleted: true }
    );
    if (result.affected === 0) throw new NotFoundException("Purchase not found");
  }
} 