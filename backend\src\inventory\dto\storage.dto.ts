import { ApiProperty } from "@nestjs/swagger";
import { StorageType } from "../storage_type.enum";

export class StorageDto {
  @ApiProperty({ enum: StorageType, example: StorageType.WAREHOUSE })
  type: StorageType;
  @ApiProperty({ example: "uuid-string" })
  uuid: string;

  @ApiProperty({ example: "Main Warehouse" })
  name: string;

  @ApiProperty({ example: "warehouse-uuid-string" })
  warehouseUuid: string;

  @ApiProperty({ example: "user-uuid-string" })
  userUuid: string;

  @ApiProperty({ example: "2025-06-17T19:36:22.000Z" })
  createdAt: Date;

  @ApiProperty({ example: "2025-06-17T19:36:22.000Z" })
  updatedAt: Date;

  @ApiProperty({ example: false })
  isDeleted: boolean;
}
