import 'dart:convert';
import 'dart:convert' show base64Url;
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:flutter/services.dart';
import '../models/user.dart';
import '../models/auth_response.dart';
import '../../core/constants/app_constants.dart';
import 'api_service.dart';
import 'user_service.dart';

class AuthService extends ChangeNotifier {
  factory AuthService() => _instance;
  AuthService._internal();
  static final AuthService _instance = AuthService._internal();

  User? _currentUser;
  String? _accessToken;
  String? _refreshToken;
  bool _isAuthenticated = false;
  bool _isLoading = false;

  // Google Sign In instance
  late GoogleSignIn _googleSignIn;
  
  // User service instance
  final UserService _userService = UserService();

  /// Initialize Google Sign In
  void _initializeGoogleSignIn() {
    debugPrint('=== GOOGLE SIGN-IN INITIALIZATION ===');
    
    // Debug: Show platform-specific configuration information
    debugPrint('Platform Configuration Debug:');
    debugPrint('- Expected Android Package: com.example.dido_distribution_mobile');
    debugPrint('- Expected SHA-1 Fingerprint: 33:54:56:67:B6:80:EE:B7:EF:52:8A:D7:9C:6D:80:CA:97:1A:19:B8');
    debugPrint('- Android Client ID (from strings.xml): should be configured for Android platform');
    debugPrint('- iOS Client ID (from Info.plist): should be configured for iOS platform');
    
    _googleSignIn = GoogleSignIn(
      scopes: ['email', 'profile'],
      // Note: Client ID will be configured in platform-specific files
    );
    debugPrint('Google Sign-In initialized with scopes: ${_googleSignIn.scopes}');
    debugPrint('=== END INITIALIZATION ===');
  }

  /// Debug method to check Google Sign-In configuration
  Future<void> debugGoogleSignInConfig() async {
    debugPrint('=== GOOGLE SIGN-IN CONFIG CHECK ===');
    
    try {
      // Check if Google Sign-In is available
      final isAvailable = await _googleSignIn.isSignedIn();
      debugPrint('Google Sign-In available: $isAvailable');
      
      // Check current user
      final currentUser = _googleSignIn.currentUser;
      debugPrint('Current Google user: ${currentUser?.email ?? 'None'}');
      
      // Check scopes
      debugPrint('Configured scopes: ${_googleSignIn.scopes}');
      
      // Configuration verification
      debugPrint('=== CONFIGURATION VERIFICATION ===');
      debugPrint('Expected Configuration:');
      debugPrint('- Android Package: com.example.dido_distribution_mobile');
      debugPrint('- SHA-1 Fingerprint: 33:54:56:67:B6:80:EE:B7:EF:52:8A:D7:9C:6D:80:CA:97:1A:19:B8');
      debugPrint('- Android Client ID: 934882028906-qthedutpkt9uf4197dq5rmd9k2pn5ug8.apps.googleusercontent.com');
      debugPrint('');
      
      // Try to get actual SHA-1 fingerprint information
      await _debugActualSigningInfo();
      
      debugPrint('Google Cloud Console Checklist:');
      debugPrint('1. ✓ Android OAuth Client created with correct package name');
      debugPrint('2. ⚠️  SHA-1 fingerprint needs verification (see above)');
      debugPrint('3. ? iOS OAuth Client created (not yet configured)');
      debugPrint('4. ? Backend has correct client IDs in .env file');
      
    } catch (e) {
      debugPrint('Error checking Google Sign-In config: $e');
    }
    
    debugPrint('=== END CONFIG CHECK ===');
  }

  /// Debug method to show actual signing information
  Future<void> _debugActualSigningInfo() async {
    debugPrint('=== ACTUAL SIGNING INFO DEBUG ===');
    
    try {
      // This is the most reliable way to check SHA-1 mismatch
      debugPrint('⚠️  CRITICAL: SHA-1 Fingerprint Verification Required');
      debugPrint('');
      debugPrint('The ApiException: 10 error typically means:');
      debugPrint('1. Your Google Cloud Console Android OAuth client exists');
      debugPrint('2. But the SHA-1 fingerprint doesn\'t match your actual debug keystore');
      debugPrint('');
      debugPrint('TO FIX THIS ISSUE:');
      debugPrint('');
      debugPrint('1. Get your ACTUAL SHA-1 fingerprint by running:');
      debugPrint('   cd mobile/android');
      debugPrint('   ./gradlew signingReport');
      debugPrint('');
      debugPrint('2. Look for the SHA1 value under "Variant: debug"');
      debugPrint('');
      debugPrint('3. Add that EXACT SHA-1 to your Google Cloud Console OAuth client');
      debugPrint('   (it might be different from: 33:54:56:67:B6:80:EE:B7:EF:52:8A:D7:9C:6D:80:CA:97:1A:19:B8)');
      debugPrint('');
      debugPrint('4. Alternative: Use keytool directly:');
      debugPrint('   keytool -list -v -keystore ~/.android/debug.keystore -alias androiddebugkey -storepass android -keypass android');
      debugPrint('');
      debugPrint('Current status: Expected SHA-1 vs Actual SHA-1 MISMATCH suspected');
      
    } catch (e) {
      debugPrint('Error getting signing info: $e');
    }
    
    debugPrint('=== END SIGNING INFO DEBUG ===');
  }

  // Getters
  User? get currentUser => _currentUser;
  String? get accessToken => _accessToken;
  bool get isAuthenticated => _isAuthenticated;
  bool get isLoading => _isLoading;

  // Role-based getters
  bool get isAdmin => _currentUser?.isAdmin ?? false;
  bool get isMobileSaleAgent => _currentUser?.isMobileSaleAgent ?? false;
  bool get isManager => _currentUser?.isManager ?? false;

  // Permission-based getters
  bool get canViewDashboard => _currentUser?.canViewDashboard ?? false;
  bool get canViewSales => _currentUser?.canViewSales ?? false;
  bool get canEditSales => _currentUser?.canEditSales ?? false;
  bool get canViewInventory => _currentUser?.canViewInventory ?? false;
  bool get canManageInventory => _currentUser?.canManageInventory ?? false;
  bool get canViewLogistics => _currentUser?.canViewLogistics ?? false;
  bool get canManageLogistics => _currentUser?.canManageLogistics ?? false;
  bool get canViewPurchasing => _currentUser?.canViewPurchasing ?? false;
  bool get canEditPurchasing => _currentUser?.canEditPurchasing ?? false;
  bool get canViewReports => _currentUser?.canViewReports ?? false;
  bool get canManageUsers => _currentUser?.canManageUsers ?? false;
  bool get canManageSettings => _currentUser?.canManageSettings ?? false;

  /// Initialize auth service and check for existing session
  Future<void> init() async {
    _isLoading = true;
    notifyListeners();

    try {
      // Initialize Google Sign In
      _initializeGoogleSignIn();

      // Check backend connectivity first
      await _checkBackendConnectivity();

      final prefs = await SharedPreferences.getInstance();
      _accessToken = prefs.getString(AppConstants.accessTokenKey);
      _refreshToken = prefs.getString(AppConstants.refreshTokenKey);
      
      final userDataString = prefs.getString(AppConstants.userDataKey);
      if (userDataString != null) {
        final userData = jsonDecode(userDataString);
        _currentUser = User.fromJson(userData);
        _isAuthenticated = true;
      }

      // Validate token if exists
      if (_accessToken != null && _currentUser != null) {
        await _validateToken();
      }
    } catch (e) {
      debugPrint('Error initializing auth service: $e');
      await _clearSession();
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// Check backend connectivity
  Future<void> _checkBackendConnectivity() async {
    try {
      debugPrint('🔍 CONNECTIVITY: Checking backend connectivity...');
      final apiService = ApiService();
      
      // Try to reach a simple endpoint with shorter timeout
      final response = await apiService.get('/').timeout(
        const Duration(seconds: 5),
        onTimeout: () {
          throw Exception('Backend connectivity check timed out');
        },
      );
      
      debugPrint('✅ CONNECTIVITY: Backend is accessible (${response.statusCode})');
    } catch (e) {
      debugPrint('❌ CONNECTIVITY: Backend connectivity check failed: $e');
      debugPrint('💡 CONNECTIVITY: Please ensure:');
      debugPrint('   1. Backend server is running on the configured port');
      debugPrint('   2. API_BASE_URL in .env is correct for your environment');
      debugPrint('   3. Network connectivity is available');
      // Don't throw - allow app to continue in offline mode
    }
  }



  /// Login with email and password
  Future<bool> login(String email, String password) async {
    _isLoading = true;
    notifyListeners();

    try {
      print('🔵 USER LOGIN: Attempting login for $email');
      final apiService = ApiService();
      final response = await apiService.post('/auth/login', data: {
        'email': email,
        'password': password,
      });

      if (response.statusCode == 200 || response.statusCode == 201) {
        print('🟢 USER LOGIN: Login successful for $email');
        
        // Check if response needs transformation (older backend format)
        Map<String, dynamic> responseData = response.data;
        if (responseData.containsKey('access_token')) {
          // Transform backend response format to match AuthResponse model
          responseData = {
            'accessToken': responseData['access_token'],
            'refreshToken': responseData['refresh_token'],
            'user': responseData.containsKey('user') && responseData['user'] is Map 
                ? _transformBackendUserToMobileUser(responseData['user'] as Map<String, dynamic>)
                : responseData['user'],
            'expiresIn': responseData['expiresIn'],
          };
        }
        
        final authResponse = AuthResponse.fromJson(responseData);
        
        // Set the basic user data and tokens first
        _accessToken = authResponse.accessToken;
        _refreshToken = authResponse.refreshToken;
        _currentUser = authResponse.user;
        _isAuthenticated = true;
        
        // Fetch user role data after successful authentication
        await _fetchAndUpdateUserRole(authResponse.user.uuid);
        
        // Now save the session with the updated user data (including roles)
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString(AppConstants.accessTokenKey, _accessToken!);
        if (_refreshToken != null) {
          await prefs.setString(AppConstants.refreshTokenKey, _refreshToken!);
        } else {
          await prefs.remove(AppConstants.refreshTokenKey);
        }
        await prefs.setString(AppConstants.userDataKey, jsonEncode(_currentUser!.toJson()));
        
        return true;
      }
      print('🔴 USER LOGIN: Login failed for $email');
      return false;
    } catch (e) {
      print('🔴 USER LOGIN ERROR: $e');
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// Login with Google
  Future<bool> loginWithGoogle() async {
    _isLoading = true;
    notifyListeners();

    try {
      debugPrint('=== GOOGLE SIGN-IN DEBUG START ===');
      debugPrint('1. Starting Google sign-in process...');
      
      // Check if Google Sign-In is properly initialized
      debugPrint('2. Checking Google Sign-In initialization...');
      debugPrint('   - Google Sign-In scopes: ${_googleSignIn.scopes}');
      debugPrint('   - Current user: ${_googleSignIn.currentUser?.email ?? 'None'}');
      
      // Trigger Google Sign In
      debugPrint('3. Triggering Google Sign-In...');
      final googleUser = await _googleSignIn.signIn();
      
      if (googleUser == null) {
        // User cancelled the sign-in
        debugPrint('4. ERROR: Google sign-in cancelled by user');
        debugPrint('=== GOOGLE SIGN-IN DEBUG END ===');
        return false;
      }

      debugPrint('4. Google sign-in successful!');
      debugPrint('   - Email: ${googleUser.email}');
      debugPrint('   - Display Name: ${googleUser.displayName}');
      debugPrint('   - Photo URL: ${googleUser.photoUrl}');
      debugPrint('   - ID: ${googleUser.id}');

      // Get Google authentication
      debugPrint('5. Getting Google authentication tokens...');
      final googleAuth = await googleUser.authentication;
      
      // Get access token and ID token
      final accessToken = googleAuth.accessToken;
      final idToken = googleAuth.idToken;
      
      debugPrint('6. Token status:');
      debugPrint('   - Access Token: ${accessToken != null ? 'Present (${accessToken.length} chars)' : 'NULL'}');
      debugPrint('   - ID Token: ${idToken != null ? 'Present (${idToken.length} chars)' : 'NULL'}');
      
      if (accessToken == null || idToken == null) {
        debugPrint('7. ERROR: Failed to get Google tokens');
        debugPrint('   - Access Token is null: ${accessToken == null}');
        debugPrint('   - ID Token is null: ${idToken == null}');
        debugPrint('=== GOOGLE SIGN-IN DEBUG END ===');
        return false;
      }

      // Validate token expiration before sending to backend
      debugPrint('6.5. Validating token expiration...');
      if (!_isTokenValid(idToken)) {
        debugPrint('   - ERROR: ID token is expired or invalid');
        debugPrint('   - Attempting to refresh Google authentication...');
        
        // Try to refresh the authentication
        try {
          await googleUser.clearAuthCache();
          final refreshedAuth = await googleUser.authentication;
          final refreshedIdToken = refreshedAuth.idToken;
          final refreshedAccessToken = refreshedAuth.accessToken;
          
          if (refreshedIdToken != null && refreshedAccessToken != null && _isTokenValid(refreshedIdToken)) {
            debugPrint('   - Token refresh successful');
            // Update tokens with refreshed ones
            final updatedRequestData = {
              'accessToken': refreshedAccessToken,
              'idToken': refreshedIdToken,
              'email': googleUser.email,
              'name': googleUser.displayName ?? '',
              'photoUrl': googleUser.photoUrl,
            };
            
            debugPrint('6.6. Using refreshed tokens for backend authentication...');
            return await _authenticateWithBackend(updatedRequestData);
          } else {
            debugPrint('   - Token refresh failed, requesting new sign-in');
            await _googleSignIn.signOut();
            throw Exception('Google token expired and refresh failed. Please sign in again.');
          }
        } catch (refreshError) {
          debugPrint('   - Token refresh error: $refreshError');
          await _googleSignIn.signOut();
          throw Exception('Google token expired and refresh failed. Please sign in again.');
        }
      }

      // Prepare backend request data
      final requestData = {
        'accessToken': accessToken,
        'idToken': idToken,
        'email': googleUser.email,
        'name': googleUser.displayName ?? '',
        'photoUrl': googleUser.photoUrl,
      };

      debugPrint('7. Token validation passed, sending to backend...');
      return await _authenticateWithBackend(requestData);
      
    } catch (e, stackTrace) {
      debugPrint('ERROR: Google login exception: $e');
      debugPrint('Stack trace: $stackTrace');
      
      // Additional error context
      if (e.toString().contains('PlatformException')) {
        debugPrint('PLATFORM EXCEPTION DETAILS:');
        debugPrint('This is likely a configuration issue with Google OAuth setup');
        debugPrint('Common causes:');
        debugPrint('1. Missing or incorrect OAuth client ID in Android strings.xml');
        debugPrint('2. Missing or incorrect OAuth client ID in iOS Info.plist');
        debugPrint('3. SHA-1 fingerprint not added to Google Console');
        debugPrint('4. Google Services not properly configured');
      }
      
      if (e.toString().contains('ApiException: 10')) {
        debugPrint('GOOGLE API EXCEPTION 10:');
        debugPrint('This usually means the OAuth client ID is not configured correctly');
        debugPrint('Check Android strings.xml and iOS Info.plist configuration');
      }
      
      debugPrint('=== GOOGLE SIGN-IN DEBUG END ===');
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// Helper method to validate token expiration
  bool _isTokenValid(String idToken) {
    try {
      // Decode JWT token (basic validation without signature verification)
      final parts = idToken.split('.');
      if (parts.length != 3) return false;
      
      // Decode payload
      final payload = parts[1];
      final normalizedPayload = base64Url.normalize(payload);
      final decodedPayload = utf8.decode(base64Url.decode(normalizedPayload));
      final tokenData = jsonDecode(decodedPayload);
      
      // Check expiration
      final exp = tokenData['exp'] as int?;
      if (exp == null) return false;
      
      final currentTime = DateTime.now().millisecondsSinceEpoch / 1000;
      final isValid = currentTime < exp;
      
      debugPrint('   - Token exp: $exp');
      debugPrint('   - Current time: $currentTime');
      debugPrint('   - Token valid: $isValid');
      debugPrint('   - Time until expiry: ${exp - currentTime} seconds');
      
      return isValid;
    } catch (e) {
      debugPrint('   - Token validation error: $e');
      return false;
    }
  }

  /// Helper method to handle backend authentication
  Future<bool> _authenticateWithBackend(Map<String, dynamic> requestData) async {
    debugPrint('Backend authentication with request data: ${requestData.toString()}');

    // Test backend connectivity first
    final apiService = ApiService();
    try {
      debugPrint('Testing backend connectivity...');
      final healthResponse = await apiService.get('/docs').timeout(const Duration(seconds: 5));
      debugPrint('   - Backend connectivity check: ${healthResponse.statusCode}');
    } catch (healthError) {
      debugPrint('   - Backend connectivity check failed: $healthError');
      debugPrint('   - This might indicate backend is not running or not accessible');
      debugPrint('   - Continuing with authentication attempt...');
    }

    // Send Google tokens to backend with improved error handling
    try {
      final response = await apiService.post('/auth/google/token', data: requestData).timeout(
        const Duration(seconds: 30),
        onTimeout: () {
          throw Exception('Backend authentication request timed out after 30 seconds');
        },
      );

      debugPrint('Backend response:');
      debugPrint('   - Status Code: ${response.statusCode}');
      debugPrint('   - Response Data: ${response.data}');

      // Accept both 200 and 201 as successful authentication
      if (response.statusCode == 200 || response.statusCode == 201) {
        debugPrint('Backend authentication successful!');
        
        // Transform backend response format to match AuthResponse model
        final transformedResponse = {
          'accessToken': response.data['access_token'], // Backend uses 'access_token', model expects 'accessToken'
          'refreshToken': response.data['refresh_token'],
          'user': _transformBackendUserToMobileUser(response.data['user']),
          'expiresIn': response.data['expiresIn'],
        };
        
        final authResponse = AuthResponse.fromJson(transformedResponse);
        debugPrint('   - User UUID: ${authResponse.user.uuid}');
        debugPrint('   - User Email: ${authResponse.user.email}');
        
        // Set the basic user data and tokens first
        _accessToken = authResponse.accessToken;
        _refreshToken = authResponse.refreshToken;
        _currentUser = authResponse.user;
        _isAuthenticated = true;
        
        // Fetch user role data after successful authentication
        await _fetchAndUpdateUserRole(authResponse.user.uuid);
        
        // Now save the session with the updated user data (including roles)
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString(AppConstants.accessTokenKey, _accessToken!);
        if (_refreshToken != null) {
          await prefs.setString(AppConstants.refreshTokenKey, _refreshToken!);
        } else {
          await prefs.remove(AppConstants.refreshTokenKey);
        }
        await prefs.setString(AppConstants.userDataKey, jsonEncode(_currentUser!.toJson()));
        
        debugPrint('Session saved successfully with role data');
        debugPrint('=== GOOGLE SIGN-IN DEBUG END ===');
        return true;
      } else {
        debugPrint('ERROR: Backend authentication failed');
        debugPrint('   - Status Code: ${response.statusCode}');
        debugPrint('   - Response Data: ${response.data}');
        debugPrint('=== GOOGLE SIGN-IN DEBUG END ===');
        return false;
      }
    } catch (e) {
      debugPrint('ERROR: Backend authentication request failed: $e');
      debugPrint('   - This could be due to:');
      debugPrint('     1. Network connectivity issues');
      debugPrint('     2. Backend server not running');
      debugPrint('     3. Invalid API endpoint or configuration');
      debugPrint('     4. Request timeout');
      debugPrint('=== GOOGLE SIGN-IN DEBUG END ===');
      return false;
    }
  }


  /// Logout user
  Future<void> logout() async {
    _isLoading = true;
    notifyListeners();

    try {
      // Call logout endpoint if token exists
      if (_accessToken != null) {
        final apiService = ApiService();
        await apiService.post('/auth/logout');
      }

      // Sign out from Google if signed in
      if (_googleSignIn.currentUser != null) {
        await _googleSignIn.signOut();
      }
    } catch (e) {
      debugPrint('Logout error: $e');
    } finally {
      await _clearSession();
      _isLoading = false;
      notifyListeners();
    }
  }

  /// Refresh access token
  Future<bool> refreshToken() async {
    if (_refreshToken == null) return false;

    try {
      final apiService = ApiService();
      final response = await apiService.post('/auth/refresh', data: {
        'refreshToken': _refreshToken,
      });

      if (response.statusCode == 200) {
        final authResponse = AuthResponse.fromJson(response.data);
        await _saveSession(authResponse);
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('Token refresh error: $e');
      await _clearSession();
      return false;
    }
  }

  /// Check if user has specific permission
  bool hasPermission(String permission) {
    final hasPermission = _currentUser?.hasPermission(permission) ?? false;
    debugPrint('🔵 AUTH SERVICE: Permission check - $permission: $hasPermission');
    return hasPermission;
  }

  /// Check if user has specific role
  bool hasRole(String roleName) {
    final hasRole = _currentUser?.hasRole(roleName) ?? false;
    debugPrint('🔵 AUTH SERVICE: Role check - $roleName: $hasRole');
    return hasRole;
  }

  /// Get user's warehouse UUID
  String? get userWarehouseUuid => _currentUser?.warehouseUuid;

  /// Get user's van UUID
  String? get userVanUuid => _currentUser?.vanUuid;

  /// Debug method to print user information
  void debugUserInfo() {
    debugPrint('=== USER DEBUG INFO ===');
    debugPrint('UUID: ${_currentUser?.uuid ?? 'No UUID'}');
    debugPrint('Name: ${_currentUser?.displayName ?? 'No name'}');
    debugPrint('Roles: ${_currentUser?.roles.map((r) => r.name).toList() ?? 'No roles'}');
    debugPrint('Is authenticated: $_isAuthenticated');
    debugPrint('Is admin: $isAdmin');
    debugPrint('Is mobile sale agent: $isMobileSaleAgent');
    debugPrint('Is manager: $isManager');
    debugPrint('Can view dashboard: $canViewDashboard');
    debugPrint('Can view sales: $canViewSales');
    debugPrint('Can view inventory: $canViewInventory');
    debugPrint('=== END USER DEBUG INFO ===');
  }

  /// Refresh Google authentication if needed
  Future<bool> refreshGoogleAuthIfNeeded() async {
    try {
      final currentUser = _googleSignIn.currentUser;
      if (currentUser == null) {
        debugPrint('No current Google user to refresh');
        return false;
      }

      // Get current authentication
      final currentAuth = await currentUser.authentication;
      final currentIdToken = currentAuth.idToken;
      
      if (currentIdToken == null) {
        debugPrint('No current ID token to check');
        return false;
      }

      // Check if token is about to expire (within 5 minutes)
      if (_isTokenExpiringSoon(currentIdToken, 300)) {
        debugPrint('Google token is expiring soon, refreshing...');
        
        // Clear auth cache to force refresh
        await currentUser.clearAuthCache();
        
        // Get fresh authentication
        final refreshedAuth = await currentUser.authentication;
        final refreshedIdToken = refreshedAuth.idToken;
        
        if (refreshedIdToken != null && _isTokenValid(refreshedIdToken)) {
          debugPrint('Google token refreshed successfully');
          
          // Update backend with new token
          final requestData = {
            'accessToken': refreshedAuth.accessToken,
            'idToken': refreshedIdToken,
            'email': currentUser.email,
            'name': currentUser.displayName ?? '',
            'photoUrl': currentUser.photoUrl,
          };
          
          return await _authenticateWithBackend(requestData);
        }
      }
      
      return true; // Token is still valid
    } catch (e) {
      debugPrint('Error refreshing Google auth: $e');
      return false;
    }
  }

  /// Check if token is expiring soon
  bool _isTokenExpiringSoon(String idToken, int thresholdSeconds) {
    try {
      final parts = idToken.split('.');
      if (parts.length != 3) return true;
      
      final payload = parts[1];
      final normalizedPayload = base64Url.normalize(payload);
      final decodedPayload = utf8.decode(base64Url.decode(normalizedPayload));
      final tokenData = jsonDecode(decodedPayload);
      
      final exp = tokenData['exp'] as int?;
      if (exp == null) return true;
      
      final currentTime = DateTime.now().millisecondsSinceEpoch / 1000;
      final timeUntilExpiry = exp - currentTime;
      
      return timeUntilExpiry <= thresholdSeconds;
    } catch (e) {
      debugPrint('Error checking token expiry: $e');
      return true; // Assume expiring if we can't check
    }
  }

  /// Private method to save session data
  Future<void> _saveSession(AuthResponse authResponse) async {
    final prefs = await SharedPreferences.getInstance();
    
    _accessToken = authResponse.accessToken;
    _refreshToken = authResponse.refreshToken;
    _currentUser = authResponse.user;
    _isAuthenticated = true;

    await prefs.setString(AppConstants.accessTokenKey, _accessToken!);
    if (_refreshToken != null) {
      await prefs.setString(AppConstants.refreshTokenKey, _refreshToken!);
    } else {
      await prefs.remove(AppConstants.refreshTokenKey);
    }
    await prefs.setString(AppConstants.userDataKey, jsonEncode(_currentUser!.toJson()));
  }

  /// Private method to clear session data
  Future<void> _clearSession() async {
    final prefs = await SharedPreferences.getInstance();
    
    _accessToken = null;
    _refreshToken = null;
    _currentUser = null;
    _isAuthenticated = false;

    await prefs.remove(AppConstants.accessTokenKey);
    await prefs.remove(AppConstants.refreshTokenKey);
    await prefs.remove(AppConstants.userDataKey);
  }

  /// Private method to validate token
  Future<void> _validateToken() async {
    try {
      final apiService = ApiService();
      final response = await apiService.get('/auth/me');
      
      if (response.statusCode == 200) {
        _currentUser = User.fromJson(response.data);
        _isAuthenticated = true;
        
        // Fetch user role data
        await _fetchAndUpdateUserRole(_currentUser!.uuid);
        
        // Save updated user data
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString(AppConstants.userDataKey, jsonEncode(_currentUser!.toJson()));
      } else {
        await _clearSession();
      }
    } catch (e) {
      debugPrint('Token validation error: $e');
      await _clearSession();
    }
  }

  /// Fetch and update user role data
  Future<void> _fetchAndUpdateUserRole(String userUuid) async {
    try {
      debugPrint('🔵 AUTH SERVICE: Fetching role data for user: $userUuid');
      
      // Use the correct backend endpoint: /users/:uuid/role
      // This endpoint is specifically designed to get the role for a user
      debugPrint('🔵 AUTH SERVICE: Using /users/$userUuid/role endpoint');
      
      final userWithRole = await _userService.getUserWithRole(userUuid);
      if (userWithRole != null) {
        _currentUser = userWithRole;
        debugPrint('🟢 AUTH SERVICE: Updated user with role data');
        debugPrint('🟢 AUTH SERVICE: User role: ${userWithRole.roles.isNotEmpty ? userWithRole.roles.first.name : 'No role'}');
        debugPrint('🟢 AUTH SERVICE: User permissions: ${userWithRole.allPermissions}');
        
        // Save updated user data to local storage
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString(AppConstants.userDataKey, jsonEncode(_currentUser!.toJson()));
      } else {
        debugPrint('🔴 AUTH SERVICE: Failed to fetch user role data');
        
        // If we can't fetch role data, still update the user with empty roles
        if (_currentUser != null) {
          _currentUser = User(
            uuid: _currentUser!.uuid,
            email: _currentUser!.email,
            firstName: _currentUser!.firstName,
            lastName: _currentUser!.lastName,
            phone: _currentUser!.phone,
            isActive: _currentUser!.isActive,
            roles: [], // Empty roles array
            warehouseUuid: _currentUser!.warehouseUuid,
            vanUuid: _currentUser!.vanUuid,
            createdAt: _currentUser!.createdAt,
            updatedAt: _currentUser!.updatedAt,
          );
          debugPrint('🟡 AUTH SERVICE: Updated user with empty roles due to fetch failure');
        }
      }
    } catch (e) {
      debugPrint('🔴 AUTH SERVICE: Error fetching user role: $e');
      
      // On error, still update the user with empty roles so the app can continue
      if (_currentUser != null) {
        _currentUser = User(
          uuid: _currentUser!.uuid,
          email: _currentUser!.email,
          firstName: _currentUser!.firstName,
          lastName: _currentUser!.lastName,
          phone: _currentUser!.phone,
          isActive: _currentUser!.isActive,
          roles: [], // Empty roles array
          warehouseUuid: _currentUser!.warehouseUuid,
          vanUuid: _currentUser!.vanUuid,
          createdAt: _currentUser!.createdAt,
          updatedAt: _currentUser!.updatedAt,
        );
        debugPrint('🟡 AUTH SERVICE: Updated user with empty roles due to error');
      }
    }
  }

  /// Helper method to transform backend user data to mobile user data
  Map<String, dynamic> _transformBackendUserToMobileUser(Map<String, dynamic> backendUser) {
    // Backend returns: { uuid, email, name, roleUuidString, userType }
    // Mobile expects: { uuid, email, firstName, lastName, phone, isActive, roles, warehouseUuid, vanUuid, createdAt, updatedAt }
    
    // Split name into firstName and lastName
    final String fullName = backendUser['name'] ?? '';
    final nameParts = fullName.split(' ');
    final firstName = nameParts.isNotEmpty ? nameParts.first : '';
    final lastName = nameParts.length > 1 ? nameParts.skip(1).join(' ') : '';
    
    return {
      'uuid': backendUser['uuid'],
      'email': backendUser['email'],
      'firstName': firstName,
      'lastName': lastName,
      'phone': null, // Backend doesn't provide phone
      'isActive': true, // Assume active if user can login
      'roles': [], // Will be populated by _fetchAndUpdateUserRole
      'warehouseUuid': backendUser['warehouseUuidString'], // Backend provides this
      'vanUuid': null, // Backend may provide this in the future
      'createdAt': DateTime.now().toIso8601String(), // Placeholder
      'updatedAt': DateTime.now().toIso8601String(), // Placeholder
    };
  }
} 