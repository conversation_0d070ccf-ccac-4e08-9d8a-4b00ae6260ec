# POS Configuration Usage Examples

This document shows how to use the new POS configuration and URL parameters for enhanced POS functionality.

## URL Parameters

### Edit an Existing Sale
```
/sales/pos?edit=sale-uuid-here
```

### Load a Sale for Continuation
```
/sales/pos?load=sale-uuid-here
```

### Load with Pre-selected Customer
```
/sales/pos?customer=customer-uuid-here
```

### Load with Specific Warehouse
```
/sales/pos?warehouse=warehouse-uuid-here
```

### Combined Parameters
```
/sales/pos?edit=sale-uuid&customer=customer-uuid&warehouse=warehouse-uuid
```

## Configuration Usage

### Using the Configuration in Code

```typescript
import { defaultPOSConfig, buildPOSUrl, parsePOSUrlParams } from './config/posConfig';

// Build a POS URL for editing a sale
const editUrl = buildPOSUrl('sale-uuid-123', 'customer-uuid-456', 'warehouse-uuid-789');
// Result: /sales/pos?edit=sale-uuid-123&customer=customer-uuid-456&warehouse=warehouse-uuid-789

// Parse URL parameters
const searchParams = new URLSearchParams('?edit=sale-uuid-123&customer=customer-uuid-456');
const params = parsePOSUrlParams(searchParams);
// Result: { editSaleUuid: 'sale-uuid-123', customerUuid: 'customer-uuid-456' }

// Access configuration values
const defaultPayment = defaultPOSConfig.defaultPaymentMethod; // 'cash'
const taxRate = defaultPOSConfig.cart.taxRate; // 0.1
const preserveSelection = defaultPOSConfig.keyboard.preserveSelectionAfterAdd; // true
```

### Customizing Configuration

```typescript
import { defaultPOSConfig, type POSConfig } from './config/posConfig';

// Create custom configuration
const customConfig: POSConfig = {
  ...defaultPOSConfig,
  defaultPaymentMethod: 'card',
  cart: {
    ...defaultPOSConfig.cart,
    taxRate: 0.08, // 8% tax rate
  },
  keyboard: {
    ...defaultPOSConfig.keyboard,
    preserveSelectionAfterAdd: false,
  },
};
```

## Key Features

### 1. Keyboard Navigation Preservation
- After adding a product to cart, the selected product index is preserved
- Users can continue adding the same product or navigate to nearby products
- Search input is automatically focused after adding a product

### 2. Default Payment Method
- Payment method defaults to 'Cash' instead of empty selection
- Users can still change the payment method as needed

### 3. Optional Customer Selection
- Sales can be completed without selecting a customer
- The backend uses the warehouse as the default customer
- Customer selection is still available but not required

### 4. Sale Loading Modes
- **Edit Mode**: Modify existing sale details
- **Load Mode**: Continue with an existing sale
- **Create Mode**: Start a new sale (default)

### 5. Enhanced URL Support
- Easy integration with other parts of the application
- Support for deep linking to specific sales
- Pre-populated customer and warehouse selection

## Integration Examples

### From Sales List
```typescript
// Link to edit a sale
const editLink = buildPOSUrl(sale.uuid);
// <Link href={editLink}>Edit Sale</Link>

// Link to continue a sale
const continueLink = buildPOSUrl(sale.uuid, undefined, undefined, 'load');
// <Link href={continueLink}>Continue Sale</Link>
```

### From Customer Management
```typescript
// Link to POS with pre-selected customer
const posWithCustomer = buildPOSUrl(undefined, customer.uuid);
// <Link href={posWithCustomer}>New Sale for Customer</Link>
```

### From Dashboard
```typescript
// Quick access to POS
const quickPOS = buildPOSUrl();
// <Link href={quickPOS}>Quick Sale</Link>
``` 