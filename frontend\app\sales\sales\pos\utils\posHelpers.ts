// POS Helper Functions

import type { Customer, Product, SaleItem, Sale } from '../types';

/**
 * Validate a sale before submission
 */
export function validateSale(
  customer: Customer | null,
  items: SaleItem[],
  warehouseUuid: string | undefined,
  userUuid: string | undefined,
  paymentMethod: string
): { isValid: boolean; error?: string } {
  if (!warehouseUuid) {
    return { isValid: false, error: 'Warehouse UUID is required' };
  }

  if (!userUuid) {
    return { isValid: false, error: 'User UUID is required' };
  }

  if (items.length === 0) {
    return { isValid: false, error: 'At least one item is required' };
  }

  if (!paymentMethod) {
    return { isValid: false, error: 'Payment method is required' };
  }

  // Validate each item
  for (const item of items) {
    if (!item.productUuid || !item.name) {
      return { isValid: false, error: 'All items must have valid product information' };
    }
    
    if (item.quantity <= 0) {
      return { isValid: false, error: 'All items must have positive quantities' };
    }
    
    if (item.unitPrice < 0) {
      return { isValid: false, error: 'All items must have non-negative prices' };
    }
  }

  // Customer is optional - sales can be made without a customer
  // The backend will use the warehouse as the default customer

  return { isValid: true };
}

/**
 * Calculate totals for a sale
 */
export function calculateSaleTotals(items: SaleItem[], taxRate: number = 0.1): {
  subtotal: number;
  tax: number;
  total: number;
} {
  const subtotal = items.reduce((sum, item) => sum + item.totalPrice, 0);
  const tax = subtotal * taxRate;
  const total = subtotal + tax;

  return {
    subtotal: Math.round(subtotal * 100) / 100,
    tax: Math.round(tax * 100) / 100,
    total: Math.round(total * 100) / 100,
  };
}

/**
 * Create a sale payload for API submission
 */
export function createSalePayload(
  customer: Customer | null,
  items: SaleItem[],
  warehouseUuid: string,
  userUuid: string,
  paymentMethod: string,
  notes?: string
): {
  userUuid: string;
  warehouseUuid: string;
  customerUuid?: string;
  items: SaleItem[];
  paymentMethod: string;
  notes?: string;
} {
  const { subtotal, tax, total } = calculateSaleTotals(items);

  return {
    userUuid,
    warehouseUuid,
    customerUuid: customer?.uuid,
    items,
    paymentMethod,
    notes,
  };
}

/**
 * Format currency for display
 */
export function formatCurrency(amount: number, currency: string = 'USD'): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(amount);
}

/**
 * Format product display name
 */
export function formatProductName(product: Product): string {
  let name = product.name;
  if (product.sku) {
    name += ` (${product.sku})`;
  }
  return name;
}

/**
 * Check if product is in stock
 * Updated to use currentStock from main storage instead of stockQuantity
 */
export function isProductInStock(product: Product): boolean {
  return (product.currentStock !== undefined ? product.currentStock : product.stockQuantity || 0) > 0;
}

/**
 * Get stock status display
 * Updated to use currentStock from main storage and handle negative stock scenarios
 */
export function getStockStatus(product: Product): {
  text: string;
  color: string;
} {
  // Use currentStock from main storage if available, fallback to stockQuantity
  const stock = product.currentStock !== undefined ? product.currentStock : (product.stockQuantity || 0);
  
  if (stock < 0) {
    return { text: `Negative Stock (${stock})`, color: 'text-red-600' };
  } else if (stock === 0) {
    return { text: 'Out of Stock', color: 'text-red-500' };
  } else if (stock < 10) {
    return { text: `Low Stock (${stock})`, color: 'text-yellow-500' };
  } else {
    return { text: `In Stock (${stock})`, color: 'text-green-500' };
  }
}

/**
 * Filter products by search term
 */
export function filterProducts(products: Product[], searchTerm: string): Product[] {
  if (!products || !Array.isArray(products)) {
    return [];
  }
  
  if (!searchTerm || !searchTerm.trim()) {
    return products;
  }

  const term = searchTerm.toLowerCase();
  return products.filter(product =>
    product.name?.toLowerCase().includes(term) ||
    (product.sku && product.sku.toLowerCase().includes(term)) ||
    (product.barcode && product.barcode.toLowerCase().includes(term)) ||
    (product.category && product.category.toLowerCase().includes(term)) ||
    (product.description && product.description.toLowerCase().includes(term))
  );
}

/**
 * Filter customers by search term
 */
export function filterCustomers(customers: Customer[], searchTerm: string): Customer[] {
  if (!searchTerm || !searchTerm.trim()) {
    return customers;
  }

  const term = searchTerm.toLowerCase();
  return customers.filter(customer =>
    customer.name.toLowerCase().includes(term) ||
    (customer.email && customer.email.toLowerCase().includes(term)) ||
    (customer.phone && customer.phone.toLowerCase().includes(term)) ||
    (customer.fiscalId && customer.fiscalId.toLowerCase().includes(term))
  );
}

/**
 * Generate a unique sale reference number
 */
export function generateSaleReference(): string {
  const timestamp = Date.now().toString(36);
  const random = Math.random().toString(36).substr(2, 5);
  return `SALE-${timestamp}-${random}`.toUpperCase();
}

/**
 * Validate customer data
 */
export function validateCustomer(customer: Partial<Customer>): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];

  if (!customer.name || customer.name.trim().length === 0) {
    errors.push('Customer name is required');
  }

  if (!customer.customerType) {
    errors.push('Customer type is required');
  }

  if (customer.email && !isValidEmail(customer.email)) {
    errors.push('Invalid email format');
  }

  if (customer.phone && !isValidPhone(customer.phone)) {
    errors.push('Invalid phone number format');
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

/**
 * Validate email format
 */
function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Validate phone number format
 */
function isValidPhone(phone: string): boolean {
  const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
  return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''));
}

/**
 * Debounce function for search input
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let timeoutId: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func(...args), delay);
  };
}

/**
 * Get payment method display name
 */
export function getPaymentMethodName(method: string): string {
  const methods: Record<string, string> = {
    cash: 'Cash',
    credit_card: 'Credit/Debit Card',
    bank_transfer: 'Bank Transfer',
    mobile_payment: 'Mobile Payment',
    cheque: 'Cheque',
    other: 'Other',
  };
  return methods[method] || method;
} 