"use client";
import React from "react";
import ItemsTable from "@/components/itemsTable/ItemsTable";
import TableActionButtons from "@/components/itemsTable/TableActionButtons";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useAuth } from "@/contexts/AuthContext";
import { 
  getAllProductCategories, 
  searchProductCategories,
  createProductCategory, 
  updateProductCategory, 
  deleteProductCategory, 
  ProductCategory, 
  PaginatedResponse 
} from "../productCategoriesApi";

// Modal component with Escape-to-close support
const Modal: React.FC<{ open: boolean; onClose: () => void; title: string; children: React.ReactNode }> = ({ open, onClose, title, children }) => {
  React.useEffect(() => {
    if (!open) return;
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === "Escape") onClose();
    };
    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [open, onClose]);
  if (!open) return null;
  return (
    <div className="fixed inset-0 flex items-center justify-center z-50 bg-black/40 backdrop-blur-sm">
      <div className="bg-white rounded-2xl shadow-2xl min-w-[340px] max-w-md w-full p-0 relative border border-gray-200 animate-fade-in">
        <div className="flex items-center justify-between px-6 py-4 bg-blue-50 rounded-t-2xl border-b border-gray-100">
          <div className="flex items-center gap-2">
            <span className="inline-flex items-center justify-center w-8 h-8 rounded-full bg-blue-100">
              <svg className="w-5 h-5 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" /></svg>
            </span>
            <h2 className="text-lg font-semibold text-blue-900">{title}</h2>
          </div>
          <button
            onClick={onClose}
            aria-label="Close"
            className="p-2 rounded-full hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-blue-400 transition"
            tabIndex={0}
          >
            <svg className="w-5 h-5 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" /></svg>
          </button>
        </div>
        <div className="px-6 py-6">{children}</div>
      </div>
    </div>
  );
};

// Category Form Component
const CategoryForm: React.FC<{
  initialValues?: Partial<{ name: string }>;
  onSubmit: (values: { name: string }) => void;
  submitLabel?: string;
  loading?: boolean;
}> = ({ initialValues = {}, onSubmit, submitLabel = 'Save', loading }) => {
  const nameInputRef = React.useRef<HTMLInputElement | null>(null);

  React.useEffect(() => {
    if (nameInputRef.current) {
      nameInputRef.current.focus();
    }
  }, []);

  const [name, setName] = React.useState(initialValues.name || '');
  const [error, setError] = React.useState<string | null>(null);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!name.trim()) {
      setError('Name is required');
      return;
    }
    setError(null);
    onSubmit({ name: name.trim() });
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="mb-4">
        <label className="block text-sm font-medium mb-1">Name<span className="text-red-500">*</span></label>
        <input
          ref={nameInputRef}
          type="text"
          value={name}
          onChange={(e) => {
            setName(e.target.value);
            if (error) setError(null);
          }}
          className={`w-full rounded-lg bg-gray-50 border border-gray-300 focus:ring-2 focus:ring-blue-400 focus:border-blue-400 shadow-sm px-4 py-2 transition-all outline-none text-base ${error ? 'border-red-400' : ''}`}
          placeholder="Enter category name"
        />
        {error && <div className="flex items-center gap-2 mt-1 text-sm text-red-600"><svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01M21 12A9 9 0 1 1 3 12a9 9 0 0 1 18 0Z" /></svg>{error}</div>}
      </div>
      <button type="submit" className="w-full flex items-center justify-center gap-2 bg-blue-600 text-white font-medium rounded-lg py-2.5 mt-2 shadow hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-400 text-sm transition-all disabled:opacity-60 disabled:cursor-not-allowed" disabled={loading}>
        {loading ? (
          <svg className="w-5 h-5 animate-spin" fill="none" viewBox="0 0 24 24" stroke="currentColor"><circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle><path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v8z"></path></svg>
        ) : (
          <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" /></svg>
        )}
        {loading ? 'Saving...' : submitLabel}
      </button>
    </form>
  );
};

export default function ProductCategoriesPage() {
  // Get warehouse and user context from AuthContext - inject warehouseUuid and userUuid from context
  const { user } = useAuth();
  const warehouseUuid = user?.warehouseUuid;
  const userUuid = user?.uuid;

  React.useEffect(() => {
    const handleF2 = (e: KeyboardEvent) => {
      if (e.key === "F2") {
        e.preventDefault();
        handleAdd();
      }
    };
    window.addEventListener("keydown", handleF2);
    return () => window.removeEventListener("keydown", handleF2);
  }, []);
  
  const queryClient = useQueryClient();
  const [search, setSearch] = React.useState("");
  const [currentPage, setCurrentPage] = React.useState(1);
  const [pageSize, setPageSize] = React.useState(10);
  
  const { data: paginatedData, isLoading, isError } = useQuery<PaginatedResponse<ProductCategory>>({
    queryKey: ["productCategories", warehouseUuid, search, currentPage, pageSize],
    queryFn: () => {
      if (!warehouseUuid) {
        throw new Error("Warehouse UUID not available");
      }
      if (search.trim()) {
        return searchProductCategories(search.trim(), warehouseUuid, { page: currentPage, limit: pageSize });
      } else {
        return getAllProductCategories(warehouseUuid, { page: currentPage, limit: pageSize });
      }
    },
    enabled: !!warehouseUuid,
  });

  const [modalOpen, setModalOpen] = React.useState(false);
  const [editing, setEditing] = React.useState<ProductCategory | null>(null);
  const [error, setError] = React.useState<string | null>(null);
  const [loading, setLoading] = React.useState(false);

  const deleteMutation = useMutation({
    mutationFn: async (uuid: string) => {
      await deleteProductCategory(uuid);
    },
    onSuccess: () => queryClient.invalidateQueries({ queryKey: ["productCategories"] }),
  });

  const handleAdd = () => {
    setEditing(null);
    setModalOpen(true);
    setError(null);
  };

  const handleEdit = (category: ProductCategory) => {
    setEditing(category);
    setModalOpen(true);
    setError(null);
  };

  const handleDelete = (category: ProductCategory) => {
    if (window.confirm("Are you sure you want to delete this category?")) {
      deleteMutation.mutate(category.uuid);
    }
  };

  const handleClose = () => {
    setModalOpen(false);
    setEditing(null);
    setError(null);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleSearchChange = (value: string) => {
    setSearch(value);
    setCurrentPage(1);
  };

  const handleSubmit = async (values: { name: string }) => {
    if (!warehouseUuid) {
      setError("Warehouse context not available");
      return;
    }

    setLoading(true);
    setError(null);
    try {
      if (editing) {
        await updateProductCategory(editing.uuid, values, userUuid || '');
      } else {
        await createProductCategory(values, warehouseUuid, userUuid || '');
      }
      queryClient.invalidateQueries({ queryKey: ["productCategories"] });
      setModalOpen(false);
    } catch (e: any) {
      setError(e?.response?.data?.message || "Failed to save category");
    }
    setLoading(false);
  };

  const categories = paginatedData?.data || [];
  const meta = paginatedData?.meta;

  // Show loading state if warehouse context is not available
  if (!warehouseUuid) {
    return (
      <div className="p-4 sm:p-6 w-full">
        <div className="flex items-center justify-center h-64">
          <div className="text-base text-gray-500">Loading warehouse context...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-4 sm:p-6 w-full">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6">
        <div className="flex items-center gap-4">
        </div>
        <button
          className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors text-sm font-medium"
          onClick={handleAdd}
        >
          Add Category (F2)
        </button>
      </div>
      
      <div className="flex flex-col sm:flex-row sm:items-center gap-4 mb-6">
        <input
          type="text"
          placeholder="Search categories..."
          className="border rounded px-4 py-3 w-full sm:w-96 text-base"
          value={search}
          onChange={e => handleSearchChange(e.target.value)}
        />
        <select
          value={pageSize}
          onChange={e => {
            setPageSize(Number(e.target.value));
            setCurrentPage(1);
          }}
          className="border rounded px-3 py-2 text-sm"
        >
          <option value={10}>10 per page</option>
          <option value={25}>25 per page</option>
          <option value={50}>50 per page</option>
          <option value={100}>100 per page</option>
        </select>
      </div>
      
      {error && <div className="text-sm text-red-600 mb-2">{error}</div>}
      {isError && <div className="text-sm text-red-600">Failed to load categories.</div>}
      
      <ItemsTable
        columns={[
          { key: "name", header: "Name", cellClassName: "text-gray-900 font-medium text-sm" },
          { 
            key: "createdByName", 
            header: "Created By", 
            render: (value) => value || "Unknown User",
            cellClassName: "text-gray-600 text-sm"
          },
          { 
            key: "updatedByName", 
            header: "Updated By", 
            render: (value) => value || "Unknown User",
            cellClassName: "text-gray-600 text-sm"
          },
          { key: "createdAt", header: "Created", render: (value) => value ? new Date(value).toLocaleDateString() : "", cellClassName: "text-sm" },
          { key: "updatedAt", header: "Updated", render: (value) => value ? new Date(value).toLocaleDateString() : "", cellClassName: "text-sm" },
          {
            key: "actions",
            header: <span className="block text-center text-sm font-semibold">Actions</span>,
            cellClassName: "text-center",
            render: (_: any, category: ProductCategory) => (
              <TableActionButtons
                onEdit={() => handleEdit(category)}
                onDelete={() => handleDelete(category)}
                editDisabled={false}
                deleteDisabled={deleteMutation.isPending}
              />
            ),
          },
        ]}
        data={categories}
        noDataText={!isLoading ? "No categories found." : ""}
        isLoading={isLoading}
        loadingText="Loading categories..."
        pagination={meta ? {
          currentPage: meta.page,
          totalPages: meta.totalPages,
          onPageChange: handlePageChange,
          totalItems: meta.total,
          itemsPerPage: pageSize,
        } : undefined}
      />
      
      <Modal open={modalOpen} onClose={handleClose} title={editing ? "Edit Category" : "Add Category"}>
        <CategoryForm
          initialValues={editing ? editing : {}}
          onSubmit={handleSubmit}
          submitLabel={editing ? "Update" : "Create"}
          loading={loading}
        />
      </Modal>
    </div>
  );
} 