import { ApiProperty } from "@nestjs/swagger";
import {
  IsString,
  IsNotEmpty,
  IsOptional,
  IsNumber,
  IsUUID,
  MinLength,
} from "class-validator";

export class CreateProductDto {
  @ApiProperty({
    example: "01978136-dd91-7cbe-b248-735a37a514c3",
    description: "UUIDv7 string of the warehouse this entity belongs to",
  })
  @IsUUID("all", { message: "warehouseUuid must be a valid UUID" })
  @IsNotEmpty({ message: "warehouseUuid is required" })
  warehouseUuid: string;

  @ApiProperty({ example: "Product 1" })
  @IsString({ message: "name must be a string" })
  @IsNotEmpty({ message: "name is required" })
  @MinLength(1, { message: "name cannot be empty" })
  name: string;

  @ApiProperty({ example: "" })
  @IsOptional()
  @IsString({ message: "description must be a string" })
  description?: string;

  @ApiProperty({ example: "" })
  @IsOptional()
  @IsString({ message: "sku must be a string" })
  sku?: string;

  @ApiProperty({ example: "" })
  @IsOptional()
  @IsString({ message: "barcode must be a string" })
  barcode?: string;

  @ApiProperty({
    example: "018ea2bb-6b8d-7e9a-8a2a-7e9a8a2a7e9a",
    description: "UUIDv7 string of the product category",
  })
  @IsOptional()
  @IsUUID("all", { message: "productCategoryUuid must be a valid UUID" })
  productCategoryUuid?: string;

  @ApiProperty({ example: 25.99, description: "Price for retail customers" })
  @IsOptional()
  @IsNumber({}, { message: "retailPrice must be a number" })
  retailPrice?: number;

  @ApiProperty({ example: 22.99, description: "Price for wholesale customers" })
  @IsOptional()
  @IsNumber({}, { message: "wholesalePrice must be a number" })
  wholesalePrice?: number;

  @ApiProperty({
    example: 20.99,
    description: "Price for mid-wholesale customers",
  })
  @IsOptional()
  @IsNumber({}, { message: "midWholesalePrice must be a number" })
  midWholesalePrice?: number;

  @ApiProperty({
    example: 18.99,
    description: "Price for institutional customers",
  })
  @IsOptional()
  @IsNumber({}, { message: "institutionalPrice must be a number" })
  institutionalPrice?: number;

  @ApiProperty({ example: 15.5, description: "Cost price of the product" })
  @IsOptional()
  @IsNumber({}, { message: "cost must be a number" })
  cost?: number;
}
