async function createWarehouseStorage() {
  try {
    const response = await fetch('http://localhost:8000/inventory/storage', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        type: 'warehouse',
        name: 'Main Warehouse Storage',
        warehouseUuid: '0197df04-c82b-7e03-a61a-1136803b0502',
        userUuid: '0197df04-c850-711d-8ab5-edfe953a7aef'
      })
    });
    
    const data = await response.json();
    
    if (response.ok) {
      console.log('Storage created successfully:', data);
    } else {
      console.error('Error creating storage:', data);
    }
  } catch (error) {
    console.error('Error creating storage:', error.message);
  }
}

createWarehouseStorage(); 