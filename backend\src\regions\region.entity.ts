import { <PERSON>tity, PrimaryColumn, Column, CreateDateColumn, UpdateDateColumn, Index } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Uuid7 } from '../utils/uuid7';

@Entity('regions')
export class Region {
  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the region (primary key)",
  })
  @PrimaryColumn('uuid')
  id: string;

  @ApiProperty({
    example: "Downtown District",
    description: "Region name (required)",
  })
  @Column()
  name: string;

  @ApiProperty({
    example: "Central business district with high customer density",
    description: "Region description",
    required: false,
  })
  @Column({ nullable: true })
  description?: string;

  @ApiProperty({
    example: 40.7128,
    description: "Latitude coordinate for region center",
    required: false,
  })
  @Column('decimal', { precision: 10, scale: 8, nullable: true })
  latitude?: number;

  @ApiProperty({
    example: -74.006,
    description: "Longitude coordinate for region center",
    required: false,
  })
  @Column('decimal', { precision: 11, scale: 8, nullable: true })
  longitude?: number;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the warehouse this region belongs to (required)",
  })
  @Column('uuid')
  @Index()
  warehouseUuid: string;

  @ApiProperty({ example: false, description: "Soft delete flag" })
  @Column({ default: false })
  isDeleted: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Helper method to generate UUID
  static generateId(): string {
    return new Uuid7().toString();
  }

  // Helper method to generate UUID
  static fromBinary(binary: any): string {
    if (!binary) return null;
    try {
      return new Uuid7(binary).toString();
    } catch {
      return null;
    }
  }
} 