import { ApiProperty } from "@nestjs/swagger";

export class UpdateProductDto {
  @ApiProperty({ example: "Product Name", required: false })
  name?: string;

  @ApiProperty({ example: "Optional description", required: false })
  description?: string;

  @ApiProperty({ example: "SKU-123", required: false })
  sku?: string;

  @ApiProperty({ example: "1234567890", required: false })
  barcode?: string;

  @ApiProperty({
    example: "018ea2bb-6b8d-7e9a-8a2a-7e9a8a2a7e9a",
    description: "UUIDv7 string of the product category",
    required: false,
  })
  productCategoryUuid?: string;

  @ApiProperty({
    example: 25.99,
    description: "Price for retail customers",
    required: false,
  })
  retailPrice?: number;

  @ApiProperty({
    example: 22.99,
    description: "Price for wholesale customers",
    required: false,
  })
  wholesalePrice?: number;

  @ApiProperty({
    example: 20.99,
    description: "Price for mid-wholesale customers",
    required: false,
  })
  midWholesalePrice?: number;

  @ApiProperty({
    example: 18.99,
    description: "Price for institutional customers",
    required: false,
  })
  institutionalPrice?: number;

  @ApiProperty({
    example: 15.5,
    description: "Cost price of the product",
    required: false,
  })
  cost?: number;
}
