import { Module, forwardRef } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { UsersService } from "./users.service";
import { UsersController } from "./users.controller";
import { User } from "./user.entity";
import { Role } from "./role.entity";
import { WarehousesModule } from "../warehouses/warehouses.module";
import { CommonModule } from "../common/common.module";
import { RolesModule } from "../roles/roles.module";
import { UserRolesModule } from "./user_roles.module";
import { VansModule } from "../vans/vans.module";

@Module({
  imports: [
    TypeOrmModule.forFeature([User, Role]),
    WarehousesModule,
    forwardRef(() => CommonModule),
    RolesModule,
    UserRolesModule,
    VansModule,
  ],
  controllers: [UsersController],
  providers: [UsersService],
  exports: [UsersService, TypeOrmModule], // Export TypeOrmModule to make it available to other modules
})
export class UsersModule {}
