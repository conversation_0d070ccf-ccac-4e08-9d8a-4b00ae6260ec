import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useSearchParams } from 'next/navigation';
import { toast } from 'sonner';
import { FiUser } from 'react-icons/fi';

// Import existing POS components
import {
  SearchBar,
  ProductList,
  SalesCart,
  QuantityModal,
  Pagination,
} from '../pos/components';
import { CustomerModal } from '@/components/CustomerModal';
import type { Product } from '../pos/types';
import type { Customer, CreateCustomerDto } from '@/components/CustomerModal';
import { parsePOSUrlParams } from '../pos/config/posConfig';
import type { Sale } from '../salesApi';
import { CategoryFilter } from '../pos/components/CategoryFilter';
import { useKeyboardNavigation } from '../pos/hooks';

interface POSViewProps {
  onSaleComplete?: () => void;
  onNewSale?: () => void;
  editSaleData?: Sale | null;
  posState?: any; // Will be properly typed
}

export const POSView: React.FC<POSViewProps> = ({ 
  onSaleComplete, 
  onNewSale,
  editSaleData,
  posState
}) => {
  const { user } = useAuth();
  const warehouseUuid = user?.warehouseUuid;
  const searchParams = useSearchParams();

  // Parse URL parameters for POS mode
  const { editSaleUuid, loadSaleUuid, customerUuid } = parsePOSUrlParams(searchParams);
  const isEditMode = !!editSaleData;
  const isLoadMode = !!loadSaleUuid;
  
  // Local UI state - moved before early return to fix React hooks rules
  const [searchTerm, setSearchTerm] = useState('');
  const [isCustomerModalOpen, setIsCustomerModalOpen] = useState(false);
  const [isQuantityMode, setIsQuantityMode] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [currentQuantity, setCurrentQuantity] = useState('');
  const [currentPrice, setCurrentPrice] = useState('');
  const [highlightedCartItemUuid, setHighlightedCartItemUuid] = useState<string | null>(null);

  // Refs for keyboard navigation - moved before early return
  const searchInputRef = useRef<HTMLInputElement>(null);
  const quantityInputRef = useRef<HTMLInputElement>(null);
  const priceInputRef = useRef<HTMLInputElement>(null);
  const setSelectedIndexRef = useRef<((index: number) => void) | null>(null);
  
  // Ref to track loaded sales to prevent infinite loops
  const loadedSaleRef = useRef<string | null>(null);
  const loadingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Extract POS state and operations from the passed posState (if available)
  const saleState = posState?.saleState;
  const startNewSale = posState?.startNewSale;
  const loadSaleForEdit = posState?.loadSaleForEdit;
  const loadSaleForContinue = posState?.loadSaleForContinue;
  const clearSale = posState?.clearSale;
  const setCustomer = posState?.setCustomer;
  const addItem = posState?.addItem;
  const removeItem = posState?.removeItem;
  const updateItemQuantity = posState?.updateItemQuantity;
  const updateItemPrice = posState?.updateItemPrice;
  const clearItems = posState?.clearItems;
  const setTaxEnabled = posState?.setTaxEnabled;
  const setTaxRate = posState?.setTaxRate;
  const recalculateTotals = posState?.recalculateTotals;
  const setPaymentMethod = posState?.setPaymentMethod;
  const setAmountPaid = posState?.setAmountPaid;
  const setNotes = posState?.setNotes;
  const setNotesEnabled = posState?.setNotesEnabled;
  const setError = posState?.setError;
  const setSubmitting = posState?.setSubmitting;
  const setLoading = posState?.setLoading;
  const markClean = posState?.markClean;
  const validateSale = posState?.validateSale;
  const saveSale = posState?.saveSale;
  const loadSale = posState?.loadSale;
  const createNewCustomer = posState?.createNewCustomer;
  const getDefaultCustomer = posState?.getDefaultCustomer;
  const searchCustomers = posState?.searchCustomers;
  const validateStock = posState?.validateStock;
  const products = posState?.products || [];
  const productsLoading = posState?.isLoading || false;
  const currentPage = posState?.currentPage || 1;
  const totalPages = posState?.totalPages || 1;
  const hasNext = posState?.hasNext || false;
  const hasPrev = posState?.hasPrev || false;
  const loadPage = posState?.loadPage;
  const searchProducts = posState?.searchProducts;
  const updateCustomerType = posState?.updateCustomerType;
  const updateCategory = posState?.updateCategory;
  const currentCategory = posState?.currentCategory || '';
  const posWarehouseUuid = posState?.warehouseUuid;

  // Check if UI should be disabled (during loading or submitting)
  const isUIDisabled = saleState?.isLoading || saleState?.isSubmitting || false;

  // Define handlers before keyboard navigation to avoid reference issues
  const handleProductSelect = (product: Product, index?: number) => {
    if (typeof index === 'number') {
      setSelectedIndexRef.current?.(index);
    }
    
    // Add product directly to cart using centralized state
    const price = product.customerPrice || product.price || 0;
    addItem?.(product, 1, price);
    
    // Highlight the cart item after adding
    setHighlightedCartItemUuid(product.uuid);
    
    // Remove highlight after 5 seconds (increased from 3 seconds)
    setTimeout(() => {
      setHighlightedCartItemUuid(null);
    }, 5000);
  };

  const handleProductSelectWithModal = (product: Product, index: number) => {
    handleProductSelect(product, index);
  };

  // Keyboard navigation - properly initialized at component level
  const keyboardNavigation = useKeyboardNavigation({
    itemCount: products.length,
    onSelect: (index: number) => {
      if (products[index]) {
        setSelectedIndexRef.current?.(index);
        handleProductSelectWithModal(products[index], index);
      }
    },
    onEscape: () => {
      setSearchTerm('');
      if (searchInputRef.current) {
        searchInputRef.current.focus();
      }
    },
    isModalOpen: isCustomerModalOpen || isQuantityMode,
    isQuantityMode: isQuantityMode,
  });

  const { selectedIndex, setSelectedIndex } = keyboardNavigation;
  
  // Store setSelectedIndex in ref for use in keyboard navigation callback
  setSelectedIndexRef.current = setSelectedIndex;

  // Early return if posState is not available
  if (!posState || !saleState) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <FiUser className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">POS State Error</h3>
          <p className="mt-1 text-sm text-gray-500">POS state is not available.</p>
        </div>
      </div>
    );
  }

  // Customer handlers
  const handleSelectCustomer = (customer: Customer | null) => {
    setCustomer(customer);
    setIsCustomerModalOpen(false);
    
    // Update customer type for product pricing
    if (customer && customer.customerType) {
      updateCustomerType(customer.customerType);
    } else {
      // Default to retail if no customer or no customer type
      updateCustomerType('retail');
    }
  };

  const handleCreateCustomer = async (customerData: CreateCustomerDto) => {
    try {
      setLoading(true);
      const newCustomer = await createNewCustomer(customerData);
      setCustomer(newCustomer);
      setIsCustomerModalOpen(false);
      
      // Update customer type for product pricing
      if (newCustomer && newCustomer.customerType) {
        updateCustomerType(newCustomer.customerType);
      }
      
      toast.success('Customer created successfully');
    } catch (error) {
      console.error('Failed to create customer:', error);
      toast.error('Failed to create customer');
      setError('Failed to create customer');
    } finally {
      setLoading(false);
    }
  };

  // Sale submission
  const handleSubmit = useCallback(async () => {
    try {
      setError(null);
      
      // Validate sale using centralized validation
      const validation = validateSale();
      
      if (!validation.isValid) {
        setError(validation.errors.join(', '));
        toast.error('Validation failed: ' + validation.errors.join(', '));
        return;
      }

      setSubmitting(true);

      // Save sale using operations hook
      const saleUuid = await saveSale(saleState);
      
      // Show success message
      const mode = saleState.mode;
      toast.success(mode === 'edit' ? 'Sale updated successfully' : 'Sale created successfully');

      // Clear the sale and reset to new state
      clearSale();
      startNewSale();

      // Notify parent component if callback provided
      if (onSaleComplete) {
        onSaleComplete();
      }

    } catch (error) {
      console.error('[POSView] Error processing sale:', error);
      const errorMessage = error instanceof Error ? error.message : 'An error occurred while processing the sale';
      setError(errorMessage);
      toast.error('Failed to process sale: ' + errorMessage);
    } finally {
      setSubmitting(false);
    }
  }, [saleState, startNewSale, clearSale, saveSale, onSaleComplete, setError, setSubmitting, validateSale]);

  // Quantity modal handlers
  const handleQuantityModalConfirm = () => {
    if (selectedProduct) {
      const qty = parseFloat(currentQuantity) || 1;
      const price = parseFloat(currentPrice) || selectedProduct.price || 0;
      addItem(selectedProduct, qty, price);
      
      // Reset modal state
      setCurrentQuantity('');
      setCurrentPrice('');
      setSelectedProduct(null);
      setIsQuantityMode(false);
    }
  };

  const handleQuantityModalCancel = () => {
    setCurrentQuantity('');
    setCurrentPrice('');
    setSelectedProduct(null);
    setIsQuantityMode(false);
  };

  // Search handling with debouncing
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      loadPage(1, searchTerm);
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [searchTerm, loadPage]);

  // Category change handling
  useEffect(() => {
    loadPage(1, searchTerm);
  }, [currentCategory, loadPage, searchTerm]);

  // Reset selected index when search term changes or products change
  useEffect(() => {
    setSelectedIndex(0);
  }, [searchTerm, currentCategory, products.length, setSelectedIndex]);

  // Load sale data for editing/loading - now using prop data instead of fetching
  useEffect(() => {
    // Clear any existing timeout
    if (loadingTimeoutRef.current) {
      clearTimeout(loadingTimeoutRef.current);
    }

    const loadSaleData = () => {
      const saleUuid = editSaleData?.uuid || loadSaleUuid;
      if (!saleUuid || !warehouseUuid) return;

      // Additional check: if we're in edit/load mode and already have a sale loaded, skip
      if ((isEditMode || isLoadMode) && saleState.originalSaleUuid === saleUuid) {
        
        loadedSaleRef.current = saleUuid;
        return;
      }

      // If we have editSaleData prop, use it directly
      if (editSaleData && editSaleData.uuid === saleUuid) {
        try {
          
          if (isEditMode) {
            loadSaleForEdit(saleUuid, editSaleData);
          } else if (isLoadMode) {
            loadSaleForContinue(saleUuid, editSaleData);
          }
          
          loadedSaleRef.current = saleUuid;
          
        } catch (error) {
          console.error('Failed to load sale from prop data:', error);
          toast.error('Failed to load sale');
          setError('Failed to load sale');
        }
      } else if (saleUuid && !editSaleData) {
        // Fallback: fetch from API if we don't have prop data
        const fetchSaleData = async () => {
          try {
            setLoading(true);
            const saleData = await loadSale(saleUuid);
            
            if (isEditMode) {
              loadSaleForEdit(saleUuid, saleData);
            } else if (isLoadMode) {
              loadSaleForContinue(saleUuid, saleData);
            }
            
            loadedSaleRef.current = saleUuid;
            
          } catch (error) {
            console.error('Failed to load sale from API:', error);
            toast.error('Failed to load sale');
            setError('Failed to load sale');
          } finally {
            setLoading(false);
          }
        };

        fetchSaleData();
      }
    };

    // Execute immediately if we have the data, otherwise add delay
    if (editSaleData || loadSaleUuid) {
      loadSaleData();
    } else {
      // Add a small delay to prevent rapid successive calls
      loadingTimeoutRef.current = setTimeout(loadSaleData, 100);
    }
  }, [editSaleData, loadSaleUuid, warehouseUuid, isEditMode, isLoadMode, saleState, loadSaleForEdit, loadSaleForContinue, setError, setLoading]);

  // Load customer from URL parameter
  useEffect(() => {
    const loadCustomerFromUrl = async () => {
      // Only load customer from URL for new sales (not edit/load mode)
      if (customerUuid && warehouseUuid && !isEditMode && !isLoadMode) {
        // Check if we already have this customer loaded
        if (saleState.customerUuid === customerUuid) {
          return;
        }

        try {
          setLoading(true);
          const customers = await searchCustomers(warehouseUuid);
          const customer = customers.find(c => c.uuid === customerUuid);
          if (customer) {
            setCustomer(customer);
            
            // Update customer type for product pricing
            if (customer.customerType) {
              updateCustomerType(customer.customerType);
            }
            
            toast.success(`Customer ${customer.name} pre-selected`);
          } else {
            console.warn(`Customer with UUID ${customerUuid} not found`);
          }
        } catch (error) {
          console.error('Failed to load customer from URL:', error);
        } finally {
          setLoading(false);
        }
      }
    };

    loadCustomerFromUrl();
  }, [customerUuid, warehouseUuid, isEditMode, isLoadMode, saleState, setCustomer, setLoading, searchCustomers, updateCustomerType]);

  // Load customer from sale data in edit/load mode
  useEffect(() => {
    // Only proceed if we're in edit/load mode and have sale data
    if (!isEditMode && !isLoadMode) return;
    if (!editSaleData) return;

    // Check if the sale has been loaded into state
    const saleLoaded = saleState.originalSaleUuid === editSaleData.uuid;
    
    if (saleLoaded && (editSaleData as any).customer && !saleState.customer) {
      const customer = (editSaleData as any).customer;
      setCustomer(customer);
      
      // Update customer type for product pricing
      if (customer && customer.customerType) {
        updateCustomerType(customer.customerType);
      }
    } else if (saleLoaded && editSaleData.customerUuidString && !saleState.customer) {
      // Fallback: if we have customer UUID but not customer object, try to load it
      // The customer should be loaded by the loadSaleForEdit function, so this is just a fallback
    }
  }, [isEditMode, isLoadMode, editSaleData, saleState.customer, saleState.originalSaleUuid, setCustomer, updateCustomerType]);

  // Handle new sale request
  const handleNewSale = useCallback(() => {
    clearSale();
    startNewSale();
    setSearchTerm('');
    setSelectedIndex(0);
    
    // Reset loaded sale ref
    loadedSaleRef.current = null;
    
    // Focus on search bar
    if (searchInputRef.current) {
      searchInputRef.current.focus();
    }
    
    toast.success('New sale started');
  }, [clearSale, startNewSale, setSearchTerm, setSelectedIndex]);

  // Listen for new sale requests from parent component
  useEffect(() => {
    const handleNewSaleRequest = () => {
      handleNewSale();
    };
    
    window.addEventListener('newSaleRequest', handleNewSaleRequest);
    return () => {
      window.removeEventListener('newSaleRequest', handleNewSaleRequest);
    };
  }, [handleNewSale]);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // F4 shortcut for Complete Sale
      if (event.key === 'F4') {
        event.preventDefault();
        const validation = validateSale();
        if (validation.isValid && !saleState.isSubmitting) {
          handleSubmit();
        } else if (!validation.isValid) {
          // Show validation errors as toast
          toast.error('Validation failed: ' + validation.errors.join(', '));
        }
      }
    };

    const handleClearCart = () => {
      clearSale();
      startNewSale();
      loadedSaleRef.current = null;
    };

    window.addEventListener('keydown', handleKeyDown);
    window.addEventListener('clearCart', handleClearCart);
    
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
      window.removeEventListener('clearCart', handleClearCart);
    };
  }, [handleSubmit, clearSale, startNewSale, saleState.isSubmitting, validateSale]);

  // Start with a new sale when component mounts
  useEffect(() => {
    if (warehouseUuid && !saleState.saleUuid) {
      startNewSale();
      loadedSaleRef.current = null;
    }
  }, [warehouseUuid, startNewSale, saleState.saleUuid]);

  // Cleanup timeouts on unmount
  useEffect(() => {
    return () => {
      if (loadingTimeoutRef.current) {
        clearTimeout(loadingTimeoutRef.current);
      }
    };
  }, []);

  if (!warehouseUuid) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <FiUser className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No warehouse assigned</h3>
          <p className="mt-1 text-sm text-gray-500">Please contact your administrator.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-[calc(100vh-128px-24px-24px)] flex flex-col bg-white rounded-lg shadow-sm">
      <div className="flex-1 grid grid-cols-1 lg:grid-cols-3 gap-6 min-h-0">
        {/* Products Section */}
        <div className="lg:col-span-2 flex flex-col min-h-0">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 flex flex-col h-full">
            <div className="p-3 border-b border-gray-200 flex-shrink-0">
              <div className="flex flex-col lg:flex-row lg:items-start space-y-3 lg:space-y-0 lg:space-x-3">
                <div className="flex-1 min-w-0">
                  <SearchBar
                    value={searchTerm}
                    onChange={setSearchTerm}
                    ref={searchInputRef}
                    disabled={isUIDisabled}
                    placeholder="Search by name, SKU, or barcode..."
                  />
                </div>
                <div className="w-full lg:w-80 flex-shrink-0">
                  <CategoryFilter
                    selectedCategory={currentCategory}
                    onCategoryChange={updateCategory}
                    disabled={isUIDisabled}
                  />
                </div>
              </div>
            </div>

            <div className="flex-1 flex flex-col min-h-0">
              <div className="flex-1 min-h-0" style={{ height: 'calc(70vh - 200px)' }}>
                <ProductList
                  products={products}
                  selectedIndex={selectedIndex}
                  searchTerm={searchTerm}
                  cartItems={saleState.items}
                  onProductSelect={handleProductSelect}
                  isLoading={productsLoading}
                  disabled={isUIDisabled}
                />
              </div>
              
              <div className="flex-shrink-0 border-t border-gray-200">
                <Pagination
                  pagination={{ page: currentPage, limit: 20, total: totalPages * 20, hasNext, hasPrev }}
                  onPageChange={(page) => loadPage(page, searchTerm)}
                  onNextPage={() => loadPage(currentPage + 1, searchTerm)}
                  onPrevPage={() => loadPage(currentPage - 1, searchTerm)}
                  isLoading={productsLoading}
                  disabled={isUIDisabled}
                />
              </div>
            </div>
          </div>
        </div>

        {/* Cart Section */}
        <div className="flex flex-col min-h-0">
          <SalesCart
            items={saleState.items}
            selectedCustomer={saleState.customer}
            notes={saleState.notes}
            subtotal={saleState.subtotal}
            tax={saleState.tax}
            total={saleState.total}
            isSubmitting={saleState.isSubmitting}
            error={saleState.error || ''}
            paymentMethod={saleState.paymentMethod}
            amountPaid={saleState.amountPaid}
            taxEnabled={saleState.taxEnabled}
            taxRate={saleState.taxRate}
            notesEnabled={saleState.notesEnabled}
            onRemoveItem={removeItem}
            onUpdateQuantity={updateItemQuantity}
            onUpdatePrice={updateItemPrice}
            onNotesChange={setNotes}
            onSubmit={handleSubmit}
            onCustomerSelect={() => setIsCustomerModalOpen(true)}
            onPaymentMethodChange={setPaymentMethod}
            onAmountPaidChange={setAmountPaid}
            onTaxEnabledChange={setTaxEnabled}
            onTaxRateChange={setTaxRate}
            onNotesEnabledChange={setNotesEnabled}
            highlightedCartItemUuid={highlightedCartItemUuid}
            isEditMode={isEditMode}
            isLoadMode={isLoadMode}
            isLoading={saleState.isLoading}
            disabled={isUIDisabled}
          />
        </div>
      </div>

      {/* Modals */}
      <CustomerModal
        isOpen={isCustomerModalOpen}
        customers={[]} // CustomerModal handles its own customer loading
        onSelect={handleSelectCustomer}
        onClose={() => setIsCustomerModalOpen(false)}
        onCreateNew={handleCreateCustomer}
        disabled={isUIDisabled}
      />

      <QuantityModal
        isOpen={isQuantityMode}
        product={selectedProduct}
        quantity={currentQuantity}
        price={currentPrice}
        onQuantityChange={setCurrentQuantity}
        onPriceChange={setCurrentPrice}
        onConfirm={handleQuantityModalConfirm}
        onCancel={handleQuantityModalCancel}
        quantityInputRef={quantityInputRef}
        priceInputRef={priceInputRef}
      />
    </div>
  );
}; 