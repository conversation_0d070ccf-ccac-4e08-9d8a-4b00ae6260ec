"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/logs/test-diff/page",{

/***/ "(app-pages-browser)/./app/logs/test-diff/page.tsx":
/*!*************************************!*\
  !*** ./app/logs/test-diff/page.tsx ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ TestDiffPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_LogDetailsModal__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/LogDetailsModal */ \"(app-pages-browser)/./app/logs/components/LogDetailsModal.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction TestDiffPage() {\n    _s();\n    const [isModalOpen, setIsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedLog, setSelectedLog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Sample log data with delta changes\n    const sampleLogs = [\n        {\n            id: \"01890b6e-7b8c-7e6b-8e2e-7b8c7e6b8e2e\",\n            userUuid: \"01890b6e-7b8c-7e6b-8e2e-7b8c7e6b8e2f\",\n            operation: \"updated\",\n            entity: \"sale_123\",\n            description: \"Sale updated with new customer and payment info\",\n            createdAt: new Date(\"2025-01-15T10:30:00.000Z\"),\n            data: {\n                saleUuid: \"sale_123\",\n                customerName: \"John Smith\",\n                userName: \"Admin User\",\n                updatedAt: \"2025-01-15T10:30:00.000Z\",\n                changes: {\n                    customerName: {\n                        before: \"John Doe\",\n                        after: \"John Smith\"\n                    },\n                    status: {\n                        before: \"pending\",\n                        after: \"completed\"\n                    },\n                    amountPaid: {\n                        before: 0,\n                        after: 150.00\n                    },\n                    paymentDate: {\n                        before: null,\n                        after: \"2025-01-15T14:30:00.000Z\"\n                    },\n                    items: {\n                        before: [\n                            {\n                                id: 1,\n                                name: \"Product A\",\n                                quantity: 2,\n                                price: 50\n                            },\n                            {\n                                id: 2,\n                                name: \"Product B\",\n                                quantity: 1,\n                                price: 30\n                            }\n                        ],\n                        after: [\n                            {\n                                id: 1,\n                                name: \"Product A\",\n                                quantity: 3,\n                                price: 50\n                            },\n                            {\n                                id: 2,\n                                name: \"Product B\",\n                                quantity: 1,\n                                price: 30\n                            },\n                            {\n                                id: 3,\n                                name: \"Product C\",\n                                quantity: 1,\n                                price: 20\n                            }\n                        ]\n                    },\n                    metadata: {\n                        before: {\n                            source: \"web\",\n                            version: \"1.0\",\n                            tags: [\n                                \"urgent\"\n                            ]\n                        },\n                        after: {\n                            source: \"mobile\",\n                            version: \"1.1\",\n                            tags: [\n                                \"urgent\",\n                                \"priority\"\n                            ],\n                            notes: \"Updated via mobile app\"\n                        }\n                    }\n                },\n                changedFields: [\n                    \"customerName\",\n                    \"status\",\n                    \"amountPaid\",\n                    \"paymentDate\",\n                    \"items\",\n                    \"metadata\"\n                ],\n                changeCount: 6\n            }\n        },\n        {\n            id: \"01890b6e-7b8c-7e6b-8e2e-7b8c7e6b8e3e\",\n            userUuid: \"01890b6e-7b8c-7e6b-8e2e-7b8c7e6b8e2f\",\n            operation: \"created\",\n            entity: \"product_456\",\n            description: \"New product created\",\n            createdAt: new Date(\"2025-01-15T11:00:00.000Z\"),\n            data: {\n                productUuid: \"product_456\",\n                userName: \"Admin User\",\n                createdAt: \"2025-01-15T11:00:00.000Z\",\n                changes: {\n                    name: {\n                        before: null,\n                        after: \"New Awesome Product\"\n                    },\n                    price: {\n                        before: null,\n                        after: 99.99\n                    },\n                    category: {\n                        before: null,\n                        after: \"Electronics\"\n                    }\n                },\n                changedFields: [\n                    \"name\",\n                    \"price\",\n                    \"category\"\n                ],\n                changeCount: 3\n            }\n        },\n        {\n            id: \"01890b6e-7b8c-7e6b-8e2e-7b8c7e6b8e4e\",\n            userUuid: \"01890b6e-7b8c-7e6b-8e2e-7b8c7e6b8e2f\",\n            operation: \"deleted\",\n            entity: \"customer_789\",\n            description: \"Customer deleted\",\n            createdAt: new Date(\"2025-01-15T12:00:00.000Z\"),\n            data: {\n                customerUuid: \"customer_789\",\n                userName: \"Admin User\",\n                deletedAt: \"2025-01-15T12:00:00.000Z\",\n                changes: {\n                    name: {\n                        before: \"Old Customer\",\n                        after: null\n                    },\n                    email: {\n                        before: \"<EMAIL>\",\n                        after: null\n                    },\n                    status: {\n                        before: \"active\",\n                        after: null\n                    }\n                },\n                changedFields: [\n                    \"name\",\n                    \"email\",\n                    \"status\"\n                ],\n                changeCount: 3\n            }\n        }\n    ];\n    const openModal = (log)=>{\n        setSelectedLog(log);\n        setIsModalOpen(true);\n    };\n    const closeModal = ()=>{\n        setIsModalOpen(false);\n        setSelectedLog(null);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 py-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-6xl mx-auto px-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold text-gray-900 mb-8\",\n                        children: \"Delta Diff Viewer Test\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\test-diff\\\\page.tsx\",\n                        lineNumber: 144,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-6 py-4 border-b border-gray-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-lg font-medium text-gray-900\",\n                                        children: \"Sample Logs with Delta Changes\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\test-diff\\\\page.tsx\",\n                                        lineNumber: 150,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600 mt-1\",\n                                        children: \"Click on any log to see the new diff viewer in action\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\test-diff\\\\page.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\test-diff\\\\page.tsx\",\n                                lineNumber: 149,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"divide-y divide-gray-200\",\n                                children: sampleLogs.map((log)=>{\n                                    var _log_data;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"px-6 py-4 hover:bg-gray-50 cursor-pointer transition-colors\",\n                                        onClick: ()=>openModal(log),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-sm font-medium text-gray-900\",\n                                                            children: log.description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\test-diff\\\\page.tsx\",\n                                                            lineNumber: 167,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: [\n                                                                log.operation,\n                                                                \" • \",\n                                                                log.entity,\n                                                                \" • \",\n                                                                ((_log_data = log.data) === null || _log_data === void 0 ? void 0 : _log_data.changeCount) || 0,\n                                                                \" changes\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\test-diff\\\\page.tsx\",\n                                                            lineNumber: 170,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\test-diff\\\\page.tsx\",\n                                                    lineNumber: 166,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-gray-500\",\n                                                    children: log.createdAt.toLocaleString()\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\test-diff\\\\page.tsx\",\n                                                    lineNumber: 174,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\test-diff\\\\page.tsx\",\n                                            lineNumber: 165,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, log.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\test-diff\\\\page.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 15\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\test-diff\\\\page.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\test-diff\\\\page.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-8 bg-white rounded-lg shadow p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-lg font-medium text-gray-900 mb-4\",\n                                children: \"Features Demonstrated\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\test-diff\\\\page.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"space-y-2 text-sm text-gray-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            \"• \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Before/After Comparison:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\test-diff\\\\page.tsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 19\n                                            }, this),\n                                            \" Inline view with strikethrough for removed content and highlighting for added content\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\test-diff\\\\page.tsx\",\n                                        lineNumber: 188,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            \"• \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Complex Objects:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\test-diff\\\\page.tsx\",\n                                                lineNumber: 189,\n                                                columnNumber: 19\n                                            }, this),\n                                            \" JSON objects and arrays are properly displayed\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\test-diff\\\\page.tsx\",\n                                        lineNumber: 189,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            \"• \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Null Handling:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\test-diff\\\\page.tsx\",\n                                                lineNumber: 190,\n                                                columnNumber: 19\n                                            }, this),\n                                            \" Creation and deletion scenarios with null values\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\test-diff\\\\page.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            \"• \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Visual Clarity:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\test-diff\\\\page.tsx\",\n                                                lineNumber: 191,\n                                                columnNumber: 19\n                                            }, this),\n                                            \" Clear distinction between unchanged, added, and removed content\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\test-diff\\\\page.tsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: [\n                                            \"• \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Compact Storage:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\test-diff\\\\page.tsx\",\n                                                lineNumber: 192,\n                                                columnNumber: 19\n                                            }, this),\n                                            \" Only changed fields are stored, reducing log size significantly\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\test-diff\\\\page.tsx\",\n                                        lineNumber: 192,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\test-diff\\\\page.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\test-diff\\\\page.tsx\",\n                        lineNumber: 183,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\test-diff\\\\page.tsx\",\n                lineNumber: 143,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LogDetailsModal__WEBPACK_IMPORTED_MODULE_2__.LogDetailsModal, {\n                isOpen: isModalOpen,\n                log: selectedLog,\n                onClose: closeModal,\n                userName: \"Admin User\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\test-diff\\\\page.tsx\",\n                lineNumber: 197,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\test-diff\\\\page.tsx\",\n        lineNumber: 142,\n        columnNumber: 5\n    }, this);\n}\n_s(TestDiffPage, \"TVA07niKGVHurS9Ob4BIGJFk+Xk=\");\n_c = TestDiffPage;\nvar _c;\n$RefreshReg$(_c, \"TestDiffPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/logs/test-diff/page.tsx\n"));

/***/ })

});