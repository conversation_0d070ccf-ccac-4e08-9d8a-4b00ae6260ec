import { ApiProperty } from "@nestjs/swagger";
import { PurchaseStatus, PaymentMethods } from "../purchase.entity";

export class PurchaseItemDto {
  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the product",
  })
  productUuid: string;

  @ApiProperty({ example: "Product Name", description: "Name of the product" })
  name: string;

  @ApiProperty({ example: 5, description: "Quantity purchased" })
  quantity: number;

  @ApiProperty({ example: 9.99, description: "Unit price of the product" })
  unitPrice: number;

  @ApiProperty({
    example: 49.95,
    description: "Line total (quantity * unit price)",
  })
  lineTotal: number;
}

export class PurchaseDto {
  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the purchase",
  })
  uuid: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the user creating the purchase",
  })
  userUuid: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the warehouse",
  })
  warehouseUuid: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the supplier",
    required: false,
  })
  supplierUuid?: string;

  @ApiProperty({
    type: [PurchaseItemDto],
    description: "Array of items in the purchase",
  })
  itemsSnapshot: PurchaseItemDto[];

  @ApiProperty({ example: 100.0, description: "Total amount of the purchase" })
  totalAmount: number;

  @ApiProperty({ example: 50.0, description: "Amount already paid" })
  amountPaid: number;

  @ApiProperty({
    example: 50.0,
    description: "Balance due (totalAmount - amountPaid)",
  })
  balanceDue: number;

  @ApiProperty({
    example: PaymentMethods.CASH,
    description: "Payment method used",
    enum: PaymentMethods,
  })
  paymentMethod: PaymentMethods;

  @ApiProperty({
    example: "2024-01-15T10:30:00.000Z",
    description: "Date when payment was made",
  })
  paymentDate: string;

  @ApiProperty({
    example: "2024-01-15T10:30:00.000Z",
    description: "Invoice date",
  })
  invoiceDate: string;

  @ApiProperty({
    example: "2024-02-14T10:30:00.000Z",
    description: "Due date for payment",
  })
  dueDate: string;

  @ApiProperty({
    example: PurchaseStatus.UNPAID,
    description: "Current status of the purchase",
    enum: PurchaseStatus,
  })
  status: PurchaseStatus;

  @ApiProperty({ example: false, description: "Soft delete flag" })
  isDeleted: boolean;

  @ApiProperty({
    example: "2024-01-15T10:30:00.000Z",
    description: "Creation timestamp",
  })
  createdAt: string;

  @ApiProperty({
    example: "2024-01-15T10:30:00.000Z",
    description: "Last update timestamp",
  })
  updatedAt: string;
}

export function toPurchaseDto(doc: any): PurchaseDto {
  return {
    uuid:
      doc.uuid ??
      (doc._id
        ? typeof doc._id === "string"
          ? doc._id
          : undefined
        : undefined),
    userUuid: doc.userUuidString ?? undefined,
    warehouseUuid: doc.warehouseUuidString ?? undefined,
    supplierUuid: doc.supplierUuidString ?? undefined,
    itemsSnapshot: doc.itemsSnapshot || [],
    totalAmount: doc.totalAmount,
    amountPaid: doc.amountPaid,
    balanceDue: doc.balanceDue,
    paymentMethod: doc.paymentMethod,
    paymentDate: doc.paymentDate,
    invoiceDate: doc.invoiceDate,
    dueDate: doc.dueDate,
    status: doc.status,
    isDeleted: doc.isDeleted,
    createdAt: doc.createdAt,
    updatedAt: doc.updatedAt,
  };
}

export function toPurchaseDtoArray(docs: any[]): PurchaseDto[] {
  return docs.map(toPurchaseDto);
}
