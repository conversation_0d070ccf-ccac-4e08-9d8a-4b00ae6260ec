import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";
import { Storage } from "./storage.entity";
import { StorageType } from "./storage_type.enum";
import { Warehouse } from "../warehouses/warehouse.entity";
import { InventoryItem } from "./inventory-item.entity";
import { Product } from "../products/product.entity";
import { StorageDto } from "./dto/storage.dto";

@Injectable()
export class InventoryService {
  constructor(
    @InjectRepository(Storage)
    private storageRepository: Repository<Storage>,
    @InjectRepository(InventoryItem)
    private inventoryItemRepository: Repository<InventoryItem>,
    @InjectRepository(Warehouse)
    private warehouseRepository: Repository<Warehouse>,
    @InjectRepository(Product)
    private productRepository: Repository<Product>,
  ) {}

  // Helper to map Storage entity to DTO
  private toStorageDto(storage: Storage): StorageDto {
    return {
      uuid: storage.id,
      name: storage.name,
      warehouseUuid: storage.warehouseUuid,
      userUuid: storage.userUuid,
      type: storage.type,
      createdAt: storage.createdAt,
      updatedAt: storage.updatedAt,
      isDeleted: storage.isDeleted,
    };
  }

  // Find storage by uuid, not deleted
  public async getStorageByUuid(uuid: string): Promise<Storage | null> {
    return this.storageRepository.findOne({
      where: { id: uuid, isDeleted: false },
    });
  }

  // Helper to map InventoryItem entity to DTO
  private toInventoryItemDto(item: InventoryItem): any {
    return {
      uuid: item.id,
      productUuid: item.productUuid,
      storageUuid: item.storageUuid,
      quantity: item.quantity,
      createdAt: item.createdAt,
      updatedAt: item.updatedAt,
      productSnapshot: null, // productSnapshot field doesn't exist in entity
    };
  }

  // Storage CRUD
  async createStorage(
    data: {
      name: string;
      warehouseUuid: string;
      userUuid: string;
      type: StorageType;
    },
  ): Promise<StorageDto> {
    const storage = this.storageRepository.create({
      id: Storage.generateId(),
      name: data.name,
      warehouseUuid: data.warehouseUuid,
      userUuid: data.userUuid,
      type: data.type,
    });

    const savedStorage = await this.storageRepository.save(storage);
    return this.toStorageDto(savedStorage);
  }

  async listStorages(warehouseUuid: string): Promise<StorageDto[]> {
    const storages = await this.storageRepository.find({
      where: { warehouseUuid: warehouseUuid, isDeleted: false },
      // No relations, since Storage has no user or warehouse relation
    });

    return storages.map((storage) => this.toStorageDto(storage));
  }

  async listAllStorages(): Promise<StorageDto[]> {
    const storages = await this.storageRepository.find({
      where: { isDeleted: false },
      // No relations, since Storage has no user or warehouse relation
    });

    return storages.map((storage) => this.toStorageDto(storage));
  }

  async deleteAllStorages(): Promise<{ deletedCount: number }> {
    const result = await this.storageRepository.update(
      { isDeleted: false },
      { isDeleted: true }
    );
    return { deletedCount: result.affected || 0 };
  }

  async softDeleteStorage(uuid: string): Promise<StorageDto> {
    const storage = await this.storageRepository.findOne({
      where: { id: uuid, isDeleted: false },
    });

    if (!storage) {
      throw new NotFoundException(`Storage with UUID ${uuid} not found`);
    }

    storage.isDeleted = true;
    const updatedStorage = await this.storageRepository.save(storage);
    return this.toStorageDto(updatedStorage);
  }

  async updateStorageName(
    uuid: string,
    dto: { name: string },
  ): Promise<StorageDto> {
    const storage = await this.storageRepository.findOne({
      where: { id: uuid, isDeleted: false },
    });

    if (!storage) {
      throw new NotFoundException(`Storage with UUID ${uuid} not found`);
    }

    storage.name = dto.name;
    const updatedStorage = await this.storageRepository.save(storage);
    return this.toStorageDto(updatedStorage);
  }

  // Inventory Item CRUD
  async createInventoryItem(data: {
    productUuid: string;
    storageUuid: string;
    quantity: number;
  }): Promise<any> {
    // Get product snapshot
    const product = await this.productRepository.findOne({
      where: { id: data.productUuid },
    });

    if (!product) {
      throw new NotFoundException(`Product with UUID ${data.productUuid} not found`);
    }

    // Check if inventory item already exists
    const existingItem = await this.inventoryItemRepository.findOne({
      where: {
        productUuid: data.productUuid,
        storageUuid: data.storageUuid,
      },
    });

    if (existingItem) {
      // Update existing item
      existingItem.quantity += data.quantity;
      // productSnapshot field doesn't exist in entity, so we'll skip this
      // existingItem.productSnapshot = {
      //   name: product.name,
      //   sku: product.sku,
      //   price: product.price,
      //   category: product.category,
      // };
      const updatedItem = await this.inventoryItemRepository.save(existingItem);
      return this.toInventoryItemDto(updatedItem);
    } else {
      // Create new item
      const inventoryItem = this.inventoryItemRepository.create({
        id: InventoryItem.generateId(),
        productUuid: data.productUuid,
        storageUuid: data.storageUuid,
        quantity: data.quantity,
        // productSnapshot field doesn't exist in entity
      });

      const savedItem = await this.inventoryItemRepository.save(inventoryItem);
      return this.toInventoryItemDto(savedItem);
    }
  }

  async listInventoryItems(filter?: {
    storageUuid?: string;
    productName?: string;
  }): Promise<any[]> {
    const queryBuilder = this.inventoryItemRepository
      .createQueryBuilder("item")
      .leftJoinAndSelect("item.product", "product")
      .leftJoinAndSelect("item.storage", "storage");

    if (filter?.storageUuid) {
      queryBuilder.andWhere("item.storageUuid = :storageUuid", {
        storageUuid: filter.storageUuid,
      });
    }

    if (filter?.productName) {
      queryBuilder.andWhere("product.name ILIKE :productName", {
        productName: `%${filter.productName}%`,
      });
    }

    const items = await queryBuilder.getMany();
    return items.map((item) => this.toInventoryItemDto(item));
  }

  async updateInventoryItemQuantity(
    uuid: string,
    quantity: number,
  ): Promise<any> {
    const item = await this.inventoryItemRepository.findOne({
      where: { id: uuid },
    });

    if (!item) {
      throw new NotFoundException(`Inventory item with UUID ${uuid} not found`);
    }

    item.quantity = quantity;
    const updatedItem = await this.inventoryItemRepository.save(item);
    return this.toInventoryItemDto(updatedItem);
  }

  async deleteInventoryItem(uuid: string): Promise<void> {
    const item = await this.inventoryItemRepository.findOne({
      where: { id: uuid },
    });

    if (!item) {
      throw new NotFoundException(`Inventory item with UUID ${uuid} not found`);
    }

    await this.inventoryItemRepository.remove(item);
  }
} 