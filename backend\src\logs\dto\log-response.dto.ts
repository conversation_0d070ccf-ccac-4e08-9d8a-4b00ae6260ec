import { ApiProperty } from "@nestjs/swagger";

export class LogResponseDto {
  @ApiProperty({
    example: "01890b6e-7b8c-7e6b-8e2e-7b8c7e6b8e2e",
    description: "The UUID of the log entry",
  })
  id: string;

  @ApiProperty({
    example: "01890b6e-7b8c-7e6b-8e2e-7b8c7e6b8e2e",
    description: "The UUID of the user who performed the action",
  })
  userUuid: string;

  @ApiProperty({
    example: "deleted",
    description: "The operation performed",
  })
  operation: string;

  @ApiProperty({
    example: "sale123",
    description: "The entity that was affected",
  })
  entity: string;

  @ApiProperty({
    example: "User <PERSON> deleted sale order #12345",
    description: "Human-readable description of the action performed",
  })
  description?: string;

  @ApiProperty({
    example: { "orderId": "12345", "amount": 150.00, "customerName": "John Doe" },
    description: "Additional JSON data related to the action",
  })
  data?: Record<string, any>;

  @ApiProperty({
    example: "2025-01-15T10:30:00.000Z",
    description: "Timestamp when the log entry was created",
  })
  createdAt: Date;
} 