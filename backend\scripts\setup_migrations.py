#!/usr/bin/env python3
"""
Migration System Setup Script

This script helps set up the TypeORM migration system and verifies the environment.
It also validates the backup API connectivity for safe migration operations.
"""

import sys
import subprocess
import os
import requests
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

BACKEND_DIR = Path(__file__).resolve().parent.parent

# API configuration
BACKUP_API_BASE_URL = os.environ.get("BACKUP_API_BASE_URL", "http://localhost:5000")

def check_prerequisites():
    """Check if all prerequisites are installed."""
    print("🔍 Checking prerequisites...")
    
    # Check Node.js and npm
    try:
        result = subprocess.run(["node", "--version"], capture_output=True, text=True, check=True)
        print(f"✅ Node.js: {result.stdout.strip()}")
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ Node.js not found. Please install Node.js.")
        return False
    
    try:
        result = subprocess.run(["npm", "--version"], capture_output=True, text=True, check=True)
        print(f"✅ npm: {result.stdout.strip()}")
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ npm not found. Please install npm.")
        return False
    
    # Check PostgreSQL client tools
    try:
        result = subprocess.run(["pg_dump", "--version"], capture_output=True, text=True, check=True)
        print(f"✅ pg_dump: {result.stdout.strip()}")
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ pg_dump not found. Please install PostgreSQL client tools.")
        return False
    
    try:
        result = subprocess.run(["psql", "--version"], capture_output=True, text=True, check=True)
        print(f"✅ psql: {result.stdout.strip()}")
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ psql not found. Please install PostgreSQL client tools.")
        return False
    
    # Check Python dependencies
    try:
        import dotenv
        print("✅ python-dotenv: installed")
    except ImportError:
        print("❌ python-dotenv not found. Please install: pip install python-dotenv")
        return False
    
    try:
        import requests
        print("✅ requests: installed")
    except ImportError:
        print("❌ requests not found. Please install: pip install requests")
        return False
    
    return True

def check_environment():
    """Check environment variables."""
    print("\n🔍 Checking environment variables...")
    
    required_vars = [
        "YUGABYTE_HOST",
        "YUGABYTE_PORT", 
        "YUGABYTE_DATABASE",
        "YUGABYTE_USER",
        "YUGABYTE_PASSWORD"
    ]
    
    missing_vars = []
    for var in required_vars:
        value = os.environ.get(var)
        if value:
            if var == "YUGABYTE_PASSWORD":
                print(f"✅ {var}: {'*' * len(value)}")
            else:
                print(f"✅ {var}: {value}")
        else:
            print(f"❌ {var}: NOT SET")
            missing_vars.append(var)
    
    # Check backup API URL
    backup_api_url = os.environ.get("BACKUP_API_BASE_URL", "http://localhost:5000")
    print(f"✅ BACKUP_API_BASE_URL: {backup_api_url}")
    
    if missing_vars:
        print(f"\n❌ Missing environment variables: {', '.join(missing_vars)}")
        print(f"Please check your .env file at: {BACKEND_DIR / '.env'}")
        return False
    
    return True

def check_backup_api():
    """Check backup API connectivity."""
    print("\n🔍 Checking backup API connectivity...")
    
    try:
        response = requests.get(f"{BACKUP_API_BASE_URL}/health/", timeout=10)
        if response.status_code == 200:
            health_data = response.json()
            if health_data.get("status") == "healthy":
                print("✅ Backup API is healthy and accessible")
                
                # Get additional API status
                status_response = requests.get(f"{BACKUP_API_BASE_URL}/health/status", timeout=10)
                if status_response.status_code == 200:
                    status_data = status_response.json()
                    env_status = status_data.get('environment', {})
                    tools_status = status_data.get('yugabyte_tools', {})
                    backups_count = status_data.get('backups_count', 0)
                    
                    print(f"   Environment Valid: {env_status.get('valid', 'Unknown')}")
                    print(f"   YugabyteDB Tools Available: {tools_status.get('available', 'Unknown')}")
                    print(f"   Total Backups: {backups_count}")
                
                return True
            else:
                print(f"⚠️  Backup API status: {health_data.get('status')}")
                return True
        else:
            print(f"❌ Backup API health check failed: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Cannot connect to backup API at {BACKUP_API_BASE_URL}: {e}")
        print(f"   This is required for safe migration operations with automatic backups")
        return False

def check_database_connection():
    """Test database connection."""
    print("\n🔍 Testing database connection...")
    
    try:
        env = os.environ.copy()
        env['PGPASSWORD'] = os.environ.get('YUGABYTE_PASSWORD')
        
        result = subprocess.run([
            "psql",
            f"--host={os.environ.get('YUGABYTE_HOST')}",
            f"--port={os.environ.get('YUGABYTE_PORT')}",
            f"--username={os.environ.get('YUGABYTE_USER')}",
            f"--dbname={os.environ.get('YUGABYTE_DATABASE')}",
            "--no-password",
            "-c", "SELECT version();"
        ], env=env, capture_output=True, text=True, check=True)
        
        print("✅ Database connection successful")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Database connection failed: {e}")
        if e.stderr:
            print(f"   Error: {e.stderr}")
        return False
    except FileNotFoundError:
        print("❌ psql command not found")
        return False

def check_npm_dependencies():
    """Check if npm dependencies are installed."""
    print("\n🔍 Checking npm dependencies...")
    
    package_json = BACKEND_DIR / "package.json"
    if not package_json.exists():
        print("❌ package.json not found")
        return False
    
    node_modules = BACKEND_DIR / "node_modules"
    if not node_modules.exists():
        print("❌ node_modules not found. Please run: npm install")
        return False
    
    print("✅ npm dependencies installed")
    return True

def create_directories():
    """Create necessary directories."""
    print("\n🔍 Creating directories...")
    
    directories = [
        BACKEND_DIR / "src" / "migrations",
        BACKEND_DIR / "backups"
    ]
    
    for directory in directories:
        directory.mkdir(parents=True, exist_ok=True)
        print(f"✅ Created: {directory}")

def test_migration_commands():
    """Test migration commands."""
    print("\n🔍 Testing migration commands...")
    
    try:
        # Test migration:show command
        result = subprocess.run([
            "npm", "run", "migration:show"
        ], cwd=BACKEND_DIR, capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("✅ Migration commands working")
            return True
        else:
            print(f"❌ Migration commands failed: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ Migration commands timed out")
        return False
    except Exception as e:
        print(f"❌ Migration commands error: {e}")
        return False

def main():
    """Main setup function."""
    print("🚀 TypeORM Migration System Setup")
    print("=" * 50)
    
    # Check prerequisites
    if not check_prerequisites():
        print("\n❌ Prerequisites check failed. Please install missing dependencies.")
        sys.exit(1)
    
    # Check environment
    if not check_environment():
        print("\n❌ Environment check failed. Please configure your .env file.")
        sys.exit(1)
    
    # Check backup API connectivity
    if not check_backup_api():
        print("\n⚠️  Backup API check failed. Safe migration operations may not be available.")
        print("   You can still run migrations, but automatic backups won't work.")
        print("   To enable safe migrations, ensure the backup backend is running.")
    
    # Check database connection
    if not check_database_connection():
        print("\n❌ Database connection failed. Please check your database settings.")
        sys.exit(1)
    
    # Check npm dependencies
    if not check_npm_dependencies():
        print("\n❌ NPM dependencies check failed. Please run: npm install")
        sys.exit(1)
    
    # Create directories
    create_directories()
    
    # Test migration commands
    if not test_migration_commands():
        print("\n⚠️  Migration commands test failed. This might be normal if no migrations exist yet.")
    
    print("\n🎉 Setup completed successfully!")
    print("\n📋 Next steps:")
    print("1. Make changes to your entities")
    print("2. Generate a migration: npm run migration:generate -- -n MigrationName")
    print("3. Run migrations safely: python scripts/migration_manager_ysqlsh.py run")
    print("4. Check status: python scripts/migration_manager_ysqlsh.py status")
    print("\n🔒 Safe Migration Workflow:")
    print("   - Always use: python scripts/migration_manager_ysqlsh.py run")
    print("   - This automatically creates backups before running migrations")
    print("   - If something goes wrong, restore with: python scripts/migration_manager_ysqlsh.py restore")
    print("\n📚 For more information, see: docs/MIGRATION_WORKFLOW.md")

if __name__ == "__main__":
    main() 