// Default roles for new warehouses in Dido Distribution
// These roles use the Permission enum from user_permissions.ts
import { Permission } from "./user_permissions";

export interface DefaultRole {
  name: string;
  permissions: Permission[];
}

// Role name constants
export const ADMIN = "admin";
export const MANAGER = "manager";
export const SALES = "sales";
export const PURCHASING = "purchasing";
export const WAREHOUSE = "warehouse";
export const STAFF = "staff";
export const MOBILE_SALE_AGENT = "mobile sale agent";

export const DEFAULT_ROLES: DefaultRole[] = [
  {
    name: ADMIN,
    permissions: [
      Permission.DASHBOARD_VIEW,
      Permission.SALES_VIEW,
      Permission.SALES_EDIT,
      Permission.SALES_APPROVE,
      Permission.PURCHASING_VIEW,
      Permission.PURCHASING_EDIT,
      Permission.INVENTORY_VIEW,
      Permission.INVENTORY_MANAGE,
      Permission.LOGISTICS_VIEW,
      Permission.LOGISTICS_MANAGE,
      Permission.REPORTS_VIEW,
      Permission.USER_MANAGE,
      Permission.SETTINGS_MANAGE,
    ],
  },
  {
    name: <PERSON>NAGE<PERSON>,
    permissions: [
      Permission.DASHBOARD_VIEW,
      Permission.SALES_VIEW,
      Permission.SALES_EDIT,
      Permission.PURCHASING_VIEW,
      Permission.PURCHASING_EDIT,
      Permission.INVENTORY_VIEW,
      Permission.INVENTORY_MANAGE,
      Permission.LOGISTICS_VIEW,
      Permission.LOGISTICS_MANAGE,
      Permission.REPORTS_VIEW,
    ],
  },
  {
    name: SALES,
    permissions: [
      Permission.SALES_VIEW,
      Permission.SALES_EDIT,
      Permission.SALES_APPROVE,
      Permission.INVENTORY_VIEW,
      Permission.REPORTS_VIEW,
    ],
  },
  {
    name: PURCHASING,
    permissions: [
      Permission.PURCHASING_VIEW,
      Permission.PURCHASING_EDIT,
      Permission.INVENTORY_VIEW,
      Permission.REPORTS_VIEW,
    ],
  },
  {
    name: WAREHOUSE,
    permissions: [
      Permission.INVENTORY_VIEW,
      Permission.INVENTORY_MANAGE,
      Permission.LOGISTICS_VIEW,
      Permission.LOGISTICS_MANAGE,
    ],
  },
  {
    name: STAFF,
    permissions: [
      Permission.DASHBOARD_VIEW,
      Permission.SALES_VIEW,
      Permission.PURCHASING_VIEW,
      Permission.INVENTORY_VIEW,
      Permission.LOGISTICS_VIEW,
      Permission.REPORTS_VIEW,
    ],
  },
  {
    name: MOBILE_SALE_AGENT,
    permissions: [
      Permission.SALES_VIEW,
      Permission.SALES_EDIT,
      Permission.INVENTORY_VIEW,
      Permission.LOGISTICS_VIEW,
      Permission.REPORTS_VIEW,
    ],
  },
];
