import axios from 'axios';
import { getAxiosAuthHeaders } from '@/utils/authHeaders';

const API_BASE = '/api';

// Dashboard statistics interface
export interface DashboardStats {
  products: number;
  warehouses: number;
  securityEvents: SecurityEvent[];
  userRole?: string;
  warehouseName?: string;
}

// Security event interface
export interface SecurityEvent {
  eventType: string;
  timestamp: string;
  ipAddress?: string;
  success: boolean;
}

// Security status interface
export interface SecurityStatus {
  isLocked: boolean;
  lockedUntil: string | null;
  remainingAttempts: number;
  suspiciousActivityDetected: boolean;
  recentEvents: SecurityEvent[];
}

// Dashboard API service
export class DashboardApi {
  // Get products count
  static async getProductsCount(): Promise<number> {
    try {
      const response = await axios.get(`${API_BASE}/products/count`, {
        headers: getAxiosAuthHeaders(),
      });
      return response.data.count || 0;
    } catch (error) {
      console.error('Failed to fetch products count:', error);
      return 0;
    }
  }

  // Get warehouses count
  static async getWarehousesCount(userUuid?: string): Promise<number> {
    try {
      const url = userUuid 
        ? `${API_BASE}/warehouses/count?userUuid=${encodeURIComponent(userUuid)}`
        : `${API_BASE}/warehouses/count`;
      const response = await axios.get(url, {
        headers: getAxiosAuthHeaders(),
      });
      return response.data.count || 0;
    } catch (error) {
      console.error('Failed to fetch warehouses count:', error);
      return 0;
    }
  }

  // Get security status and recent events
  static async getSecurityStatus(): Promise<SecurityStatus> {
    try {
      const response = await axios.get(`${API_BASE}/auth/security-status`, {
        headers: getAxiosAuthHeaders(),
      });
      return response.data;
    } catch (error) {
      console.error('Failed to fetch security status:', error);
      return {
        isLocked: false,
        lockedUntil: null,
        remainingAttempts: 5,
        suspiciousActivityDetected: false,
        recentEvents: []
      };
    }
  }

  // Get comprehensive dashboard data
  static async getDashboardStats(userUuid?: string): Promise<DashboardStats> {
    try {
      const [productsCount, warehousesCount, securityStatus] = await Promise.all([
        this.getProductsCount(),
        this.getWarehousesCount(userUuid),
        this.getSecurityStatus()
      ]);

      return {
        products: productsCount,
        warehouses: warehousesCount,
        securityEvents: securityStatus.recentEvents,
        userRole: 'admin', // This should come from user context
        warehouseName: 'Main Warehouse' // This should come from user context
      };
    } catch (error) {
      console.error('Failed to fetch dashboard stats:', error);
      return {
        products: 0,
        warehouses: 0,
        securityEvents: [],
        userRole: 'user',
        warehouseName: 'Unknown'
      };
    }
  }
}

// Export individual functions for convenience
export const getProductsCount = DashboardApi.getProductsCount;
export const getWarehousesCount = DashboardApi.getWarehousesCount;
export const getSecurityStatus = DashboardApi.getSecurityStatus;
export const getDashboardStats = DashboardApi.getDashboardStats; 