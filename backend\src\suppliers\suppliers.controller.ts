import {
  Controller,
  Get,
  Post,
  Patch,
  Delete,
  Param,
  Body,
  Query,
  UseGuards,
  ParseUUIDPipe,
} from "@nestjs/common";
import { SuppliersService } from "./suppliers.service";
import { CreateSupplierDto } from "./dto/create-supplier.dto";
import { UpdateSupplierDto } from "./dto/update-supplier.dto";
import { SupplierDto, toSupplierDto, toSupplierDtoArray } from "./dto/supplier.dto";
import {
  ApiTags,
  ApiQuery,
  ApiOperation,
  ApiResponse,
  ApiParam,
} from "@nestjs/swagger";
@ApiTags("suppliers")
@Controller("suppliers")
export class SuppliersController {
  constructor(private readonly suppliersService: SuppliersService) {}

  @Post()
  @ApiOperation({
    summary: "Create a new supplier",
    description:
      "Creates a new supplier. userUuid and name fields are required; all other fields are optional.",
  })
  @ApiResponse({
    status: 201,
    description: "Supplier created successfully",
    type: SupplierDto,
  })
  @ApiResponse({
    status: 400,
    description: "Bad request - validation error or warehouse not found",
  })
  async create(
    @Body() createSupplierDto: CreateSupplierDto,
  ): Promise<SupplierDto> {
    const supplier = await this.suppliersService.create(createSupplierDto);
    return toSupplierDto(supplier);
  }

  @Get()
  @ApiOperation({
    summary: "Get all suppliers",
    description: "Returns all non-deleted suppliers. Can be filtered by warehouseUuid and userUuid.",
  })
  @ApiQuery({
    name: "warehouseUuid",
    type: String,
    required: false,
    description: "UUIDv7 string of the warehouse (optional)",
  })
  @ApiQuery({
    name: "userUuid",
    type: String,
    required: false,
    description: "UUIDv7 string of the user (optional)",
  })
  @ApiResponse({
    status: 200,
    description: "List of suppliers retrieved successfully",
    type: [SupplierDto],
  })
  async findAll(
    @Query("warehouseUuid") warehouseUuid?: string,
    @Query("userUuid") userUuid?: string,
  ): Promise<SupplierDto[]> {
    const suppliers = await this.suppliersService.findAll(warehouseUuid, userUuid);
    return toSupplierDtoArray(suppliers);
  }

  @Get("filter")
  @ApiOperation({
    summary: "Filter suppliers by multiple criteria",
    description:
      "All filters are optional. Returns suppliers matching the provided criteria.",
  })
  @ApiQuery({
    name: "warehouseUuid",
    type: String,
    required: false,
    description: "UUIDv7 string of the warehouse (optional)",
  })
  @ApiQuery({
    name: "userUuid",
    type: String,
    required: false,
    description: "UUIDv7 string of the user (optional)",
  })
  @ApiQuery({
    name: "name",
    type: String,
    required: false,
    description: "Partial supplier name, case-insensitive (optional)",
  })
  @ApiQuery({
    name: "email",
    type: String,
    required: false,
    description: "Partial email address, case-insensitive (optional)",
  })
  @ApiQuery({
    name: "phone",
    type: String,
    required: false,
    description: "Partial phone number, case-insensitive (optional)",
  })
  @ApiQuery({
    name: "code",
    type: String,
    required: false,
    description: "Partial supplier code, case-insensitive (optional)",
  })
  @ApiQuery({
    name: "fiscalId",
    type: String,
    required: false,
    description: "Partial fiscal ID, case-insensitive (optional)",
  })
  @ApiResponse({
    status: 200,
    description: "Filtered list of suppliers",
    type: [SupplierDto],
  })
  async filter(
    @Query("warehouseUuid") warehouseUuid?: string,
    @Query("userUuid") userUuid?: string,
    @Query("name") name?: string,
    @Query("email") email?: string,
    @Query("phone") phone?: string,
    @Query("code") code?: string,
    @Query("fiscalId") fiscalId?: string,
  ): Promise<SupplierDto[]> {
    const suppliers = await this.suppliersService.filterSuppliers({ 
      warehouseUuid, 
      userUuid, 
      name, 
      email, 
      phone, 
      code, 
      fiscalId 
    });
    return toSupplierDtoArray(suppliers);
  }

  @Get(":uuid")
  @ApiOperation({
    summary: "Get supplier by UUID",
    description: "Returns a specific supplier by its UUID. Can be filtered by userUuid.",
  })
  @ApiParam({
    name: "uuid",
    description: "UUIDv7 string of the supplier",
    example: "018e6b7c-9c2a-7b73-bb6d-0d1e8e2f4b1a",
  })
  @ApiQuery({
    name: "userUuid",
    type: String,
    required: false,
    description: "UUIDv7 string of the user (optional)",
  })
  @ApiResponse({
    status: 200,
    description: "Supplier retrieved successfully",
    type: SupplierDto,
  })
  @ApiResponse({
    status: 404,
    description: "Supplier not found",
  })
  async findOne(
    @Param("uuid", ParseUUIDPipe) uuid: string,
    @Query("userUuid") userUuid?: string,
  ): Promise<SupplierDto> {
    const supplier = await this.suppliersService.findOne(uuid, userUuid);
    return toSupplierDto(supplier);
  }

  @Patch(":uuid")
  @ApiOperation({
    summary: "Update supplier",
    description:
      "Updates a specific supplier by its UUID. All fields are optional. Can be filtered by userUuid.",
  })
  @ApiParam({
    name: "uuid",
    description: "UUIDv7 string of the supplier",
    example: "018e6b7c-9c2a-7b73-bb6d-0d1e8e2f4b1a",
  })
  @ApiQuery({
    name: "userUuid",
    type: String,
    required: false,
    description: "UUIDv7 string of the user (optional)",
  })
  @ApiResponse({
    status: 200,
    description: "Supplier updated successfully",
    type: SupplierDto,
  })
  @ApiResponse({
    status: 404,
    description: "Supplier not found or warehouse not found",
  })
  async update(
    @Param("uuid", ParseUUIDPipe) uuid: string,
    @Body() updateSupplierDto: UpdateSupplierDto,
    @Query("userUuid") userUuid?: string,
  ): Promise<SupplierDto> {
    const supplier = await this.suppliersService.update(uuid, updateSupplierDto, userUuid);
    return toSupplierDto(supplier);
  }

  @Delete(":uuid")
  @ApiOperation({
    summary: "Soft delete supplier",
    description:
      "Soft deletes a supplier by setting isDeleted to true. The supplier will not appear in normal queries but data is preserved. Can be filtered by userUuid.",
  })
  @ApiParam({
    name: "uuid",
    description: "UUIDv7 string of the supplier",
    example: "018e6b7c-9c2a-7b73-bb6d-0d1e8e2f4b1a",
  })
  @ApiQuery({
    name: "userUuid",
    type: String,
    required: false,
    description: "UUIDv7 string of the user (optional)",
  })
  @ApiResponse({
    status: 200,
    description: "Supplier soft deleted successfully",
  })
  @ApiResponse({
    status: 404,
    description: "Supplier not found",
  })
  async remove(
    @Param("uuid", ParseUUIDPipe) uuid: string,
    @Query("userUuid") userUuid?: string,
  ): Promise<void> {
    return this.suppliersService.remove(uuid, userUuid);
  }
}
