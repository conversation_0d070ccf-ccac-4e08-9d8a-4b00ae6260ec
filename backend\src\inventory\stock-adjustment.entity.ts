import { <PERSON>ti<PERSON>, PrimaryColumn, Column, CreateDateColumn, Index } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Uuid7 } from '../utils/uuid7';

@Entity('stock_adjustments')
export class StockAdjustment {
  @ApiProperty({
    description: "UUIDv7 as string",
    type: String,
    format: "uuid",
  })
  @PrimaryColumn('uuid')
  id: string;

  @ApiProperty({ description: "User UUID", type: String, format: "uuid" })
  @Column('uuid')
  @Index()
  userUuid: string;

  @ApiProperty({ description: "Warehouse UUID", type: String, format: "uuid" })
  @Column('uuid')
  @Index()
  warehouseUuid: string;

  @ApiProperty({ description: "Storage UUID", type: String, format: "uuid" })
  @Column('uuid')
  @Index()
  storageUuid: string;

  @ApiProperty({ description: "Product UUID", type: String, format: "uuid" })
  @Column('uuid')
  @Index()
  productUuid: string;

  @ApiProperty({ description: "Adjusted quantity (non-zero)" })
  @Column('integer')
  quantityAdjusted: number;

  @ApiProperty({ description: "Reason for adjustment" })
  @Column({ nullable: true })
  reason: string;

  @ApiProperty({ description: "Creation date", type: Date })
  @CreateDateColumn()
  createdAt: Date;

  // Helper method to generate UUID
  static generateId(): string {
    return new Uuid7().toString();
  }

  // Helper method to generate UUID
  static fromBinary(binary: any): string {
    if (!binary) return null;
    try {
      return new Uuid7(binary).toString();
    } catch {
      return null;
    }
  }
} 