import { Injectable, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource, In } from 'typeorm';
import { StockAdjustment } from './stock-adjustment.entity';
import { InventoryItem } from './inventory-item.entity';
import { Product } from '../products/product.entity';
import { User } from '../users/user.entity';
import { CreateStockAdjustmentDto } from './dto/create-stock-adjustment.dto';

@Injectable()
export class StockAdjustmentService {
  constructor(
    @InjectRepository(StockAdjustment)
    private stockAdjustmentRepository: Repository<StockAdjustment>,
    @InjectRepository(InventoryItem)
    private inventoryItemRepository: Repository<InventoryItem>,
    @InjectRepository(Product)
    private productRepository: Repository<Product>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    private dataSource: DataSource,
  ) {}

  /**
   * List all stock adjustments, sorted by createdAt descending.
   */
  async listStockAdjustments(): Promise<any[]> {
    const adjustments = await this.stockAdjustmentRepository.find({
      order: { createdAt: 'DESC' },
    });
    
    return adjustments.map(adjustment => ({
      uuid: adjustment.id,
      userUuid: adjustment.userUuid,
      warehouseUuid: adjustment.warehouseUuid,
      storageUuid: adjustment.storageUuid,
      productUuid: adjustment.productUuid,
      quantityAdjusted: adjustment.quantityAdjusted,
      reason: adjustment.reason,
      createdAt: adjustment.createdAt,
    }));
  }

  /**
   * List stock adjustments for a specific storage with product and user information.
   */
  async listStockAdjustmentsByStorage(
    storageUuid: string, 
    page: number = 1, 
    limit: number = 10
  ): Promise<{ data: any[], total: number, page: number, limit: number, totalPages: number }> {
    const queryBuilder = this.stockAdjustmentRepository
      .createQueryBuilder('adjustment')
      .where('adjustment.storageUuid = :storageUuid', { storageUuid })
      .orderBy('adjustment.createdAt', 'DESC');

    // Get total count
    const total = await queryBuilder.getCount();

    // Apply pagination
    const skip = (page - 1) * limit;
    queryBuilder.skip(skip).take(limit);

    const adjustments = await queryBuilder.getMany();
    
    // Get unique product and user UUIDs
    const productUuids = [...new Set(adjustments.map(adj => adj.productUuid))];
    const userUuids = [...new Set(adjustments.map(adj => adj.userUuid))];
    
    // Fetch product and user data
    const products = await this.productRepository.find({
      where: { id: In(productUuids) },
      select: ['id', 'name', 'sku']
    });
    const users = await this.userRepository.find({
      where: { id: In(userUuids) },
      select: ['id', 'email']
    });
    
    // Create lookup maps
    const productMap = new Map(products.map(p => [p.id, p]));
    const userMap = new Map(users.map(u => [u.id, u]));
    
    const data = adjustments.map(adjustment => {
      const product = productMap.get(adjustment.productUuid);
      const user = userMap.get(adjustment.userUuid);
      
      return {
        uuid: adjustment.id,
        userUuid: adjustment.userUuid,
        userEmail: user?.email || 'Unknown User',
        warehouseUuid: adjustment.warehouseUuid,
        storageUuid: adjustment.storageUuid,
        productUuid: adjustment.productUuid,
        productName: product?.name || 'Unknown Product',
        productSku: product?.sku || '',
        quantityAdjusted: adjustment.quantityAdjusted,
        reason: adjustment.reason,
        createdAt: adjustment.createdAt,
      };
    });

    return {
      data,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    };
  }

  /**
   * Atomically create a StockAdjustment and update InventoryItem quantity.
   * Throws if adjustment would result in negative quantity.
   * Uses PostgreSQL transactions for atomicity.
   */
  async createStockAdjustment(dto: CreateStockAdjustmentDto) {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Create StockAdjustment first
      const stockAdjustment = this.stockAdjustmentRepository.create({
        id: StockAdjustment.generateId(),
        userUuid: dto.userUuid,
        warehouseUuid: dto.warehouseUuid,
        storageUuid: dto.storageUuid,
        productUuid: dto.productUuid,
        quantityAdjusted: dto.quantityAdjusted,
        reason: dto.reason ?? '',
        createdAt: new Date(),
      });

      const savedAdjustment = await queryRunner.manager.save(stockAdjustment);

      // Check if inventory item exists
      let inventoryItem = await queryRunner.manager.findOne(InventoryItem, {
        where: {
          productUuid: dto.productUuid,
          storageUuid: dto.storageUuid,
        },
      });

      if (inventoryItem) {
        // Update existing inventory item
        const newQuantity = inventoryItem.quantity + dto.quantityAdjusted;
        
        if (newQuantity < 0) {
          throw new BadRequestException(
            'Stock adjustment would result in negative inventory',
          );
        }

        inventoryItem.quantity = newQuantity;
        await queryRunner.manager.save(inventoryItem);
      } else {
        // Create new inventory item if adjustment is positive
        if (dto.quantityAdjusted < 0) {
          throw new BadRequestException(
            'Cannot create inventory item with negative quantity',
          );
        }

        inventoryItem = this.inventoryItemRepository.create({
          id: InventoryItem.generateId(),
          productUuid: dto.productUuid,
          storageUuid: dto.storageUuid,
          quantity: dto.quantityAdjusted,
        });

        await queryRunner.manager.save(inventoryItem);
      }

      await queryRunner.commitTransaction();
      
      return {
        uuid: savedAdjustment.id,
        userUuid: savedAdjustment.userUuid,
        warehouseUuid: savedAdjustment.warehouseUuid,
        storageUuid: savedAdjustment.storageUuid,
        productUuid: savedAdjustment.productUuid,
        quantityAdjusted: savedAdjustment.quantityAdjusted,
        reason: savedAdjustment.reason,
        createdAt: savedAdjustment.createdAt,
      };
    } catch (err) {
      await queryRunner.rollbackTransaction();
      throw err;
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * Bulk stock adjustment allowing negative stock - processes multiple adjustments atomically
   * This method allows inventory to go negative, useful for sales transactions
   */
  async createBulkStockAdjustmentsAllowingNegativeStock(
    adjustments: CreateStockAdjustmentDto[],
  ): Promise<any[]> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const results = [];

      for (const dto of adjustments) {
        // Create StockAdjustment
        const stockAdjustment = this.stockAdjustmentRepository.create({
          id: StockAdjustment.generateId(),
          userUuid: dto.userUuid,
          warehouseUuid: dto.warehouseUuid,
          storageUuid: dto.storageUuid,
          productUuid: dto.productUuid,
          quantityAdjusted: dto.quantityAdjusted,
          reason: dto.reason ?? '',
          createdAt: new Date(),
        });

        const savedAdjustment = await queryRunner.manager.save(stockAdjustment);

        // Update or create inventory item
        let inventoryItem = await queryRunner.manager.findOne(InventoryItem, {
          where: {
            productUuid: dto.productUuid,
            storageUuid: dto.storageUuid,
          },
        });

        if (inventoryItem) {
          inventoryItem.quantity += dto.quantityAdjusted;
          await queryRunner.manager.save(inventoryItem);
        } else {
          inventoryItem = this.inventoryItemRepository.create({
            id: InventoryItem.generateId(),
            productUuid: dto.productUuid,
            storageUuid: dto.storageUuid,
            quantity: dto.quantityAdjusted,
          });
          await queryRunner.manager.save(inventoryItem);
        }

        results.push({
          uuid: savedAdjustment.id,
          userUuid: savedAdjustment.userUuid,
          warehouseUuid: savedAdjustment.warehouseUuid,
          storageUuid: savedAdjustment.storageUuid,
          productUuid: savedAdjustment.productUuid,
          quantityAdjusted: savedAdjustment.quantityAdjusted,
          reason: savedAdjustment.reason,
          createdAt: savedAdjustment.createdAt,
        });
      }

      await queryRunner.commitTransaction();
      return results;
    } catch (err) {
      await queryRunner.rollbackTransaction();
      throw err;
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * Bulk stock adjustment - processes multiple adjustments atomically
   * Throws if any adjustment would result in negative quantity
   */
  async createBulkStockAdjustments(
    adjustments: CreateStockAdjustmentDto[],
  ): Promise<any[]> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const results = [];

      for (const dto of adjustments) {
        // Create StockAdjustment
        const stockAdjustment = this.stockAdjustmentRepository.create({
          id: StockAdjustment.generateId(),
          userUuid: dto.userUuid,
          warehouseUuid: dto.warehouseUuid,
          storageUuid: dto.storageUuid,
          productUuid: dto.productUuid,
          quantityAdjusted: dto.quantityAdjusted,
          reason: dto.reason ?? '',
          createdAt: new Date(),
        });

        const savedAdjustment = await queryRunner.manager.save(stockAdjustment);

        // Update or create inventory item
        let inventoryItem = await queryRunner.manager.findOne(InventoryItem, {
          where: {
            productUuid: dto.productUuid,
            storageUuid: dto.storageUuid,
          },
        });

        if (inventoryItem) {
          const newQuantity = inventoryItem.quantity + dto.quantityAdjusted;
          
          if (newQuantity < 0) {
            throw new BadRequestException(
              `Stock adjustment would result in negative inventory for product ${dto.productUuid}`,
            );
          }

          inventoryItem.quantity = newQuantity;
          await queryRunner.manager.save(inventoryItem);
        } else {
          if (dto.quantityAdjusted < 0) {
            throw new BadRequestException(
              `Cannot create inventory item with negative quantity for product ${dto.productUuid}`,
            );
          }

          inventoryItem = this.inventoryItemRepository.create({
            id: InventoryItem.generateId(),
            productUuid: dto.productUuid,
            storageUuid: dto.storageUuid,
            quantity: dto.quantityAdjusted,
          });
          await queryRunner.manager.save(inventoryItem);
        }

        results.push({
          uuid: savedAdjustment.id,
          userUuid: savedAdjustment.userUuid,
          warehouseUuid: savedAdjustment.warehouseUuid,
          storageUuid: savedAdjustment.storageUuid,
          productUuid: savedAdjustment.productUuid,
          quantityAdjusted: savedAdjustment.quantityAdjusted,
          reason: savedAdjustment.reason,
          createdAt: savedAdjustment.createdAt,
        });
      }

      await queryRunner.commitTransaction();
      return results;
    } catch (err) {
      await queryRunner.rollbackTransaction();
      throw err;
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * Get current stock level for a product in a storage
   */
  async getCurrentStockLevel(
    productUuid: string,
    storageUuid: string,
  ): Promise<number> {
    const inventoryItem = await this.inventoryItemRepository.findOne({
      where: { productUuid, storageUuid },
    });
    return inventoryItem ? inventoryItem.quantity : 0;
  }

  /**
   * Check if an adjustment would result in negative inventory
   */
  async wouldResultInNegativeInventory(
    productUuid: string,
    storageUuid: string,
    quantityToAdjust: number,
  ): Promise<boolean> {
    const currentStock = await this.getCurrentStockLevel(productUuid, storageUuid);
    return currentStock + quantityToAdjust < 0;
  }

  /**
   * Get stock levels for multiple products in a storage
   */
  async getStockLevels(
    storageUuid: string,
    productUuids: string[],
  ): Promise<{ productUuid: string; quantity: number }[]> {
    if (productUuids.length === 0) {
      return [];
    }

    const inventoryItems = await this.inventoryItemRepository
      .createQueryBuilder('item')
      .where('item.storageUuid = :storageUuid', { storageUuid })
      .andWhere('item.productUuid IN (:...productUuids)', { productUuids })
      .getMany();

    // Create a map of productUuid to quantity
    const stockMap = new Map<string, number>();
    inventoryItems.forEach(item => {
      stockMap.set(item.productUuid, item.quantity);
    });

    // Return results for all requested productUuids
    return productUuids.map(productUuid => ({
      productUuid,
      quantity: stockMap.get(productUuid) || 0,
    }));
  }

  /**
   * Get stock levels for all products in a storage
   */
  async getStockLevelsForStorage(
    storageUuid: string,
  ): Promise<{ productUuid: string; quantity: number }[]> {
    const inventoryItems = await this.inventoryItemRepository.find({
      where: { storageUuid },
    });

    return inventoryItems.map(item => ({
      productUuid: item.productUuid,
      quantity: item.quantity,
    }));
  }
} 