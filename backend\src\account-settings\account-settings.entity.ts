import {
  <PERSON><PERSON>ty,
  PrimaryColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from "typeorm";
import { ApiProperty } from "@nestjs/swagger";
import { Uuid7 } from "../utils/uuid7";
import { User } from "../users/user.entity";

@Entity("account_settings")
export class AccountSettings {
  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the account settings (primary key)",
  })
  @PrimaryColumn("uuid")
  id: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the user who owns these account settings",
  })
  @Column({ type: "uuid", nullable: false })
  userId: string;

  @ApiProperty({
    example: "en",
    description: "Preferred language for the user interface",
  })
  @Column({ type: "varchar", length: 10, default: "en" })
  preferredLanguage: string;

  @ApiProperty({
    example: "light",
    description: "Preferred theme (light/dark)",
  })
  @Column({ type: "varchar", length: 10, default: "light" })
  preferredTheme: string;

  @ApiProperty({
    example: "cash",
    description: "Preferred payment method for sales",
  })
  @Column({ type: "varchar", length: 50, default: "cash" })
  preferredPaymentMethod: string;

  @ApiProperty({
    example: "standard",
    description: "Invoice format preference",
  })
  @Column({ type: "varchar", length: 50, default: "standard" })
  invoiceFormat: string;

  @ApiProperty({ example: 20.0, description: "Preferred tax rate percentage" })
  @Column({ type: "decimal", precision: 5, scale: 2, default: 20.0 })
  preferredTaxRate: number;

  @ApiProperty({
    example: true,
    description: "Flag to indicate if tax should be used by default",
  })
  @Column({ type: "boolean", default: true })
  preferredUseTax: boolean;

  @ApiProperty({ example: "basic", description: "User account plan type" })
  @Column({ type: "varchar", length: 50, default: "basic" })
  userAccountPlan: string;

  @ApiProperty({ example: false, description: "Soft delete flag" })
  @Column({ type: "boolean", default: false })
  isDeleted: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Relations
  @ManyToOne(() => User, { nullable: false })
  @JoinColumn({ name: "userId" })
  user: User;

  // Helper method to generate UUID
  static generateId(): string {
    return new Uuid7().toString();
  }
} 