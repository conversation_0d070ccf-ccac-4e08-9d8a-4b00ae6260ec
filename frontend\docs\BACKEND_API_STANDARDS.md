# Backend API Standards

This document outlines the standards and conventions for backend API endpoints used in the Dido Distribution system.

## Authentication

### JWT Token Storage & Usage
- **Storage**: JWT tokens are stored in `localStorage` with key `'dido_token'`
- **Token Format**: `Authorization: Bearer <jwt_token>`
- **Utility Functions**: Use centralized auth headers from `frontend/utils/authHeaders.ts`

### Token Management Standards
- **Access Tokens**: 1-hour expiration with automatic refresh
- **Refresh Tokens**: 7-day expiration with rotation on each use
- **Automatic Refresh**: Background token refresh every 30 minutes
- **Token Revocation**: Proper logout with backend token revocation
- **Secure Storage**: Tokens stored in localStorage with proper cleanup

### Auth Headers Utility
```typescript
// Standard headers for fetch requests
getAuthHeadersWithContentType(): HeadersInit
// Returns: { 'Content-Type': 'application/json', 'Authorization': 'Bearer <token>' }

// Simple headers for axios requests  
getAxiosAuthHeaders(): { Authorization?: string }
// Returns: { Authorization: 'Bearer <token>' } or {}

// Basic headers
getAuthHeaders(): { Authorization: string } | {}
// Returns: { Authorization: 'Bearer <token>' } or {}
```

### Authentication Requirements
- **All Endpoints**: Require valid JWT token in Authorization header
- **Token Validation**: Backend validates token on every request
- **Error Response**: 401 Unauthorized for missing/invalid tokens
- **Session Management**: Frontend checks token validity every 30 minutes

### Enhanced Authentication Endpoints
- `POST /api/auth/login` - Enhanced login with security features
- `POST /api/auth/refresh` - Token refresh with rotation
- `POST /api/auth/logout` - Secure logout with token revocation
- `GET /api/auth/security-status` - Security status monitoring
- `POST /api/auth/login/legacy` - Fallback login endpoint
- `GET /api/auth/validate` - Session validation
- `POST /api/auth/google/token` - Mobile Google authentication

### Security Features
- **Rate Limiting**: Account lockout after failed attempts
- **Suspicious Activity**: Detection and user notification
- **Security Monitoring**: Optional security status display
- **Session Validation**: Regular checks with token refresh

## UUID Usage

### UUID Format
- **Standard**: All UUIDs must use UUIDv7 format
- **Format**: `018f1234-5678-9abc-def0-123456789abc`
- **Validation**: Backend validates UUID format before processing
- **Error Response**: Invalid UUID format returns 400 Bad Request

### UUID Fields
- **Primary Keys**: All entities use UUID as primary key
- **Foreign Keys**: References between entities use UUID
- **Naming Convention**: 
  - Database fields: `uuid` (primary key), `warehouse_uuid`, `customer_uuid`, etc.
  - API responses: `uuid`, `warehouseUuidString`, `customerUuidString`, etc.
  - API requests: `warehouseUuid`, `customerUuid`, etc.

## Filter Parameters

### Empty Filter Behavior
**CRITICAL**: Empty filter parameters should return all values, not no results.

#### Correct Implementation
```typescript
// ✅ CORRECT: Empty string means "get all"
if (name && name.trim() !== '') {
  query = query.where('name', 'ILIKE', `%${name.trim()}%`);
}
// If name is empty, undefined, or null, return all records
```

#### Incorrect Implementation
```typescript
// ❌ WRONG: Empty string returns no results
if (name) {
  query = query.where('name', 'ILIKE', `%${name}%`);
}
// This would return no results for empty string
```

### Filter Parameter Standards
- **Empty String (`''`)**: Return all values (no filter applied)
- **Whitespace Only**: Trim and treat as empty string
- **Valid String**: Apply case-insensitive partial match
- **Null/Undefined**: Return all values (no filter applied)

## Pagination Standards

### Pagination Parameters
- **page**: 1-based page number (default: 1)
- **limit**: Items per page (max: 100, default: 20)
- **total**: Total number of items
- **hasNext**: Boolean indicating if next page exists
- **hasPrev**: Boolean indicating if previous page exists

### Pagination Response Format
```typescript
{
  data: T[],
  total: number,
  page: number,
  limit: number,
  hasNext: boolean,
  hasPrev: boolean
}
```

### Pagination State Preservation
- **Loading States**: Preserve pagination metadata during data fetching
- **State Management**: Store previous pagination data to maintain UI consistency
- **Fallback Values**: Use cached pagination data when current data is unavailable
- **User Experience**: Prevent pagination controls from disappearing during page transitions

## Error Handling

### Standard Error Response
```typescript
{
  message: string,
  error?: string,
  statusCode: number
}
```

### Common Error Codes
- **400**: Bad Request (invalid parameters, UUID format)
- **401**: Unauthorized (missing/invalid authentication)
- **403**: Forbidden (insufficient permissions)
- **404**: Not Found (resource doesn't exist)
- **500**: Internal Server Error

### Security Error Handling
- **Rate Limiting**: Clear error messages for account lockouts
- **Network Errors**: Graceful fallback to legacy endpoints
- **Token Expiration**: Automatic refresh with user transparency
- **Security Alerts**: User-friendly security status notifications

## API Integration Standards

### Frontend API Calls
- **API Prefix**: Use `/api/` prefix for all backend calls
- **No Direct URLs**: Never use backend URLs directly (e.g., `localhost:8000`)
- **Proxy Setup**: Next.js rewrites `/api/:path*` to backend server
- **Module Structure**: Each module should have dedicated `api.ts` file

### Example API Call
```typescript
import { getAuthHeadersWithContentType } from '@/utils/authHeaders';

const response = await fetch('/api/products/filter', {
  method: 'POST',
  headers: getAuthHeadersWithContentType(),
  body: JSON.stringify({
    warehouseUuid: "018f1234-5678-9abc-def0-123456789abc",
    name: "",
    page: 1,
    limit: 20
  })
});
```

## Security Standards

### UUID Security
- UUIDs are not sequential or predictable
- No information leakage through UUIDs
- Validate UUID ownership before operations

### Input Sanitization
- Sanitize all user inputs
- Prevent SQL injection
- Validate file uploads
- Escape output data

### Session Security
- Enhanced validation with regular session checks
- Security monitoring with optional status display
- Account protection with rate limiting and lockout handling
- Suspicious activity detection and user notification

## Performance Standards

### Response Times
- **Simple Queries**: < 100ms
- **Complex Queries**: < 500ms
- **Bulk Operations**: < 2000ms

### Database Queries
- Use indexes on UUID fields
- Optimize filter queries
- Implement proper pagination
- Avoid N+1 query problems

## React Query Patterns for Filter Requests

### Problem Statement
When users rapidly change filters, multiple API requests can be in flight simultaneously, leading to:
- Outdated data being displayed
- Race conditions where older requests complete after newer ones
- Confusing user experience with inconsistent results

### Solution: React Query with Proper Query Keys

**CRITICAL**: Use React Query's `useQuery` hook with memoized query keys to ensure only the latest filter request is processed.