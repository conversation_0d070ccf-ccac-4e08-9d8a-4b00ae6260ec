import { Entity, PrimaryColumn, Column, CreateDateColumn, Index } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Uuid7 } from '../utils/uuid7';

@Entity('logs')
export class Log {
  @ApiProperty({
    example: "01890b6e-7b8c-7e6b-8e2e-7b8c7e6b8e2e",
    description: "The UUID of the log entry (primary key)",
  })
  @PrimaryColumn('uuid')
  id: string;

  @ApiProperty({
    example: "01890b6e-7b8c-7e6b-8e2e-7b8c7e6b8e2e",
    description: "The UUID of the user who performed the action",
  })
  @Column('uuid')
  @Index()
  userUuid: string;

  @ApiProperty({
    example: "deleted",
    description: "The operation performed (e.g., 'created', 'updated', 'deleted', 'cancelled')",
  })
  @Column()
  @Index()
  operation: string;

  @ApiProperty({
    example: "sale123",
    description: "The entity that was affected (e.g., 'sale123', 'product456', 'customer789')",
  })
  @Column()
  @Index()
  entity: string;

  @ApiProperty({
    example: "User John Doe deleted sale order #12345",
    description: "Human-readable description of the action performed",
  })
  @Column({ type: 'text', nullable: true })
  description?: string;

  @ApiProperty({
    example: { "orderId": "12345", "amount": 150.00, "customerName": "John Doe" },
    description: "Additional JSON data related to the action",
  })
  @Column({ type: 'json', nullable: true })
  data?: Record<string, any>;

  @ApiProperty({
    example: "2025-01-15T10:30:00.000Z",
    description: "Timestamp when the log entry was created",
  })
  @CreateDateColumn()
  createdAt: Date;

  static generateId(): string {
    return Uuid7.generate().toString();
  }
} 