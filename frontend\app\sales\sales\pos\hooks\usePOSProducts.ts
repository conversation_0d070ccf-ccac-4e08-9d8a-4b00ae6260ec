import { useState, useCallback, useEffect, useRef } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { getProductsWithCustomerPricing } from '../posApi';
import { getWarehouseMainStorage } from '@/app/settings/warehousesApi';
import { getStockLevelsForStorage } from '@/app/inventory/stock-levels/api';
import type { Product, PaginationInfo } from '../types';

export interface UsePOSProductsReturn {
  // Product data
  products: Product[];
  pagination: PaginationInfo;
  
  // Loading states
  isLoading: boolean;
  error: string | null;
  
  // Product operations
  loadProducts: (searchTerm?: string, page?: number, customerType?: string, category?: string) => Promise<void>;
  refreshProducts: () => Promise<void>;
  
  // Pagination
  loadPage: (page: number, searchTerm?: string) => Promise<void>;
  loadNextPage: (searchTerm?: string) => Promise<void>;
  loadPrevPage: (searchTerm?: string) => Promise<void>;
  
  // Customer type management
  currentCustomerType: string;
  updateCustomerType: (customerType: string) => void;
  
  // Category management
  currentCategory: string;
  updateCategory: (category: string) => void;
}

const DEFAULT_CUSTOMER_TYPE = 'retail';
const DEFAULT_PAGE_SIZE = 13;

export function usePOSProducts(): UsePOSProductsReturn {
  const { user } = useAuth();
  const warehouseUuid = user?.warehouseUuid;
  
  // State
  const [products, setProducts] = useState<Product[]>([]);
  const [pagination, setPagination] = useState<PaginationInfo>({
    page: 1,
    limit: DEFAULT_PAGE_SIZE,
    total: 0,
    hasNext: false,
    hasPrev: false
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentCustomerType, setCurrentCustomerType] = useState(DEFAULT_CUSTOMER_TYPE);
  const [currentCategory, setCurrentCategory] = useState<string>('');
  const [mainStorageUuid, setMainStorageUuid] = useState<string | null>(null);
  
  // Cache for search results
  const searchCacheRef = useRef<Map<string, { products: Product[]; pagination: PaginationInfo }>>(new Map());
  const lastSearchRef = useRef<{ term: string; page: number; customerType: string; category: string } | null>(null);
  
  // Request cancellation
  const abortControllerRef = useRef<AbortController | null>(null);
  const currentRequestIdRef = useRef<number>(0);

  // Initialize main storage
  useEffect(() => {
    const initializeMainStorage = async () => {
      if (!warehouseUuid || mainStorageUuid) return;
      try {
        const response = await getWarehouseMainStorage(warehouseUuid);
        setMainStorageUuid(response.mainStorageUuid);
      } catch (err) {
        console.error('Failed to load main storage:', err);
        setError('Failed to load warehouse storage information');
      }
    };

    initializeMainStorage();
  }, [warehouseUuid, mainStorageUuid]);

  // Add stock levels to products
  const addStockLevelsToProducts = useCallback(async (products: Product[]): Promise<Product[]> => {
    if (!mainStorageUuid || !products.length) return products;

    try {
      const stockLevels = await getStockLevelsForStorage(mainStorageUuid);
      
      // Create a map of product UUID to stock level
      const stockMap = new Map(
        stockLevels.map(stock => [stock.productUuid, stock.quantity])
      );

      // Add stock levels to products
      return products.map(product => ({
        ...product,
        currentStock: stockMap.get(product.uuid) || 0,
        stockStorageUuid: mainStorageUuid,
      }));
    } catch (err) {
      console.error('Failed to add stock levels:', err);
      // Return products without stock levels if stock loading fails
      return products.map(product => ({
        ...product,
        currentStock: 0,
        stockStorageUuid: mainStorageUuid,
      }));
    }
  }, [mainStorageUuid]);

  // Generate cache key
  const getCacheKey = (searchTerm: string = '', page: number = 1, customerType: string = DEFAULT_CUSTOMER_TYPE, category: string = '') => {
    return `${searchTerm}_${page}_${customerType}_${category}`;
  };

  // Load products from API or cache
  const loadProducts = useCallback(async (
    searchTerm: string = '', 
    page: number = 1, 
    customerType: string = currentCustomerType,
    category: string = currentCategory
  ) => {
    if (!warehouseUuid) return;

    const cacheKey = getCacheKey(searchTerm, page, customerType, category);
    
    // Check cache first
    const cached = searchCacheRef.current.get(cacheKey);
    if (cached) {
      setProducts(cached.products);
      setPagination(cached.pagination);
      return;
    }

    // Cancel any ongoing request
    if (abortControllerRef.current) {
      console.log('[usePOSProducts] Cancelling previous request');
      abortControllerRef.current.abort();
    }

    // Create new AbortController for this request
    const abortController = new AbortController();
    abortControllerRef.current = abortController;
    
    // Generate unique request ID
    const requestId = ++currentRequestIdRef.current;
    
    setIsLoading(true);
    setError(null);

    try {
      console.log('[usePOSProducts] Loading products with search:', {
        requestId,
        searchTerm: searchTerm || '(empty)',
        page,
        customerType,
        category: category || '(all categories)'
      });

      const response = await getProductsWithCustomerPricing(
        warehouseUuid,
        customerType as any,
        searchTerm || undefined,
        page,
        DEFAULT_PAGE_SIZE,
        category || undefined,
        abortController.signal
      );

      // Check if this request is still the latest
      if (requestId !== currentRequestIdRef.current) {
        console.log('[usePOSProducts] Ignoring response from outdated request:', requestId);
        return;
      }

      // Add stock levels to products
      const productsWithStock = await addStockLevelsToProducts(response.data);
      
      // Check again after stock levels are added
      if (requestId !== currentRequestIdRef.current) {
        console.log('[usePOSProducts] Ignoring response from outdated request after stock levels:', requestId);
        return;
      }
      
      const newPagination: PaginationInfo = {
        page: response.page,
        limit: response.limit,
        total: response.total,
        hasNext: response.hasNext,
        hasPrev: response.hasPrev
      };

      // Cache the results
      searchCacheRef.current.set(cacheKey, {
        products: productsWithStock,
        pagination: newPagination
      });

      // Update state
      setProducts(productsWithStock);
      setPagination(newPagination);
      
      // Track last search
      lastSearchRef.current = { term: searchTerm, page, customerType, category };

      console.log('[usePOSProducts] Successfully loaded products for request:', requestId);

    } catch (err) {
      // Check if this request is still the latest
      if (requestId !== currentRequestIdRef.current) {
        console.log('[usePOSProducts] Ignoring error from outdated request:', requestId);
        return;
      }
      
      // Check if it's a cancellation error
      if (err instanceof Error && err.message === 'Request cancelled') {
        console.log('[usePOSProducts] Request was cancelled:', requestId);
        return;
      }
      
      console.error('Failed to load products:', err);
      setError('Failed to load products');
    } finally {
      // Only update loading state if this is still the current request
      if (requestId === currentRequestIdRef.current) {
        setIsLoading(false);
      }
    }
  }, [warehouseUuid, currentCustomerType, currentCategory, addStockLevelsToProducts]);

  // Refresh current products (clear cache and reload)
  const refreshProducts = useCallback(async () => {
    if (!lastSearchRef.current) return;
    
    const { term, page, customerType, category } = lastSearchRef.current;
    const cacheKey = getCacheKey(term, page, customerType, category);
    
    // Clear cache for current search
    searchCacheRef.current.delete(cacheKey);
    
    await loadProducts(term, page, customerType, category);
  }, [loadProducts]);

  // Load specific page
  const loadPage = useCallback(async (page: number, searchTerm: string = '') => {
    await loadProducts(searchTerm, page, currentCustomerType, currentCategory);
  }, [loadProducts, currentCustomerType, currentCategory]);

  // Load next page
  const loadNextPage = useCallback(async (searchTerm: string = '') => {
    if (pagination.hasNext) {
      await loadProducts(searchTerm, pagination.page + 1, currentCustomerType, currentCategory);
    }
  }, [loadProducts, pagination.hasNext, pagination.page, currentCustomerType, currentCategory]);

  // Load previous page
  const loadPrevPage = useCallback(async (searchTerm: string = '') => {
    if (pagination.hasPrev) {
      await loadProducts(searchTerm, pagination.page - 1, currentCustomerType, currentCategory);
    }
  }, [loadProducts, pagination.hasPrev, pagination.page, currentCustomerType, currentCategory]);

  // Update customer type and refresh products
  const updateCustomerType = useCallback((customerType: string) => {
    console.log('[usePOSProducts] Updating customer type:', { 
      from: currentCustomerType, 
      to: customerType,
      hasLastSearch: !!lastSearchRef.current 
    });
    
    setCurrentCustomerType(customerType);
    
    // If we have a previous search, reload with new customer type
    if (lastSearchRef.current) {
      const { term, page, category } = lastSearchRef.current;
      console.log('[usePOSProducts] Reloading products with new customer type:', { term, page, customerType, category });
      loadProducts(term, page, customerType, category);
    } else {
      console.log('[usePOSProducts] No previous search, loading initial products with customer type:', customerType);
      loadProducts('', 1, customerType, currentCategory);
    }
  }, [loadProducts, currentCustomerType, currentCategory]);

  // Update category and refresh products
  const updateCategory = useCallback((category: string) => {
    console.log('[usePOSProducts] Updating category:', { 
      from: currentCategory, 
      to: category,
      hasLastSearch: !!lastSearchRef.current 
    });
    
    setCurrentCategory(category);
    
    // If we have a previous search, reload with new category
    if (lastSearchRef.current) {
      const { term, page, customerType } = lastSearchRef.current;
      console.log('[usePOSProducts] Reloading products with new category:', { term, page, customerType, category });
      loadProducts(term, page, customerType, category);
    } else {
      console.log('[usePOSProducts] No previous search, loading initial products with category:', category);
      loadProducts('', 1, currentCustomerType, category);
    }
  }, [loadProducts, currentCategory, currentCustomerType]);

  // Initial load when warehouse is available
  useEffect(() => {
    if (warehouseUuid && mainStorageUuid) {
      loadProducts('', 1, currentCustomerType, currentCategory);
    }
  }, [warehouseUuid, mainStorageUuid, loadProducts, currentCustomerType, currentCategory]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  return {
    products,
    pagination,
    isLoading,
    error,
    loadProducts,
    refreshProducts,
    loadPage,
    loadNextPage,
    loadPrevPage,
    currentCustomerType,
    updateCustomerType,
    currentCategory,
    updateCategory
  };
} 