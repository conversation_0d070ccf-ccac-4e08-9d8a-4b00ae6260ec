"use client";

import React, { useEffect, useRef, useState } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, use<PERSON>ap, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "react-leaflet";
import { Icon, divIcon } from "leaflet";
import { Customer } from "../../../sales/customers/customersApi";
import { Route, CustomerLocation, OptimizedRouteLocation } from "../api";
import { MapPin, Route as RouteIcon, Users, Building2, Store, ShoppingBag, User, Trash2, Edit3, Save, X } from "lucide-react";
import "leaflet/dist/leaflet.css";
import "./map.css";

// Fix for default markers in react-leaflet
const defaultIcon = new Icon({
  iconUrl: "https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png",
  iconRetinaUrl: "https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png",
  shadowUrl: "https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png",
  iconSize: [25, 41],
  iconAnchor: [12, 41],
  popupAnchor: [1, -34],
  shadowSize: [41, 41]
});

// Custom marker icons
const getCustomerIcon = (customerType: string, isSelected: boolean = false) => {
  const customerConfig = {
    retail: { color: '#3B82F6', icon: '👤' },
    wholesale: { color: '#10B981', icon: '🏪' },
    'mid-wholesale': { color: '#F59E0B', icon: '🛒' },
    institutional: { color: '#8B5CF6', icon: '🏢' }
  };
  
  const config = customerConfig[customerType as keyof typeof customerConfig] || { color: '#6B7280', icon: '👤' };
  const borderColor = isSelected ? '#EF4444' : 'white';
  const borderWidth = isSelected ? 4 : 3;
  
  return divIcon({
    html: `
      <div style="
        background-color: ${config.color};
        width: 32px;
        height: 32px;
        border-radius: 50%;
        border: ${borderWidth}px solid ${borderColor};
        box-shadow: 0 3px 6px rgba(0,0,0,0.2);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 16px;
        position: relative;
      ">
        <span style="
          filter: drop-shadow(0 1px 1px rgba(0,0,0,0.3));
        ">${config.icon}</span>
      </div>
    `,
    className: 'custom-marker',
    iconSize: [32, 32],
    iconAnchor: [16, 16],
    popupAnchor: [0, -16]
  });
};

const getRouteStopIcon = (order: number) => {
  return divIcon({
    html: `
      <div style="
        background-color: #EF4444;
        width: 36px;
        height: 36px;
        border-radius: 50%;
        border: 3px solid white;
        box-shadow: 0 3px 6px rgba(0,0,0,0.2);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        font-weight: bold;
        color: white;
        position: relative;
      ">
        ${order}
      </div>
    `,
    className: 'route-stop-marker',
    iconSize: [36, 36],
    iconAnchor: [18, 18],
    popupAnchor: [0, -18]
  });
};



// Component to fit map bounds
const FitBounds: React.FC<{ 
  customers: Customer[]; 
  route?: Route;
  isExpanded: boolean;
}> = ({ customers, route, isExpanded }) => {
  const map = useMap();
  
  useEffect(() => {
    setTimeout(() => {
      map.invalidateSize();
    }, 100);
  }, [isExpanded, map]);
  
  useEffect(() => {
    const locations: [number, number][] = [];
    
    // Add customer locations
    customers.forEach(customer => {
      if (customer.latitude && customer.longitude) {
        locations.push([customer.latitude, customer.longitude]);
      }
    });
    
    // Add route locations if available
    if (route?.optimizedRoute) {
      route.optimizedRoute.forEach(stop => {
        locations.push([stop.latitude, stop.longitude]);
      });
    }
    
    if (locations.length > 0) {
      if (locations.length === 1) {
        map.setView(locations[0], 10);
      } else {
        map.fitBounds(locations, { padding: [20, 20] });
      }
    }
  }, [customers, route, map]);
  
  return null;
};

// Zoom-based tooltip component
const ZoomBasedTooltip: React.FC<{ customer: Customer }> = ({ customer }) => {
  const map = useMap();
  const [zoom, setZoom] = useState(map.getZoom());

  useEffect(() => {
    const handleZoom = () => {
      setZoom(map.getZoom());
    };

    map.on('zoomend', handleZoom);
    return () => {
      map.off('zoomend', handleZoom);
    };
  }, [map]);

  // Only show tooltip when zoomed in enough (zoom level 12 or higher)
  if (zoom < 12) return null;

  return (
    <Tooltip 
      permanent={true}
      direction="top"
      offset={[0, -15]}
      className="customer-name-tooltip"
    >
      {customer.name}
    </Tooltip>
  );
};

// Customer type badge component
const CustomerTypeBadge: React.FC<{ type: string }> = ({ type }) => {
  const colors = {
    retail: 'bg-blue-100 text-blue-800',
    wholesale: 'bg-green-100 text-green-800',
    'mid-wholesale': 'bg-yellow-100 text-yellow-800',
    institutional: 'bg-purple-100 text-purple-800'
  };
  
  const colorClass = colors[type as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  
  return (
    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${colorClass}`}>
      {type.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
    </span>
  );
};

interface RouteMapProps {
  customers: Customer[];
  selectedCustomers: Customer[];
  route?: Route;
  isExpanded?: boolean;
  onCustomerSelect: (customer: Customer) => void;
  onCustomerDeselect: (customer: Customer) => void;
  onRouteEdit?: (route: Route) => void;
  onRouteSave?: (route: Route) => void;
  onRouteCancel?: () => void;
  isEditing?: boolean;
}

const RouteMap: React.FC<RouteMapProps> = ({ 
  customers, 
  selectedCustomers, 
  route, 
  isExpanded = false,
  onCustomerSelect,
  onCustomerDeselect,
  onRouteEdit,
  onRouteSave,
  onRouteCancel,
  isEditing = false
}) => {
  const mapRef = useRef<any>(null);

  useEffect(() => {
    if (mapRef.current) {
      setTimeout(() => {
        mapRef.current.invalidateSize();
      }, 150);
    }
  }, [isExpanded]);

  // Create route path coordinates
  const routePath = route?.optimizedRoute?.map(stop => [stop.latitude, stop.longitude] as [number, number]) || [];

  return (
    <div className="w-full h-full relative">
      <MapContainer
        ref={mapRef}
        center={[0, 0]}
        zoom={2}
        style={{ height: "100%", width: "100%" }}
        className="rounded-lg"
      >
        <TileLayer
          url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
          attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        />
        
        <FitBounds customers={customers} route={route} isExpanded={isExpanded} />
        
        {/* Route path line */}
        {routePath.length > 1 && (
          <Polyline
            positions={routePath}
            color="#EF4444"
            weight={4}
            opacity={0.8}
            dashArray="10, 5"
          />
        )}
        
        {/* Customer markers */}
        {customers.map((customer) => {
          if (!customer.latitude || !customer.longitude) return null;
          
          const isSelected = selectedCustomers.some(c => c.uuid === customer.uuid);
          
          return (
            <Marker
              key={`customer-${customer.uuid}`}
              position={[customer.latitude, customer.longitude]}
              icon={getCustomerIcon(customer.customerType, isSelected)}
              eventHandlers={{
                click: () => {
                  if (isSelected) {
                    onCustomerDeselect(customer);
                  } else {
                    onCustomerSelect(customer);
                  }
                }
              }}
            >
              <ZoomBasedTooltip customer={customer} />
              <Popup className="customer-popup" maxWidth={320}>
                <div className="p-3 min-w-[280px]">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex-1">
                      <h3 className="font-semibold text-lg text-gray-900 mb-1">
                        {customer.name}
                      </h3>
                      <CustomerTypeBadge type={customer.customerType} />
                    </div>
                    <div className="ml-3">
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                        customer.customerType === 'retail' ? 'bg-blue-100' :
                        customer.customerType === 'wholesale' ? 'bg-green-100' :
                        customer.customerType === 'mid-wholesale' ? 'bg-yellow-100' :
                        'bg-purple-100'
                      }`}>
                        {customer.customerType === 'retail' && <User className="w-4 h-4 text-blue-600" />}
                        {customer.customerType === 'wholesale' && <Store className="w-4 h-4 text-green-600" />}
                        {customer.customerType === 'mid-wholesale' && <ShoppingBag className="w-4 h-4 text-yellow-600" />}
                        {customer.customerType === 'institutional' && <Building2 className="w-4 h-4 text-purple-600" />}
                      </div>
                    </div>
                  </div>
                  
                  <div className="space-y-2 text-sm">
                    {customer.fiscalId && (
                      <div className="flex items-center space-x-2">
                        <span className="text-gray-600">Fiscal ID:</span>
                        <span className="font-medium">{customer.fiscalId}</span>
                      </div>
                    )}
                    
                    {customer.email && (
                      <div className="flex items-center space-x-2">
                        <span className="text-gray-600">Email:</span>
                        <span className="font-medium">{customer.email}</span>
                      </div>
                    )}
                    
                    {customer.phone && (
                      <div className="flex items-center space-x-2">
                        <span className="text-gray-600">Phone:</span>
                        <span className="font-medium">{customer.phone}</span>
                      </div>
                    )}
                    
                    {customer.address && (
                      <div className="flex items-start space-x-2">
                        <span className="text-gray-600">Address:</span>
                        <span className="font-medium flex-1">{customer.address}</span>
                      </div>
                    )}
                  </div>
                  
                  <div className="mt-3 pt-3 border-t border-gray-200">
                    <div className="flex items-center justify-between text-xs text-gray-500">
                      <span>Coordinates:</span>
                      <span>{customer.latitude?.toFixed(6)}, {customer.longitude?.toFixed(6)}</span>
                    </div>
                  </div>
                  
                  <div className="mt-3 pt-3 border-t border-gray-200">
                    <button
                      onClick={() => {
                        if (isSelected) {
                          onCustomerDeselect(customer);
                        } else {
                          onCustomerSelect(customer);
                        }
                      }}
                      className={`w-full px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                        isSelected
                          ? 'bg-red-100 text-red-700 hover:bg-red-200'
                          : 'bg-blue-100 text-blue-700 hover:bg-blue-200'
                      }`}
                    >
                      {isSelected ? 'Remove from Route' : 'Add to Route'}
                    </button>
                  </div>
                </div>
              </Popup>
            </Marker>
          );
        })}
        
        {/* Route stop markers */}
        {route?.optimizedRoute?.map((stop) => (
          <Marker
            key={`route-stop-${stop.order}`}
            position={[stop.latitude, stop.longitude]}
            icon={getRouteStopIcon(stop.order)}
          >
            <Popup className="route-stop-popup" maxWidth={280}>
              <div className="p-3 min-w-[240px]">
                <div className="flex items-center justify-between mb-2">
                  <h3 className="font-semibold text-lg text-gray-900">
                    Stop #{stop.order}
                  </h3>
                  <div className="w-8 h-8 rounded-full bg-red-100 flex items-center justify-center">
                    <RouteIcon className="w-4 h-4 text-red-600" />
                  </div>
                </div>
                
                {stop.customerName && (
                  <div className="mb-2">
                    <span className="text-gray-600">Customer:</span>
                    <span className="font-medium ml-2">{stop.customerName}</span>
                  </div>
                )}
                
                <div className="text-xs text-gray-500">
                  <div>Coordinates: {stop.latitude.toFixed(6)}, {stop.longitude.toFixed(6)}</div>
                </div>
              </div>
            </Popup>
          </Marker>
        ))}
      </MapContainer>
      
      {/* Legend */}
      <div className="absolute top-4 right-4 bg-white rounded-lg shadow-lg border border-gray-200 p-3 z-[1000]">
        <h4 className="font-medium text-sm text-gray-900 mb-2">Legend</h4>
        <div className="space-y-2">
          <div className="flex items-center space-x-2">
            <div className="w-5 h-5 rounded-full bg-red-500 border-2 border-white flex items-center justify-center text-xs text-white font-bold">
              1
            </div>
            <span className="text-xs text-gray-700">Route Stop</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-5 h-5 rounded-full bg-blue-500 border-2 border-white"></div>
            <span className="text-xs text-gray-700">Customer (Available)</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-5 h-5 rounded-full bg-blue-500 border-4 border-red-500"></div>
            <span className="text-xs text-gray-700">Customer (Selected)</span>
          </div>
        </div>
      </div>
      
      {/* Route Info */}
      {route && (
        <div className="absolute bottom-4 left-4 bg-white rounded-lg shadow-lg border border-gray-200 p-3 z-[1000] max-w-sm">
          <div className="flex items-center justify-between mb-2">
            <h4 className="font-medium text-sm text-gray-900">Route: {route.name}</h4>
            {onRouteEdit && !isEditing && (
              <button
                onClick={() => onRouteEdit(route)}
                className="p-1 rounded hover:bg-gray-100"
                title="Edit Route"
              >
                <Edit3 className="w-4 h-4 text-gray-600" />
              </button>
            )}
          </div>
          
          <div className="space-y-1 text-xs text-gray-600">
            <div>Total Distance: {route.totalDistance.toFixed(2)} km</div>
            <div>Stops: {route.optimizedRoute.length}</div>
            {route.description && (
              <div className="text-gray-500">{route.description}</div>
            )}
          </div>
          
          {isEditing && onRouteSave && onRouteCancel && (
            <div className="flex space-x-2 mt-3">
              <button
                onClick={() => onRouteSave(route)}
                className="px-3 py-1 bg-green-100 text-green-700 rounded text-xs hover:bg-green-200"
              >
                <Save className="w-3 h-3 inline mr-1" />
                Save
              </button>
              <button
                onClick={onRouteCancel}
                className="px-3 py-1 bg-gray-100 text-gray-700 rounded text-xs hover:bg-gray-200"
              >
                <X className="w-3 h-3 inline mr-1" />
                Cancel
              </button>
            </div>
          )}
        </div>
      )}
      
      {/* Customer Count */}
      <div className="absolute top-4 left-4 bg-white rounded-lg shadow-lg border border-gray-200 p-3 z-[1000]">
        <div className="flex items-center space-x-2">
          <Users className="w-4 h-4 text-blue-600" />
          <span className="text-sm font-medium text-gray-900">
            {selectedCustomers.length} of {customers.length} customers selected
          </span>
        </div>
      </div>
    </div>
  );
};

export default RouteMap; 