import { Entity, PrimaryColumn, Column, CreateDateColumn, UpdateDateColumn, Index } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Uuid7 } from '../utils/uuid7';

@Entity('companies')
export class Company {
  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the company (primary key)",
  })
  @PrimaryColumn('uuid')
  id: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the user who owns this company",
  })
  @Column('uuid')
  @Index()
  userUuid: string;

  @ApiProperty({ example: "Acme Corporation", description: "Company name" })
  @Column()
  name: string;

  @ApiProperty({
    example: "*********",
    description: "Tax identification number (NIF)",
  })
  @Column()
  nif: string;

  @ApiProperty({ example: "RC123456", description: "Register number (RC)" })
  @Column()
  rc: string;

  @ApiProperty({ example: "ART001", description: "Article number" })
  @Column()
  articleNumber: string;

  @ApiProperty({
    example: "123 Main Street, City, Country",
    description: "Company address",
  })
  @Column()
  address: string;

  @ApiProperty({
    example: "https://www.acme.com",
    description: "Company website URL",
    required: false,
  })
  @Column({ nullable: true })
  website?: string;

  @ApiProperty({ example: "+*********0", description: "Company phone number" })
  @Column()
  phoneNumber: string;

  @ApiProperty({ example: false, description: "Soft delete flag" })
  @Column({ default: false })
  isDeleted: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Helper method to generate UUID
  static generateId(): string {
    return new Uuid7().toString();
  }

  // Helper method to generate UUID
  static fromBinary(binary: any): string {
    if (!binary) return null;
    try {
      return new Uuid7(binary).toString();
    } catch {
      return null;
    }
  }
} 