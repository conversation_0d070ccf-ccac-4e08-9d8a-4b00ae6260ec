"use client";
// Convention: warehouseUuid and userUuid are injected from AuthContext, never from user input.
// See docs/CODE_GENERATION.md for details.
// All API calls use the abstraction in ./api.ts.
// All errors must be shown as both toast and inline summary.
// Modal is accessible and keyboard-friendly.

import React, { useEffect, useState, useRef } from "react";
import { toast } from "react-hot-toast";
import ItemsTable from "@/components/itemsTable/ItemsTable";
import TableActionButtons from "@/components/itemsTable/TableActionButtons";
import ProtectedRoute from "@/components/ProtectedRoute";
import RegionForm, { RegionFormValues } from "./RegionForm";
import { 
  listRegions, 
  getRegionByUuid, 
  createRegion, 
  updateRegion, 
  softDeleteRegion, 
  RegionDto, 
  CreateRegionDto, 
  UpdateRegionDto 
} from "./api";
import { useAuth } from "@/contexts/AuthContext";

type Region = RegionDto;

const emptyInitialValues: RegionFormValues = { 
  name: '', 
  description: '', 
  latitude: undefined, 
  longitude: undefined 
};

// Modal component with Escape-to-close, accessibility, and focus trap
const Modal: React.FC<{ onClose: () => void; children: React.ReactNode; labelId?: string }> = ({ onClose, children, labelId }) => {
  const modalRef = useRef<HTMLDivElement>(null);
  React.useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === "Escape") {
        onClose();
      }
      // Trap focus
      if (e.key === "Tab" && modalRef.current) {
        const focusableEls = modalRef.current.querySelectorAll<HTMLElement>(
          'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
        );
        const firstEl = focusableEls[0];
        const lastEl = focusableEls[focusableEls.length - 1];
        if (!e.shiftKey && document.activeElement === lastEl) {
          e.preventDefault();
          firstEl.focus();
        } else if (e.shiftKey && document.activeElement === firstEl) {
          e.preventDefault();
          lastEl.focus();
        }
      }
    };
    window.addEventListener("keydown", handleKeyDown);
    // Focus the first input/button
    setTimeout(() => {
      if (modalRef.current) {
        const el = modalRef.current.querySelector<HTMLElement>('input, button');
        el?.focus();
      }
    }, 0);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [onClose]);
  return (
    <div
      className="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-30 animate-fadein"
      role="dialog"
      aria-modal="true"
      aria-labelledby={labelId}
    >
      <div ref={modalRef}>{children}</div>
    </div>
  );
};

const RegionsPage: React.FC = () => {
  // State hooks
  const [regionsList, setRegionsList] = useState<Region[]>([]);
  const [loading, setLoading] = useState(false);
  const [editing, setEditing] = useState<Region | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [modalOpen, setModalOpen] = useState(false);
  const [viewing, setViewing] = useState<Region | null>(null);
  const [detailsLoading, setDetailsLoading] = useState(false);

  const { user } = useAuth();
  const userReady = !!(user && user.warehouseUuid && user.uuid);

  // Memoize initialValues for RegionForm to prevent unnecessary resets
  const regionInitialValues = React.useMemo(() => (editing ? { 
    name: editing.name,
    description: editing.description || '',
    latitude: editing.latitude,
    longitude: editing.longitude
  } : emptyInitialValues), [editing]);

  // --- Callbacks for performance ---

  // Unified modal close handler
  const handleCloseModal = React.useCallback(() => {
    setModalOpen(false);
    setEditing(null);
    setError(null);
  }, []);

  // Fetch regions list
  const fetchRegions = React.useCallback(async () => {
    if (!user?.warehouseUuid) return;
    setLoading(true);
    setError(null);
    try {
      const response = await listRegions({}, { warehouseUuid: user.warehouseUuid });
      setRegionsList(response.data);
    } catch (e: any) {
      const errorMessage = e.response?.data?.message || "Failed to fetch regions list";
      setError(errorMessage);
      toast.error(errorMessage);
    }
    setLoading(false);
  }, [user?.warehouseUuid]);

  // Add or update region (handler for RegionForm)
  const handleRegionSubmit = React.useCallback(async (data: RegionFormValues) => {
    if (!user?.warehouseUuid || !user?.uuid) {
      toast.error("User session invalid. Please refresh.");
      return;
    }
    setLoading(true);
    setError(null);
    try {
      if (editing) {
        const updateData: UpdateRegionDto = {
          name: data.name,
          description: data.description,
          latitude: data.latitude,
          longitude: data.longitude,
        };
        await updateRegion(editing.uuid, updateData);
      } else {
        const createData: CreateRegionDto = {
          name: data.name,
          description: data.description,
          latitude: data.latitude,
          longitude: data.longitude,
          warehouseUuid: user.warehouseUuid,
        };
        await createRegion(createData);
      }
      handleCloseModal();
      await fetchRegions();
      toast.success(`Region ${editing ? 'updated' : 'added'} successfully!`);
    } catch (e: any) {
      const msg = e.response?.data?.message || "Failed to save region";
      setError(msg);
      toast.error(msg);
      console.error("Region save error:", e);
    }
    setLoading(false);
  }, [editing, user, fetchRegions, handleCloseModal]);

  // Open modal for add
  const handleAdd = React.useCallback(() => {
    setEditing(null);
    setError(null);
    setModalOpen(true);
  }, []);

  // Start editing
  const handleEdit = React.useCallback((region: Region) => {
    setEditing(region);
    setError(null);
    setModalOpen(true);
  }, []);

  // View details
  const handleViewDetails = React.useCallback(async (uuid: string) => {
    setDetailsLoading(true);
    try {
      const details = await getRegionByUuid(uuid);
      setViewing(details);
    } catch (e: any) {
      const errorMessage = e.response?.data?.message || "Failed to load region details";
      setError(errorMessage);
      toast.error(errorMessage);
    }
    setDetailsLoading(false);
  }, []);

  // Soft delete region
  const handleDelete = React.useCallback(async (uuid: string) => {
    if (!confirm("Are you sure you want to delete this region?")) return;
    
    setLoading(true);
    try {
      await softDeleteRegion(uuid);
      await fetchRegions();
      toast.success("Region deleted successfully.");
    } catch (e: any) {
      const errorMessage = e.response?.data?.message || "Failed to delete region";
      setError(errorMessage);
      toast.error(errorMessage);
    }
    setLoading(false);
  }, [fetchRegions]);

  const handleCloseDetailsModal = React.useCallback(() => setViewing(null), []);

  // --- Effects ---

  // F2 keyboard shortcut to open Add modal
  useEffect(() => {
    const handleF2 = (e: KeyboardEvent) => {
      if (e.key === "F2" && !modalOpen && userReady) {
        e.preventDefault();
        handleAdd();
      }
    };
    window.addEventListener("keydown", handleF2);
    return () => window.removeEventListener("keydown", handleF2);
  }, [modalOpen, userReady, handleAdd]);

  // Initial fetch
  useEffect(() => {
    if (user?.warehouseUuid) {
      fetchRegions();
    }
  }, [user?.warehouseUuid, fetchRegions]);

  // --- Memoized table columns ---

  const columns = React.useMemo(() => [
    {
      key: "name",
      header: "Name",
      headerClassName: "py-3 px-4 text-left text-gray-700 font-semibold",
      cellClassName: "py-3 px-4 align-middle text-gray-900 font-medium",
    },
    {
      key: "description",
      header: "Description",
      headerClassName: "py-3 px-4 text-left text-gray-700 font-semibold",
      cellClassName: "py-3 px-4 align-middle text-gray-600",
      render: (_: any, region: Region) => region.description || "—",
    },
    {
      key: "coordinates",
      header: "Coordinates",
      headerClassName: "py-3 px-4 text-center text-gray-700 font-semibold",
      cellClassName: "py-3 px-4 align-middle text-gray-600 text-center",
      render: (_: any, region: Region) => {
        if (region.latitude !== undefined && region.longitude !== undefined) {
          return `${region.latitude.toFixed(4)}, ${region.longitude.toFixed(4)}`;
        }
        return "—";
      },
    },
    {
      key: "createdAt",
      header: "Created",
      headerClassName: "py-3 px-4 text-center text-gray-700 font-semibold",
      cellClassName: "py-3 px-4 align-middle text-gray-600 text-center",
      render: (_: any, region: Region) => new Date(region.createdAt).toLocaleDateString(),
    },
    {
      key: "actions",
      header: "Actions",
      headerClassName: "py-3 px-4 text-center text-gray-700 font-semibold",
      cellClassName: "py-3 px-4 align-middle text-center",
      render: (_: any, region: Region) => (
        <TableActionButtons
          onEdit={() => handleEdit(region)}
          onDelete={() => handleDelete(region.uuid)}
          onView={() => handleViewDetails(region.uuid)}
          viewLabel="View Details"
        />
      ),
    },
  ], [handleEdit, handleDelete, handleViewDetails]);

  return (
    <ProtectedRoute>
      <div className="overflow-x-auto py-2 max-w-7xl mx-auto w-full">
        <div className="mb-6">
          <h1 className="text-2xl font-bold mb-4">Geographical Regions</h1>
          
          {error && (
            <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
              <p className="text-red-700 text-sm">{error}</p>
            </div>
          )}

          <button
            onClick={handleAdd}
            className="mb-6 bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
            disabled={!userReady}
          >
            Add Region (F2)
          </button>
        </div>

        {loading ? (
          <div className="flex justify-center items-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span className="ml-2 text-gray-600">Loading regions...</span>
          </div>
        ) : (
          <ItemsTable
            columns={columns}
            data={regionsList}
            noDataText={<span className="text-gray-400">No regions found.</span>}
            containerClassName="bg-white rounded-xl shadow-lg border border-gray-200 w-full"
          />
        )}

        {/* Add/Edit Modal */}
        {modalOpen && (
          <Modal onClose={handleCloseModal} labelId="region-modal-title">
            <RegionForm
              initialValues={regionInitialValues}
              onSubmit={handleRegionSubmit}
              onCancel={handleCloseModal}
              isSubmitting={loading}
              error={error}
            />
          </Modal>
        )}

        {/* Details Modal */}
        {viewing && (
          <Modal onClose={handleCloseDetailsModal} labelId="region-details-title">
            <div className="bg-white rounded-lg shadow-xl p-6 w-full max-w-md mx-auto">
              <h2 id="region-details-title" className="text-xl font-bold mb-4 text-gray-800">
                Region Details
              </h2>
              {detailsLoading ? (
                <div className="flex justify-center items-center py-4">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                  <span className="ml-2 text-gray-600">Loading details...</span>
                </div>
              ) : (
                <div className="space-y-3">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Name</label>
                    <p className="text-gray-900">{viewing.name}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Description</label>
                    <p className="text-gray-900">{viewing.description || "No description"}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Coordinates</label>
                    <p className="text-gray-900">
                      {viewing.latitude !== undefined && viewing.longitude !== undefined
                        ? `${viewing.latitude}, ${viewing.longitude}`
                        : "No coordinates set"}
                    </p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Created</label>
                    <p className="text-gray-900">{new Date(viewing.createdAt).toLocaleString()}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Last Updated</label>
                    <p className="text-gray-900">{new Date(viewing.updatedAt).toLocaleString()}</p>
                  </div>
                </div>
              )}
              <button
                onClick={handleCloseDetailsModal}
                className="mt-6 w-full bg-gray-300 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500"
              >
                Close
              </button>
            </div>
          </Modal>
        )}
      </div>
    </ProtectedRoute>
  );
};

export default RegionsPage; 