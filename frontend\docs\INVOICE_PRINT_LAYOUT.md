# Invoice Print Layout Documentation

## Overview

The Invoice Print functionality provides a professional invoice printing system for the Dido Distribution sales module. It generates standardized Algerian-format invoices with complete company and customer information, itemized products, and proper tax calculations.

## Component Location

**File**: `frontend/app/sales/components/InvoicePrint.tsx`  
**Usage**: Modal component for printing sales invoices

## Key Features

### 🖨️ Professional Invoice Generation
- **Algerian Format**: Complies with Algerian invoice standards
- **Clean Layout**: Professional appearance suitable for business use
- **Auto-Print**: Automatically opens print dialog when invoice is generated
- **Responsive Design**: Adapts to different screen sizes and print formats

### 📋 Complete Invoice Information
- **Company Details**: Full seller information including NIF, RC, and article number
- **Customer Details**: Complete buyer information with fiscal ID and contact details
- **Invoice Metadata**: Invoice number, date, and payment method
- **Tax Information**: Configurable tax rates and calculations
- **Payment Status**: Shows payment amounts and outstanding balances

### 🛍️ Itemized Product Display
- **Product List**: Detailed table of all purchased items
- **Quantity & Pricing**: Unit prices and line totals
- **Tax Breakdown**: Individual tax calculations per item
- **Total Calculations**: Subtotal, tax amount, and final total

## Interface Definition

### Props Interface
```typescript
interface InvoicePrintProps {
  sale: Sale;           // Complete sale transaction data
  companyInfo: CompanyInfo;    // Seller company information
  customerInfo: CustomerInfo;  // Buyer customer information
  isOpen: boolean;      // Modal visibility state
  onClose: () => void;  // Close modal callback
}
```

### Company Information Structure
```typescript
interface CompanyInfo {
  uuid: string;
  name: string;
  nif: string;          // Tax identification number
  rc: string;           // Commercial register number
  articleNumber: string; // Article number
  address: string;
  phoneNumber: string;
  website?: string;     // Optional website URL
}
```

### Customer Information Structure
```typescript
interface CustomerInfo {
  uuid: string;
  name: string;
  fiscalId: string;     // Customer tax ID
  email?: string;
  phone?: string;
  address?: string;
  rc?: string;          // Commercial register (for businesses)
  articleNumber?: string; // Article number (for businesses)
  customerType: 'retail' | 'wholesale' | 'mid-wholesale' | 'institutional';
}
```

## Invoice Layout Structure

### 1. Header Section
**Purpose**: Invoice identification and metadata
**Contents**:
- Invoice title ("FACTURE")
- Invoice number with prefix
- Invoice date in French-Algerian format
- Payment method information

**Styling**:
- Centered alignment
- Large, bold title (24px)
- Prominent invoice number (18px)
- Subtle date and payment info (14px, gray)
- Bottom border for separation

### 2. Parties Section
**Purpose**: Seller and buyer information display
**Layout**: Two-column layout with equal spacing

#### Seller Information (Left Column)
- Company name (bold)
- NIF (Tax ID)
- RC (Commercial Register)
- Article number
- Physical address
- Phone number
- Website (if available)

#### Customer Information (Right Column)
- Customer name (bold)
- Fiscal ID (with fallback text)
- Commercial register (if applicable)
- Article number (if applicable)
- Address (if available)
- Phone number (if available)
- Email (if available)

### 3. Items Table
**Purpose**: Detailed product listing with pricing
**Columns**:
- **N°**: Sequential item number
- **Désignation**: Product name/description
- **Qté**: Quantity purchased
- **Prix Unit.**: Unit price in DZD
- **Montant HT**: Line total excluding tax
- **TVA**: Tax percentage (if tax is enabled)

**Features**:
- Alternating row colors for readability
- Bordered table structure
- Header with gray background
- Proper alignment for numbers
- Currency formatting in Algerian Dinar (DZD)

### 4. Totals Section
**Purpose**: Financial summary and calculations
**Layout**: Right-aligned summary box

**Totals Displayed**:
- **Total HT**: Subtotal excluding tax
- **TVA**: Tax amount with percentage
- **Total TTC**: Final total including tax
- **Montant Payé**: Amount already paid
- **Reste à Payer**: Outstanding balance (highlighted in red)

## Print Functionality

### Print Window Generation
The component creates a new browser window with clean HTML content optimized for printing:

```typescript
const handlePrint = () => {
  const printWindow = window.open('', '_blank');
  // Generates clean invoice HTML without modal structure
  printWindow.document.write(invoiceHTML);
  printWindow.document.close();
};
```

### Print Optimization Features
- **Clean HTML**: Removes modal structure and UI elements
- **Print Styles**: Dedicated CSS for print media
- **Auto-Print**: Automatically opens print dialog
- **Page Breaks**: Proper page break handling for long invoices
- **Font Optimization**: Uses Arial font for consistent printing

### Print CSS Styles
```css
@media print {
  body { margin: 0; padding: 20px; font-family: Arial, sans-serif; }
  .no-print { display: none !important; }
  .page-break { page-break-before: always; }
}
```

## Localization Features

### French-Algerian Format
- **Date Format**: Uses `'fr-DZ'` locale for date formatting
- **Currency Format**: Displays amounts in Algerian Dinar (DZD)
- **Labels**: French labels for all invoice sections
- **Number Format**: Proper decimal formatting with 2 decimal places

### Currency Formatting
```typescript
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('fr-DZ', {
    style: 'currency',
    currency: 'DZD',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(amount);
};
```

### Date Formatting
```typescript
const formatDate = (dateString: string | Date) => {
  return new Date(dateString).toLocaleDateString('fr-DZ', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
};
```

## Modal User Experience

### Modal Features
- **Preview Mode**: Shows invoice preview before printing
- **Print Button**: Prominent print button with printer icon
- **Close Button**: X button for closing modal
- **Escape Key**: Closes modal when Escape is pressed
- **Auto-Focus**: Focuses first interactive element

### Modal Layout
- **Centered Modal**: Responsive modal with maximum width
- **Preview Section**: Shows invoice summary information
- **Action Buttons**: Print and close buttons
- **Scrollable Content**: Handles long invoices with scroll

### User Feedback
- **Status Display**: Shows payment status with color coding
- **Item Count**: Displays number of products
- **Total Amount**: Prominent total display
- **Print Instructions**: Clear instructions for users

## Tax Handling

### Tax Configuration
- **Optional Tax**: Tax can be enabled/disabled per sale
- **Configurable Rate**: Tax rate is configurable per transaction
- **Tax Display**: Shows tax percentage and amount
- **Tax Calculation**: Automatic tax calculation on line items

### Tax Display Logic
```typescript
${sale.useTax ? `
  <div class="total-row">
    <span class="total-label">TVA (${(sale.taxRate * 100).toFixed(0)}%):</span>
    <span class="total-amount">${formatCurrency(sale.taxAmount)}</span>
  </div>
` : ''}
```

## Error Handling

### Data Validation
- **Required Data**: Validates presence of sale, company, and customer data
- **Null Checks**: Handles missing or undefined data gracefully
- **Fallback Values**: Provides default values for optional fields

### Error Prevention
```typescript
// Validate required data
if (!sale || !companyInfo || !customerInfo) {
  return null;
}
```

## Accessibility Features

### Keyboard Navigation
- **Escape Key**: Closes modal
- **Tab Navigation**: Proper tab order through modal elements
- **Focus Management**: Auto-focuses first element when modal opens

### Screen Reader Support
- **ARIA Labels**: Proper ARIA attributes for screen readers
- **Semantic HTML**: Uses semantic HTML elements
- **Alt Text**: Descriptive text for icons and buttons

## Integration Points

### Sales Module Integration
- **Sales API**: Integrates with sales transaction data
- **Customer API**: Fetches customer information
- **Company API**: Retrieves company details
- **Authentication**: Uses authenticated user context

### Data Flow
1. User selects invoice to print from sales list
2. Component receives sale data and related information
3. Modal opens with invoice preview
4. User clicks print button
5. New window opens with formatted invoice
6. Print dialog automatically appears

## Styling Guidelines

### Color Scheme
- **Primary**: Blue buttons (#2563eb)
- **Success**: Green for paid status
- **Warning**: Yellow for partially paid
- **Error**: Red for unpaid/outstanding amounts
- **Neutral**: Gray for secondary information

### Typography
- **Headers**: Bold, larger font sizes
- **Body**: Regular weight, readable sizes
- **Numbers**: Monospace for alignment
- **Labels**: Medium weight for emphasis

### Layout Principles
- **Consistent Spacing**: Uses standardized spacing units
- **Responsive Design**: Adapts to different screen sizes
- **Print Optimization**: Clean layout for printing
- **Professional Appearance**: Business-appropriate styling

## Performance Considerations

### Optimization Features
- **Lazy Loading**: Modal content only renders when open
- **Efficient Rendering**: Minimal re-renders with proper dependencies
- **Memory Management**: Cleans up event listeners on unmount
- **Fast Print**: Optimized HTML generation for quick printing

### Browser Compatibility
- **Modern Browsers**: Works with Chrome, Firefox, Safari, Edge
- **Print Support**: Compatible with standard browser print functionality
- **JavaScript Required**: Requires JavaScript for full functionality

## Future Enhancements

### Potential Improvements
1. **PDF Generation**: Direct PDF export without print dialog
2. **Email Integration**: Send invoice directly via email
3. **Template Customization**: Customizable invoice templates
4. **Multi-language Support**: Additional language options
5. **Barcode Integration**: QR codes or barcodes for digital verification
6. **Digital Signatures**: Electronic signature support
7. **Batch Printing**: Print multiple invoices at once
8. **Invoice History**: Track printed invoices

### Technical Improvements
- **Server-side Rendering**: Generate invoices on server
- **Caching**: Cache invoice data for faster loading
- **Compression**: Optimize invoice size for faster loading
- **Offline Support**: Generate invoices offline

## Usage Examples

### Basic Usage
```typescript
<InvoicePrint
  sale={saleData}
  companyInfo={companyData}
  customerInfo={customerData}
  isOpen={showPrintModal}
  onClose={() => setShowPrintModal(false)}
/>
```

### With Error Handling
```typescript
const handlePrintInvoice = async (saleId: string) => {
  try {
    const sale = await getSaleById(saleId);
    const company = await getCompanyInfo();
    const customer = await getCustomerById(sale.customerUuid);
    
    setInvoiceData({ sale, company, customer });
    setShowPrintModal(true);
  } catch (error) {
    toast.error('Failed to load invoice data');
  }
};
```

## Best Practices

### Implementation Guidelines
1. **Data Validation**: Always validate required data before rendering
2. **Error Handling**: Provide graceful fallbacks for missing data
3. **User Feedback**: Show loading states and error messages
4. **Accessibility**: Ensure keyboard navigation and screen reader support
5. **Performance**: Optimize for fast loading and rendering
6. **Testing**: Test print functionality across different browsers
7. **Localization**: Support multiple languages and formats
8. **Security**: Validate all data to prevent XSS attacks

### Common Pitfalls to Avoid
- Don't assume all data fields are present
- Don't skip print media CSS optimization
- Don't forget to handle browser popup blockers
- Don't ignore accessibility requirements
- Don't hardcode currency or date formats
- Don't skip error handling for network requests

## Dependencies

### Required Libraries
- **React**: Core component framework
- **React Icons**: For UI icons (FiPrinter, FiX)
- **TypeScript**: Type safety and interfaces

### Browser APIs Used
- **window.open()**: For creating print windows
- **window.print()**: For triggering print dialog
- **Intl.NumberFormat**: For currency formatting
- **Intl.DateTimeFormat**: For date formatting

## Troubleshooting

### Common Issues
1. **Print Window Blocked**: Browser popup blocker prevents print window
2. **Missing Data**: Required data not provided to component
3. **Formatting Issues**: Currency or date formatting problems
4. **Print Styles**: Styles not applied correctly in print view
5. **Modal Not Closing**: Event listeners not properly cleaned up

### Debug Tips
- Check browser console for JavaScript errors
- Verify all required props are provided
- Test print functionality in different browsers
- Validate data structure matches expected interfaces
- Check CSS media queries for print styles

## Security Considerations

### Data Protection
- **Input Validation**: Validate all data before display
- **XSS Prevention**: Sanitize user-generated content
- **Access Control**: Ensure user has permission to view invoice
- **Data Encryption**: Protect sensitive customer information

### Privacy Compliance
- **Data Minimization**: Only display necessary information
- **Consent Management**: Respect user privacy preferences
- **Audit Trails**: Log invoice generation activities
- **Data Retention**: Follow data retention policies

---

This documentation provides comprehensive coverage of the Invoice Print functionality, including layout structure, technical implementation, user experience, and best practices for maintenance and enhancement. 