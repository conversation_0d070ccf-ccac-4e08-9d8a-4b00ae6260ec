import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";

export class UpdateRoleDto {
  @ApiPropertyOptional({
    example: "Warehouseager",
    description: "Role name (optional, to rename)",
  })
  name?: string;

  @ApiPropertyOptional({
    example: ["sales.view", "sales.edit"],
    description: "List of permissions (optional)",
  })
  permissions?: string[];

  @ApiPropertyOptional({
    example: "018ea2bb-6b8d-7e9a-8a2a-7e9a8a2a7e9a",
    description: "UUID of the warehouse this role belongs to (optional)",
  })
  warehouseUuid?: string;
}
