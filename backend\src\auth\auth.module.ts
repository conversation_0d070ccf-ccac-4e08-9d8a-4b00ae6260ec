import { Module } from "@nestjs/common";
import { PassportModule } from "@nestjs/passport";
import { JwtModule } from "@nestjs/jwt";
import { TypeOrmModule } from "@nestjs/typeorm";
import { UsersModule } from "../users/users.module";
import { RolesModule } from "../roles/roles.module";
import { UserRolesModule } from "../users/user_roles.module";
import { UsersService } from "../users/users.service";
import { AuthController, CustomLocalGuard } from "./auth.controller";
import { GoogleStrategy } from "./google.strategy";
import { LocalStrategy } from "./local.strategy";
import { UnifiedStrategy } from "./unified.strategy";
import { UnifiedGuard } from "./unified.guard";

import { AuthAuditService } from "./auth-audit.service.typeorm";
import { RateLimitingService } from "./rate-limiting.service.typeorm";
import { RefreshTokenService } from "./refresh-token.service.typeorm";

// Import entities
import { AuthAudit } from "./auth-audit.entity";
import { RateLimit } from "./rate-limit.entity";
import { RefreshToken } from "./refresh-token.entity";

@Module({
  imports: [
    PassportModule,
    UsersModule,
    RolesModule,
    UserRolesModule,
    TypeOrmModule.forFeature([
      AuthAudit,
      RateLimit,
      RefreshToken,
    ]),
    JwtModule.register({
      secret: process.env.JWT_SECRET || "dev_secret",
      signOptions: { expiresIn: "1h" }, // Consistent with controller
    }),
  ],
  controllers: [AuthController],
  providers: [
    GoogleStrategy,
    LocalStrategy,
    CustomLocalGuard,
    UnifiedStrategy,
    UnifiedGuard,
    AuthAuditService,
    RateLimitingService,
    RefreshTokenService,
  ],
  exports: [
    AuthAuditService,
    RateLimitingService,
    RefreshTokenService,
  ],
})
export class AuthModule {}
