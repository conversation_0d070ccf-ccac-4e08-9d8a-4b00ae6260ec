import { Injectable, Logger, UnauthorizedException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, MoreThan, LessThan } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { RefreshToken } from './refresh-token.entity';

export interface RefreshTokenData {
  tokenId: string;
  userId: string;
  userEmail: string;
  token: string;
  expiresAt: Date;
  createdAt: Date;
  lastUsedAt?: Date;
  isRevoked: boolean;
  ipAddress?: string;
  userAgent?: string;
  familyId?: string; // For token rotation
}

@Injectable()
export class RefreshTokenService {
  private readonly logger = new Logger(RefreshTokenService.name);

  constructor(
    @InjectRepository(RefreshToken) private refreshTokenRepository: Repository<RefreshToken>,
    private jwtService: JwtService,
  ) {}

  async generateRefreshToken(
    userId: string,
    userEmail: string,
    ipAddress?: string,
    userAgent?: string,
  ): Promise<{ refreshToken: string; expiresAt: Date }> {
    const tokenId = uuidv4();
    const familyId = uuidv4(); // New family for token rotation
    const expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // 7 days

    const refreshToken = this.jwtService.sign(
      {
        tokenId,
        userId,
        userEmail,
        familyId,
        type: 'refresh',
      },
      {
        expiresIn: '7d',
        secret: process.env.JWT_REFRESH_SECRET || process.env.JWT_SECRET,
      },
    );

    // Store in database
    const tokenRecord = new RefreshToken();
    tokenRecord.tokenId = tokenId;
    tokenRecord.userId = userId;
    tokenRecord.userEmail = userEmail;
    tokenRecord.token = refreshToken;
    tokenRecord.expiresAt = expiresAt;
    tokenRecord.createdAt = new Date();
    tokenRecord.isRevoked = false;
    tokenRecord.ipAddress = ipAddress;
    tokenRecord.userAgent = userAgent;
    tokenRecord.familyId = familyId;

    await this.refreshTokenRepository.save(tokenRecord);

    this.logger.log(`Generated refresh token for user ${userEmail}`);

    return { refreshToken, expiresAt };
  }

  async validateRefreshToken(token: string): Promise<RefreshTokenData> {
    try {
      // Verify JWT
      const payload = this.jwtService.verify(token, {
        secret: process.env.JWT_REFRESH_SECRET || process.env.JWT_SECRET,
      });

      if (payload.type !== 'refresh') {
        throw new UnauthorizedException('Invalid token type');
      }

      // Check if token exists in database and is not revoked
      const tokenRecord = await this.refreshTokenRepository.findOne({
        where: {
          tokenId: payload.tokenId,
          isRevoked: false,
        }
      });

      if (!tokenRecord) {
        throw new UnauthorizedException('Refresh token not found or revoked');
      }

      // Check if token has expired
      if (tokenRecord.expiresAt < new Date()) {
        await this.revokeToken(payload.tokenId);
        throw new UnauthorizedException('Refresh token has expired');
      }

      // Update last used timestamp
      await this.refreshTokenRepository.update(
        { tokenId: payload.tokenId },
        { lastUsedAt: new Date() }
      );

      return tokenRecord as unknown as RefreshTokenData;
    } catch (error) {
      this.logger.warn(`Invalid refresh token: ${error.message}`);
      throw new UnauthorizedException('Invalid refresh token');
    }
  }

  async rotateRefreshToken(
    oldToken: string,
    ipAddress?: string,
    userAgent?: string,
  ): Promise<{ refreshToken: string; expiresAt: Date }> {
    const oldTokenRecord = await this.validateRefreshToken(oldToken);

    // Revoke the old token
    await this.revokeToken(oldTokenRecord.tokenId);

    // Revoke all tokens in the same family (token rotation)
    await this.revokeTokenFamily(oldTokenRecord.familyId);

    // Generate new refresh token with new family ID
    return this.generateRefreshToken(
      oldTokenRecord.userId,
      oldTokenRecord.userEmail,
      ipAddress,
      userAgent,
    );
  }

  async revokeToken(tokenId: string): Promise<void> {
    await this.refreshTokenRepository.update(
      { tokenId },
      { isRevoked: true }
    );

    this.logger.log(`Revoked refresh token ${tokenId}`);
  }

  async revokeTokenFamily(familyId: string): Promise<void> {
    await this.refreshTokenRepository.update(
      { familyId },
      { isRevoked: true }
    );

    this.logger.log(`Revoked all tokens in family ${familyId}`);
  }

  async revokeAllUserTokens(userId: string): Promise<void> {
    await this.refreshTokenRepository.update(
      { userId },
      { isRevoked: true }
    );

    this.logger.log(`Revoked all refresh tokens for user ${userId}`);
  }

  async getActiveTokens(userId: string): Promise<RefreshTokenData[]> {
    const tokens = await this.refreshTokenRepository.find({
      where: {
        userId,
        isRevoked: false,
        expiresAt: MoreThan(new Date()),
      },
      order: {
        createdAt: 'DESC',
      }
    });
    
    return tokens as unknown as RefreshTokenData[];
  }

  async cleanupExpiredTokens(): Promise<void> {
    const cutoffTime = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000); // 30 days ago

    await this.refreshTokenRepository.delete({
      expiresAt: LessThan(new Date()),
    });

    await this.refreshTokenRepository.delete({
      createdAt: LessThan(cutoffTime),
    });

    this.logger.log('Cleaned up expired refresh tokens');
  }

  async getTokenStats(userId: string): Promise<{
    activeTokens: number;
    totalTokens: number;
    lastUsed: Date | null;
  }> {
    const [activeTokens, totalTokens, lastUsedToken] = await Promise.all([
      this.refreshTokenRepository.count({
        where: {
          userId,
          isRevoked: false,
          expiresAt: MoreThan(new Date()),
        }
      }),
      this.refreshTokenRepository.count({
        where: { userId }
      }),
      this.refreshTokenRepository.findOne({
        where: { userId },
        order: { lastUsedAt: 'DESC' },
        select: ['lastUsedAt']
      }),
    ]);

    return {
      activeTokens,
      totalTokens,
      lastUsed: lastUsedToken?.lastUsedAt || null,
    };
  }

  // Security: Detect suspicious activity
  async detectSuspiciousActivity(userId: string, ipAddress?: string): Promise<boolean> {
    const recentTokens = await this.refreshTokenRepository.find({
      where: {
        userId,
        createdAt: MoreThan(new Date(Date.now() - 24 * 60 * 60 * 1000)), // Last 24 hours
      },
      select: ['ipAddress', 'userAgent']
    });

    if (recentTokens.length === 0) return false;

    // Check for multiple IP addresses
    const uniqueIPs = new Set(recentTokens.map(t => t.ipAddress).filter(Boolean));
    if (uniqueIPs.size > 3) {
      this.logger.warn(`Suspicious activity detected for user ${userId}: Multiple IP addresses`);
      return true;
    }

    // Check for rapid token generation
    if (recentTokens.length > 10) {
      this.logger.warn(`Suspicious activity detected for user ${userId}: Too many tokens generated`);
      return true;
    }

    return false;
  }
} 