export type Theme = {
  name: string;
  colors: {
    // Primary colors
    primary: string;
    primaryLight: string;
    primaryDark: string;
    // Background colors
    background: string;
    surface: string;
    // Text colors
    textPrimary: string;
    textSecondary: string;
    // Status colors
    success: string;
    warning: string;
    error: string;
    info: string;
    // Border colors
    border: string;
    // Hover states
    hover: string;
    // Active states
    active: string;
  };
  shadows: {
    small: string;
    medium: string;
    large: string;
  };
  borderRadius: {
    small: string;
    medium: string;
    large: string;
  };
};

export const lightTheme: Theme = {
  name: 'light',
  colors: {
    primary: '#2563eb',
    primaryLight: '#3b82f6',
    primaryDark: '#1d4ed8',
    background: '#f9fafb',
    surface: '#ffffff',
    textPrimary: '#111827',
    textSecondary: '#4b5563',
    success: '#10b981',
    warning: '#f59e0b',
    error: '#ef4444',
    info: '#3b82f6',
    border: '#e5e7eb',
    hover: '#f3f4f6',
    active: '#e5e7eb',
  },
  shadows: {
    small: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
    medium: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
    large: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
  },
  borderRadius: {
    small: '0.25rem',
    medium: '0.5rem',
    large: '1rem',
  },
};

export const darkTheme: Theme = {
  name: 'dark',
  colors: {
    primary: '#3b82f6',
    primaryLight: '#60a5fa',
    primaryDark: '#2563eb',
    background: '#111827',
    surface: '#1f2937',
    textPrimary: '#f9fafb',
    textSecondary: '#d1d5db',
    success: '#10b981',
    warning: '#f59e0b',
    error: '#ef4444',
    info: '#3b82f6',
    border: '#374151',
    hover: '#1f2937',
    active: '#374151',
  },
  shadows: {
    small: '0 1px 2px 0 rgb(0 0 0 / 0.25)',
    medium: '0 4px 6px -1px rgb(0 0 0 / 0.3), 0 2px 4px -2px rgb(0 0 0 / 0.3)',
    large: '0 10px 15px -3px rgb(0 0 0 / 0.3), 0 4px 6px -4px rgb(0 0 0 / 0.3)',
  },
  borderRadius: {
    small: '0.25rem',
    medium: '0.5rem',
    large: '1rem',
  },
};

export const blueTheme: Theme = {
  name: 'blue',
  colors: {
    primary: '#1d4ed8',
    primaryLight: '#2563eb',
    primaryDark: '#1e40af',
    background: '#eff6ff',
    surface: '#ffffff',
    textPrimary: '#1e3a8a',
    textSecondary: '#1e40af',
    success: '#047857',
    warning: '#b45309',
    error: '#b91c1c',
    info: '#1d4ed8',
    border: '#bfdbfe',
    hover: '#dbeafe',
    active: '#bfdbfe',
  },
  shadows: {
    small: '0 1px 3px 0 rgb(29 78 216 / 0.1), 0 1px 2px -1px rgb(29 78 216 / 0.1)',
    medium: '0 4px 6px -1px rgb(29 78 216 / 0.1), 0 2px 4px -2px rgb(29 78 216 / 0.1)',
    large: '0 10px 15px -3px rgb(29 78 216 / 0.1), 0 4px 6px -4px rgb(29 78 216 / 0.1)',
  },
  borderRadius: {
    small: '0.25rem',
    medium: '0.5rem',
    large: '1rem',
  },
};

// Export a default theme (can be changed based on user preference)
export const defaultTheme = lightTheme;

// Export all themes as an array
export const allThemes: Theme[] = [lightTheme, darkTheme, blueTheme];

// Helper function to get a theme by name
export const getThemeByName = (name: string): Theme => {
  return allThemes.find((theme) => theme.name === name) || defaultTheme;
};
