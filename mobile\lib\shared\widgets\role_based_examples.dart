import 'package:flutter/material.dart';
import '../services/permission_service.dart';
import 'auth_guard.dart';

/// Example page showing how to use role-based widgets
class RoleBasedExamplesPage extends StatelessWidget {
  const RoleBasedExamplesPage({super.key});

  @override
  Widget build(BuildContext context) => Scaffold(
      appBar: AppBar(
        title: const Text('Role-Based Examples'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Role-Based UI Examples',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 24),
            
            // Example 1: Permission-based button
            PermissionWidget(
              permission: Permissions.inventoryManage,
              child: ElevatedButton(
                onPressed: () {},
                child: const Text('Manage Inventory (Admin Only)'),
              ),
              fallback: const Text('You cannot manage inventory'),
            ),
            
            const SizedBox(height: 16),
            
            // Example 2: Role-based content
            RoleBasedWidget(
              adminWidget: const Card(
                child: ListTile(
                  leading: Icon(Icons.admin_panel_settings),
                  title: Text('Admin Panel'),
                  subtitle: Text('Full system access'),
                ),
              ),
              mobileSaleAgentWidget: const Card(
                child: ListTile(
                  leading: Icon(Icons.person_pin),
                  title: Text('Sales Agent Dashboard'),
                  subtitle: Text('Client and sales management'),
                ),
              ),
              managerWidget: const Card(
                child: ListTile(
                  leading: Icon(Icons.supervisor_account),
                  title: Text('Manager Dashboard'),
                  subtitle: Text('Team oversight and reporting'),
                ),
              ),
              defaultWidget: const Card(
                child: ListTile(
                  leading: Icon(Icons.person),
                  title: Text('User Dashboard'),
                  subtitle: Text('Basic access'),
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Example 3: Multiple permission check
            PermissionWidget(
              permission: Permissions.salesEdit,
              child: const Card(
                child: ListTile(
                  leading: Icon(Icons.edit),
                  title: Text('Edit Sales'),
                  subtitle: Text('Modify sales records'),
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Example 4: Role-based navigation item
            PermissionWidget(
              role: Roles.admin,
              child: const ListTile(
                leading: Icon(Icons.settings),
                title: Text('Admin Settings'),
                subtitle: Text('System configuration'),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Example 5: AuthGuard protecting entire section
            Expanded(
              child: AuthGuard(
                requiredPermission: Permissions.reportsView,
                child: const Card(
                  child: Padding(
                    padding: EdgeInsets.all(16.0),
                    child: Column(
                      children: [
                        Text('Protected Reports Section'),
                        SizedBox(height: 8),
                        Text('This content is only visible to users with reports.view permission'),
                      ],
                    ),
                  ),
                ),
                fallback: const Card(
                  child: Padding(
                    padding: EdgeInsets.all(16.0),
                    child: Column(
                      children: [
                        Icon(Icons.lock, size: 48, color: Colors.grey),
                        SizedBox(height: 8),
                        Text('Reports access required'),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
}

/// Example of a feature page with role-based functionality
class FeaturePage extends StatelessWidget {
  const FeaturePage({super.key});

  @override
  Widget build(BuildContext context) => Scaffold(
      appBar: AppBar(
        title: const Text('Feature Page'),
        actions: [
          // Only show admin actions to admins
          PermissionWidget(
            role: Roles.admin,
            child: IconButton(
              icon: const Icon(Icons.admin_panel_settings),
              onPressed: () {
                // Admin-only action
              },
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          // Content visible to all authenticated users
          const ListTile(
            title: Text('General Content'),
            subtitle: Text('Visible to all users'),
          ),
          
          // Content only for mobile sale agents
          PermissionWidget(
            role: Roles.mobileSaleAgent,
            child: const ListTile(
              leading: Icon(Icons.person_pin),
              title: Text('Sales Agent Tools'),
              subtitle: Text('Client management and sales tracking'),
            ),
          ),
          
          // Content only for users with inventory permissions
          PermissionWidget(
            permission: Permissions.inventoryView,
            child: const ListTile(
              leading: Icon(Icons.inventory),
              title: Text('Inventory Overview'),
              subtitle: Text('Stock levels and product information'),
            ),
          ),
          
          // Admin-only content
          AuthGuard(
            requiredRole: Roles.admin,
            child: const Card(
              margin: EdgeInsets.all(16),
              child: Padding(
                padding: EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Admin Panel',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 8),
                    Text('System configuration and user management'),
                    SizedBox(height: 16),
                    Row(
                      children: [
                        Expanded(
                          child: ElevatedButton(
                            onPressed: null, // Placeholder
                            child: Text('Manage Users'),
                          ),
                        ),
                        SizedBox(width: 8),
                        Expanded(
                          child: ElevatedButton(
                            onPressed: null, // Placeholder
                            child: Text('System Settings'),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
      
      // Role-based floating action button
      floatingActionButton: RoleBasedWidget(
        adminWidget: FloatingActionButton(
          onPressed: () {
            // Admin action
          },
          child: const Icon(Icons.add),
        ),
        mobileSaleAgentWidget: FloatingActionButton(
          onPressed: () {
            // Mobile sale agent action
          },
          child: const Icon(Icons.person_add),
        ),
        managerWidget: FloatingActionButton(
          onPressed: () {
            // Manager action
          },
          child: const Icon(Icons.analytics),
        ),
      ),
    );
} 