import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Like } from 'typeorm';
import { Log } from './log.entity';
import { CreateLogDto } from './dto/create-log.dto';
import { FilterLogsDto } from './dto/filter-logs.dto';
import { PaginatedResponseDto } from '../dto/pagination.dto';

@Injectable()
export class LogsService {
  private readonly logger = new Logger(LogsService.name);

  constructor(
    @InjectRepository(Log)
    private readonly logsRepository: Repository<Log>,
  ) {}

  async create(createLogDto: CreateLogDto): Promise<Log> {
    this.logger.log(`Creating log entry: ${createLogDto.operation} on ${createLogDto.entity} by user ${createLogDto.userUuid}`);
    
    const log = this.logsRepository.create({
      id: Log.generateId(),
      ...createLogDto,
    });

    return await this.logsRepository.save(log);
  }

  async findAll(filterDto: FilterLogsDto): Promise<PaginatedResponseDto<Log>> {
    const { page = 1, limit = 10, userUuid, operation, entity } = filterDto;
    const skip = (page - 1) * limit;

    // Build where conditions
    const whereConditions: any = {};
    
    if (userUuid && userUuid.trim() !== '') {
      whereConditions.userUuid = userUuid;
    }
    
    if (operation && operation.trim() !== '') {
      whereConditions.operation = Like(`%${operation.trim()}%`);
    }
    
    if (entity && entity.trim() !== '') {
      whereConditions.entity = Like(`%${entity.trim()}%`);
    }

    // Get total count
    const total = await this.logsRepository.count({ where: whereConditions });

    // Get paginated results
    const logs = await this.logsRepository.find({
      where: whereConditions,
      order: { createdAt: 'DESC' },
      skip,
      take: limit,
    });

    return new PaginatedResponseDto(logs, total, page, limit);
  }

  async findOne(id: string): Promise<Log> {
    const log = await this.logsRepository.findOne({ where: { id } });
    if (!log) {
      throw new Error(`Log with ID ${id} not found`);
    }
    return log;
  }

  async findByUser(userUuid: string, filterDto: FilterLogsDto): Promise<PaginatedResponseDto<Log>> {
    const { page = 1, limit = 10, operation, entity } = filterDto;
    const skip = (page - 1) * limit;

    // Build where conditions
    const whereConditions: any = { userUuid };
    
    if (operation && operation.trim() !== '') {
      whereConditions.operation = Like(`%${operation.trim()}%`);
    }
    
    if (entity && entity.trim() !== '') {
      whereConditions.entity = Like(`%${entity.trim()}%`);
    }

    // Get total count
    const total = await this.logsRepository.count({ where: whereConditions });

    // Get paginated results
    const logs = await this.logsRepository.find({
      where: whereConditions,
      order: { createdAt: 'DESC' },
      skip,
      take: limit,
    });

    return new PaginatedResponseDto(logs, total, page, limit);
  }

  async findByOperation(operation: string, filterDto: FilterLogsDto): Promise<PaginatedResponseDto<Log>> {
    const { page = 1, limit = 10, userUuid, entity } = filterDto;
    const skip = (page - 1) * limit;

    // Build where conditions
    const whereConditions: any = { operation };
    
    if (userUuid && userUuid.trim() !== '') {
      whereConditions.userUuid = userUuid;
    }
    
    if (entity && entity.trim() !== '') {
      whereConditions.entity = Like(`%${entity.trim()}%`);
    }

    // Get total count
    const total = await this.logsRepository.count({ where: whereConditions });

    // Get paginated results
    const logs = await this.logsRepository.find({
      where: whereConditions,
      order: { createdAt: 'DESC' },
      skip,
      take: limit,
    });

    return new PaginatedResponseDto(logs, total, page, limit);
  }

  async findByEntity(entity: string, filterDto: FilterLogsDto): Promise<PaginatedResponseDto<Log>> {
    const { page = 1, limit = 10, userUuid, operation } = filterDto;
    const skip = (page - 1) * limit;

    // Build where conditions
    const whereConditions: any = { entity };
    
    if (userUuid && userUuid.trim() !== '') {
      whereConditions.userUuid = userUuid;
    }
    
    if (operation && operation.trim() !== '') {
      whereConditions.operation = Like(`%${operation.trim()}%`);
    }

    // Get total count
    const total = await this.logsRepository.count({ where: whereConditions });

    // Get paginated results
    const logs = await this.logsRepository.find({
      where: whereConditions,
      order: { createdAt: 'DESC' },
      skip,
      take: limit,
    });

    return new PaginatedResponseDto(logs, total, page, limit);
  }

  async remove(id: string): Promise<void> {
    const result = await this.logsRepository.delete(id);
    if (result.affected === 0) {
      throw new Error(`Log with ID ${id} not found`);
    }
    this.logger.log(`Deleted log entry with ID: ${id}`);
  }
} 