import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class AppTheme {
  AppTheme._();

  // =============================================================================
  // Color Schemes
  // =============================================================================

  static const ColorScheme _lightColorScheme = ColorScheme(
    brightness: Brightness.light,
    primary: Color(0xFF2563EB), // Blue 600
    onPrimary: Color(0xFFFFFFFF),
    primaryContainer: Color(0xFFDBEAFE), // Blue 100
    onPrimaryContainer: Color(0xFF1E40AF), // Blue 700
    secondary: Color(0xFF059669), // Emerald 600
    onSecondary: Color(0xFFFFFFFF),
    secondaryContainer: Color(0xFFD1FAE5), // Emerald 100
    onSecondaryContainer: Color(0xFF065F46), // Emerald 800
    tertiary: Color(0xFF7C3AED), // Violet 600
    onTertiary: Color(0xFFFFFFFF),
    tertiaryContainer: Color(0xFFEDE9FE), // Violet 100
    onTertiaryContainer: Color(0xFF5B21B6), // Violet 800
    error: Color(0xFFDC2626), // Red 600
    onError: Color(0xFFFFFFFF),
    errorContainer: Color(0xFFFEE2E2), // Red 100
    onErrorContainer: Color(0xFF991B1B), // Gray 900
    surface: Color(0xFFFFFFFF),
    onSurface: Color(0xFF111827), // Gray 900
    surfaceContainerHighest: Color(0xFFF3F4F6), // Gray 100
    onSurfaceVariant: Color(0xFF6B7280), // Gray 500
    outline: Color(0xFFD1D5DB), // Gray 300
    outlineVariant: Color(0xFFE5E7EB), // Gray 200
    shadow: Color(0xFF000000),
    scrim: Color(0xFF000000),
    inverseSurface: Color(0xFF111827), // Gray 900
    onInverseSurface: Color(0xFFF9FAFB), // Gray 50
    inversePrimary: Color(0xFF93C5FD), // Blue 300
    surfaceTint: Color(0xFF2563EB), // Blue 600
  );

  static const ColorScheme _darkColorScheme = ColorScheme(
    brightness: Brightness.dark,
    primary: Color(0xFF3B82F6), // Blue 500
    onPrimary: Color(0xFF1E3A8A), // Blue 800
    primaryContainer: Color(0xFF1E40AF), // Blue 700
    onPrimaryContainer: Color(0xFFDBEAFE), // Blue 100
    secondary: Color(0xFF10B981), // Emerald 500
    onSecondary: Color(0xFF064E3B), // Emerald 900
    secondaryContainer: Color(0xFF065F46), // Emerald 800
    onSecondaryContainer: Color(0xFFD1FAE5), // Emerald 100
    tertiary: Color(0xFF8B5CF6), // Violet 500
    onTertiary: Color(0xFF4C1D95), // Violet 900
    tertiaryContainer: Color(0xFF5B21B6), // Violet 800
    onTertiaryContainer: Color(0xFFEDE9FE), // Violet 100
    error: Color(0xFFEF4444), // Red 500
    onError: Color(0xFF7F1D1D), // Red 900
    errorContainer: Color(0xFF991B1B), // Red 800
    onErrorContainer: Color(0xFFFEE2E2), // Slate 100
    surface: Color(0xFF1E293B), // Slate 800
    onSurface: Color(0xFFF1F5F9), // Slate 100
    surfaceContainerHighest: Color(0xFF334155), // Slate 700
    onSurfaceVariant: Color(0xFF94A3B8), // Slate 400
    outline: Color(0xFF64748B), // Slate 500
    outlineVariant: Color(0xFF475569), // Slate 600
    shadow: Color(0xFF000000),
    scrim: Color(0xFF000000),
    inverseSurface: Color(0xFFF1F5F9), // Slate 100
    onInverseSurface: Color(0xFF1E293B), // Slate 800
    inversePrimary: Color(0xFF1E40AF), // Blue 700
    surfaceTint: Color(0xFF3B82F6), // Blue 500
  );

  // =============================================================================
  // Typography
  // =============================================================================

  static const TextTheme _textTheme = TextTheme(
    displayLarge: TextStyle(
      fontSize: 57,
      fontWeight: FontWeight.w400,
      letterSpacing: -0.25,
      height: 1.12,
    ),
    displayMedium: TextStyle(
      fontSize: 45,
      fontWeight: FontWeight.w400,
      letterSpacing: 0,
      height: 1.16,
    ),
    displaySmall: TextStyle(
      fontSize: 36,
      fontWeight: FontWeight.w400,
      letterSpacing: 0,
      height: 1.22,
    ),
    headlineLarge: TextStyle(
      fontSize: 32,
      fontWeight: FontWeight.w400,
      letterSpacing: 0,
      height: 1.25,
    ),
    headlineMedium: TextStyle(
      fontSize: 28,
      fontWeight: FontWeight.w400,
      letterSpacing: 0,
      height: 1.29,
    ),
    headlineSmall: TextStyle(
      fontSize: 24,
      fontWeight: FontWeight.w400,
      letterSpacing: 0,
      height: 1.33,
    ),
    titleLarge: TextStyle(
      fontSize: 22,
      fontWeight: FontWeight.w500,
      letterSpacing: 0,
      height: 1.27,
    ),
    titleMedium: TextStyle(
      fontSize: 16,
      fontWeight: FontWeight.w500,
      letterSpacing: 0.15,
      height: 1.50,
    ),
    titleSmall: TextStyle(
      fontSize: 14,
      fontWeight: FontWeight.w500,
      letterSpacing: 0.1,
      height: 1.43,
    ),
    bodyLarge: TextStyle(
      fontSize: 16,
      fontWeight: FontWeight.w400,
      letterSpacing: 0.5,
      height: 1.50,
    ),
    bodyMedium: TextStyle(
      fontSize: 14,
      fontWeight: FontWeight.w400,
      letterSpacing: 0.25,
      height: 1.43,
    ),
    bodySmall: TextStyle(
      fontSize: 12,
      fontWeight: FontWeight.w400,
      letterSpacing: 0.4,
      height: 1.33,
    ),
    labelLarge: TextStyle(
      fontSize: 14,
      fontWeight: FontWeight.w500,
      letterSpacing: 0.1,
      height: 1.43,
    ),
    labelMedium: TextStyle(
      fontSize: 12,
      fontWeight: FontWeight.w500,
      letterSpacing: 0.5,
      height: 1.33,
    ),
    labelSmall: TextStyle(
      fontSize: 11,
      fontWeight: FontWeight.w500,
      letterSpacing: 0.5,
      height: 1.45,
    ),
  );

  // =============================================================================
  // Component Themes
  // =============================================================================

  static AppBarTheme _appBarTheme(ColorScheme colorScheme) => AppBarTheme(
      centerTitle: true,
      elevation: 0,
      scrolledUnderElevation: 1,
      backgroundColor: colorScheme.surface,
      foregroundColor: colorScheme.onSurface,
      surfaceTintColor: colorScheme.surfaceTint,
      iconTheme: IconThemeData(
        color: colorScheme.onSurface,
        size: 24,
      ),
      actionsIconTheme: IconThemeData(
        color: colorScheme.onSurface,
        size: 24,
      ),
      titleTextStyle: _textTheme.titleLarge?.copyWith(
        color: colorScheme.onSurface,
        fontWeight: FontWeight.w600,
      ),
      systemOverlayStyle: SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: colorScheme.brightness == Brightness.light
            ? Brightness.dark
            : Brightness.light,
        statusBarBrightness: colorScheme.brightness,
      ),
    );

  static BottomNavigationBarThemeData _bottomNavigationBarTheme(ColorScheme colorScheme) => BottomNavigationBarThemeData(
      type: BottomNavigationBarType.fixed,
      backgroundColor: colorScheme.surface,
      selectedItemColor: colorScheme.primary,
      unselectedItemColor: colorScheme.onSurfaceVariant,
      selectedLabelStyle: _textTheme.labelMedium?.copyWith(
        fontWeight: FontWeight.w600,
      ),
      unselectedLabelStyle: _textTheme.labelMedium,
      showSelectedLabels: true,
      showUnselectedLabels: true,
      elevation: 8,
    );

  static ElevatedButtonThemeData _elevatedButtonTheme(ColorScheme colorScheme) => ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: colorScheme.primary,
        foregroundColor: colorScheme.onPrimary,
        disabledBackgroundColor: colorScheme.onSurface.withOpacity(0.12),
        disabledForegroundColor: colorScheme.onSurface.withOpacity(0.38),
        elevation: 1,
        shadowColor: colorScheme.shadow,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(24),
        ),
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        textStyle: _textTheme.labelLarge?.copyWith(
          fontWeight: FontWeight.w600,
        ),
      ),
    );

  static OutlinedButtonThemeData _outlinedButtonTheme(ColorScheme colorScheme) => OutlinedButtonThemeData(
      style: OutlinedButton.styleFrom(
        foregroundColor: colorScheme.primary,
        disabledForegroundColor: colorScheme.onSurface.withOpacity(0.38),
        side: BorderSide(color: colorScheme.outline),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(24),
        ),
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        textStyle: _textTheme.labelLarge?.copyWith(
          fontWeight: FontWeight.w600,
        ),
      ),
    );

  static TextButtonThemeData _textButtonTheme(ColorScheme colorScheme) => TextButtonThemeData(
      style: TextButton.styleFrom(
        foregroundColor: colorScheme.primary,
        disabledForegroundColor: colorScheme.onSurface.withOpacity(0.38),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        textStyle: _textTheme.labelLarge?.copyWith(
          fontWeight: FontWeight.w600,
        ),
      ),
    );

  static InputDecorationTheme _inputDecorationTheme(ColorScheme colorScheme) => InputDecorationTheme(
      filled: true,
      fillColor: colorScheme.surfaceVariant,
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(color: colorScheme.outline),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(color: colorScheme.outline),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(color: colorScheme.primary, width: 2),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(color: colorScheme.error),
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(color: colorScheme.error, width: 2),
      ),
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      hintStyle: _textTheme.bodyMedium?.copyWith(
        color: colorScheme.onSurfaceVariant,
      ),
      labelStyle: _textTheme.bodyMedium?.copyWith(
        color: colorScheme.onSurfaceVariant,
      ),
      errorStyle: _textTheme.bodySmall?.copyWith(
        color: colorScheme.error,
      ),
    );

  static CardThemeData _cardTheme(ColorScheme colorScheme) => CardThemeData(
      color: colorScheme.surface,
      shadowColor: colorScheme.shadow,
      elevation: 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      margin: const EdgeInsets.all(4),
    );

  static FloatingActionButtonThemeData _fabTheme(ColorScheme colorScheme) => FloatingActionButtonThemeData(
      backgroundColor: colorScheme.primary,
      foregroundColor: colorScheme.onPrimary,
      elevation: 6,
      focusElevation: 8,
      hoverElevation: 8,
      highlightElevation: 12,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
    );

  static ChipThemeData _chipTheme(ColorScheme colorScheme) => ChipThemeData(
      backgroundColor: colorScheme.surfaceVariant,
      selectedColor: colorScheme.primaryContainer,
      disabledColor: colorScheme.onSurface.withOpacity(0.12),
      deleteIconColor: colorScheme.onSurfaceVariant,
      labelStyle: _textTheme.labelMedium?.copyWith(
        color: colorScheme.onSurfaceVariant,
      ),
      secondaryLabelStyle: _textTheme.labelMedium?.copyWith(
        color: colorScheme.onPrimaryContainer,
      ),
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
    );

  // =============================================================================
  // Theme Data
  // =============================================================================

  static ThemeData lightTheme = ThemeData(
    useMaterial3: true,
    colorScheme: _lightColorScheme,
    textTheme: _textTheme,
    appBarTheme: _appBarTheme(_lightColorScheme),
    bottomNavigationBarTheme: _bottomNavigationBarTheme(_lightColorScheme),
    elevatedButtonTheme: _elevatedButtonTheme(_lightColorScheme),
    outlinedButtonTheme: _outlinedButtonTheme(_lightColorScheme),
    textButtonTheme: _textButtonTheme(_lightColorScheme),
    inputDecorationTheme: _inputDecorationTheme(_lightColorScheme),
    cardTheme: _cardTheme(_lightColorScheme),
    floatingActionButtonTheme: _fabTheme(_lightColorScheme),
    chipTheme: _chipTheme(_lightColorScheme),
    dividerTheme: DividerThemeData(
      color: _lightColorScheme.outlineVariant,
      thickness: 1,
    ),
    scaffoldBackgroundColor: _lightColorScheme.surface,
    visualDensity: VisualDensity.adaptivePlatformDensity,
  );

  static ThemeData darkTheme = ThemeData(
    useMaterial3: true,
    colorScheme: _darkColorScheme,
    textTheme: _textTheme,
    appBarTheme: _appBarTheme(_darkColorScheme),
    bottomNavigationBarTheme: _bottomNavigationBarTheme(_darkColorScheme),
    elevatedButtonTheme: _elevatedButtonTheme(_darkColorScheme),
    outlinedButtonTheme: _outlinedButtonTheme(_darkColorScheme),
    textButtonTheme: _textButtonTheme(_darkColorScheme),
    inputDecorationTheme: _inputDecorationTheme(_darkColorScheme),
    cardTheme: _cardTheme(_darkColorScheme),
    floatingActionButtonTheme: _fabTheme(_darkColorScheme),
    chipTheme: _chipTheme(_darkColorScheme),
    dividerTheme: DividerThemeData(
      color: _darkColorScheme.outlineVariant,
      thickness: 1,
    ),
    scaffoldBackgroundColor: _darkColorScheme.surface,
    visualDensity: VisualDensity.adaptivePlatformDensity,
  );
} 