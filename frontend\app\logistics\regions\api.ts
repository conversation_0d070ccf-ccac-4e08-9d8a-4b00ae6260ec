import axios from "axios";
import { getAxiosAuthHeaders } from '@/utils/authHeaders';

export interface RegionDto {
  uuid: string;
  name: string;
  description?: string;
  latitude?: number;
  longitude?: number;
  warehouseUuidString?: string;
  isDeleted: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface CreateRegionDto {
  name: string;
  description?: string;
  latitude?: number;
  longitude?: number;
  warehouseUuid?: string;
}

export interface UpdateRegionDto {
  name?: string;
  description?: string;
  latitude?: number;
  longitude?: number;
  warehouseUuid?: string;
}

export interface FilterRegionDto {
  warehouseUuid?: string;
  name?: string;
  description?: string;
}

export interface PaginationQueryDto {
  page?: number;
  limit?: number;
}

export interface PaginatedResponse<T> {
  data: T[];
  totalItems: number;
  totalPages: number;
  currentPage: number;
  itemsPerPage: number;
}

const REGIONS_API = "/api/regions";

export async function listRegions(
  paginationQuery: PaginationQueryDto = {},
  filter: FilterRegionDto = {}
): Promise<PaginatedResponse<RegionDto>> {
  const params = { ...paginationQuery, ...filter };
  const res = await axios.get(REGIONS_API, { 
    params,
    headers: getAxiosAuthHeaders(),
  });
  return res.data;
}

export async function getRegionByUuid(uuid: string): Promise<RegionDto> {
  const res = await axios.get(`${REGIONS_API}/${uuid}`, {
    headers: getAxiosAuthHeaders(),
  });
  return res.data;
}

export async function createRegion(data: CreateRegionDto): Promise<RegionDto> {
  const res = await axios.post(REGIONS_API, data, {
    headers: getAxiosAuthHeaders(),
  });
  return res.data;
}

export async function updateRegion(uuid: string, data: UpdateRegionDto): Promise<RegionDto> {
  const res = await axios.patch(`${REGIONS_API}/${uuid}`, data, {
    headers: getAxiosAuthHeaders(),
  });
  return res.data;
}

export async function softDeleteRegion(uuid: string): Promise<void> {
  await axios.delete(`${REGIONS_API}/${uuid}`, {
    headers: getAxiosAuthHeaders(),
  });
}

export async function hardDeleteAllRegions(): Promise<{ deletedCount: number; message: string }> {
  const res = await axios.delete(`${REGIONS_API}/all`, {
    headers: getAxiosAuthHeaders(),
  });
  return res.data;
} 