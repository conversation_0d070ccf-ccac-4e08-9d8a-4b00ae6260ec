import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, Is<PERSON>UI<PERSON>, IsEmail, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ength, Matches } from 'class-validator';

export class EnhancedLoginDto {
  @ApiProperty({
    example: '<EMAIL>',
    description: 'User identifier - can be UUID, email, or name',
    type: String,
  })
  @IsString()
  @MaxLength(255)
  identifier: string;

  @ApiProperty({
    example: 'SecurePassword123!',
    description: 'User password (optional for passwordless accounts)',
    required: false,
    type: String,
  })
  @IsOptional()
  @IsString()
  @MinLength(1)
  @MaxLength(128)
  password?: string;

  @ApiProperty({
    example: '***********',
    description: 'Client IP address for security tracking',
    required: false,
    type: String,
  })
  @IsOptional()
  @IsString()
  clientIp?: string;

  @ApiProperty({
    example: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    description: 'User agent string for security tracking',
    required: false,
    type: String,
  })
  @IsOptional()
  @IsString()
  userAgent?: string;
}

export class RefreshTokenDto {
  @ApiProperty({
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
    description: 'Refresh token for obtaining new access token',
    type: String,
  })
  @IsString()
  refreshToken: string;

  @ApiProperty({
    example: '***********',
    description: 'Client IP address for security tracking',
    required: false,
    type: String,
  })
  @IsOptional()
  @IsString()
  clientIp?: string;

  @ApiProperty({
    example: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    description: 'User agent string for security tracking',
    required: false,
    type: String,
  })
  @IsOptional()
  @IsString()
  userAgent?: string;
}

export class LogoutDto {
  @ApiProperty({
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
    description: 'Refresh token to revoke (optional)',
    required: false,
    type: String,
  })
  @IsOptional()
  @IsString()
  refreshToken?: string;

  @ApiProperty({
    example: true,
    description: 'Whether to revoke all user sessions',
    required: false,
    type: Boolean,
  })
  @IsOptional()
  revokeAll?: boolean;
}

export class UserRoleDto {
  @ApiProperty({
    example: '018ea2bb-6b8d-7e9a-8a2a-7e9a8a2a7e9a',
    description: 'Role UUID',
  })
  uuid: string;

  @ApiProperty({
    example: 'admin',
    description: 'Role name',
  })
  name: string;

  @ApiProperty({
    example: ['read', 'write', 'delete'],
    description: 'Role permissions',
    type: [String],
  })
  permissions: string[];
}

export class UserInfoDto {
  @ApiProperty({
    example: '018ea2bb-6b8d-7e9a-8a2a-7e9a8a2a7e9a',
    description: 'User UUID',
  })
  uuid: string;

  @ApiProperty({
    example: '<EMAIL>',
    description: 'User email address',
  })
  email: string;

  @ApiProperty({
    example: 'John',
    description: 'User first name',
    nullable: true,
  })
  firstName: string | null;

  @ApiProperty({
    example: 'Doe',
    description: 'User last name',
    nullable: true,
  })
  lastName: string | null;

  @ApiProperty({
    example: '+**********',
    description: 'User phone number',
    nullable: true,
  })
  phone: string | null;

  @ApiProperty({
    example: true,
    description: 'Whether the user account is active',
  })
  isActive: boolean;

  @ApiProperty({
    example: [
      {
        uuid: '018ea2bb-6b8d-7e9a-8a2a-7e9a8a2a7e9a',
        name: 'admin',
        permissions: ['read', 'write', 'delete'],
      },
    ],
    description: 'User roles and permissions',
    type: [UserRoleDto],
  })
  roles: UserRoleDto[];

  @ApiProperty({
    example: '018ea2bb-6b8d-7e9a-8a2a-7e9a8a2a7e9a',
    description: 'Warehouse UUID the user belongs to',
    nullable: true,
  })
  warehouseUuid: string | null;

  @ApiProperty({
    example: '018ea2bb-6b8d-7e9a-8a2a-7e9a8a2a7e9a',
    description: 'Van UUID the user is assigned to',
    nullable: true,
  })
  vanUuid: string | null;

  @ApiProperty({
    example: '2024-01-15T10:30:00.000Z',
    description: 'User account creation timestamp',
  })
  createdAt: string;

  @ApiProperty({
    example: '2024-01-15T10:30:00.000Z',
    description: 'User account last update timestamp',
  })
  updatedAt: string;
}

export class SessionStatsDto {
  @ApiProperty({
    example: 2,
    description: 'Number of active tokens',
  })
  activeTokens: number;

  @ApiProperty({
    example: 5,
    description: 'Total number of tokens',
  })
  totalTokens: number;

  @ApiProperty({
    example: '2024-01-15T10:30:00.000Z',
    description: 'Last token usage timestamp',
    nullable: true,
  })
  lastUsed: string | null;
}

export class EnhancedLoginResponseDto {
  @ApiProperty({
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
    description: 'JWT access token for API authentication',
    type: String,
  })
  accessToken: string;

  @ApiProperty({
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
    description: 'Refresh token for obtaining new access tokens',
    type: String,
  })
  refreshToken: string;

  @ApiProperty({
    example: 3600,
    description: 'Access token expiration time in seconds',
    type: Number,
  })
  accessTokenExpiresIn: number;

  @ApiProperty({
    example: 604800,
    description: 'Refresh token expiration time in seconds',
    type: Number,
  })
  refreshTokenExpiresIn: number;

  @ApiProperty({
    description: 'User information',
    type: UserInfoDto,
  })
  user: UserInfoDto;

  @ApiProperty({
    description: 'Session statistics',
    type: SessionStatsDto,
  })
  sessionStats: SessionStatsDto;
}

export class RefreshTokenResponseDto {
  @ApiProperty({
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
    description: 'New JWT access token',
    type: String,
  })
  accessToken: string;

  @ApiProperty({
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
    description: 'New refresh token (rotated for security)',
    type: String,
  })
  refreshToken: string;

  @ApiProperty({
    example: 3600,
    description: 'Access token expiration time in seconds',
    type: Number,
  })
  accessTokenExpiresIn: number;

  @ApiProperty({
    example: 604800,
    description: 'Refresh token expiration time in seconds',
    type: Number,
  })
  refreshTokenExpiresIn: number;
}

export class SecurityEventDto {
  @ApiProperty({
    example: 'LOGIN_FAILURE',
    description: 'Type of security event',
  })
  eventType: string;

  @ApiProperty({
    example: '2024-01-15T10:45:00.000Z',
    description: 'Event timestamp',
  })
  timestamp: string;

  @ApiProperty({
    example: '***********',
    description: 'IP address associated with the event',
    required: false,
  })
  ipAddress?: string;

  @ApiProperty({
    example: true,
    description: 'Whether the event was successful',
  })
  success: boolean;
}

export class SecurityStatusDto {
  @ApiProperty({
    example: false,
    description: 'Whether the account is currently locked',
    type: Boolean,
  })
  isLocked: boolean;

  @ApiProperty({
    example: '2024-01-15T11:00:00.000Z',
    description: 'When the account will be unlocked (if locked)',
    nullable: true,
    type: String,
  })
  lockedUntil: string | null;

  @ApiProperty({
    example: 2,
    description: 'Number of remaining login attempts',
    type: Number,
  })
  remainingAttempts: number;

  @ApiProperty({
    example: false,
    description: 'Whether suspicious activity was detected',
    type: Boolean,
  })
  suspiciousActivityDetected: boolean;

  @ApiProperty({
    example: [
      {
        eventType: 'LOGIN_FAILURE',
        timestamp: '2024-01-15T10:45:00.000Z',
        ipAddress: '***********',
        success: false,
      },
    ],
    description: 'Recent authentication events',
    type: [SecurityEventDto],
  })
  recentEvents: SecurityEventDto[];
} 