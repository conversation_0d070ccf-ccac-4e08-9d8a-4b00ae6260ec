import {
  Injectable,
  NotFoundException,
  ConflictException,
} from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository, Like } from "typeorm";
import { ProductCategory } from "./product-category.entity";
import { CreateProductCategoryDto } from "./dto/create-product-category.dto";
import { UpdateProductCategoryDto } from "./dto/update-product-category.dto";
import {
  ProductCategoryDto,
  toProductCategoryDto,
} from "./dto/product-category.dto";
import {
  ProductCategoryPaginationDto,
  ProductCategoryListResponseDto,
} from "./dto/pagination.dto";
import { PaginationQueryDto, PaginatedResponseDto } from "../dto/pagination.dto";
import { Uuid7 } from "../utils/uuid7";
import { UsersService } from "../users/users.service";

@Injectable()
export class ProductCategoriesService {
  constructor(
    @InjectRepository(ProductCategory)
    private productCategoryRepository: Repository<ProductCategory>,
    private usersService: UsersService,
  ) {}

  async create(
    createProductCategoryDto: CreateProductCategoryDto,
    userUuid: string,
  ): Promise<ProductCategoryDto> {
    // Check if category with same name already exists in the same warehouse
    const existingCategory = await this.productCategoryRepository.findOne({
      where: {
        name: createProductCategoryDto.name,
        warehouseUuid: createProductCategoryDto.warehouseUuid,
        isDeleted: false,
      },
    });

    if (existingCategory) {
      throw new ConflictException(
        `Product category with name '${createProductCategoryDto.name}' already exists in this warehouse`,
      );
    }

    // Use userUuid from DTO if available, otherwise use the provided userUuid parameter
    const effectiveUserUuid = createProductCategoryDto.userUuid || userUuid;

    const productCategory = this.productCategoryRepository.create({
      id: new Uuid7().toString(),
      name: createProductCategoryDto.name,
      warehouseUuid: createProductCategoryDto.warehouseUuid,
      createdBy: effectiveUserUuid,
      updatedBy: effectiveUserUuid,
    });

    const savedCategory = await this.productCategoryRepository.save(productCategory);
    return toProductCategoryDto(savedCategory);
  }

  async findAll(
    paginationDto: ProductCategoryPaginationDto,
    warehouseUuid?: string,
  ): Promise<ProductCategoryListResponseDto> {
    const { page = 1, limit = 10 } = paginationDto;
    const skip = (page - 1) * limit;

    const whereCondition: any = { isDeleted: false };
    if (warehouseUuid) {
      whereCondition.warehouseUuid = warehouseUuid;
    }

    const [categories, total] = await this.productCategoryRepository.findAndCount({
      where: whereCondition,
      order: { name: 'ASC' },
      skip,
      take: limit,
    });

    const totalPages = Math.ceil(total / limit);
    const enrichedCategories =
      await this.enrichCategoriesWithUserNames(categories);

    return {
      data: enrichedCategories,
      page,
      limit,
      total,
      totalPages,
    };
  }

  async findOne(uuid: string): Promise<ProductCategoryDto> {
    const productCategory = await this.productCategoryRepository.findOne({
      where: {
        id: uuid,
        isDeleted: false,
      },
    });

    if (!productCategory) {
      throw new NotFoundException(
        `Product category with UUID '${uuid}' not found`,
      );
    }

    const [enrichedCategory] = await this.enrichCategoriesWithUserNames([
      productCategory,
    ]);
    return enrichedCategory;
  }

  async update(
    uuid: string,
    updateProductCategoryDto: UpdateProductCategoryDto,
    userUuid: string,
  ): Promise<ProductCategoryDto> {
    const productCategory = await this.productCategoryRepository.findOne({
      where: {
        id: uuid,
        isDeleted: false,
      },
    });

    if (!productCategory) {
      throw new NotFoundException(
        `Product category with UUID '${uuid}' not found`,
      );
    }

    // If name is being updated, check for conflicts within the same warehouse
    if (
      updateProductCategoryDto.name &&
      updateProductCategoryDto.name !== productCategory.name
    ) {
      const existingCategory = await this.productCategoryRepository.findOne({
        where: {
          name: updateProductCategoryDto.name,
          warehouseUuid: productCategory.warehouseUuid,
          isDeleted: false,
        },
      });

      if (existingCategory && existingCategory.id !== uuid) {
        throw new ConflictException(
          `Product category with name '${updateProductCategoryDto.name}' already exists in this warehouse`,
        );
      }
    }

    // Use userUuid from DTO if available, otherwise use the provided userUuid parameter
    const effectiveUserUuid = updateProductCategoryDto.userUuid || userUuid;

    Object.assign(productCategory, updateProductCategoryDto);
    productCategory.updatedBy = effectiveUserUuid;
    const updatedCategory = await this.productCategoryRepository.save(productCategory);
    return toProductCategoryDto(updatedCategory);
  }

  async remove(uuid: string): Promise<void> {
    const productCategory = await this.productCategoryRepository.findOne({
      where: {
        id: uuid,
        isDeleted: false,
      },
    });

    if (!productCategory) {
      throw new NotFoundException(
        `Product category with UUID '${uuid}' not found`,
      );
    }

    productCategory.isDeleted = true;
    await this.productCategoryRepository.save(productCategory);
  }

  async findByName(
    name: string,
    paginationDto: ProductCategoryPaginationDto,
    warehouseUuid?: string,
  ): Promise<ProductCategoryListResponseDto> {
    const { page = 1, limit = 10 } = paginationDto;
    const skip = (page - 1) * limit;

    const whereCondition: any = {
      name: Like(`%${name}%`),
      isDeleted: false,
    };
    if (warehouseUuid) {
      whereCondition.warehouseUuid = warehouseUuid;
    }

    const [categories, total] = await this.productCategoryRepository.findAndCount({
      where: whereCondition,
      order: { name: 'ASC' },
      skip,
      take: limit,
    });

    const totalPages = Math.ceil(total / limit);
    const enrichedCategories =
      await this.enrichCategoriesWithUserNames(categories);

    return {
      data: enrichedCategories,
      page,
      limit,
      total,
      totalPages,
    };
  }

  private async enrichCategoriesWithUserNames(
    categories: ProductCategory[],
  ): Promise<ProductCategoryDto[]> {
    if (categories.length === 0) return [];

    // Collect all unique user UUIDs
    const userUuids = new Set<string>();
    categories.forEach((category) => {
      if (category.createdBy) {
        userUuids.add(category.createdBy);
      }
      if (category.updatedBy) {
        userUuids.add(category.updatedBy);
      }
    });

    // Fetch user information
    const users = await this.usersService.findMany(Array.from(userUuids));
    const userMap = new Map(
      users.map((u) => [u.uuid, u.name || u.email || "Unknown User"]),
    );

    // Enrich categories with user names
    return categories.map((category) => {
      const categoryDto = toProductCategoryDto(category);

      // Add user names to the DTO
      if (categoryDto.createdByString) {
        (categoryDto as any).createdByName =
          userMap.get(categoryDto.createdByString) || "Unknown User";
      }
      if (categoryDto.updatedByString) {
        (categoryDto as any).updatedByName =
          userMap.get(categoryDto.updatedByString) || "Unknown User";
      }

      return categoryDto;
    });
  }
} 