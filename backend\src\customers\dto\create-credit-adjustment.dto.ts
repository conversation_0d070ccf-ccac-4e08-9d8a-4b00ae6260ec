import {
  Is<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>n,
  <PERSON><PERSON><PERSON>ber,
  IsNotEmpty,
} from "class-validator";
import { ApiProperty } from "@nestjs/swagger";
import { CreditAdjustmentType } from "../credit-adjustment.entity";

export class CreateCreditAdjustmentDto {
  @IsUUID("all")
  @IsNotEmpty()
  @ApiProperty({
    example: "uuid-v7-string",
    description: "UUID of the customer",
  })
  customerUuid: string;

  @IsUUID("all")
  @IsNotEmpty()
  @ApiProperty({
    example: "uuid-v7-string",
    description: "UUID of the user making the adjustment",
  })
  userUuid: string;

  @IsUUID("all")
  @IsNotEmpty()
  @ApiProperty({
    example: "uuid-v7-string",
    description: "UUID of the warehouse",
  })
  warehouseUuid: string;

  @IsUUID("all")
  @IsOptional()
  @ApiProperty({
    example: "uuid-v7-string",
    description: "UUID of the related sale (optional)",
    required: false,
  })
  saleUuid?: string;

  @IsString()
  @IsIn(Object.values(CreditAdjustmentType))
  @ApiProperty({
    example: "sale",
    description: "Type of credit adjustment",
    enum: Object.values(CreditAdjustmentType),
  })
  adjustmentType: CreditAdjustmentType;

  @IsNumber()
  @IsNotEmpty()
  @ApiProperty({
    example: 100.0,
    description:
      "Amount to adjust (positive for increase, negative for decrease)",
  })
  amountAdjusted: number;

  @IsString()
  @IsNotEmpty()
  @ApiProperty({
    example: "Sale payment received",
    description: "Reason for the credit adjustment",
  })
  reason: string;
}
