import {
  <PERSON><PERSON>ptional,
  Is<PERSON><PERSON>,
  <PERSON>In,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>Int,
  IsUUID,
} from "class-validator";
import { ApiProperty } from "@nestjs/swagger";
import { Type } from "class-transformer";

export class FilterCustomerDto {
  @IsString()
  @IsOptional()
  @ApiProperty({
    example: "<PERSON>",
    description: "Filter by customer name (partial match)",
    required: false,
  })
  name?: string;

  @IsString()
  @IsOptional()
  @ApiProperty({
    example: "<EMAIL>",
    description: "Filter by email (partial match)",
    required: false,
  })
  email?: string;

  @IsString()
  @IsIn(["retail", "wholesale", "mid-wholesale", "institutional"])
  @IsOptional()
  @ApiProperty({
    example: "retail",
    description: "Filter by customer type",
    enum: ["retail", "wholesale", "mid-wholesale", "institutional"],
    required: false,
  })
  customerType?: "retail" | "wholesale" | "mid-wholesale" | "institutional";

  @IsOptional()
  @IsUUID("7")
  @ApiProperty({
    example: "uuid-v7-string",
    description: "Filter by warehouse UUID",
    required: false,
  })
  warehouseUuid?: string;

  @IsOptional()
  @IsUUID("7")
  @ApiProperty({
    example: "uuid-v7-string",
    description: "Filter by region UUID",
    required: false,
  })
  regionUuid?: string;

  @IsNumber()
  @IsOptional()
  @ApiProperty({
    example: 100.0,
    description: "Filter by exact credit balance",
    required: false,
  })
  currentCredit?: number;

  @IsNumber()
  @Min(0)
  @IsOptional()
  @ApiProperty({
    example: 50.0,
    description: "Filter by minimum credit balance (inclusive)",
    required: false,
  })
  minCredit?: number;

  @IsNumber()
  @Min(0)
  @IsOptional()
  @ApiProperty({
    example: 500.0,
    description: "Filter by maximum credit balance (inclusive)",
    required: false,
  })
  maxCredit?: number;

  @IsString()
  @IsIn([
    "name",
    "email",
    "customerType",
    "currentCredit",
    "createdAt",
    "updatedAt",
  ])
  @IsOptional()
  @ApiProperty({
    example: "currentCredit",
    description: "Sort by field",
    enum: [
      "name",
      "email",
      "customerType",
      "currentCredit",
      "createdAt",
      "updatedAt",
    ],
    required: false,
  })
  sortBy?: string;

  @IsString()
  @IsIn(["asc", "desc"])
  @IsOptional()
  @ApiProperty({
    example: "desc",
    description: "Sort order",
    enum: ["asc", "desc"],
    required: false,
  })
  sortOrder?: "asc" | "desc";


}
