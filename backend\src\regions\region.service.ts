import { Injectable, NotFoundException } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository, Like, ILike } from "typeorm";
import { Region } from "./region.entity";
import { CreateRegionDto } from "./dto/create-region.dto";
import { UpdateRegionDto } from "./dto/update-region.dto";
import { FilterRegionDto } from "./dto/filter-region.dto";
import {
  RegionResponseDto,
  toRegionResponseDto,
} from "./dto/region-response.dto";
import { HardDeleteResponseDto } from "./dto/hard-delete-response.dto";
import {
  PaginationQueryDto,
  PaginatedResponseDto,
} from "../dto/pagination.dto";
import { Uuid7 } from "../utils/uuid7";

@Injectable()
export class RegionService {
  constructor(
    @InjectRepository(Region)
    private regionRepository: Repository<Region>,
  ) {}

  async create(createRegionDto: CreateRegionDto): Promise<RegionResponseDto> {
    const region = new Region();
    region.id = Region.generateId();
    region.name = createRegionDto.name;
    region.description = createRegionDto.description;
    region.latitude = createRegionDto.latitude;
    region.longitude = createRegionDto.longitude;
    region.warehouseUuid = createRegionDto.warehouseUuid;

    const savedRegion = await this.regionRepository.save(region);
    return toRegionResponseDto(savedRegion);
  }

  async findAll(
    paginationQuery: PaginationQueryDto,
    filter: FilterRegionDto,
  ): Promise<PaginatedResponseDto<RegionResponseDto>> {
    const { page = 1, limit = 10 } = paginationQuery;
    const skip = (page - 1) * limit;

    // Build filter query
    const whereConditions: any = { isDeleted: false };

    if (filter.warehouseUuid) {
      whereConditions.warehouseUuid = filter.warehouseUuid;
    }

    if (filter.name) {
      whereConditions.name = ILike(`%${filter.name}%`);
    }

    if (filter.description) {
      whereConditions.description = ILike(`%${filter.description}%`);
    }

    const [regions, total] = await this.regionRepository.findAndCount({
      where: whereConditions,
      skip,
      take: limit,
      order: { createdAt: 'DESC' },
    });

    const regionDtos = regions.map(toRegionResponseDto);

    return new PaginatedResponseDto(regionDtos, total, page, limit);
  }

  async findOne(uuid: string): Promise<RegionResponseDto> {
    const region = await this.regionRepository.findOne({
      where: { id: uuid, isDeleted: false },
    });

    if (!region) {
      throw new NotFoundException("Region not found");
    }

    return toRegionResponseDto(region);
  }

  async update(
    uuid: string,
    updateRegionDto: UpdateRegionDto,
  ): Promise<RegionResponseDto> {
    const region = await this.regionRepository.findOne({
      where: { id: uuid, isDeleted: false },
    });

    if (!region) {
      throw new NotFoundException("Region not found");
    }

    // Update fields if provided
    if (updateRegionDto.name !== undefined) {
      region.name = updateRegionDto.name;
    }
    if (updateRegionDto.description !== undefined) {
      region.description = updateRegionDto.description;
    }
    if (updateRegionDto.latitude !== undefined) {
      region.latitude = updateRegionDto.latitude;
    }
    if (updateRegionDto.longitude !== undefined) {
      region.longitude = updateRegionDto.longitude;
    }
    if (updateRegionDto.warehouseUuid !== undefined) {
      region.warehouseUuid = updateRegionDto.warehouseUuid;
    }

    const updatedRegion = await this.regionRepository.save(region);
    return toRegionResponseDto(updatedRegion);
  }

  async remove(uuid: string): Promise<void> {
    const region = await this.regionRepository.findOne({
      where: { id: uuid, isDeleted: false },
    });

    if (!region) {
      throw new NotFoundException("Region not found");
    }

    region.isDeleted = true;
    await this.regionRepository.save(region);
  }

  async hardDeleteAll(): Promise<HardDeleteResponseDto> {
    const result = await this.regionRepository.delete({});
    return {
      success: true,
      deletedCount: result.affected || 0,
      message: `Successfully deleted ${result.affected || 0} regions`,
    };
  }
} 