import { ApiProperty } from "@nestjs/swagger";
import { IsOptional, IsPositive, IsInt, Min, Max } from "class-validator";
import { Type } from "class-transformer";

export class PaginationQueryDto {
  @ApiProperty({
    example: 1,
    description: "Page number (1-based)",
    required: false,
    minimum: 1,
    default: 1,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  page?: number = 1;

  @ApiProperty({
    example: 10,
    description: "Number of items per page",
    required: false,
    minimum: 1,
    maximum: 100,
    default: 10,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @Max(100)
  limit?: number = 10;
}

export class PaginationMetaDto {
  @ApiProperty({ example: 50, description: "Total number of items" })
  total: number;

  @ApiProperty({ example: 1, description: "Current page number" })
  page: number;

  @ApiProperty({ example: 10, description: "Number of items per page" })
  limit: number;

  @ApiProperty({ example: 5, description: "Total number of pages" })
  totalPages: number;

  @ApiProperty({ example: true, description: "Whether there is a next page" })
  hasNext: boolean;

  @ApiProperty({
    example: false,
    description: "Whether there is a previous page",
  })
  hasPrev: boolean;
}

export class PaginatedResponseDto<T> {
  @ApiProperty({ description: "Array of items for the current page" })
  data: T[];

  @ApiProperty({ description: "Pagination metadata" })
  meta: PaginationMetaDto;

  constructor(data: T[], total: number, page: number, limit: number) {
    this.data = data;
    const totalPages = Math.ceil(total / limit);
    this.meta = {
      total,
      page,
      limit,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    };
  }
} 