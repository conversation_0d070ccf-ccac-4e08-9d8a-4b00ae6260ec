#!/usr/bin/env dart

/// Debug script to test Google authentication setup
/// Run with: dart debug_google_auth.dart

import 'dart:convert';
import 'dart:io';

Future<void> main() async {
  print('=== DIDO DISTRIBUTION GOOGLE AUTH DEBUG ===\n');
  
  // Test 1: Check backend connectivity
  await testBackendConnectivity();
  
  // Test 2: Check Google OAuth endpoints
  await testGoogleEndpoints();
  
  // Test 3: Check mobile configuration files
  await checkMobileConfig();
  
  print('\n=== DEBUG COMPLETE ===');
  print('Next steps:');
  print('1. Fix any issues identified above');
  print('2. Follow the setup guide in mobile/setup_env.md');
  print('3. Run the mobile app and check debug logs');
}

Future<void> testBackendConnectivity() async {
  print('1. Testing Backend Connectivity...');
  
  final baseUrls = [
    'http://localhost:8000',
    'http://********:8000',
    'http://127.0.0.1:8000',
  ];
  
  for (final url in baseUrls) {
    try {
      final client = HttpClient();
      final request = await client.getUrl(Uri.parse('$url/docs'));
      request.headers.set('Content-Type', 'application/json');
      
      final response = await request.close();
      final responseBody = await response.transform(utf8.decoder).join();
      
      print('   ✓ $url - Status: ${response.statusCode}');
      if (response.statusCode != 200) {
        print('     Response: $responseBody');
      }
      
      client.close();
    } catch (e) {
      print('   ✗ $url - Error: $e');
    }
  }
}

Future<void> testGoogleEndpoints() async {
  print('\n2. Testing Google OAuth Endpoints...');
  
  const baseUrl = 'http://localhost:8000'; // Change if needed
  final endpoints = [
    '/auth/google',
    '/auth/google/callback',
    '/auth/google/token',
  ];
  
  for (final endpoint in endpoints) {
    try {
      final client = HttpClient();
      final request = await client.getUrl(Uri.parse('$baseUrl$endpoint'));
      request.headers.set('Content-Type', 'application/json');
      
      final response = await request.close();
      final responseBody = await response.transform(utf8.decoder).join();
      
      print('   Endpoint: $endpoint');
      print('   Status: ${response.statusCode}');
      
      if (endpoint == '/auth/google/token' && response.statusCode == 400) {
        print('   ✓ Expected 400 for POST endpoint without data');
      } else if (response.statusCode == 302 && endpoint == '/auth/google') {
        print('   ✓ Expected redirect for Google OAuth');
      } else if (response.statusCode == 404) {
        print('   ✗ Endpoint not found - check backend implementation');
      }
      
      client.close();
    } catch (e) {
      print('   ✗ $endpoint - Error: $e');
    }
  }
}

Future<void> checkMobileConfig() async {
  print('\n3. Checking Mobile Configuration...');
  
  // Check Android strings.xml
  final androidStrings = File('android/app/src/main/res/values/strings.xml');
  if (await androidStrings.exists()) {
    final content = await androidStrings.readAsString();
    if (content.contains('YOUR_ANDROID_CLIENT_ID_HERE')) {
      print('   ✗ Android: strings.xml still contains placeholder');
      print('     Update: android/app/src/main/res/values/strings.xml');
    } else {
      print('   ✓ Android: strings.xml appears configured');
    }
  } else {
    print('   ✗ Android: strings.xml not found');
  }
  
  // Check iOS Info.plist
  final iosInfo = File('ios/Runner/Info.plist');
  if (await iosInfo.exists()) {
    final content = await iosInfo.readAsString();
    if (content.contains('your_ios_oauth_client_id_here')) {
      print('   ✗ iOS: Info.plist still contains placeholder');
      print('     Update: ios/Runner/Info.plist');
    } else {
      print('   ✓ iOS: Info.plist appears configured');
    }
  } else {
    print('   ✗ iOS: Info.plist not found');
  }
  
  // Check .env file
  final envFile = File('.env');
  if (await envFile.exists()) {
    print('   ✓ .env file exists');
    final content = await envFile.readAsString();
    if (content.contains('YOUR_GOOGLE_CLIENT_SECRET_HERE')) {
      print('   ⚠ .env file contains placeholder values');
    }
  } else {
    print('   ✗ .env file not found');
    print('     Create .env file from config_template.env');
  }
  
  // Check pubspec.yaml for google_sign_in
  final pubspec = File('pubspec.yaml');
  if (await pubspec.exists()) {
    final content = await pubspec.readAsString();
    if (content.contains('google_sign_in:')) {
      print('   ✓ google_sign_in dependency found in pubspec.yaml');
    } else {
      print('   ✗ google_sign_in dependency missing from pubspec.yaml');
    }
  }
} 