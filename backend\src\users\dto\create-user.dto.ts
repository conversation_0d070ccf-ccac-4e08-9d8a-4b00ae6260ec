import { ApiProperty } from "@nestjs/swagger";

export class CreateUserDto {
  @ApiProperty({
    example: "018ea2bb-6b8d-7e9a-8a2a-7e9a8a2a7e9a",
    description:
      "UUIDv7 of the user responsible for creating this user (optional, for audit and permission checks).",
    required: false,
  })
  creatorUserUuid?: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description:
      "The UUID of the warehouse the user belongs to. If omitted, a new warehouse will be created automatically.",
    required: false,
  })
  warehouseUuid?: string;

  @ApiProperty({ example: "<EMAIL>", description: "Email address" })
  email: string;

  @ApiProperty({ example: "<PERSON> Do<PERSON>", description: "User name" })
  name: string;

  @ApiProperty({
    example: "password123",
    description: "User password",
    required: false,
  })
  password?: string;

  @ApiProperty({
    example: "018ea2bb-6b8d-7e9a-8a2a-7e9a8a2a7e9a",
    description: "UUIDv7 of the user role to assign (see /roles endpoints).",
    required: false,
  })
  roleUuid?: string;

  @ApiProperty({
    example: "user",
    description: "User type: 'super' (owner) or 'user' (staff/admin)",
    required: false,
    enum: ["super", "user"],
    default: "user",
  })
  userType?: "super" | "user";
}
