"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/logs/page",{

/***/ "(app-pages-browser)/./app/logs/components/LogDetailsModal.tsx":
/*!*************************************************!*\
  !*** ./app/logs/components/LogDetailsModal.tsx ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LogDetailsModal: function() { return /* binding */ LogDetailsModal; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_FiCalendar_FiDatabase_FiFileText_FiTag_FiUser_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=FiCalendar,FiDatabase,FiFileText,FiTag,FiUser,FiX!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n\nvar _s = $RefreshSig$();\n\n\nfunction LogDetailsModal(param) {\n    let { isOpen, log, onClose, userName } = param;\n    _s();\n    // Handle escape key to close modal\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!isOpen) return;\n        const handleKeyDown = (e)=>{\n            if (e.key === \"Escape\") {\n                e.preventDefault();\n                onClose();\n            }\n        };\n        window.addEventListener(\"keydown\", handleKeyDown);\n        return ()=>window.removeEventListener(\"keydown\", handleKeyDown);\n    }, [\n        isOpen,\n        onClose\n    ]);\n    // Handle clicking outside modal to close\n    const handleOverlayClick = (e)=>{\n        if (e.target === e.currentTarget) {\n            onClose();\n        }\n    };\n    // Format date for display\n    const formatDate = (date)=>{\n        const dateObj = typeof date === \"string\" ? new Date(date) : date;\n        return dateObj.toLocaleString(\"en-US\", {\n            year: \"numeric\",\n            month: \"long\",\n            day: \"numeric\",\n            hour: \"2-digit\",\n            minute: \"2-digit\",\n            second: \"2-digit\"\n        });\n    };\n    // Get operation color based on operation type\n    const getOperationColor = (operation)=>{\n        const op = operation.toLowerCase();\n        if (op.includes(\"create\") || op.includes(\"add\")) {\n            return \"bg-green-100 text-green-800 border-green-200\";\n        } else if (op.includes(\"update\") || op.includes(\"edit\") || op.includes(\"modify\")) {\n            return \"bg-blue-100 text-blue-800 border-blue-200\";\n        } else if (op.includes(\"delete\") || op.includes(\"remove\")) {\n            return \"bg-red-100 text-red-800 border-red-200\";\n        } else if (op.includes(\"cancel\") || op.includes(\"reject\")) {\n            return \"bg-orange-100 text-orange-800 border-orange-200\";\n        } else if (op.includes(\"approve\") || op.includes(\"complete\")) {\n            return \"bg-purple-100 text-purple-800 border-purple-200\";\n        } else if (op.includes(\"transfer\") || op.includes(\"move\")) {\n            return \"bg-indigo-100 text-indigo-800 border-indigo-200\";\n        } else if (op.includes(\"adjust\") || op.includes(\"modify\")) {\n            return \"bg-yellow-100 text-yellow-800 border-yellow-200\";\n        } else {\n            return \"bg-gray-100 text-gray-800 border-gray-200\";\n        }\n    };\n    // Check if the log data contains delta changes\n    const hasDeltaChanges = (data)=>{\n        return data && data.changes && typeof data.changes === \"object\";\n    };\n    // Check if an array contains business items (has id, name, price, quantity, etc.)\n    const isBusinessItemsArray = (arr)=>{\n        if (!arr || arr.length === 0) return false;\n        const firstItem = arr[0];\n        return typeof firstItem === \"object\" && firstItem !== null && (\"id\" in firstItem || \"name\" in firstItem || \"price\" in firstItem || \"quantity\" in firstItem || \"productUuid\" in firstItem);\n    };\n    // Render business item with key details highlighted\n    const renderBusinessItem = (item)=>{\n        if (!item || typeof item !== \"object\") {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-100 p-2 rounded text-xs\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-gray-500\",\n                    children: [\n                        \"Invalid item: \",\n                        String(item)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                    lineNumber: 89,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                lineNumber: 88,\n                columnNumber: 9\n            }, this);\n        }\n        const { id, productUuid, name, price, unitPrice, quantity, lineTotal, ...otherProps } = item;\n        const displayPrice = price || unitPrice;\n        // If no recognizable properties, show the raw object\n        if (!name && !id && !productUuid && quantity === undefined && displayPrice === undefined && !lineTotal) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-100 p-2 rounded text-xs font-mono\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                    children: JSON.stringify(item, null, 2)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                    lineNumber: 101,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                lineNumber: 100,\n                columnNumber: 9\n            }, this);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-2\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-wrap gap-3\",\n                    children: [\n                        name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs text-gray-500\",\n                                    children: \"Name:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-medium text-gray-900\",\n                                    children: name\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 13\n                        }, this),\n                        quantity !== undefined && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs text-gray-500\",\n                                    children: \"Qty:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-medium text-purple-600\",\n                                    children: quantity\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 118,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                            lineNumber: 116,\n                            columnNumber: 13\n                        }, this),\n                        displayPrice !== undefined && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs text-gray-500\",\n                                    children: \"Price:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-medium text-green-600\",\n                                    children: [\n                                        \"$\",\n                                        displayPrice\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 124,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 13\n                        }, this),\n                        lineTotal !== undefined && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs text-gray-500\",\n                                    children: \"Total:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 129,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-medium text-blue-600\",\n                                    children: [\n                                        \"$\",\n                                        lineTotal\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 130,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                            lineNumber: 128,\n                            columnNumber: 13\n                        }, this),\n                        (id !== undefined || productUuid !== undefined) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs text-gray-500\",\n                                    children: \"ID:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-mono text-xs text-gray-600\",\n                                    children: id || productUuid\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                            lineNumber: 134,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                    lineNumber: 108,\n                    columnNumber: 9\n                }, this),\n                Object.keys(otherProps).length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-xs text-gray-500\",\n                    children: [\n                        \"+\",\n                        Object.keys(otherProps).length,\n                        \" other properties\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                    lineNumber: 141,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n            lineNumber: 107,\n            columnNumber: 7\n        }, this);\n    };\n    // Render items snapshot side by side\n    const renderItemsSnapshot = (beforeItems, afterItems)=>{\n        // Ensure we have arrays to work with\n        const before = Array.isArray(beforeItems) ? beforeItems : [];\n        const after = Array.isArray(afterItems) ? afterItems : [];\n        // Check if either array contains business items, or if this looks like an items field\n        const hasBusinessItems = isBusinessItemsArray(before) || isBusinessItemsArray(after);\n        // Always show the snapshot if we have arrays (even if they don't look like business items)\n        // This handles cases where the detection might fail but we still want to show the items\n        if (!hasBusinessItems && before.length === 0 && after.length === 0) {\n            return null;\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm font-medium text-red-700 mb-3 flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"w-2 h-2 bg-red-500 rounded-full\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 13\n                                }, this),\n                                \"Old Items (\",\n                                before.length,\n                                \" items)\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                            lineNumber: 168,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3 max-h-96 overflow-y-auto\",\n                            children: before.length > 0 ? before.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-red-50 border border-red-200 rounded p-3\",\n                                    children: renderBusinessItem(item)\n                                }, index, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 17\n                                }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-gray-500 italic text-center py-4\",\n                                children: \"No items\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                lineNumber: 180,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                            lineNumber: 172,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                    lineNumber: 167,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm font-medium text-green-700 mb-3 flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"w-2 h-2 bg-green-500 rounded-full\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 13\n                                }, this),\n                                \"New Items (\",\n                                after.length,\n                                \" items)\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                            lineNumber: 187,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3 max-h-96 overflow-y-auto\",\n                            children: after.length > 0 ? after.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-green-50 border border-green-200 rounded p-3\",\n                                    children: renderBusinessItem(item)\n                                }, index, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 17\n                                }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-gray-500 italic text-center py-4\",\n                                children: \"No items\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                            lineNumber: 191,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                    lineNumber: 186,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n            lineNumber: 165,\n            columnNumber: 7\n        }, this);\n    };\n    // Check if a value looks like a date\n    const isDateValue = (value)=>{\n        if (typeof value === \"string\") {\n            // Check for ISO date format or common date patterns\n            return /^\\d{4}-\\d{2}-\\d{2}/.test(value) || /\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}/.test(value);\n        }\n        return false;\n    };\n    // Format a date value nicely\n    const formatDateValue = (value)=>{\n        try {\n            const date = new Date(value);\n            return date.toLocaleDateString(\"en-US\", {\n                year: \"numeric\",\n                month: \"short\",\n                day: \"numeric\",\n                hour: \"2-digit\",\n                minute: \"2-digit\"\n            });\n        } catch (e) {\n            return value;\n        }\n    };\n    // Simple value renderer\n    const renderSimpleValue = (value)=>{\n        if (value === null) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"text-gray-500 italic\",\n            children: \"null\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n            lineNumber: 234,\n            columnNumber: 32\n        }, this);\n        if (value === undefined) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"text-gray-500 italic\",\n            children: \"undefined\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n            lineNumber: 235,\n            columnNumber: 37\n        }, this);\n        if (typeof value === \"string\") {\n            // Check if it's a date string\n            if (isDateValue(value)) {\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-blue-600\",\n                    children: formatDateValue(value)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                    lineNumber: 239,\n                    columnNumber: 16\n                }, this);\n            }\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-blue-600\",\n                children: [\n                    '\"',\n                    value,\n                    '\"'\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                lineNumber: 241,\n                columnNumber: 14\n            }, this);\n        }\n        if (typeof value === \"number\") return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"text-purple-600\",\n            children: value\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n            lineNumber: 243,\n            columnNumber: 43\n        }, this);\n        if (typeof value === \"boolean\") return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"text-orange-600\",\n            children: String(value)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n            lineNumber: 244,\n            columnNumber: 44\n        }, this);\n        if (Array.isArray(value)) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-gray-600\",\n                children: [\n                    \"[\",\n                    value.length,\n                    \" items]\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                lineNumber: 246,\n                columnNumber: 14\n            }, this);\n        }\n        if (typeof value === \"object\") {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-100 p-2 rounded text-xs font-mono max-w-md overflow-x-auto\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                    children: JSON.stringify(value, null, 2)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                    lineNumber: 251,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                lineNumber: 250,\n                columnNumber: 9\n            }, this);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"text-gray-600\",\n            children: String(value)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n            lineNumber: 255,\n            columnNumber: 12\n        }, this);\n    };\n    // Render simple field changes\n    const renderFieldChange = (field, change)=>{\n        // Special handling for items arrays - check both field name and if arrays contain business items\n        const isItemsField = field.toLowerCase().includes(\"items\") || field.toLowerCase().includes(\"products\");\n        const hasBusinessItems = Array.isArray(change.before) && isBusinessItemsArray(change.before) || Array.isArray(change.after) && isBusinessItemsArray(change.after);\n        if (isItemsField && (Array.isArray(change.before) || Array.isArray(change.after))) {\n            return renderItemsSnapshot(change.before || [], change.after || []);\n        }\n        // Also handle any array that contains business items, regardless of field name\n        if (hasBusinessItems) {\n            return renderItemsSnapshot(change.before || [], change.after || []);\n        }\n        // Simple before/after display for other fields\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 gap-3\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs font-medium text-red-700 mb-1\",\n                            children: \"Before\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                            lineNumber: 278,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-red-50 border border-red-200 rounded p-3\",\n                            children: renderSimpleValue(change.before)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                            lineNumber: 279,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                    lineNumber: 277,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs font-medium text-green-700 mb-1\",\n                            children: \"After\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                            lineNumber: 284,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-green-50 border border-green-200 rounded p-3\",\n                            children: renderSimpleValue(change.after)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                            lineNumber: 285,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                    lineNumber: 283,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n            lineNumber: 276,\n            columnNumber: 7\n        }, this);\n    };\n    // Render delta changes in a clean format\n    const renderDeltaChanges = (changes)=>{\n        const changeEntries = Object.entries(changes);\n        if (changeEntries.length === 0) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-500 text-sm\",\n                    children: \"No changes detected\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                    lineNumber: 300,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                lineNumber: 299,\n                columnNumber: 9\n            }, this);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: changeEntries.map((param)=>{\n                let [field, change] = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border border-gray-200 rounded-lg p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                            className: \"font-medium text-gray-900 mb-4 capitalize\",\n                            children: field.replace(/([A-Z])/g, \" $1\").replace(/^./, (str)=>str.toUpperCase())\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                            lineNumber: 310,\n                            columnNumber: 15\n                        }, this),\n                        renderFieldChange(field, change)\n                    ]\n                }, field, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                    lineNumber: 309,\n                    columnNumber: 13\n                }, this);\n            })\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n            lineNumber: 306,\n            columnNumber: 7\n        }, this);\n    };\n    if (!isOpen || !log) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n        onClick: handleOverlayClick,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg max-w-6xl w-full mx-4 max-h-[90vh] overflow-hidden flex flex-col\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 border-b border-gray-200 flex-shrink-0 flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-bold text-gray-900\",\n                                    children: \"Log Details\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 329,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600 mt-1\",\n                                    children: \"View detailed information about this log entry\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 332,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                            lineNumber: 328,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"p-2 rounded-full hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors\",\n                            title: \"Close\",\n                            \"aria-label\": \"Close modal\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCalendar_FiDatabase_FiFileText_FiTag_FiUser_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiX, {\n                                className: \"h-5 w-5 text-gray-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                lineNumber: 342,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                            lineNumber: 336,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                    lineNumber: 327,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 overflow-y-auto p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                    children: \"Basic Information\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 350,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2 flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCalendar_FiDatabase_FiFileText_FiTag_FiUser_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiTag, {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                            lineNumber: 356,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"Operation\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                    lineNumber: 355,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border \".concat(getOperationColor(log.operation)),\n                                                    children: log.operation\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                    lineNumber: 359,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                            lineNumber: 354,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2 flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCalendar_FiDatabase_FiFileText_FiTag_FiUser_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiDatabase, {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                            lineNumber: 367,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"Entity\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                    lineNumber: 366,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-900 font-medium\",\n                                                    children: log.entity\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                    lineNumber: 370,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                            lineNumber: 365,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2 flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCalendar_FiDatabase_FiFileText_FiTag_FiUser_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiUser, {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                            lineNumber: 376,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"User\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                    lineNumber: 375,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-900 font-medium\",\n                                                    children: userName || \"Unknown User\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                    lineNumber: 379,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-500 mt-1\",\n                                                    children: log.userUuid\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                    lineNumber: 382,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                            lineNumber: 374,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2 flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCalendar_FiDatabase_FiFileText_FiTag_FiUser_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiCalendar, {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                            lineNumber: 388,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"Timestamp\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                    lineNumber: 387,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-900\",\n                                                    children: formatDate(log.createdAt)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                    lineNumber: 391,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                            lineNumber: 386,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 352,\n                                    columnNumber: 13\n                                }, this),\n                                log.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2 flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCalendar_FiDatabase_FiFileText_FiTag_FiUser_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiFileText, {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                    lineNumber: 399,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Description\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                            lineNumber: 398,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-900 bg-gray-50 p-3 rounded-lg border\",\n                                            children: log.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                            lineNumber: 402,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 397,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                            lineNumber: 349,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                    children: log.data && hasDeltaChanges(log.data) ? \"Changes\" : \"Additional Data\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 411,\n                                    columnNumber: 13\n                                }, this),\n                                log.data ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: hasDeltaChanges(log.data) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-3\",\n                                                    children: [\n                                                        \"Field Changes (\",\n                                                        log.data.changeCount || 0,\n                                                        \" changes)\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                    lineNumber: 421,\n                                                    columnNumber: 23\n                                                }, this),\n                                                renderDeltaChanges(log.data.changes)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                            lineNumber: 420,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                        lineNumber: 418,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                children: \"JSON Data\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                lineNumber: 429,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-900 text-green-400 p-4 rounded-lg border overflow-x-auto\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                    className: \"text-sm font-mono whitespace-pre-wrap\",\n                                                    children: JSON.stringify(log.data, null, 2)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                    lineNumber: 433,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                lineNumber: 432,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                        lineNumber: 428,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 416,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCalendar_FiDatabase_FiFileText_FiTag_FiUser_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiDatabase, {\n                                            className: \"w-12 h-12 text-gray-300 mx-auto mb-3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                            lineNumber: 442,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-500 text-sm\",\n                                            children: \"No additional data available\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                            lineNumber: 443,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 441,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                            lineNumber: 410,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"pt-6 border-t border-gray-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"Log ID\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 450,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                    className: \"text-xs text-gray-600 bg-gray-100 px-2 py-1 rounded\",\n                                    children: log.id\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 453,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                            lineNumber: 449,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                    lineNumber: 347,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 border-t border-gray-200 flex-shrink-0 flex justify-end\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onClose,\n                        className: \"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-400 transition-colors\",\n                        children: \"Close\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                        lineNumber: 461,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                    lineNumber: 460,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n            lineNumber: 325,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n        lineNumber: 324,\n        columnNumber: 5\n    }, this);\n}\n_s(LogDetailsModal, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\n_c = LogDetailsModal;\nvar _c;\n$RefreshReg$(_c, \"LogDetailsModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9sb2dzL2NvbXBvbmVudHMvTG9nRGV0YWlsc01vZGFsLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQXlDO0FBQytDO0FBVWpGLFNBQVNRLGdCQUFnQixLQUF3RDtRQUF4RCxFQUFFQyxNQUFNLEVBQUVDLEdBQUcsRUFBRUMsT0FBTyxFQUFFQyxRQUFRLEVBQXdCLEdBQXhEOztJQUM5QixtQ0FBbUM7SUFDbkNYLGdEQUFTQSxDQUFDO1FBQ1IsSUFBSSxDQUFDUSxRQUFRO1FBRWIsTUFBTUksZ0JBQWdCLENBQUNDO1lBQ3JCLElBQUlBLEVBQUVDLEdBQUcsS0FBSyxVQUFVO2dCQUN0QkQsRUFBRUUsY0FBYztnQkFDaEJMO1lBQ0Y7UUFDRjtRQUVBTSxPQUFPQyxnQkFBZ0IsQ0FBQyxXQUFXTDtRQUNuQyxPQUFPLElBQU1JLE9BQU9FLG1CQUFtQixDQUFDLFdBQVdOO0lBQ3JELEdBQUc7UUFBQ0o7UUFBUUU7S0FBUTtJQUVwQix5Q0FBeUM7SUFDekMsTUFBTVMscUJBQXFCLENBQUNOO1FBQzFCLElBQUlBLEVBQUVPLE1BQU0sS0FBS1AsRUFBRVEsYUFBYSxFQUFFO1lBQ2hDWDtRQUNGO0lBQ0Y7SUFFQSwwQkFBMEI7SUFDMUIsTUFBTVksYUFBYSxDQUFDQztRQUNsQixNQUFNQyxVQUFVLE9BQU9ELFNBQVMsV0FBVyxJQUFJRSxLQUFLRixRQUFRQTtRQUM1RCxPQUFPQyxRQUFRRSxjQUFjLENBQUMsU0FBUztZQUNyQ0MsTUFBTTtZQUNOQyxPQUFPO1lBQ1BDLEtBQUs7WUFDTEMsTUFBTTtZQUNOQyxRQUFRO1lBQ1JDLFFBQVE7UUFDVjtJQUNGO0lBRUEsOENBQThDO0lBQzlDLE1BQU1DLG9CQUFvQixDQUFDQztRQUN6QixNQUFNQyxLQUFLRCxVQUFVRSxXQUFXO1FBQ2hDLElBQUlELEdBQUdFLFFBQVEsQ0FBQyxhQUFhRixHQUFHRSxRQUFRLENBQUMsUUFBUTtZQUMvQyxPQUFPO1FBQ1QsT0FBTyxJQUFJRixHQUFHRSxRQUFRLENBQUMsYUFBYUYsR0FBR0UsUUFBUSxDQUFDLFdBQVdGLEdBQUdFLFFBQVEsQ0FBQyxXQUFXO1lBQ2hGLE9BQU87UUFDVCxPQUFPLElBQUlGLEdBQUdFLFFBQVEsQ0FBQyxhQUFhRixHQUFHRSxRQUFRLENBQUMsV0FBVztZQUN6RCxPQUFPO1FBQ1QsT0FBTyxJQUFJRixHQUFHRSxRQUFRLENBQUMsYUFBYUYsR0FBR0UsUUFBUSxDQUFDLFdBQVc7WUFDekQsT0FBTztRQUNULE9BQU8sSUFBSUYsR0FBR0UsUUFBUSxDQUFDLGNBQWNGLEdBQUdFLFFBQVEsQ0FBQyxhQUFhO1lBQzVELE9BQU87UUFDVCxPQUFPLElBQUlGLEdBQUdFLFFBQVEsQ0FBQyxlQUFlRixHQUFHRSxRQUFRLENBQUMsU0FBUztZQUN6RCxPQUFPO1FBQ1QsT0FBTyxJQUFJRixHQUFHRSxRQUFRLENBQUMsYUFBYUYsR0FBR0UsUUFBUSxDQUFDLFdBQVc7WUFDekQsT0FBTztRQUNULE9BQU87WUFDTCxPQUFPO1FBQ1Q7SUFDRjtJQUVBLCtDQUErQztJQUMvQyxNQUFNQyxrQkFBa0IsQ0FBQ0M7UUFDdkIsT0FBT0EsUUFBUUEsS0FBS0MsT0FBTyxJQUFJLE9BQU9ELEtBQUtDLE9BQU8sS0FBSztJQUN6RDtJQUVBLGtGQUFrRjtJQUNsRixNQUFNQyx1QkFBdUIsQ0FBQ0M7UUFDNUIsSUFBSSxDQUFDQSxPQUFPQSxJQUFJQyxNQUFNLEtBQUssR0FBRyxPQUFPO1FBQ3JDLE1BQU1DLFlBQVlGLEdBQUcsQ0FBQyxFQUFFO1FBQ3hCLE9BQU8sT0FBT0UsY0FBYyxZQUNyQkEsY0FBYyxRQUNiLFNBQVFBLGFBQWEsVUFBVUEsYUFBYSxXQUFXQSxhQUFhLGNBQWNBLGFBQWEsaUJBQWlCQSxTQUFRO0lBQ2xJO0lBRUEsb0RBQW9EO0lBQ3BELE1BQU1DLHFCQUFxQixDQUFDQztRQUMxQixJQUFJLENBQUNBLFFBQVEsT0FBT0EsU0FBUyxVQUFVO1lBQ3JDLHFCQUNFLDhEQUFDQztnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ0M7b0JBQUtELFdBQVU7O3dCQUFnQjt3QkFBZUUsT0FBT0o7Ozs7Ozs7Ozs7OztRQUc1RDtRQUVBLE1BQU0sRUFBRUssRUFBRSxFQUFFQyxXQUFXLEVBQUVDLElBQUksRUFBRUMsS0FBSyxFQUFFQyxTQUFTLEVBQUVDLFFBQVEsRUFBRUMsU0FBUyxFQUFFLEdBQUdDLFlBQVksR0FBR1o7UUFDeEYsTUFBTWEsZUFBZUwsU0FBU0M7UUFFOUIscURBQXFEO1FBQ3JELElBQUksQ0FBQ0YsUUFBUSxDQUFDRixNQUFNLENBQUNDLGVBQWVJLGFBQWFJLGFBQWFELGlCQUFpQkMsYUFBYSxDQUFDSCxXQUFXO1lBQ3RHLHFCQUNFLDhEQUFDVjtnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ2E7OEJBQUtDLEtBQUtDLFNBQVMsQ0FBQ2pCLE1BQU0sTUFBTTs7Ozs7Ozs7Ozs7UUFHdkM7UUFFQSxxQkFDRSw4REFBQ0M7WUFBSUMsV0FBVTs7OEJBQ2IsOERBQUNEO29CQUFJQyxXQUFVOzt3QkFDWkssc0JBQ0MsOERBQUNOOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0M7b0NBQUtELFdBQVU7OENBQXdCOzs7Ozs7OENBQ3hDLDhEQUFDQztvQ0FBS0QsV0FBVTs4Q0FBNkJLOzs7Ozs7Ozs7Ozs7d0JBR2hERyxhQUFhSSwyQkFDWiw4REFBQ2I7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDQztvQ0FBS0QsV0FBVTs4Q0FBd0I7Ozs7Ozs4Q0FDeEMsOERBQUNDO29DQUFLRCxXQUFVOzhDQUErQlE7Ozs7Ozs7Ozs7Ozt3QkFHbERHLGlCQUFpQkMsMkJBQ2hCLDhEQUFDYjs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNDO29DQUFLRCxXQUFVOzhDQUF3Qjs7Ozs7OzhDQUN4Qyw4REFBQ0M7b0NBQUtELFdBQVU7O3dDQUE2Qjt3Q0FBRVc7Ozs7Ozs7Ozs7Ozs7d0JBR2xERixjQUFjRywyQkFDYiw4REFBQ2I7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDQztvQ0FBS0QsV0FBVTs4Q0FBd0I7Ozs7Ozs4Q0FDeEMsOERBQUNDO29DQUFLRCxXQUFVOzt3Q0FBNEI7d0NBQUVTOzs7Ozs7Ozs7Ozs7O3dCQUdoRE4sQ0FBQUEsT0FBT1MsYUFBYVIsZ0JBQWdCUSxTQUFRLG1CQUM1Qyw4REFBQ2I7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDQztvQ0FBS0QsV0FBVTs4Q0FBd0I7Ozs7Ozs4Q0FDeEMsOERBQUNDO29DQUFLRCxXQUFVOzhDQUFtQ0csTUFBTUM7Ozs7Ozs7Ozs7Ozs7Ozs7OztnQkFJOURZLE9BQU9DLElBQUksQ0FBQ1AsWUFBWWYsTUFBTSxHQUFHLG1CQUNoQyw4REFBQ0k7b0JBQUlDLFdBQVU7O3dCQUF3Qjt3QkFDbkNnQixPQUFPQyxJQUFJLENBQUNQLFlBQVlmLE1BQU07d0JBQUM7Ozs7Ozs7Ozs7Ozs7SUFLM0M7SUFFQSxxQ0FBcUM7SUFDckMsTUFBTXVCLHNCQUFzQixDQUFDQyxhQUFvQkM7UUFDL0MscUNBQXFDO1FBQ3JDLE1BQU1DLFNBQVNDLE1BQU1DLE9BQU8sQ0FBQ0osZUFBZUEsY0FBYyxFQUFFO1FBQzVELE1BQU1LLFFBQVFGLE1BQU1DLE9BQU8sQ0FBQ0gsY0FBY0EsYUFBYSxFQUFFO1FBRXpELHNGQUFzRjtRQUN0RixNQUFNSyxtQkFBbUJoQyxxQkFBcUI0QixXQUFXNUIscUJBQXFCK0I7UUFFOUUsMkZBQTJGO1FBQzNGLHdGQUF3RjtRQUN4RixJQUFJLENBQUNDLG9CQUFvQkosT0FBTzFCLE1BQU0sS0FBSyxLQUFLNkIsTUFBTTdCLE1BQU0sS0FBSyxHQUFHO1lBQ2xFLE9BQU87UUFDVDtRQUVBLHFCQUNFLDhEQUFDSTtZQUFJQyxXQUFVOzs4QkFFYiw4REFBQ0Q7O3NDQUNDLDhEQUFDQTs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNDO29DQUFLRCxXQUFVOzs7Ozs7Z0NBQXlDO2dDQUM3Q3FCLE9BQU8xQixNQUFNO2dDQUFDOzs7Ozs7O3NDQUU1Qiw4REFBQ0k7NEJBQUlDLFdBQVU7c0NBQ1pxQixPQUFPMUIsTUFBTSxHQUFHLElBQ2YwQixPQUFPSyxHQUFHLENBQUMsQ0FBQzVCLE1BQU02QixzQkFDaEIsOERBQUM1QjtvQ0FBZ0JDLFdBQVU7OENBQ3hCSCxtQkFBbUJDO21DQURaNkI7Ozs7MERBS1osOERBQUM1QjtnQ0FBSUMsV0FBVTswQ0FBd0M7Ozs7Ozs7Ozs7Ozs7Ozs7OzhCQU03RCw4REFBQ0Q7O3NDQUNDLDhEQUFDQTs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNDO29DQUFLRCxXQUFVOzs7Ozs7Z0NBQTJDO2dDQUMvQ3dCLE1BQU03QixNQUFNO2dDQUFDOzs7Ozs7O3NDQUUzQiw4REFBQ0k7NEJBQUlDLFdBQVU7c0NBQ1p3QixNQUFNN0IsTUFBTSxHQUFHLElBQ2Q2QixNQUFNRSxHQUFHLENBQUMsQ0FBQzVCLE1BQU02QixzQkFDZiw4REFBQzVCO29DQUFnQkMsV0FBVTs4Q0FDeEJILG1CQUFtQkM7bUNBRFo2Qjs7OzswREFLWiw4REFBQzVCO2dDQUFJQyxXQUFVOzBDQUF3Qzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7SUFNbkU7SUFFQSxxQ0FBcUM7SUFDckMsTUFBTTRCLGNBQWMsQ0FBQ0M7UUFDbkIsSUFBSSxPQUFPQSxVQUFVLFVBQVU7WUFDN0Isb0RBQW9EO1lBQ3BELE9BQU8scUJBQXFCQyxJQUFJLENBQUNELFVBQVUsc0NBQXNDQyxJQUFJLENBQUNEO1FBQ3hGO1FBQ0EsT0FBTztJQUNUO0lBRUEsNkJBQTZCO0lBQzdCLE1BQU1FLGtCQUFrQixDQUFDRjtRQUN2QixJQUFJO1lBQ0YsTUFBTXRELE9BQU8sSUFBSUUsS0FBS29EO1lBQ3RCLE9BQU90RCxLQUFLeUQsa0JBQWtCLENBQUMsU0FBUztnQkFDdENyRCxNQUFNO2dCQUNOQyxPQUFPO2dCQUNQQyxLQUFLO2dCQUNMQyxNQUFNO2dCQUNOQyxRQUFRO1lBQ1Y7UUFDRixFQUFFLFVBQU07WUFDTixPQUFPOEM7UUFDVDtJQUNGO0lBRUEsd0JBQXdCO0lBQ3hCLE1BQU1JLG9CQUFvQixDQUFDSjtRQUN6QixJQUFJQSxVQUFVLE1BQU0scUJBQU8sOERBQUM1QjtZQUFLRCxXQUFVO3NCQUF1Qjs7Ozs7O1FBQ2xFLElBQUk2QixVQUFVakIsV0FBVyxxQkFBTyw4REFBQ1g7WUFBS0QsV0FBVTtzQkFBdUI7Ozs7OztRQUN2RSxJQUFJLE9BQU82QixVQUFVLFVBQVU7WUFDN0IsOEJBQThCO1lBQzlCLElBQUlELFlBQVlDLFFBQVE7Z0JBQ3RCLHFCQUFPLDhEQUFDNUI7b0JBQUtELFdBQVU7OEJBQWlCK0IsZ0JBQWdCRjs7Ozs7O1lBQzFEO1lBQ0EscUJBQU8sOERBQUM1QjtnQkFBS0QsV0FBVTs7b0JBQWdCO29CQUFFNkI7b0JBQU07Ozs7Ozs7UUFDakQ7UUFDQSxJQUFJLE9BQU9BLFVBQVUsVUFBVSxxQkFBTyw4REFBQzVCO1lBQUtELFdBQVU7c0JBQW1CNkI7Ozs7OztRQUN6RSxJQUFJLE9BQU9BLFVBQVUsV0FBVyxxQkFBTyw4REFBQzVCO1lBQUtELFdBQVU7c0JBQW1CRSxPQUFPMkI7Ozs7OztRQUNqRixJQUFJUCxNQUFNQyxPQUFPLENBQUNNLFFBQVE7WUFDeEIscUJBQU8sOERBQUM1QjtnQkFBS0QsV0FBVTs7b0JBQWdCO29CQUFFNkIsTUFBTWxDLE1BQU07b0JBQUM7Ozs7Ozs7UUFDeEQ7UUFDQSxJQUFJLE9BQU9rQyxVQUFVLFVBQVU7WUFDN0IscUJBQ0UsOERBQUM5QjtnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ2E7OEJBQUtDLEtBQUtDLFNBQVMsQ0FBQ2MsT0FBTyxNQUFNOzs7Ozs7Ozs7OztRQUd4QztRQUNBLHFCQUFPLDhEQUFDNUI7WUFBS0QsV0FBVTtzQkFBaUJFLE9BQU8yQjs7Ozs7O0lBQ2pEO0lBRUEsOEJBQThCO0lBQzlCLE1BQU1LLG9CQUFvQixDQUFDQyxPQUFlQztRQUN4QyxpR0FBaUc7UUFDakcsTUFBTUMsZUFBZUYsTUFBTS9DLFdBQVcsR0FBR0MsUUFBUSxDQUFDLFlBQVk4QyxNQUFNL0MsV0FBVyxHQUFHQyxRQUFRLENBQUM7UUFDM0YsTUFBTW9DLG1CQUFtQixNQUFPRixPQUFPLENBQUNhLE9BQU9mLE1BQU0sS0FBSzVCLHFCQUFxQjJDLE9BQU9mLE1BQU0sS0FDbkVDLE1BQU1DLE9BQU8sQ0FBQ2EsT0FBT1osS0FBSyxLQUFLL0IscUJBQXFCMkMsT0FBT1osS0FBSztRQUV6RixJQUFJYSxnQkFBaUJmLENBQUFBLE1BQU1DLE9BQU8sQ0FBQ2EsT0FBT2YsTUFBTSxLQUFLQyxNQUFNQyxPQUFPLENBQUNhLE9BQU9aLEtBQUssSUFBSTtZQUNqRixPQUFPTixvQkFBb0JrQixPQUFPZixNQUFNLElBQUksRUFBRSxFQUFFZSxPQUFPWixLQUFLLElBQUksRUFBRTtRQUNwRTtRQUVBLCtFQUErRTtRQUMvRSxJQUFJQyxrQkFBa0I7WUFDcEIsT0FBT1Asb0JBQW9Ca0IsT0FBT2YsTUFBTSxJQUFJLEVBQUUsRUFBRWUsT0FBT1osS0FBSyxJQUFJLEVBQUU7UUFDcEU7UUFFQSwrQ0FBK0M7UUFDL0MscUJBQ0UsOERBQUN6QjtZQUFJQyxXQUFVOzs4QkFDYiw4REFBQ0Q7O3NDQUNDLDhEQUFDQTs0QkFBSUMsV0FBVTtzQ0FBd0M7Ozs7OztzQ0FDdkQsOERBQUNEOzRCQUFJQyxXQUFVO3NDQUNaaUMsa0JBQWtCRyxPQUFPZixNQUFNOzs7Ozs7Ozs7Ozs7OEJBR3BDLDhEQUFDdEI7O3NDQUNDLDhEQUFDQTs0QkFBSUMsV0FBVTtzQ0FBMEM7Ozs7OztzQ0FDekQsOERBQUNEOzRCQUFJQyxXQUFVO3NDQUNaaUMsa0JBQWtCRyxPQUFPWixLQUFLOzs7Ozs7Ozs7Ozs7Ozs7Ozs7SUFLekM7SUFFQSx5Q0FBeUM7SUFDekMsTUFBTWMscUJBQXFCLENBQUM5QztRQUMxQixNQUFNK0MsZ0JBQWdCdkIsT0FBT3dCLE9BQU8sQ0FBQ2hEO1FBRXJDLElBQUkrQyxjQUFjNUMsTUFBTSxLQUFLLEdBQUc7WUFDOUIscUJBQ0UsOERBQUNJO2dCQUFJQyxXQUFVOzBCQUNiLDRFQUFDeUM7b0JBQUV6QyxXQUFVOzhCQUF3Qjs7Ozs7Ozs7Ozs7UUFHM0M7UUFFQSxxQkFDRSw4REFBQ0Q7WUFBSUMsV0FBVTtzQkFDWnVDLGNBQWNiLEdBQUcsQ0FBQztvQkFBQyxDQUFDUyxPQUFPQyxPQUFPO2dCQUNqQyxxQkFDRSw4REFBQ3JDO29CQUFnQkMsV0FBVTs7c0NBQ3pCLDhEQUFDMEM7NEJBQUcxQyxXQUFVO3NDQUNYbUMsTUFBTVEsT0FBTyxDQUFDLFlBQVksT0FBT0EsT0FBTyxDQUFDLE1BQU1DLENBQUFBLE1BQU9BLElBQUlDLFdBQVc7Ozs7Ozt3QkFFdkVYLGtCQUFrQkMsT0FBT0M7O21CQUpsQkQ7Ozs7O1lBT2Q7Ozs7OztJQUdOO0lBRUEsSUFBSSxDQUFDM0UsVUFBVSxDQUFDQyxLQUFLLE9BQU87SUFFNUIscUJBQ0UsOERBQUNzQztRQUFJQyxXQUFVO1FBQTZFOEMsU0FBUzNFO2tCQUNuRyw0RUFBQzRCO1lBQUlDLFdBQVU7OzhCQUViLDhEQUFDRDtvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUNEOzs4Q0FDQyw4REFBQ2dEO29DQUFHL0MsV0FBVTs4Q0FBa0M7Ozs7Ozs4Q0FHaEQsOERBQUN5QztvQ0FBRXpDLFdBQVU7OENBQTZCOzs7Ozs7Ozs7Ozs7c0NBSTVDLDhEQUFDZ0Q7NEJBQ0NGLFNBQVNwRjs0QkFDVHNDLFdBQVU7NEJBQ1ZpRCxPQUFNOzRCQUNOQyxjQUFXO3NDQUVYLDRFQUFDakcsd0hBQUdBO2dDQUFDK0MsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7OEJBS25CLDhEQUFDRDtvQkFBSUMsV0FBVTs7c0NBRWIsOERBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ21EO29DQUFHbkQsV0FBVTs4Q0FBMkM7Ozs7Ozs4Q0FFekQsOERBQUNEO29DQUFJQyxXQUFVOztzREFFYiw4REFBQ0Q7OzhEQUNDLDhEQUFDcUQ7b0RBQU1wRCxXQUFVOztzRUFDZiw4REFBQzVDLDBIQUFLQTs0REFBQzRDLFdBQVU7Ozs7Ozt3REFBWTs7Ozs7Ozs4REFHL0IsOERBQUNDO29EQUFLRCxXQUFXLDhFQUErRyxPQUFqQ2Ysa0JBQWtCeEIsSUFBSXlCLFNBQVM7OERBQzNIekIsSUFBSXlCLFNBQVM7Ozs7Ozs7Ozs7OztzREFLbEIsOERBQUNhOzs4REFDQyw4REFBQ3FEO29EQUFNcEQsV0FBVTs7c0VBQ2YsOERBQUMxQywrSEFBVUE7NERBQUMwQyxXQUFVOzs7Ozs7d0RBQVk7Ozs7Ozs7OERBR3BDLDhEQUFDQztvREFBS0QsV0FBVTs4REFBNkJ2QyxJQUFJNEYsTUFBTTs7Ozs7Ozs7Ozs7O3NEQUl6RCw4REFBQ3REOzs4REFDQyw4REFBQ3FEO29EQUFNcEQsV0FBVTs7c0VBQ2YsOERBQUM5QywySEFBTUE7NERBQUM4QyxXQUFVOzs7Ozs7d0RBQVk7Ozs7Ozs7OERBR2hDLDhEQUFDQztvREFBS0QsV0FBVTs4REFDYnJDLFlBQVk7Ozs7Ozs4REFFZiw4REFBQzhFO29EQUFFekMsV0FBVTs4REFBOEJ2QyxJQUFJNkYsUUFBUTs7Ozs7Ozs7Ozs7O3NEQUl6RCw4REFBQ3ZEOzs4REFDQyw4REFBQ3FEO29EQUFNcEQsV0FBVTs7c0VBQ2YsOERBQUM3QywrSEFBVUE7NERBQUM2QyxXQUFVOzs7Ozs7d0RBQVk7Ozs7Ozs7OERBR3BDLDhEQUFDQztvREFBS0QsV0FBVTs4REFBaUIxQixXQUFXYixJQUFJOEYsU0FBUzs7Ozs7Ozs7Ozs7Ozs7Ozs7O2dDQUs1RDlGLElBQUkrRixXQUFXLGtCQUNkLDhEQUFDekQ7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDb0Q7NENBQU1wRCxXQUFVOzs4REFDZiw4REFBQzNDLCtIQUFVQTtvREFBQzJDLFdBQVU7Ozs7OztnREFBWTs7Ozs7OztzREFHcEMsOERBQUN5Qzs0Q0FBRXpDLFdBQVU7c0RBQ1Z2QyxJQUFJK0YsV0FBVzs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQU94Qiw4REFBQ3pEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ21EO29DQUFHbkQsV0FBVTs4Q0FDWHZDLElBQUk4QixJQUFJLElBQUlELGdCQUFnQjdCLElBQUk4QixJQUFJLElBQUksWUFBWTs7Ozs7O2dDQUd0RDlCLElBQUk4QixJQUFJLGlCQUNQLDhEQUFDUTs4Q0FDRVQsZ0JBQWdCN0IsSUFBSThCLElBQUksa0JBQ3ZCLDhEQUFDUTtrREFFQyw0RUFBQ0E7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDb0Q7b0RBQU1wRCxXQUFVOzt3REFBK0M7d0RBQzlDdkMsSUFBSThCLElBQUksQ0FBQ2tFLFdBQVcsSUFBSTt3REFBRTs7Ozs7OztnREFFM0NuQixtQkFBbUI3RSxJQUFJOEIsSUFBSSxDQUFDQyxPQUFPOzs7Ozs7Ozs7Ozs2REFJeEMsOERBQUNPOzswREFDQyw4REFBQ3FEO2dEQUFNcEQsV0FBVTswREFBK0M7Ozs7OzswREFHaEUsOERBQUNEO2dEQUFJQyxXQUFVOzBEQUNiLDRFQUFDYTtvREFBSWIsV0FBVTs4REFDWmMsS0FBS0MsU0FBUyxDQUFDdEQsSUFBSThCLElBQUksRUFBRSxNQUFNOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7eURBTzFDLDhEQUFDUTtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUMxQywrSEFBVUE7NENBQUMwQyxXQUFVOzs7Ozs7c0RBQ3RCLDhEQUFDeUM7NENBQUV6QyxXQUFVO3NEQUF3Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQU0zQyw4REFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDb0Q7b0NBQU1wRCxXQUFVOzhDQUErQzs7Ozs7OzhDQUdoRSw4REFBQzBEO29DQUFLMUQsV0FBVTs4Q0FDYnZDLElBQUkwQyxFQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OEJBTWIsOERBQUNKO29CQUFJQyxXQUFVOzhCQUNiLDRFQUFDZ0Q7d0JBQ0NGLFNBQVNwRjt3QkFDVHNDLFdBQVU7a0NBQ1g7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFPWDtHQTNjZ0J6QztLQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9hcHAvbG9ncy9jb21wb25lbnRzL0xvZ0RldGFpbHNNb2RhbC50c3g/YjJkNCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUmVhY3QsIHsgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgRmlYLCBGaVVzZXIsIEZpQ2FsZW5kYXIsIEZpVGFnLCBGaUZpbGVUZXh0LCBGaURhdGFiYXNlIH0gZnJvbSAncmVhY3QtaWNvbnMvZmknO1xuaW1wb3J0IHR5cGUgeyBMb2cgfSBmcm9tICcuLi9sb2dzQXBpJztcblxuZXhwb3J0IGludGVyZmFjZSBMb2dEZXRhaWxzTW9kYWxQcm9wcyB7XG4gIGlzT3BlbjogYm9vbGVhbjtcbiAgbG9nOiBMb2cgfCBudWxsO1xuICBvbkNsb3NlOiAoKSA9PiB2b2lkO1xuICB1c2VyTmFtZT86IHN0cmluZztcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIExvZ0RldGFpbHNNb2RhbCh7IGlzT3BlbiwgbG9nLCBvbkNsb3NlLCB1c2VyTmFtZSB9OiBMb2dEZXRhaWxzTW9kYWxQcm9wcykge1xuICAvLyBIYW5kbGUgZXNjYXBlIGtleSB0byBjbG9zZSBtb2RhbFxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmICghaXNPcGVuKSByZXR1cm47XG4gICAgXG4gICAgY29uc3QgaGFuZGxlS2V5RG93biA9IChlOiBLZXlib2FyZEV2ZW50KSA9PiB7XG4gICAgICBpZiAoZS5rZXkgPT09ICdFc2NhcGUnKSB7XG4gICAgICAgIGUucHJldmVudERlZmF1bHQoKTtcbiAgICAgICAgb25DbG9zZSgpO1xuICAgICAgfVxuICAgIH07XG4gICAgXG4gICAgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoJ2tleWRvd24nLCBoYW5kbGVLZXlEb3duKTtcbiAgICByZXR1cm4gKCkgPT4gd2luZG93LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ2tleWRvd24nLCBoYW5kbGVLZXlEb3duKTtcbiAgfSwgW2lzT3Blbiwgb25DbG9zZV0pO1xuXG4gIC8vIEhhbmRsZSBjbGlja2luZyBvdXRzaWRlIG1vZGFsIHRvIGNsb3NlXG4gIGNvbnN0IGhhbmRsZU92ZXJsYXlDbGljayA9IChlOiBSZWFjdC5Nb3VzZUV2ZW50KSA9PiB7XG4gICAgaWYgKGUudGFyZ2V0ID09PSBlLmN1cnJlbnRUYXJnZXQpIHtcbiAgICAgIG9uQ2xvc2UoKTtcbiAgICB9XG4gIH07XG5cbiAgLy8gRm9ybWF0IGRhdGUgZm9yIGRpc3BsYXlcbiAgY29uc3QgZm9ybWF0RGF0ZSA9IChkYXRlOiBEYXRlIHwgc3RyaW5nKSA9PiB7XG4gICAgY29uc3QgZGF0ZU9iaiA9IHR5cGVvZiBkYXRlID09PSAnc3RyaW5nJyA/IG5ldyBEYXRlKGRhdGUpIDogZGF0ZTtcbiAgICByZXR1cm4gZGF0ZU9iai50b0xvY2FsZVN0cmluZygnZW4tVVMnLCB7XG4gICAgICB5ZWFyOiAnbnVtZXJpYycsXG4gICAgICBtb250aDogJ2xvbmcnLFxuICAgICAgZGF5OiAnbnVtZXJpYycsXG4gICAgICBob3VyOiAnMi1kaWdpdCcsXG4gICAgICBtaW51dGU6ICcyLWRpZ2l0JyxcbiAgICAgIHNlY29uZDogJzItZGlnaXQnLFxuICAgIH0pO1xuICB9O1xuXG4gIC8vIEdldCBvcGVyYXRpb24gY29sb3IgYmFzZWQgb24gb3BlcmF0aW9uIHR5cGVcbiAgY29uc3QgZ2V0T3BlcmF0aW9uQ29sb3IgPSAob3BlcmF0aW9uOiBzdHJpbmcpID0+IHtcbiAgICBjb25zdCBvcCA9IG9wZXJhdGlvbi50b0xvd2VyQ2FzZSgpO1xuICAgIGlmIChvcC5pbmNsdWRlcygnY3JlYXRlJykgfHwgb3AuaW5jbHVkZXMoJ2FkZCcpKSB7XG4gICAgICByZXR1cm4gJ2JnLWdyZWVuLTEwMCB0ZXh0LWdyZWVuLTgwMCBib3JkZXItZ3JlZW4tMjAwJztcbiAgICB9IGVsc2UgaWYgKG9wLmluY2x1ZGVzKCd1cGRhdGUnKSB8fCBvcC5pbmNsdWRlcygnZWRpdCcpIHx8IG9wLmluY2x1ZGVzKCdtb2RpZnknKSkge1xuICAgICAgcmV0dXJuICdiZy1ibHVlLTEwMCB0ZXh0LWJsdWUtODAwIGJvcmRlci1ibHVlLTIwMCc7XG4gICAgfSBlbHNlIGlmIChvcC5pbmNsdWRlcygnZGVsZXRlJykgfHwgb3AuaW5jbHVkZXMoJ3JlbW92ZScpKSB7XG4gICAgICByZXR1cm4gJ2JnLXJlZC0xMDAgdGV4dC1yZWQtODAwIGJvcmRlci1yZWQtMjAwJztcbiAgICB9IGVsc2UgaWYgKG9wLmluY2x1ZGVzKCdjYW5jZWwnKSB8fCBvcC5pbmNsdWRlcygncmVqZWN0JykpIHtcbiAgICAgIHJldHVybiAnYmctb3JhbmdlLTEwMCB0ZXh0LW9yYW5nZS04MDAgYm9yZGVyLW9yYW5nZS0yMDAnO1xuICAgIH0gZWxzZSBpZiAob3AuaW5jbHVkZXMoJ2FwcHJvdmUnKSB8fCBvcC5pbmNsdWRlcygnY29tcGxldGUnKSkge1xuICAgICAgcmV0dXJuICdiZy1wdXJwbGUtMTAwIHRleHQtcHVycGxlLTgwMCBib3JkZXItcHVycGxlLTIwMCc7XG4gICAgfSBlbHNlIGlmIChvcC5pbmNsdWRlcygndHJhbnNmZXInKSB8fCBvcC5pbmNsdWRlcygnbW92ZScpKSB7XG4gICAgICByZXR1cm4gJ2JnLWluZGlnby0xMDAgdGV4dC1pbmRpZ28tODAwIGJvcmRlci1pbmRpZ28tMjAwJztcbiAgICB9IGVsc2UgaWYgKG9wLmluY2x1ZGVzKCdhZGp1c3QnKSB8fCBvcC5pbmNsdWRlcygnbW9kaWZ5JykpIHtcbiAgICAgIHJldHVybiAnYmcteWVsbG93LTEwMCB0ZXh0LXllbGxvdy04MDAgYm9yZGVyLXllbGxvdy0yMDAnO1xuICAgIH0gZWxzZSB7XG4gICAgICByZXR1cm4gJ2JnLWdyYXktMTAwIHRleHQtZ3JheS04MDAgYm9yZGVyLWdyYXktMjAwJztcbiAgICB9XG4gIH07XG5cbiAgLy8gQ2hlY2sgaWYgdGhlIGxvZyBkYXRhIGNvbnRhaW5zIGRlbHRhIGNoYW5nZXNcbiAgY29uc3QgaGFzRGVsdGFDaGFuZ2VzID0gKGRhdGE6IFJlY29yZDxzdHJpbmcsIGFueT4pID0+IHtcbiAgICByZXR1cm4gZGF0YSAmJiBkYXRhLmNoYW5nZXMgJiYgdHlwZW9mIGRhdGEuY2hhbmdlcyA9PT0gJ29iamVjdCc7XG4gIH07XG5cbiAgLy8gQ2hlY2sgaWYgYW4gYXJyYXkgY29udGFpbnMgYnVzaW5lc3MgaXRlbXMgKGhhcyBpZCwgbmFtZSwgcHJpY2UsIHF1YW50aXR5LCBldGMuKVxuICBjb25zdCBpc0J1c2luZXNzSXRlbXNBcnJheSA9IChhcnI6IGFueVtdKSA9PiB7XG4gICAgaWYgKCFhcnIgfHwgYXJyLmxlbmd0aCA9PT0gMCkgcmV0dXJuIGZhbHNlO1xuICAgIGNvbnN0IGZpcnN0SXRlbSA9IGFyclswXTtcbiAgICByZXR1cm4gdHlwZW9mIGZpcnN0SXRlbSA9PT0gJ29iamVjdCcgJiZcbiAgICAgICAgICAgZmlyc3RJdGVtICE9PSBudWxsICYmXG4gICAgICAgICAgICgnaWQnIGluIGZpcnN0SXRlbSB8fCAnbmFtZScgaW4gZmlyc3RJdGVtIHx8ICdwcmljZScgaW4gZmlyc3RJdGVtIHx8ICdxdWFudGl0eScgaW4gZmlyc3RJdGVtIHx8ICdwcm9kdWN0VXVpZCcgaW4gZmlyc3RJdGVtKTtcbiAgfTtcblxuICAvLyBSZW5kZXIgYnVzaW5lc3MgaXRlbSB3aXRoIGtleSBkZXRhaWxzIGhpZ2hsaWdodGVkXG4gIGNvbnN0IHJlbmRlckJ1c2luZXNzSXRlbSA9IChpdGVtOiBhbnkpID0+IHtcbiAgICBpZiAoIWl0ZW0gfHwgdHlwZW9mIGl0ZW0gIT09ICdvYmplY3QnKSB7XG4gICAgICByZXR1cm4gKFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyYXktMTAwIHAtMiByb3VuZGVkIHRleHQteHNcIj5cbiAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNTAwXCI+SW52YWxpZCBpdGVtOiB7U3RyaW5nKGl0ZW0pfTwvc3Bhbj5cbiAgICAgICAgPC9kaXY+XG4gICAgICApO1xuICAgIH1cblxuICAgIGNvbnN0IHsgaWQsIHByb2R1Y3RVdWlkLCBuYW1lLCBwcmljZSwgdW5pdFByaWNlLCBxdWFudGl0eSwgbGluZVRvdGFsLCAuLi5vdGhlclByb3BzIH0gPSBpdGVtO1xuICAgIGNvbnN0IGRpc3BsYXlQcmljZSA9IHByaWNlIHx8IHVuaXRQcmljZTtcblxuICAgIC8vIElmIG5vIHJlY29nbml6YWJsZSBwcm9wZXJ0aWVzLCBzaG93IHRoZSByYXcgb2JqZWN0XG4gICAgaWYgKCFuYW1lICYmICFpZCAmJiAhcHJvZHVjdFV1aWQgJiYgcXVhbnRpdHkgPT09IHVuZGVmaW5lZCAmJiBkaXNwbGF5UHJpY2UgPT09IHVuZGVmaW5lZCAmJiAhbGluZVRvdGFsKSB7XG4gICAgICByZXR1cm4gKFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyYXktMTAwIHAtMiByb3VuZGVkIHRleHQteHMgZm9udC1tb25vXCI+XG4gICAgICAgICAgPHByZT57SlNPTi5zdHJpbmdpZnkoaXRlbSwgbnVsbCwgMil9PC9wcmU+XG4gICAgICAgIDwvZGl2PlxuICAgICAgKTtcbiAgICB9XG5cbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtd3JhcCBnYXAtM1wiPlxuICAgICAgICAgIHtuYW1lICYmIChcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTFcIj5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNTAwXCI+TmFtZTo8L3NwYW4+XG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDBcIj57bmFtZX08L3NwYW4+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICApfVxuICAgICAgICAgIHtxdWFudGl0eSAhPT0gdW5kZWZpbmVkICYmIChcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTFcIj5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNTAwXCI+UXR5Ojwvc3Bhbj5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gdGV4dC1wdXJwbGUtNjAwXCI+e3F1YW50aXR5fTwvc3Bhbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICl9XG4gICAgICAgICAge2Rpc3BsYXlQcmljZSAhPT0gdW5kZWZpbmVkICYmIChcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTFcIj5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNTAwXCI+UHJpY2U6PC9zcGFuPlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJmb250LW1lZGl1bSB0ZXh0LWdyZWVuLTYwMFwiPiR7ZGlzcGxheVByaWNlfTwvc3Bhbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICl9XG4gICAgICAgICAge2xpbmVUb3RhbCAhPT0gdW5kZWZpbmVkICYmIChcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTFcIj5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNTAwXCI+VG90YWw6PC9zcGFuPlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJmb250LW1lZGl1bSB0ZXh0LWJsdWUtNjAwXCI+JHtsaW5lVG90YWx9PC9zcGFuPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgKX1cbiAgICAgICAgICB7KGlkICE9PSB1bmRlZmluZWQgfHwgcHJvZHVjdFV1aWQgIT09IHVuZGVmaW5lZCkgJiYgKFxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMVwiPlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS01MDBcIj5JRDo8L3NwYW4+XG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImZvbnQtbW9ubyB0ZXh0LXhzIHRleHQtZ3JheS02MDBcIj57aWQgfHwgcHJvZHVjdFV1aWR9PC9zcGFuPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgKX1cbiAgICAgICAgPC9kaXY+XG4gICAgICAgIHtPYmplY3Qua2V5cyhvdGhlclByb3BzKS5sZW5ndGggPiAwICYmIChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTUwMFwiPlxuICAgICAgICAgICAgK3tPYmplY3Qua2V5cyhvdGhlclByb3BzKS5sZW5ndGh9IG90aGVyIHByb3BlcnRpZXNcbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKX1cbiAgICAgIDwvZGl2PlxuICAgICk7XG4gIH07XG5cbiAgLy8gUmVuZGVyIGl0ZW1zIHNuYXBzaG90IHNpZGUgYnkgc2lkZVxuICBjb25zdCByZW5kZXJJdGVtc1NuYXBzaG90ID0gKGJlZm9yZUl0ZW1zOiBhbnlbXSwgYWZ0ZXJJdGVtczogYW55W10pID0+IHtcbiAgICAvLyBFbnN1cmUgd2UgaGF2ZSBhcnJheXMgdG8gd29yayB3aXRoXG4gICAgY29uc3QgYmVmb3JlID0gQXJyYXkuaXNBcnJheShiZWZvcmVJdGVtcykgPyBiZWZvcmVJdGVtcyA6IFtdO1xuICAgIGNvbnN0IGFmdGVyID0gQXJyYXkuaXNBcnJheShhZnRlckl0ZW1zKSA/IGFmdGVySXRlbXMgOiBbXTtcblxuICAgIC8vIENoZWNrIGlmIGVpdGhlciBhcnJheSBjb250YWlucyBidXNpbmVzcyBpdGVtcywgb3IgaWYgdGhpcyBsb29rcyBsaWtlIGFuIGl0ZW1zIGZpZWxkXG4gICAgY29uc3QgaGFzQnVzaW5lc3NJdGVtcyA9IGlzQnVzaW5lc3NJdGVtc0FycmF5KGJlZm9yZSkgfHwgaXNCdXNpbmVzc0l0ZW1zQXJyYXkoYWZ0ZXIpO1xuXG4gICAgLy8gQWx3YXlzIHNob3cgdGhlIHNuYXBzaG90IGlmIHdlIGhhdmUgYXJyYXlzIChldmVuIGlmIHRoZXkgZG9uJ3QgbG9vayBsaWtlIGJ1c2luZXNzIGl0ZW1zKVxuICAgIC8vIFRoaXMgaGFuZGxlcyBjYXNlcyB3aGVyZSB0aGUgZGV0ZWN0aW9uIG1pZ2h0IGZhaWwgYnV0IHdlIHN0aWxsIHdhbnQgdG8gc2hvdyB0aGUgaXRlbXNcbiAgICBpZiAoIWhhc0J1c2luZXNzSXRlbXMgJiYgYmVmb3JlLmxlbmd0aCA9PT0gMCAmJiBhZnRlci5sZW5ndGggPT09IDApIHtcbiAgICAgIHJldHVybiBudWxsO1xuICAgIH1cblxuICAgIHJldHVybiAoXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbGc6Z3JpZC1jb2xzLTIgZ2FwLTZcIj5cbiAgICAgICAgey8qIE9sZCBJdGVtcyAqL31cbiAgICAgICAgPGRpdj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1yZWQtNzAwIG1iLTMgZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cbiAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInctMiBoLTIgYmctcmVkLTUwMCByb3VuZGVkLWZ1bGxcIj48L3NwYW4+XG4gICAgICAgICAgICBPbGQgSXRlbXMgKHtiZWZvcmUubGVuZ3RofSBpdGVtcylcbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMyBtYXgtaC05NiBvdmVyZmxvdy15LWF1dG9cIj5cbiAgICAgICAgICAgIHtiZWZvcmUubGVuZ3RoID4gMCA/IChcbiAgICAgICAgICAgICAgYmVmb3JlLm1hcCgoaXRlbSwgaW5kZXgpID0+IChcbiAgICAgICAgICAgICAgICA8ZGl2IGtleT17aW5kZXh9IGNsYXNzTmFtZT1cImJnLXJlZC01MCBib3JkZXIgYm9yZGVyLXJlZC0yMDAgcm91bmRlZCBwLTNcIj5cbiAgICAgICAgICAgICAgICAgIHtyZW5kZXJCdXNpbmVzc0l0ZW0oaXRlbSl9XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICkpXG4gICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtZ3JheS01MDAgaXRhbGljIHRleHQtY2VudGVyIHB5LTRcIj5ObyBpdGVtczwvZGl2PlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIE5ldyBJdGVtcyAqL31cbiAgICAgICAgPGRpdj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmVlbi03MDAgbWItMyBmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidy0yIGgtMiBiZy1ncmVlbi01MDAgcm91bmRlZC1mdWxsXCI+PC9zcGFuPlxuICAgICAgICAgICAgTmV3IEl0ZW1zICh7YWZ0ZXIubGVuZ3RofSBpdGVtcylcbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMyBtYXgtaC05NiBvdmVyZmxvdy15LWF1dG9cIj5cbiAgICAgICAgICAgIHthZnRlci5sZW5ndGggPiAwID8gKFxuICAgICAgICAgICAgICBhZnRlci5tYXAoKGl0ZW0sIGluZGV4KSA9PiAoXG4gICAgICAgICAgICAgICAgPGRpdiBrZXk9e2luZGV4fSBjbGFzc05hbWU9XCJiZy1ncmVlbi01MCBib3JkZXIgYm9yZGVyLWdyZWVuLTIwMCByb3VuZGVkIHAtM1wiPlxuICAgICAgICAgICAgICAgICAge3JlbmRlckJ1c2luZXNzSXRlbShpdGVtKX1cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgKSlcbiAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTUwMCBpdGFsaWMgdGV4dC1jZW50ZXIgcHktNFwiPk5vIGl0ZW1zPC9kaXY+XG4gICAgICAgICAgICApfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgICk7XG4gIH07XG5cbiAgLy8gQ2hlY2sgaWYgYSB2YWx1ZSBsb29rcyBsaWtlIGEgZGF0ZVxuICBjb25zdCBpc0RhdGVWYWx1ZSA9ICh2YWx1ZTogYW55KSA9PiB7XG4gICAgaWYgKHR5cGVvZiB2YWx1ZSA9PT0gJ3N0cmluZycpIHtcbiAgICAgIC8vIENoZWNrIGZvciBJU08gZGF0ZSBmb3JtYXQgb3IgY29tbW9uIGRhdGUgcGF0dGVybnNcbiAgICAgIHJldHVybiAvXlxcZHs0fS1cXGR7Mn0tXFxkezJ9Ly50ZXN0KHZhbHVlKSB8fCAvXFxkezR9LVxcZHsyfS1cXGR7Mn1UXFxkezJ9OlxcZHsyfTpcXGR7Mn0vLnRlc3QodmFsdWUpO1xuICAgIH1cbiAgICByZXR1cm4gZmFsc2U7XG4gIH07XG5cbiAgLy8gRm9ybWF0IGEgZGF0ZSB2YWx1ZSBuaWNlbHlcbiAgY29uc3QgZm9ybWF0RGF0ZVZhbHVlID0gKHZhbHVlOiBzdHJpbmcpID0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgZGF0ZSA9IG5ldyBEYXRlKHZhbHVlKTtcbiAgICAgIHJldHVybiBkYXRlLnRvTG9jYWxlRGF0ZVN0cmluZygnZW4tVVMnLCB7XG4gICAgICAgIHllYXI6ICdudW1lcmljJyxcbiAgICAgICAgbW9udGg6ICdzaG9ydCcsXG4gICAgICAgIGRheTogJ251bWVyaWMnLFxuICAgICAgICBob3VyOiAnMi1kaWdpdCcsXG4gICAgICAgIG1pbnV0ZTogJzItZGlnaXQnXG4gICAgICB9KTtcbiAgICB9IGNhdGNoIHtcbiAgICAgIHJldHVybiB2YWx1ZTtcbiAgICB9XG4gIH07XG5cbiAgLy8gU2ltcGxlIHZhbHVlIHJlbmRlcmVyXG4gIGNvbnN0IHJlbmRlclNpbXBsZVZhbHVlID0gKHZhbHVlOiBhbnkpID0+IHtcbiAgICBpZiAodmFsdWUgPT09IG51bGwpIHJldHVybiA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNTAwIGl0YWxpY1wiPm51bGw8L3NwYW4+O1xuICAgIGlmICh2YWx1ZSA9PT0gdW5kZWZpbmVkKSByZXR1cm4gPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTUwMCBpdGFsaWNcIj51bmRlZmluZWQ8L3NwYW4+O1xuICAgIGlmICh0eXBlb2YgdmFsdWUgPT09ICdzdHJpbmcnKSB7XG4gICAgICAvLyBDaGVjayBpZiBpdCdzIGEgZGF0ZSBzdHJpbmdcbiAgICAgIGlmIChpc0RhdGVWYWx1ZSh2YWx1ZSkpIHtcbiAgICAgICAgcmV0dXJuIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtYmx1ZS02MDBcIj57Zm9ybWF0RGF0ZVZhbHVlKHZhbHVlKX08L3NwYW4+O1xuICAgICAgfVxuICAgICAgcmV0dXJuIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtYmx1ZS02MDBcIj5cInt2YWx1ZX1cIjwvc3Bhbj47XG4gICAgfVxuICAgIGlmICh0eXBlb2YgdmFsdWUgPT09ICdudW1iZXInKSByZXR1cm4gPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1wdXJwbGUtNjAwXCI+e3ZhbHVlfTwvc3Bhbj47XG4gICAgaWYgKHR5cGVvZiB2YWx1ZSA9PT0gJ2Jvb2xlYW4nKSByZXR1cm4gPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1vcmFuZ2UtNjAwXCI+e1N0cmluZyh2YWx1ZSl9PC9zcGFuPjtcbiAgICBpZiAoQXJyYXkuaXNBcnJheSh2YWx1ZSkpIHtcbiAgICAgIHJldHVybiA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwXCI+W3t2YWx1ZS5sZW5ndGh9IGl0ZW1zXTwvc3Bhbj47XG4gICAgfVxuICAgIGlmICh0eXBlb2YgdmFsdWUgPT09ICdvYmplY3QnKSB7XG4gICAgICByZXR1cm4gKFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyYXktMTAwIHAtMiByb3VuZGVkIHRleHQteHMgZm9udC1tb25vIG1heC13LW1kIG92ZXJmbG93LXgtYXV0b1wiPlxuICAgICAgICAgIDxwcmU+e0pTT04uc3RyaW5naWZ5KHZhbHVlLCBudWxsLCAyKX08L3ByZT5cbiAgICAgICAgPC9kaXY+XG4gICAgICApO1xuICAgIH1cbiAgICByZXR1cm4gPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMFwiPntTdHJpbmcodmFsdWUpfTwvc3Bhbj47XG4gIH07XG5cbiAgLy8gUmVuZGVyIHNpbXBsZSBmaWVsZCBjaGFuZ2VzXG4gIGNvbnN0IHJlbmRlckZpZWxkQ2hhbmdlID0gKGZpZWxkOiBzdHJpbmcsIGNoYW5nZTogeyBiZWZvcmU6IGFueTsgYWZ0ZXI6IGFueSB9KSA9PiB7XG4gICAgLy8gU3BlY2lhbCBoYW5kbGluZyBmb3IgaXRlbXMgYXJyYXlzIC0gY2hlY2sgYm90aCBmaWVsZCBuYW1lIGFuZCBpZiBhcnJheXMgY29udGFpbiBidXNpbmVzcyBpdGVtc1xuICAgIGNvbnN0IGlzSXRlbXNGaWVsZCA9IGZpZWxkLnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoJ2l0ZW1zJykgfHwgZmllbGQudG9Mb3dlckNhc2UoKS5pbmNsdWRlcygncHJvZHVjdHMnKTtcbiAgICBjb25zdCBoYXNCdXNpbmVzc0l0ZW1zID0gKEFycmF5LmlzQXJyYXkoY2hhbmdlLmJlZm9yZSkgJiYgaXNCdXNpbmVzc0l0ZW1zQXJyYXkoY2hhbmdlLmJlZm9yZSkpIHx8XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgKEFycmF5LmlzQXJyYXkoY2hhbmdlLmFmdGVyKSAmJiBpc0J1c2luZXNzSXRlbXNBcnJheShjaGFuZ2UuYWZ0ZXIpKTtcblxuICAgIGlmIChpc0l0ZW1zRmllbGQgJiYgKEFycmF5LmlzQXJyYXkoY2hhbmdlLmJlZm9yZSkgfHwgQXJyYXkuaXNBcnJheShjaGFuZ2UuYWZ0ZXIpKSkge1xuICAgICAgcmV0dXJuIHJlbmRlckl0ZW1zU25hcHNob3QoY2hhbmdlLmJlZm9yZSB8fCBbXSwgY2hhbmdlLmFmdGVyIHx8IFtdKTtcbiAgICB9XG5cbiAgICAvLyBBbHNvIGhhbmRsZSBhbnkgYXJyYXkgdGhhdCBjb250YWlucyBidXNpbmVzcyBpdGVtcywgcmVnYXJkbGVzcyBvZiBmaWVsZCBuYW1lXG4gICAgaWYgKGhhc0J1c2luZXNzSXRlbXMpIHtcbiAgICAgIHJldHVybiByZW5kZXJJdGVtc1NuYXBzaG90KGNoYW5nZS5iZWZvcmUgfHwgW10sIGNoYW5nZS5hZnRlciB8fCBbXSk7XG4gICAgfVxuXG4gICAgLy8gU2ltcGxlIGJlZm9yZS9hZnRlciBkaXNwbGF5IGZvciBvdGhlciBmaWVsZHNcbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0yIGdhcC0zXCI+XG4gICAgICAgIDxkaXY+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhzIGZvbnQtbWVkaXVtIHRleHQtcmVkLTcwMCBtYi0xXCI+QmVmb3JlPC9kaXY+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1yZWQtNTAgYm9yZGVyIGJvcmRlci1yZWQtMjAwIHJvdW5kZWQgcC0zXCI+XG4gICAgICAgICAgICB7cmVuZGVyU2ltcGxlVmFsdWUoY2hhbmdlLmJlZm9yZSl9XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgICA8ZGl2PlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC14cyBmb250LW1lZGl1bSB0ZXh0LWdyZWVuLTcwMCBtYi0xXCI+QWZ0ZXI8L2Rpdj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyZWVuLTUwIGJvcmRlciBib3JkZXItZ3JlZW4tMjAwIHJvdW5kZWQgcC0zXCI+XG4gICAgICAgICAgICB7cmVuZGVyU2ltcGxlVmFsdWUoY2hhbmdlLmFmdGVyKX1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICApO1xuICB9O1xuXG4gIC8vIFJlbmRlciBkZWx0YSBjaGFuZ2VzIGluIGEgY2xlYW4gZm9ybWF0XG4gIGNvbnN0IHJlbmRlckRlbHRhQ2hhbmdlcyA9IChjaGFuZ2VzOiBSZWNvcmQ8c3RyaW5nLCB7IGJlZm9yZTogYW55OyBhZnRlcjogYW55IH0+KSA9PiB7XG4gICAgY29uc3QgY2hhbmdlRW50cmllcyA9IE9iamVjdC5lbnRyaWVzKGNoYW5nZXMpO1xuXG4gICAgaWYgKGNoYW5nZUVudHJpZXMubGVuZ3RoID09PSAwKSB7XG4gICAgICByZXR1cm4gKFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHB5LTRcIj5cbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNTAwIHRleHQtc21cIj5ObyBjaGFuZ2VzIGRldGVjdGVkPC9wPlxuICAgICAgICA8L2Rpdj5cbiAgICAgICk7XG4gICAgfVxuXG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS02XCI+XG4gICAgICAgIHtjaGFuZ2VFbnRyaWVzLm1hcCgoW2ZpZWxkLCBjaGFuZ2VdKSA9PiB7XG4gICAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICAgIDxkaXYga2V5PXtmaWVsZH0gY2xhc3NOYW1lPVwiYm9yZGVyIGJvcmRlci1ncmF5LTIwMCByb3VuZGVkLWxnIHAtNFwiPlxuICAgICAgICAgICAgICA8aDUgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMCBtYi00IGNhcGl0YWxpemVcIj5cbiAgICAgICAgICAgICAgICB7ZmllbGQucmVwbGFjZSgvKFtBLVpdKS9nLCAnICQxJykucmVwbGFjZSgvXi4vLCBzdHIgPT4gc3RyLnRvVXBwZXJDYXNlKCkpfVxuICAgICAgICAgICAgICA8L2g1PlxuICAgICAgICAgICAgICB7cmVuZGVyRmllbGRDaGFuZ2UoZmllbGQsIGNoYW5nZSl9XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICApO1xuICAgICAgICB9KX1cbiAgICAgIDwvZGl2PlxuICAgICk7XG4gIH07XG5cbiAgaWYgKCFpc09wZW4gfHwgIWxvZykgcmV0dXJuIG51bGw7XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cImZpeGVkIGluc2V0LTAgYmctYmxhY2sgYmctb3BhY2l0eS01MCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciB6LTUwXCIgb25DbGljaz17aGFuZGxlT3ZlcmxheUNsaWNrfT5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgcm91bmRlZC1sZyBzaGFkb3ctbGcgbWF4LXctNnhsIHctZnVsbCBteC00IG1heC1oLVs5MHZoXSBvdmVyZmxvdy1oaWRkZW4gZmxleCBmbGV4LWNvbFwiPlxuICAgICAgICB7LyogSGVhZGVyICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtNiBib3JkZXItYiBib3JkZXItZ3JheS0yMDAgZmxleC1zaHJpbmstMCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1ib2xkIHRleHQtZ3JheS05MDBcIj5cbiAgICAgICAgICAgICAgTG9nIERldGFpbHNcbiAgICAgICAgICAgIDwvaDM+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDAgbXQtMVwiPlxuICAgICAgICAgICAgICBWaWV3IGRldGFpbGVkIGluZm9ybWF0aW9uIGFib3V0IHRoaXMgbG9nIGVudHJ5XG4gICAgICAgICAgICA8L3A+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgb25DbGljaz17b25DbG9zZX1cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cInAtMiByb3VuZGVkLWZ1bGwgaG92ZXI6YmctZ3JheS0xMDAgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLWJsdWUtNTAwIHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgICAgICAgIHRpdGxlPVwiQ2xvc2VcIlxuICAgICAgICAgICAgYXJpYS1sYWJlbD1cIkNsb3NlIG1vZGFsXCJcbiAgICAgICAgICA+XG4gICAgICAgICAgICA8RmlYIGNsYXNzTmFtZT1cImgtNSB3LTUgdGV4dC1ncmF5LTUwMFwiIC8+XG4gICAgICAgICAgPC9idXR0b24+XG4gICAgICAgIDwvZGl2PlxuICAgICAgICBcbiAgICAgICAgey8qIENvbnRlbnQgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIG92ZXJmbG93LXktYXV0byBwLTZcIj5cbiAgICAgICAgICB7LyogQmFzaWMgSW5mb3JtYXRpb24gLSBDb21wYWN0IExheW91dCAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTZcIj5cbiAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTkwMCBtYi00XCI+QmFzaWMgSW5mb3JtYXRpb248L2g0PlxuICAgICAgICAgICAgXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgbGc6Z3JpZC1jb2xzLTQgZ2FwLTRcIj5cbiAgICAgICAgICAgICAgey8qIE9wZXJhdGlvbiAqL31cbiAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIG1iLTIgZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cbiAgICAgICAgICAgICAgICAgIDxGaVRhZyBjbGFzc05hbWU9XCJ3LTQgaC00XCIgLz5cbiAgICAgICAgICAgICAgICAgIE9wZXJhdGlvblxuICAgICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPXtgaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIHB4LTMgcHktMSByb3VuZGVkLWZ1bGwgdGV4dC1zbSBmb250LW1lZGl1bSBib3JkZXIgJHtnZXRPcGVyYXRpb25Db2xvcihsb2cub3BlcmF0aW9uKX1gfT5cbiAgICAgICAgICAgICAgICAgIHtsb2cub3BlcmF0aW9ufVxuICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgey8qIEVudGl0eSAqL31cbiAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIG1iLTIgZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cbiAgICAgICAgICAgICAgICAgIDxGaURhdGFiYXNlIGNsYXNzTmFtZT1cInctNCBoLTRcIiAvPlxuICAgICAgICAgICAgICAgICAgRW50aXR5XG4gICAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktOTAwIGZvbnQtbWVkaXVtXCI+e2xvZy5lbnRpdHl9PC9zcGFuPlxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICB7LyogVXNlciAqL31cbiAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIG1iLTIgZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cbiAgICAgICAgICAgICAgICAgIDxGaVVzZXIgY2xhc3NOYW1lPVwidy00IGgtNFwiIC8+XG4gICAgICAgICAgICAgICAgICBVc2VyXG4gICAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktOTAwIGZvbnQtbWVkaXVtXCI+XG4gICAgICAgICAgICAgICAgICB7dXNlck5hbWUgfHwgJ1Vua25vd24gVXNlcid9XG4gICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTUwMCBtdC0xXCI+e2xvZy51c2VyVXVpZH08L3A+XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIHsvKiBUaW1lc3RhbXAgKi99XG4gICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBtYi0yIGZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XG4gICAgICAgICAgICAgICAgICA8RmlDYWxlbmRhciBjbGFzc05hbWU9XCJ3LTQgaC00XCIgLz5cbiAgICAgICAgICAgICAgICAgIFRpbWVzdGFtcFxuICAgICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTkwMFwiPntmb3JtYXREYXRlKGxvZy5jcmVhdGVkQXQpfTwvc3Bhbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgey8qIERlc2NyaXB0aW9uIC0gRnVsbCBXaWR0aCAqL31cbiAgICAgICAgICAgIHtsb2cuZGVzY3JpcHRpb24gJiYgKFxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTRcIj5cbiAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIG1iLTIgZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cbiAgICAgICAgICAgICAgICAgIDxGaUZpbGVUZXh0IGNsYXNzTmFtZT1cInctNCBoLTRcIiAvPlxuICAgICAgICAgICAgICAgICAgRGVzY3JpcHRpb25cbiAgICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS05MDAgYmctZ3JheS01MCBwLTMgcm91bmRlZC1sZyBib3JkZXJcIj5cbiAgICAgICAgICAgICAgICAgIHtsb2cuZGVzY3JpcHRpb259XG4gICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7LyogRGF0YSBTZWN0aW9uIC0gRnVsbCBXaWR0aCAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTZcIj5cbiAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTkwMCBtYi00XCI+XG4gICAgICAgICAgICAgIHtsb2cuZGF0YSAmJiBoYXNEZWx0YUNoYW5nZXMobG9nLmRhdGEpID8gJ0NoYW5nZXMnIDogJ0FkZGl0aW9uYWwgRGF0YSd9XG4gICAgICAgICAgICA8L2g0PlxuXG4gICAgICAgICAgICB7bG9nLmRhdGEgPyAoXG4gICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAge2hhc0RlbHRhQ2hhbmdlcyhsb2cuZGF0YSkgPyAoXG4gICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICB7LyogRGVsdGEgQ2hhbmdlcyAqL31cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi02XCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBtYi0zXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICBGaWVsZCBDaGFuZ2VzICh7bG9nLmRhdGEuY2hhbmdlQ291bnQgfHwgMH0gY2hhbmdlcylcbiAgICAgICAgICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgIHtyZW5kZXJEZWx0YUNoYW5nZXMobG9nLmRhdGEuY2hhbmdlcyl9XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgbWItMlwiPlxuICAgICAgICAgICAgICAgICAgICAgIEpTT04gRGF0YVxuICAgICAgICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyYXktOTAwIHRleHQtZ3JlZW4tNDAwIHAtNCByb3VuZGVkLWxnIGJvcmRlciBvdmVyZmxvdy14LWF1dG9cIj5cbiAgICAgICAgICAgICAgICAgICAgICA8cHJlIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tb25vIHdoaXRlc3BhY2UtcHJlLXdyYXBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtKU09OLnN0cmluZ2lmeShsb2cuZGF0YSwgbnVsbCwgMil9XG4gICAgICAgICAgICAgICAgICAgICAgPC9wcmU+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHB5LThcIj5cbiAgICAgICAgICAgICAgICA8RmlEYXRhYmFzZSBjbGFzc05hbWU9XCJ3LTEyIGgtMTIgdGV4dC1ncmF5LTMwMCBteC1hdXRvIG1iLTNcIiAvPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS01MDAgdGV4dC1zbVwiPk5vIGFkZGl0aW9uYWwgZGF0YSBhdmFpbGFibGU8L3A+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIHsvKiBMb2cgSUQgKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwdC02IGJvcmRlci10IGJvcmRlci1ncmF5LTIwMFwiPlxuICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBtYi0yXCI+XG4gICAgICAgICAgICAgIExvZyBJRFxuICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgIDxjb2RlIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTYwMCBiZy1ncmF5LTEwMCBweC0yIHB5LTEgcm91bmRlZFwiPlxuICAgICAgICAgICAgICB7bG9nLmlkfVxuICAgICAgICAgICAgPC9jb2RlPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogRm9vdGVyICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtNiBib3JkZXItdCBib3JkZXItZ3JheS0yMDAgZmxleC1zaHJpbmstMCBmbGV4IGp1c3RpZnktZW5kXCI+XG4gICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgb25DbGljaz17b25DbG9zZX1cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cInB4LTQgcHktMiB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgYmctZ3JheS0xMDAgcm91bmRlZC1sZyBob3ZlcjpiZy1ncmF5LTIwMCBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctZ3JheS00MDAgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIENsb3NlXG4gICAgICAgICAgPC9idXR0b24+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgPC9kaXY+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VFZmZlY3QiLCJGaVgiLCJGaVVzZXIiLCJGaUNhbGVuZGFyIiwiRmlUYWciLCJGaUZpbGVUZXh0IiwiRmlEYXRhYmFzZSIsIkxvZ0RldGFpbHNNb2RhbCIsImlzT3BlbiIsImxvZyIsIm9uQ2xvc2UiLCJ1c2VyTmFtZSIsImhhbmRsZUtleURvd24iLCJlIiwia2V5IiwicHJldmVudERlZmF1bHQiLCJ3aW5kb3ciLCJhZGRFdmVudExpc3RlbmVyIiwicmVtb3ZlRXZlbnRMaXN0ZW5lciIsImhhbmRsZU92ZXJsYXlDbGljayIsInRhcmdldCIsImN1cnJlbnRUYXJnZXQiLCJmb3JtYXREYXRlIiwiZGF0ZSIsImRhdGVPYmoiLCJEYXRlIiwidG9Mb2NhbGVTdHJpbmciLCJ5ZWFyIiwibW9udGgiLCJkYXkiLCJob3VyIiwibWludXRlIiwic2Vjb25kIiwiZ2V0T3BlcmF0aW9uQ29sb3IiLCJvcGVyYXRpb24iLCJvcCIsInRvTG93ZXJDYXNlIiwiaW5jbHVkZXMiLCJoYXNEZWx0YUNoYW5nZXMiLCJkYXRhIiwiY2hhbmdlcyIsImlzQnVzaW5lc3NJdGVtc0FycmF5IiwiYXJyIiwibGVuZ3RoIiwiZmlyc3RJdGVtIiwicmVuZGVyQnVzaW5lc3NJdGVtIiwiaXRlbSIsImRpdiIsImNsYXNzTmFtZSIsInNwYW4iLCJTdHJpbmciLCJpZCIsInByb2R1Y3RVdWlkIiwibmFtZSIsInByaWNlIiwidW5pdFByaWNlIiwicXVhbnRpdHkiLCJsaW5lVG90YWwiLCJvdGhlclByb3BzIiwiZGlzcGxheVByaWNlIiwidW5kZWZpbmVkIiwicHJlIiwiSlNPTiIsInN0cmluZ2lmeSIsIk9iamVjdCIsImtleXMiLCJyZW5kZXJJdGVtc1NuYXBzaG90IiwiYmVmb3JlSXRlbXMiLCJhZnRlckl0ZW1zIiwiYmVmb3JlIiwiQXJyYXkiLCJpc0FycmF5IiwiYWZ0ZXIiLCJoYXNCdXNpbmVzc0l0ZW1zIiwibWFwIiwiaW5kZXgiLCJpc0RhdGVWYWx1ZSIsInZhbHVlIiwidGVzdCIsImZvcm1hdERhdGVWYWx1ZSIsInRvTG9jYWxlRGF0ZVN0cmluZyIsInJlbmRlclNpbXBsZVZhbHVlIiwicmVuZGVyRmllbGRDaGFuZ2UiLCJmaWVsZCIsImNoYW5nZSIsImlzSXRlbXNGaWVsZCIsInJlbmRlckRlbHRhQ2hhbmdlcyIsImNoYW5nZUVudHJpZXMiLCJlbnRyaWVzIiwicCIsImg1IiwicmVwbGFjZSIsInN0ciIsInRvVXBwZXJDYXNlIiwib25DbGljayIsImgzIiwiYnV0dG9uIiwidGl0bGUiLCJhcmlhLWxhYmVsIiwiaDQiLCJsYWJlbCIsImVudGl0eSIsInVzZXJVdWlkIiwiY3JlYXRlZEF0IiwiZGVzY3JpcHRpb24iLCJjaGFuZ2VDb3VudCIsImNvZGUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/logs/components/LogDetailsModal.tsx\n"));

/***/ })

});