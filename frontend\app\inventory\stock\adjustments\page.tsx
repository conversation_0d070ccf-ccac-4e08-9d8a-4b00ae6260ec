"use client";
/**
 * [Pattern] Inject warehouseUuid from AuthContext for all product queries/mutations.
 * Do NOT ask the user for warehouseUuid in forms. Use useAuth() and pass warehouseUuid to API calls.
 * Always use filterProducts(warehouseUuid, name?) as per backend contract.
 * See frontend/docs/CODE_GENERATION.md for details.
 */
import React, { useEffect, useState } from "react";
import { useForm, useFieldArray } from "react-hook-form";
import { useAuth } from "@/contexts/AuthContext";
import { Product } from "@/app/inventory/products/productsApi";
import toast from "react-hot-toast";
import { getStockLevels, getStockAdjustmentHistory, createStockAdjustments, getStoragesByWarehouse, StockAdjustmentHistoryResponse } from "./api";
import ProductSelectionModal from "./components/ProductSelectionModal";
import { Plus, Package, ChevronLeft, ChevronRight } from "lucide-react";

interface Storage {
  uuid: string;
  name: string;
}

interface AdjustmentRow {
  productUuid: string;
  productName?: string;
  sku?: string;
  currentStock?: number;
  adjustment: number;
  reason?: string;
}

export interface AdjustmentForm {
  storageUuid: string;
  adjustments: AdjustmentRow[];
}

interface StockAdjustmentHistoryItem {
  uuid: string;
  userUuid: string;
  userEmail: string;
  warehouseUuid: string;
  storageUuid: string;
  productUuid: string;
  productName: string;
  productSku?: string;
  quantityAdjusted: number;
  reason?: string;
  createdAt: string;
}

const StockAdjustments: React.FC = () => {
  const { user } = useAuth();
  const [storages, setStorages] = useState<Storage[]>([]);
  const [products, setProducts] = useState<Product[]>([]);
  const [history, setHistory] = useState<StockAdjustmentHistoryItem[]>([]);
  const [historyPagination, setHistoryPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0
  });
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [fetchingStock, setFetchingStock] = useState(false);
  const [formError, setFormError] = useState<string | null>(null);

  // Modal state
  const [isProductModalOpen, setIsProductModalOpen] = useState(false);
  const [selectedRowIndex, setSelectedRowIndex] = useState<number>(0);

  const { register, handleSubmit, watch, control, setValue, reset, formState: { errors } } = useForm<AdjustmentForm>({
    defaultValues: {
      storageUuid: "",
      adjustments: [{ productUuid: "", adjustment: 0, reason: "" }],
    },
  });
  const { fields, append, remove } = useFieldArray({
    control,
    name: "adjustments",
  });

  const storageUuid = watch("storageUuid");
  const adjustments = watch("adjustments");

  // Fetch storages
  useEffect(() => {
    if (!user?.warehouseUuid) return;
    
    const fetchStorages = async () => {
      setLoading(true);
      try {
        const storagesData = await getStoragesByWarehouse(user.warehouseUuid);
        setStorages(storagesData);
      } catch (error) {
        console.error("Failed to fetch storages:", error);
        toast.error("Failed to fetch storages");
      } finally {
        setLoading(false);
      }
    };
    
    fetchStorages();
  }, [user?.warehouseUuid]);

  // Handle product selection from modal
  const handleProductSelect = (product: Product) => {
    setValue(`adjustments.${selectedRowIndex}.productUuid`, product.uuid);
    setValue(`adjustments.${selectedRowIndex}.productName`, product.name);
    setValue(`adjustments.${selectedRowIndex}.sku`, product.sku);
    setIsProductModalOpen(false);
  };

  // Open product selection modal for a specific row
  const openProductModal = (rowIndex: number) => {
    setSelectedRowIndex(rowIndex);
    setIsProductModalOpen(true);
  };

  // Get currently selected product UUIDs to disable them in modal
  const getSelectedProductUuids = () => {
    return adjustments.map(adj => adj.productUuid).filter(Boolean);
  };

  // Fetch current stock for selected products in storage
  useEffect(() => {
    const fetchStock = async () => {
      if (!storageUuid || adjustments.length === 0) return;
      setFetchingStock(true);
      try {
        const productUuids = adjustments.map(adj => adj.productUuid).filter(Boolean);
        if (productUuids.length === 0) {
          adjustments.forEach((_, idx) => {
            setValue(`adjustments.${idx}.currentStock`, undefined);
          });
          return;
        }
        
        const stockLevels = await getStockLevels(storageUuid, productUuids);
        const stockMap: Record<string, number> = {};
        stockLevels.forEach((s: any) => { stockMap[s.productUuid] = s.quantity; });
        adjustments.forEach((adj, idx) => {
          setValue(`adjustments.${idx}.currentStock`, stockMap[adj.productUuid] ?? 0);
        });
      } catch (err) {
        console.error("Failed to fetch current stock", err);
        toast.error("Failed to fetch current stock");
      } finally {
        setFetchingStock(false);
      }
    };
    fetchStock();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [storageUuid, adjustments.map(a => a.productUuid).join(",")]);

  // Fetch adjustment history for storage with pagination
  useEffect(() => {
    if (!storageUuid) {
      setHistory([]);
      setHistoryPagination({ page: 1, limit: 10, total: 0, totalPages: 0 });
      return;
    }
    
    const fetchHistory = async () => {
      try {
        const historyResponse = await getStockAdjustmentHistory(
          storageUuid, 
          historyPagination.page, 
          historyPagination.limit
        );
        setHistory(historyResponse.data);
        setHistoryPagination({
          page: historyResponse.page,
          limit: historyResponse.limit,
          total: historyResponse.total,
          totalPages: historyResponse.totalPages
        });
      } catch (error) {
        console.error("Failed to fetch adjustment history:", error);
        toast.error("Failed to fetch adjustment history.");
        setHistory([]);
      }
    };
    
    fetchHistory();
  }, [storageUuid, historyPagination.page, historyPagination.limit, submitting]);

  // Handle pagination for history
  const handleHistoryPageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= historyPagination.totalPages) {
      setHistoryPagination(prev => ({ ...prev, page: newPage }));
    }
  };

  // Add row handler
  const handleAddRow = () => {
    append({ productUuid: "", adjustment: 0, reason: "" });
  };

  // Remove row handler
  const handleRemoveRow = (idx: number) => {
    if (fields.length === 1) return;
    remove(idx);
  };

  // Form submit
  const onSubmit = async (data: AdjustmentForm) => {
    setFormError(null);
    if (!user?.uuid || !user?.warehouseUuid) {
      const msg = "User not authenticated. Please log in again.";
      setFormError(msg);
      toast.error(msg);
      return;
    }
    setSubmitting(true);
    try {
      await createStockAdjustments(data, user.uuid, user.warehouseUuid);
      toast.success("Successfully submitted adjustment(s).");
      reset({ storageUuid: data.storageUuid, adjustments: [{ productUuid: "", adjustment: 0, reason: "" }] });
      // Reset to first page after successful submission
      setHistoryPagination(prev => ({ ...prev, page: 1 }));
    } catch (e: any) {
      const errorMessage = e?.message || e?.response?.data?.message || "An error occurred during submission.";
      setFormError(errorMessage);
      toast.error(`Failed to submit adjustments: ${errorMessage}`);
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <div className="p-6 max-w-5xl mx-auto">
      <h1 className="text-3xl font-bold mb-6">Stock Adjustments</h1>
      <form onSubmit={handleSubmit(onSubmit)} className="bg-white shadow rounded-lg p-6 mb-8">
        {formError && (
          <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
            <strong>Error:</strong> {formError}
          </div>
        )}
        <div className="mb-4">
          <label className="block font-semibold mb-1">Select Storage <span className="text-red-500">*</span></label>
          <select {...register("storageUuid", { required: true })} className="input input-bordered w-full" disabled={loading}>
            <option value="">-- Choose Storage --</option>
            {storages.map(s => (
              <option key={s.uuid} value={s.uuid}>{s.name}</option>
            ))}
          </select>
          {errors.storageUuid && <span className="text-red-500 text-sm">Storage is required</span>}
        </div>
        
        <table className="min-w-full border mb-4">
          <thead>
            <tr className="bg-gray-50">
              <th className="p-2">Product</th>
              <th className="p-2">SKU</th>
              <th className="p-2">Current Stock</th>
              <th className="p-2">Adjustment</th>
              <th className="p-2">Reason (Optional)</th>
              <th className="p-2"></th>
            </tr>
          </thead>
          <tbody>
            {fields.map((field, idx) => {
              const currentAdjustment = adjustments[idx];
              const hasSelectedProduct = currentAdjustment?.productUuid && currentAdjustment?.productName;
              
              return (
                <tr key={field.id}>
                  <td className="p-2">
                    {hasSelectedProduct ? (
                      <div className="flex items-center justify-between">
                        <div>
                          <div className="font-medium">{currentAdjustment.productName}</div>
                          {currentAdjustment.sku && (
                            <div className="text-sm text-gray-500">SKU: {currentAdjustment.sku}</div>
                          )}
                        </div>
                        <button
                          type="button"
                          onClick={() => openProductModal(idx)}
                          className="text-blue-600 hover:text-blue-800 text-sm"
                        >
                          Change
                        </button>
                      </div>
                    ) : (
                      <button
                        type="button"
                        onClick={() => openProductModal(idx)}
                        disabled={!storageUuid || loading}
                        className="w-full flex items-center justify-center gap-2 px-4 py-2 border border-dashed border-gray-300 rounded-lg hover:border-blue-500 hover:bg-blue-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        <Package className="w-4 h-4" />
                        Select Product
                      </button>
                    )}
                    {/* Hidden input to register the productUuid */}
                    <input
                      type="hidden"
                      {...register(`adjustments.${idx}.productUuid` as const, { required: true })}
                    />
                  </td>
                  <td className="p-2 text-gray-700">{currentAdjustment?.sku || "-"}</td>
                  <td className="p-2 text-center">{currentAdjustment?.currentStock ?? "-"}</td>
                  <td className="p-2">
                    <input 
                      type="number" 
                      step="1" 
                      {...register(`adjustments.${idx}.adjustment` as const, { 
                        required: true, 
                        valueAsNumber: true, 
                        validate: (v) => v !== 0 || "Adjustment cannot be zero" 
                      })} 
                      className="input input-bordered w-24" 
                      disabled={!storageUuid} 
                    />
                  </td>
                  <td className="p-2">
                    <input 
                      type="text" 
                      {...register(`adjustments.${idx}.reason` as const)} 
                      className="input input-bordered w-full" 
                      placeholder="Reason (optional)" 
                    />
                  </td>
                  <td className="p-2">
                    <button 
                      type="button" 
                      className="btn btn-ghost btn-sm" 
                      onClick={() => handleRemoveRow(idx)} 
                      disabled={fields.length === 1}
                    >
                      🗑️
                    </button>
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
        <button 
          type="button" 
          className="btn btn-outline btn-sm mb-4 flex items-center gap-2" 
          onClick={handleAddRow} 
          disabled={!storageUuid}
        >
          <Plus className="w-4 h-4" />
          Add Product
        </button>
        <button type="submit" className="btn btn-primary" disabled={submitting || loading || fetchingStock}>Submit Adjustment</button>
      </form>

      {/* Adjustment History */}
      {history.length > 0 && (
        <div className="bg-white shadow rounded-lg p-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold">Recent Adjustments</h2>
            <div className="text-sm text-gray-600">
              Showing {((historyPagination.page - 1) * historyPagination.limit) + 1} to {Math.min(historyPagination.page * historyPagination.limit, historyPagination.total)} of {historyPagination.total} adjustments
            </div>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full border">
              <thead>
                <tr className="bg-gray-50">
                  <th className="p-2 text-left">Product</th>
                  <th className="p-2 text-left">SKU</th>
                  <th className="p-2 text-center">Adjustment</th>
                  <th className="p-2 text-left">Reason</th>
                  <th className="p-2 text-left">Date</th>
                  <th className="p-2 text-left">User</th>
                </tr>
              </thead>
              <tbody>
                {history.map((item, idx) => (
                  <tr key={idx} className="border-t">
                    <td className="p-2">{item.productName}</td>
                    <td className="p-2">{item.productSku || "-"}</td>
                    <td className={`p-2 text-center font-medium ${item.quantityAdjusted > 0 ? "text-green-600" : "text-red-600"}`}>
                      {item.quantityAdjusted > 0 ? "+" : ""}{item.quantityAdjusted}
                    </td>
                    <td className="p-2">{item.reason || "-"}</td>
                    <td className="p-2">{new Date(item.createdAt).toLocaleDateString()}</td>
                    <td className="p-2">{item.userEmail}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          
          {/* Pagination Controls */}
          {historyPagination.totalPages > 1 && (
            <div className="flex justify-between items-center mt-4">
              <div className="text-sm text-gray-600">
                Page {historyPagination.page} of {historyPagination.totalPages}
              </div>
              <div className="flex gap-2">
                <button
                  onClick={() => handleHistoryPageChange(historyPagination.page - 1)}
                  disabled={historyPagination.page <= 1}
                  className="btn btn-outline btn-sm flex items-center gap-1 disabled:opacity-50"
                >
                  <ChevronLeft className="w-4 h-4" />
                  Previous
                </button>
                <button
                  onClick={() => handleHistoryPageChange(historyPagination.page + 1)}
                  disabled={historyPagination.page >= historyPagination.totalPages}
                  className="btn btn-outline btn-sm flex items-center gap-1 disabled:opacity-50"
                >
                  Next
                  <ChevronRight className="w-4 h-4" />
                </button>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Product Selection Modal */}
      <ProductSelectionModal
        isOpen={isProductModalOpen}
        onClose={() => setIsProductModalOpen(false)}
        onSelect={handleProductSelect}
        selectedProductUuids={getSelectedProductUuids()}
        title="Select Product for Stock Adjustment"
      />
    </div>
  );
};

export default StockAdjustments;
