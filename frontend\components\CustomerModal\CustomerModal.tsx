import React, { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { FiSearch, FiChevronLeft, FiChevronRight, FiLoader, FiX } from 'react-icons/fi';
import { useAuth } from '@/contexts/AuthContext';
import { useCustomerData } from './hooks/useCustomerData';
import { validateCustomer } from './utils/customerHelpers';
import { customerModalStyles } from './styles/customerModalStyles';
import type { CustomerModalProps, Customer, CreateCustomerDto } from './types';

export function CustomerModal({
  isOpen,
  customers: initialCustomers = [], // Keep for backward compatibility but won't use
  onSelect,
  onClose,
  onCreateNew,
  disabled = false,
}: CustomerModalProps) {
  const { user } = useAuth();
  const warehouseUuid = user?.warehouseUuid;
  
  const [searchTerm, setSearchTerm] = useState('');
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');
  const [showCreateForm, setShowCreateForm] = useState(false);
  
  // Use the customer data hook for efficient loading and caching
  const {
    customers,
    isLoading,
    isSearching,
    error,
    currentPage,
    totalPages,
    totalCustomers,
    loadCustomers,
    clearError
  } = useCustomerData();
  
  const pageSize = 20; // Customers per page
  
  // Request debouncing
  const requestTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const [formData, setFormData] = useState({
    name: '',
    fiscalId: '',
    email: '',
    phone: '',
    address: '',
    rc: '',
    articleNumber: '',
    customerType: 'retail' as 'retail' | 'wholesale' | 'mid-wholesale' | 'institutional',
    latitude: 0,
    longitude: 0,
    warehouseUuid: '',
    regionUuid: '',
  });
  const [errors, setErrors] = useState<string[]>([]);

  // Handle escape key to close modal
  useEffect(() => {
    if (!isOpen) return;
    
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        e.preventDefault();
        handleClose();
      }
    };
    
    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [isOpen]);

  // Handle clicking outside modal to close
  const handleOverlayClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      handleClose();
    }
  };

  // Centralized close handler
  const handleClose = useCallback(() => {
    if (showCreateForm) {
      // If in create form, go back to customer list
      setShowCreateForm(false);
      setErrors([]);
    } else {
      // Close the modal completely
      onClose();
    }
  }, [showCreateForm, onClose]);

  // Load customers when modal opens or search term changes
  useEffect(() => {
    if (!isOpen || !warehouseUuid) return;
    
    loadCustomers(1, debouncedSearchTerm);
  }, [isOpen, warehouseUuid, loadCustomers]);

  // Handle debounced search - only trigger when search term actually changes
  useEffect(() => {
    if (!isOpen || !warehouseUuid) return;
    
    // Clear any existing timeout
    if (requestTimeoutRef.current) {
      clearTimeout(requestTimeoutRef.current);
    }
    
    // Set new timeout for debounced search
    requestTimeoutRef.current = setTimeout(() => {
      loadCustomers(1, debouncedSearchTerm);
    }, 300);
    
    // Cleanup timeout on unmount or dependency change
    return () => {
      if (requestTimeoutRef.current) {
        clearTimeout(requestTimeoutRef.current);
      }
    };
  }, [debouncedSearchTerm, isOpen, warehouseUuid, loadCustomers]);

  // Cleanup effect for component unmount
  useEffect(() => {
    return () => {
      // Clear any pending timeouts
      if (requestTimeoutRef.current) {
        clearTimeout(requestTimeoutRef.current);
      }
    };
  }, []);

  // Handle page changes
  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= totalPages && !isLoading && !isSearching) {
      loadCustomers(newPage, debouncedSearchTerm);
    }
  };

  const handleCreateCustomer = () => {
    const validation = validateCustomer(formData);
    if (!validation.isValid) {
      setErrors(validation.errors);
      return;
    }

    onCreateNew({
      ...formData,
      warehouseUuid: warehouseUuid!
    });
    onClose();
  };

  // Reset form when modal opens
  useEffect(() => {
    if (isOpen) {
      setSearchTerm('');
      setDebouncedSearchTerm('');
      setShowCreateForm(false);
      setErrors([]);
      clearError();
    }
  }, [isOpen, clearError]);

  // Update debounced search term when search term changes
  useEffect(() => {
    setDebouncedSearchTerm(searchTerm);
  }, [searchTerm]);

  if (!isOpen) return null;

  return (
    <div className={customerModalStyles.modal.overlay} onClick={handleOverlayClick}>
      <div className="bg-white rounded-lg shadow-lg max-w-lg w-full mx-4 h-[600px] overflow-hidden flex flex-col">
        <div className="p-4 border-b border-gray-200 flex-shrink-0 flex items-center justify-between">
          <div>
            <h3 className="text-lg font-bold text-gray-900">
              {showCreateForm ? 'Create New Customer' : 'Select Customer'}
            </h3>
            {!showCreateForm && totalCustomers > 0 && (
              <p className="text-xs text-gray-600 mt-1">
                {totalCustomers} customers found
              </p>
            )}
          </div>
          <button
            onClick={handleClose}
            className="p-1 rounded-full hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors"
            title="Close"
            aria-label="Close modal"
          >
            <FiX className="h-5 w-5 text-gray-500" />
          </button>
        </div>
        
        <div className="flex-1 flex flex-col min-h-0">
          {/* Scrollable Content Area */}
          <div className="flex-1 overflow-y-auto p-4">
            {!showCreateForm ? (
              <>
                {/* Search Bar */}
                <div className="mb-5">
                  <div className="relative">
                    <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <input
                      type="text"
                      placeholder="Search customers by name..."
                      value={searchTerm}
                      onChange={(e) => {
                        if (disabled) return;
                        setSearchTerm(e.target.value);
                      }}
                      className={`w-full pl-9 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
                      autoFocus
                      disabled={disabled}
                    />
                    {isSearching && (
                      <FiLoader className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4 animate-spin" />
                    )}
                  </div>
                </div>

                {/* Error Display */}
                {error && (
                  <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
                    <div className="text-red-700 text-sm">
                      {error}
                    </div>
                  </div>
                )}

                {/* Loading State */}
                {isLoading && (
                  <div className="flex items-center justify-center py-8">
                    <FiLoader className="animate-spin h-6 w-6 text-blue-500" />
                    <span className="ml-2 text-gray-600">Loading customers...</span>
                  </div>
                )}

                {/* Customer List */}
                {!isLoading && customers.length > 0 && (
                  <div className="space-y-2">
                    {customers.map((customer) => (
                      <div
                        key={customer.uuid}
                        className={`p-3 border border-gray-200 rounded-lg transition-all duration-200 ${
                          disabled 
                            ? 'opacity-50 cursor-not-allowed' 
                            : 'cursor-pointer hover:bg-gray-50 hover:border-blue-300'
                        }`}
                        onClick={() => {
                          if (disabled) return;
                          onSelect(customer);
                          onClose();
                        }}
                      >
                        <div className="font-medium text-gray-900 text-sm">
                          {customer.name}
                        </div>
                        <div className="text-xs text-gray-600 mt-1">
                          {customer.fiscalId && `ID: ${customer.fiscalId} • `}
                          {customer.customerType && `${customer.customerType} • `}
                          {customer.phone && `${customer.phone} • `}
                          {customer.email}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
                
                {/* No Results */}
                {!isLoading && customers.length === 0 && searchTerm && (
                  <div className="text-center py-8">
                    <p className="text-gray-500 text-sm">No customers found matching "{searchTerm}"</p>
                    <button
                      onClick={() => !disabled && setShowCreateForm(true)}
                      disabled={disabled}
                      className={`mt-2 text-sm font-medium ${disabled ? 'text-gray-400 cursor-not-allowed' : 'text-blue-600 hover:text-blue-800'}`}
                    >
                      Create new customer
                    </button>
                  </div>
                )}

                {/* Pagination */}
                {totalPages > 1 && (
                  <div className="flex items-center justify-between mt-4 pt-4 border-t border-gray-200">
                    <button
                      onClick={() => handlePageChange(currentPage - 1)}
                      disabled={currentPage <= 1 || isLoading || disabled}
                      className="flex items-center px-3 py-1 text-sm text-gray-600 hover:text-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <FiChevronLeft className="h-4 w-4 mr-1" />
                      Previous
                    </button>
                    
                    <span className="text-sm text-gray-600">
                      Page {currentPage} of {totalPages}
                    </span>
                    
                    <button
                      onClick={() => handlePageChange(currentPage + 1)}
                      disabled={currentPage >= totalPages || isLoading || disabled}
                      className="flex items-center px-3 py-1 text-sm text-gray-600 hover:text-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Next
                      <FiChevronRight className="h-4 w-4 ml-1" />
                    </button>
                  </div>
                )}
              </>
            ) : (
              /* Create Customer Form */
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Name *
                  </label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => !disabled && setFormData({ ...formData, name: e.target.value })}
                    className={`w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
                    placeholder="Customer name"
                    autoFocus
                    disabled={disabled}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Fiscal ID
                  </label>
                  <input
                    type="text"
                    value={formData.fiscalId}
                    onChange={(e) => !disabled && setFormData({ ...formData, fiscalId: e.target.value })}
                    className={`w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
                    placeholder="Fiscal identification"
                    disabled={disabled}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Email
                  </label>
                  <input
                    type="email"
                    value={formData.email}
                    onChange={(e) => !disabled && setFormData({ ...formData, email: e.target.value })}
                    className={`w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
                    placeholder="Email address"
                    disabled={disabled}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Phone
                  </label>
                  <input
                    type="tel"
                    value={formData.phone}
                    onChange={(e) => !disabled && setFormData({ ...formData, phone: e.target.value })}
                    className={`w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
                    placeholder="Phone number"
                    disabled={disabled}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Customer Type
                  </label>
                  <select
                    value={formData.customerType}
                    onChange={(e) => !disabled && setFormData({ ...formData, customerType: e.target.value as any })}
                    className={`w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
                    disabled={disabled}
                  >
                    <option value="retail">Retail</option>
                    <option value="wholesale">Wholesale</option>
                    <option value="mid-wholesale">Mid-Wholesale</option>
                    <option value="institutional">Institutional</option>
                  </select>
                </div>

                {errors.length > 0 && (
                  <div className="p-3 bg-red-50 border border-red-200 rounded-md">
                    <div className="text-red-700 text-sm">
                      {errors.map((error, index) => (
                        <div key={index}>{error}</div>
                      ))}
                    </div>
                  </div>
                )}

                <div className="flex space-x-3">
                  <button
                    onClick={() => !disabled && setShowCreateForm(false)}
                    disabled={disabled}
                    className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm font-medium disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleCreateCustomer}
                    disabled={disabled}
                    className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm font-medium disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Create Customer
                  </button>
                </div>
              </div>
            )}
          </div>
          
          {/* Fixed Bottom Section - Create Customer Button */}
          {!showCreateForm && (
            <div className="flex-shrink-0 p-4 border-t border-gray-200">
              <button
                onClick={() => !disabled && setShowCreateForm(true)}
                disabled={disabled}
                className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm font-medium disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Create New Customer
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
} 