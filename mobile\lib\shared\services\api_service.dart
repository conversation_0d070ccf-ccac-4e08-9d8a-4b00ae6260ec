import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import '../../core/constants/app_constants.dart';

class ApiService {
  factory ApiService() => _instance;
  ApiService._internal() {
    _dio = Dio();
    _setupInterceptors();
  }
  static final ApiService _instance = ApiService._internal();

  late final Dio _dio;
  String? _accessToken;

  /// Get the Dio instance
  Dio get dio => _dio;

  /// Set access token for authenticated requests
  void setAccessToken(String? token) {
    _accessToken = token;
    if (token != null) {
      _dio.options.headers['Authorization'] = 'Bearer $token';
    } else {
      _dio.options.headers.remove('Authorization');
    }
  }

  /// Setup interceptors for logging and error handling
  void _setupInterceptors() {
    // Get API configuration from environment variables or use defaults
    final apiBaseUrl = dotenv.env['API_BASE_URL'] ?? AppConstants.apiBaseUrl;
    final apiTimeout = int.tryParse(dotenv.env['API_TIMEOUT'] ?? '') ?? AppConstants.apiTimeout;
    
    _dio.options.baseUrl = apiBaseUrl;
    _dio.options.connectTimeout = Duration(milliseconds: apiTimeout);
    _dio.options.receiveTimeout = Duration(milliseconds: apiTimeout);
    
    print('🔧 API SERVICE: Base URL set to $apiBaseUrl');
    print('🔧 API SERVICE: Timeout set to ${apiTimeout}ms');

    // Request interceptor - Only show custom user logs
    _dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) {
        // Only show user-relevant request logs
        print('🔵 USER REQUEST: ${options.method} ${options.path}');
        if (options.data != null) {
          print('🔵 USER DATA: ${options.data}');
        }
        handler.next(options);
      },
      onResponse: (response, handler) {
        // Only show user-relevant response logs
        print('🟢 USER RESPONSE: ${response.statusCode} ${response.requestOptions.path}');
        handler.next(response);
      },
      onError: (error, handler) {
        // Only show user-relevant error logs
        print('🔴 USER ERROR: ${error.response?.statusCode} ${error.requestOptions.path}');
        if (error.response?.data != null) {
          print('🔴 USER ERROR DATA: ${error.response?.data}');
        }
        handler.next(error);
      },
    ));
  }

  /// GET request
  Future<Response> get(String path, {Map<String, dynamic>? queryParameters}) async => await _dio.get(path, queryParameters: queryParameters);

  /// POST request
  Future<Response> post(String path, {data, Map<String, dynamic>? queryParameters}) async => await _dio.post(path, data: data, queryParameters: queryParameters);

  /// PUT request
  Future<Response> put(String path, {data, Map<String, dynamic>? queryParameters}) async => await _dio.put(path, data: data, queryParameters: queryParameters);

  /// DELETE request
  Future<Response> delete(String path, {data, Map<String, dynamic>? queryParameters}) async => await _dio.delete(path, data: data, queryParameters: queryParameters);

  /// PATCH request
  Future<Response> patch(String path, {data, Map<String, dynamic>? queryParameters}) async => await _dio.patch(path, data: data, queryParameters: queryParameters);
} 