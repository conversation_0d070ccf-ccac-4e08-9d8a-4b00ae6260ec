// DTO utility for Product output, following UUID_USAGE_GUIDELINES.md
import { Product } from "../product.entity";

export interface ProductDto {
  uuid: string; // from virtual
  warehouseUuidString?: string; // from virtual
  productCategoryUuidString?: string; // from virtual
  name: string;
  description?: string;
  sku?: string;
  barcode?: string;
  retailPrice: number;
  wholesalePrice: number;
  midWholesalePrice: number;
  institutionalPrice: number;
  cost?: number;
  isDeleted: boolean;
  createdAt?: Date | string;
  updatedAt?: Date | string;
  // Add other fields as needed, but do NOT include _id, warehouseUuid (Buffer), or any Buffer fields
}

export function toProductDto(product: any): ProductDto {
  // Accepts a TypeORM entity or plain object
  return {
    uuid: product.id, // TypeORM uses 'id' instead of virtual 'uuid'
    warehouseUuidString: product.warehouseUuid, // Direct UUID string in TypeORM
    productCategoryUuidString: product.productCategoryUuid, // Direct UUID string in TypeORM
    name: product.name,
    description: product.description ?? "",
    sku: product.sku ?? "",
    barcode: product.barcode ?? "",
    retailPrice: product.retailPrice,
    wholesalePrice: product.wholesalePrice,
    midWholesalePrice: product.midWholesalePrice,
    institutionalPrice: product.institutionalPrice,
    cost: product.cost ?? null,
    isDeleted: product.isDeleted,
    createdAt: product.createdAt,
    updatedAt: product.updatedAt,
  };
}

export function toProductDtoArray(products: any[]): ProductDto[] {
  return products.map(toProductDto);
}
