"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/logs/page",{

/***/ "(app-pages-browser)/./app/logs/components/LogDetailsModal.tsx":
/*!*************************************************!*\
  !*** ./app/logs/components/LogDetailsModal.tsx ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LogDetailsModal: function() { return /* binding */ LogDetailsModal; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_FiCalendar_FiChevronDown_FiChevronRight_FiDatabase_FiFileText_FiTag_FiUser_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=FiCalendar,FiChevronDown,FiChevronRight,FiDatabase,FiFileText,FiTag,FiUser,FiX!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n\nvar _s = $RefreshSig$();\n\n\nfunction LogDetailsModal(param) {\n    let { isOpen, log, onClose, userName } = param;\n    _s();\n    // State for expandable tabs\n    const [showOldDataTab, setShowOldDataTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showNewDataTab, setShowNewDataTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Handle escape key to close modal\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!isOpen) return;\n        const handleKeyDown = (e)=>{\n            if (e.key === \"Escape\") {\n                e.preventDefault();\n                onClose();\n            }\n        };\n        window.addEventListener(\"keydown\", handleKeyDown);\n        return ()=>window.removeEventListener(\"keydown\", handleKeyDown);\n    }, [\n        isOpen,\n        onClose\n    ]);\n    // Handle clicking outside modal to close\n    const handleOverlayClick = (e)=>{\n        if (e.target === e.currentTarget) {\n            onClose();\n        }\n    };\n    // Format date for display\n    const formatDate = (date)=>{\n        const dateObj = typeof date === \"string\" ? new Date(date) : date;\n        return dateObj.toLocaleString(\"en-US\", {\n            year: \"numeric\",\n            month: \"long\",\n            day: \"numeric\",\n            hour: \"2-digit\",\n            minute: \"2-digit\",\n            second: \"2-digit\"\n        });\n    };\n    // Get operation color based on operation type\n    const getOperationColor = (operation)=>{\n        const op = operation.toLowerCase();\n        if (op.includes(\"create\") || op.includes(\"add\")) {\n            return \"bg-green-100 text-green-800 border-green-200\";\n        } else if (op.includes(\"update\") || op.includes(\"edit\") || op.includes(\"modify\")) {\n            return \"bg-blue-100 text-blue-800 border-blue-200\";\n        } else if (op.includes(\"delete\") || op.includes(\"remove\")) {\n            return \"bg-red-100 text-red-800 border-red-200\";\n        } else if (op.includes(\"cancel\") || op.includes(\"reject\")) {\n            return \"bg-orange-100 text-orange-800 border-orange-200\";\n        } else if (op.includes(\"approve\") || op.includes(\"complete\")) {\n            return \"bg-purple-100 text-purple-800 border-purple-200\";\n        } else if (op.includes(\"transfer\") || op.includes(\"move\")) {\n            return \"bg-indigo-100 text-indigo-800 border-indigo-200\";\n        } else if (op.includes(\"adjust\") || op.includes(\"modify\")) {\n            return \"bg-yellow-100 text-yellow-800 border-yellow-200\";\n        } else {\n            return \"bg-gray-100 text-gray-800 border-gray-200\";\n        }\n    };\n    // Format JSON data for display\n    const formatJsonData = (data)=>{\n        try {\n            return JSON.stringify(data, null, 2);\n        } catch (error) {\n            return \"Invalid JSON data\";\n        }\n    };\n    // Check if the log data contains delta changes\n    const hasDeltaChanges = (data)=>{\n        return data && data.changes && typeof data.changes === \"object\";\n    };\n    // Extract raw old data from delta changes\n    const extractOldData = (data)=>{\n        if (!hasDeltaChanges(data)) return null;\n        const oldData = {};\n        Object.entries(data.changes).forEach((param)=>{\n            let [field, change] = param;\n            if (change && typeof change === \"object\" && \"before\" in change) {\n                oldData[field] = change.before;\n            }\n        });\n        return Object.keys(oldData).length > 0 ? oldData : null;\n    };\n    // Extract raw new data from delta changes\n    const extractNewData = (data)=>{\n        if (!hasDeltaChanges(data)) return null;\n        const newData = {};\n        Object.entries(data.changes).forEach((param)=>{\n            let [field, change] = param;\n            if (change && typeof change === \"object\" && \"after\" in change) {\n                newData[field] = change.after;\n            }\n        });\n        return Object.keys(newData).length > 0 ? newData : null;\n    };\n    // Sophisticated change viewer that analyzes data types and shows meaningful differences\n    const renderSophisticatedChanges = (before, after)=>{\n        // Handle null/undefined cases\n        if (before === null && after === null) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-gray-500 italic\",\n                children: \"No change (both null)\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                lineNumber: 120,\n                columnNumber: 14\n            }, this);\n        }\n        if (before === null) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 text-sm font-medium text-green-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"w-2 h-2 bg-green-500 rounded-full\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 13\n                            }, this),\n                            \"Added\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-green-50 border border-green-200 rounded p-3\",\n                        children: renderValue(after)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                        lineNumber: 130,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                lineNumber: 125,\n                columnNumber: 9\n            }, this);\n        }\n        if (after === null) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 text-sm font-medium text-red-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"w-2 h-2 bg-red-500 rounded-full\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 13\n                            }, this),\n                            \"Deleted\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-red-50 border border-red-200 rounded p-3\",\n                        children: renderValue(before)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                        lineNumber: 144,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                lineNumber: 139,\n                columnNumber: 9\n            }, this);\n        }\n        // Both values exist - analyze types\n        const beforeType = Array.isArray(before) ? \"array\" : typeof before;\n        const afterType = Array.isArray(after) ? \"array\" : typeof after;\n        // Type changed\n        if (beforeType !== afterType) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm font-medium text-orange-700\",\n                        children: [\n                            \"Type Changed: \",\n                            beforeType,\n                            \" → \",\n                            afterType\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs font-medium text-red-700 mb-1\",\n                                        children: [\n                                            \"Before (\",\n                                            beforeType,\n                                            \")\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-red-50 border border-red-200 rounded p-3\",\n                                        children: renderValue(before)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs font-medium text-green-700 mb-1\",\n                                        children: [\n                                            \"After (\",\n                                            afterType,\n                                            \")\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-green-50 border border-green-200 rounded p-3\",\n                                        children: renderValue(after)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                        lineNumber: 160,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                lineNumber: 158,\n                columnNumber: 9\n            }, this);\n        }\n        // Same type - handle specific comparisons\n        if (beforeType === \"array\") {\n            return renderArrayChanges(before, after);\n        } else if (beforeType === \"object\") {\n            return renderObjectChanges(before, after);\n        } else {\n            // Primitive values\n            if (before === after) {\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-gray-500 italic\",\n                    children: \"No change\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                    lineNumber: 186,\n                    columnNumber: 16\n                }, this);\n            }\n            return renderPrimitiveChange(before, after);\n        }\n    };\n    // Helper function to render a value with appropriate formatting\n    const renderValue = (value)=>{\n        if (value === null) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"text-gray-500 italic\",\n            children: \"null\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n            lineNumber: 194,\n            columnNumber: 32\n        }, this);\n        if (value === undefined) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"text-gray-500 italic\",\n            children: \"undefined\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n            lineNumber: 195,\n            columnNumber: 37\n        }, this);\n        if (typeof value === \"string\") return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"text-blue-600\",\n            children: [\n                '\"',\n                value,\n                '\"'\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n            lineNumber: 196,\n            columnNumber: 43\n        }, this);\n        if (typeof value === \"number\") return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"text-purple-600\",\n            children: value\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n            lineNumber: 197,\n            columnNumber: 43\n        }, this);\n        if (typeof value === \"boolean\") return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"text-orange-600\",\n            children: String(value)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n            lineNumber: 198,\n            columnNumber: 44\n        }, this);\n        if (Array.isArray(value)) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"font-mono text-sm\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-gray-500\",\n                        children: \"[\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                        lineNumber: 202,\n                        columnNumber: 11\n                    }, this),\n                    value.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-gray-400 italic\",\n                        children: \"empty\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                        lineNumber: 204,\n                        columnNumber: 13\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-gray-600\",\n                        children: [\n                            value.length,\n                            \" items\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                        lineNumber: 206,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-gray-500\",\n                        children: \"]\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                        lineNumber: 208,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                lineNumber: 201,\n                columnNumber: 9\n            }, this);\n        }\n        if (typeof value === \"object\") {\n            const keys = Object.keys(value);\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"font-mono text-sm\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-gray-500\",\n                        children: \"{\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                        lineNumber: 216,\n                        columnNumber: 11\n                    }, this),\n                    keys.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-gray-400 italic\",\n                        children: \"empty\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                        lineNumber: 218,\n                        columnNumber: 13\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-gray-600\",\n                        children: [\n                            keys.length,\n                            \" properties\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                        lineNumber: 220,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-gray-500\",\n                        children: \"}\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                        lineNumber: 222,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                lineNumber: 215,\n                columnNumber: 9\n            }, this);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"text-gray-600\",\n            children: String(value)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n            lineNumber: 226,\n            columnNumber: 12\n        }, this);\n    };\n    // Render changes for primitive values\n    const renderPrimitiveChange = (before, after)=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 gap-3\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs font-medium text-red-700 mb-1\",\n                            children: \"Before\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                            lineNumber: 234,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-red-50 border border-red-200 rounded p-3\",\n                            children: renderValue(before)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                            lineNumber: 235,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                    lineNumber: 233,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs font-medium text-green-700 mb-1\",\n                            children: \"After\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                            lineNumber: 240,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-green-50 border border-green-200 rounded p-3\",\n                            children: renderValue(after)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                            lineNumber: 241,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                    lineNumber: 239,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n            lineNumber: 232,\n            columnNumber: 7\n        }, this);\n    };\n    // Check if an array contains business items (has id, name, price, quantity, etc.)\n    const isBusinessItemsArray = (arr)=>{\n        if (!arr || arr.length === 0) return false;\n        const firstItem = arr[0];\n        return typeof firstItem === \"object\" && firstItem !== null && (\"id\" in firstItem || \"name\" in firstItem || \"price\" in firstItem || \"quantity\" in firstItem);\n    };\n    // Render business item with key details highlighted\n    const renderBusinessItem = (item)=>{\n        if (!item || typeof item !== \"object\") {\n            return renderValue(item);\n        }\n        const { id, name, price, quantity, ...otherProps } = item;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-2\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-wrap gap-3\",\n                    children: [\n                        name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs text-gray-500\",\n                                    children: \"Name:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 271,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-medium text-gray-900\",\n                                    children: name\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 272,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                            lineNumber: 270,\n                            columnNumber: 13\n                        }, this),\n                        quantity !== undefined && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs text-gray-500\",\n                                    children: \"Qty:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 277,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-medium text-purple-600\",\n                                    children: quantity\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 278,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                            lineNumber: 276,\n                            columnNumber: 13\n                        }, this),\n                        price !== undefined && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs text-gray-500\",\n                                    children: \"Price:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 283,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-medium text-green-600\",\n                                    children: [\n                                        \"$\",\n                                        price\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 284,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                            lineNumber: 282,\n                            columnNumber: 13\n                        }, this),\n                        id !== undefined && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs text-gray-500\",\n                                    children: \"ID:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 289,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-mono text-xs text-gray-600\",\n                                    children: id\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 290,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                            lineNumber: 288,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                    lineNumber: 268,\n                    columnNumber: 9\n                }, this),\n                Object.keys(otherProps).length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-xs text-gray-500\",\n                    children: [\n                        \"+\",\n                        Object.keys(otherProps).length,\n                        \" other properties\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                    lineNumber: 295,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n            lineNumber: 267,\n            columnNumber: 7\n        }, this);\n    };\n    // Compare business items by ID or by content\n    const findMatchingItem = (item, array)=>{\n        if (!item || !array) return null;\n        // Try to match by ID first\n        if (item.id !== undefined) {\n            return array.find((arrItem)=>arrItem && arrItem.id === item.id);\n        }\n        // Fallback to exact match\n        return array.find((arrItem)=>JSON.stringify(arrItem) === JSON.stringify(item));\n    };\n    // Render changes for arrays - enhanced for business items\n    const renderArrayChanges = (before, after)=>{\n        const isBusinessItems = isBusinessItemsArray(before) || isBusinessItemsArray(after);\n        if (isBusinessItems) {\n            return renderBusinessItemsChanges(before, after);\n        }\n        // For non-business arrays, use simple comparison\n        const maxLength = Math.max(before.length, after.length);\n        const changes = [];\n        for(let i = 0; i < maxLength; i++){\n            const beforeItem = i < before.length ? before[i] : undefined;\n            const afterItem = i < after.length ? after[i] : undefined;\n            if (beforeItem === undefined) {\n                changes.push({\n                    type: \"added\",\n                    index: i,\n                    value: afterItem\n                });\n            } else if (afterItem === undefined) {\n                changes.push({\n                    type: \"removed\",\n                    index: i,\n                    value: beforeItem\n                });\n            } else if (JSON.stringify(beforeItem) !== JSON.stringify(afterItem)) {\n                changes.push({\n                    type: \"modified\",\n                    index: i,\n                    before: beforeItem,\n                    after: afterItem\n                });\n            }\n        }\n        if (changes.length === 0) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-gray-500 italic\",\n                children: \"No changes in array\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                lineNumber: 342,\n                columnNumber: 14\n            }, this);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-3\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm font-medium text-gray-700\",\n                    children: [\n                        \"Array Changes (\",\n                        before.length,\n                        \" → \",\n                        after.length,\n                        \" items)\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                    lineNumber: 347,\n                    columnNumber: 9\n                }, this),\n                changes.map((change, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border border-gray-200 rounded p-3\",\n                        children: [\n                            change.type === \"added\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 text-sm font-medium text-green-700\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"w-2 h-2 bg-green-500 rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                lineNumber: 355,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Added at index \",\n                                            change.index\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                        lineNumber: 354,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-green-50 border border-green-200 rounded p-2\",\n                                        children: renderValue(change.value)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                        lineNumber: 358,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                lineNumber: 353,\n                                columnNumber: 15\n                            }, this),\n                            change.type === \"removed\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 text-sm font-medium text-red-700\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"w-2 h-2 bg-red-500 rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                lineNumber: 366,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Removed from index \",\n                                            change.index\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                        lineNumber: 365,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-red-50 border border-red-200 rounded p-2\",\n                                        children: renderValue(change.value)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                        lineNumber: 369,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                lineNumber: 364,\n                                columnNumber: 15\n                            }, this),\n                            change.type === \"modified\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 text-sm font-medium text-blue-700\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"w-2 h-2 bg-blue-500 rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                lineNumber: 377,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Modified at index \",\n                                            change.index\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                        lineNumber: 376,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs font-medium text-red-700 mb-1\",\n                                                        children: \"Before\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                        lineNumber: 382,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-red-50 border border-red-200 rounded p-2\",\n                                                        children: renderValue(change.before)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                        lineNumber: 383,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                lineNumber: 381,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs font-medium text-green-700 mb-1\",\n                                                        children: \"After\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                        lineNumber: 388,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-green-50 border border-green-200 rounded p-2\",\n                                                        children: renderValue(change.after)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                        lineNumber: 389,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                lineNumber: 387,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                        lineNumber: 380,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                lineNumber: 375,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, idx, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                        lineNumber: 351,\n                        columnNumber: 11\n                    }, this))\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n            lineNumber: 346,\n            columnNumber: 7\n        }, this);\n    };\n    // Specialized renderer for business items (products, line items, etc.)\n    const renderBusinessItemsChanges = (before, after)=>{\n        const changes = [];\n        const processedAfterItems = new Set();\n        // Find removed and modified items\n        for (const beforeItem of before){\n            const matchingAfterItem = findMatchingItem(beforeItem, after);\n            if (!matchingAfterItem) {\n                changes.push({\n                    type: \"removed\",\n                    item: beforeItem\n                });\n            } else {\n                processedAfterItems.add(after.indexOf(matchingAfterItem));\n                if (JSON.stringify(beforeItem) !== JSON.stringify(matchingAfterItem)) {\n                    changes.push({\n                        type: \"modified\",\n                        before: beforeItem,\n                        after: matchingAfterItem\n                    });\n                }\n            }\n        }\n        // Find added items\n        for(let i = 0; i < after.length; i++){\n            if (!processedAfterItems.has(i)) {\n                const afterItem = after[i];\n                const matchingBeforeItem = findMatchingItem(afterItem, before);\n                if (!matchingBeforeItem) {\n                    changes.push({\n                        type: \"added\",\n                        item: afterItem\n                    });\n                }\n            }\n        }\n        if (changes.length === 0) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-gray-500 italic\",\n                children: \"No changes in items\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                lineNumber: 433,\n                columnNumber: 14\n            }, this);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-3\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm font-medium text-gray-700\",\n                    children: [\n                        \"Items Changes (\",\n                        before.length,\n                        \" → \",\n                        after.length,\n                        \" items)\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                    lineNumber: 438,\n                    columnNumber: 9\n                }, this),\n                changes.map((change, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border border-gray-200 rounded p-3\",\n                        children: [\n                            change.type === \"added\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 text-sm font-medium text-green-700\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"w-2 h-2 bg-green-500 rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                lineNumber: 446,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Added Item\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                        lineNumber: 445,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-green-50 border border-green-200 rounded p-3\",\n                                        children: renderBusinessItem(change.item)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                        lineNumber: 449,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                lineNumber: 444,\n                                columnNumber: 15\n                            }, this),\n                            change.type === \"removed\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 text-sm font-medium text-red-700\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"w-2 h-2 bg-red-500 rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                lineNumber: 457,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Removed Item\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                        lineNumber: 456,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-red-50 border border-red-200 rounded p-3\",\n                                        children: renderBusinessItem(change.item)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                        lineNumber: 460,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                lineNumber: 455,\n                                columnNumber: 15\n                            }, this),\n                            change.type === \"modified\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 text-sm font-medium text-blue-700\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"w-2 h-2 bg-blue-500 rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                lineNumber: 468,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Modified Item \",\n                                            change.before.name && '\"'.concat(change.before.name, '\"')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                        lineNumber: 467,\n                                        columnNumber: 17\n                                    }, this),\n                                    renderBusinessItemChanges(change.before, change.after)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                lineNumber: 466,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, idx, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                        lineNumber: 442,\n                        columnNumber: 11\n                    }, this))\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n            lineNumber: 437,\n            columnNumber: 7\n        }, this);\n    };\n    // Render detailed changes within a business item\n    const renderBusinessItemChanges = (before, after)=>{\n        const beforeKeys = Object.keys(before);\n        const afterKeys = Object.keys(after);\n        const allKeys = Array.from(new Set([\n            ...beforeKeys,\n            ...afterKeys\n        ]));\n        const changes = [];\n        for (const key of allKeys){\n            const beforeValue = before[key];\n            const afterValue = after[key];\n            if (!beforeKeys.includes(key)) {\n                changes.push({\n                    type: \"added\",\n                    key,\n                    value: afterValue\n                });\n            } else if (!afterKeys.includes(key)) {\n                changes.push({\n                    type: \"removed\",\n                    key,\n                    value: beforeValue\n                });\n            } else if (JSON.stringify(beforeValue) !== JSON.stringify(afterValue)) {\n                changes.push({\n                    type: \"modified\",\n                    key,\n                    before: beforeValue,\n                    after: afterValue\n                });\n            }\n        }\n        if (changes.length === 0) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-gray-500 italic\",\n                children: \"No property changes\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                lineNumber: 502,\n                columnNumber: 14\n            }, this);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-blue-50 border border-blue-200 rounded p-3 space-y-2\",\n            children: changes.map((change, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between text-sm\",\n                    children: [\n                        change.type === \"modified\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-medium text-gray-700 capitalize\",\n                                    children: [\n                                        change.key,\n                                        \":\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 511,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-red-600 line-through\",\n                                            children: renderInlineValue(change.before)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                            lineNumber: 513,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-400\",\n                                            children: \"→\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                            lineNumber: 514,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-green-600 font-medium\",\n                                            children: renderInlineValue(change.after)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                            lineNumber: 515,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 512,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true),\n                        change.type === \"added\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-medium text-gray-700 capitalize\",\n                                    children: [\n                                        change.key,\n                                        \":\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 521,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-green-600 font-medium\",\n                                    children: [\n                                        \"+\",\n                                        renderInlineValue(change.value)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 522,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true),\n                        change.type === \"removed\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-medium text-gray-700 capitalize\",\n                                    children: [\n                                        change.key,\n                                        \":\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 527,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-red-600 line-through\",\n                                    children: [\n                                        \"-\",\n                                        renderInlineValue(change.value)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 528,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true)\n                    ]\n                }, idx, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                    lineNumber: 508,\n                    columnNumber: 11\n                }, this))\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n            lineNumber: 506,\n            columnNumber: 7\n        }, this);\n    };\n    // Render a value inline (for compact display)\n    const renderInlineValue = (value)=>{\n        if (value === null) return \"null\";\n        if (value === undefined) return \"undefined\";\n        if (typeof value === \"string\") return '\"'.concat(value, '\"');\n        if (typeof value === \"number\") return value.toString();\n        if (typeof value === \"boolean\") return value.toString();\n        if (Array.isArray(value)) return \"[\".concat(value.length, \" items]\");\n        if (typeof value === \"object\") return \"{\".concat(Object.keys(value).length, \" props}\");\n        return String(value);\n    };\n    // Render changes for objects - show added, removed, and modified properties\n    const renderObjectChanges = (before, after)=>{\n        const beforeKeys = Object.keys(before);\n        const afterKeys = Object.keys(after);\n        const allKeys = Array.from(new Set([\n            ...beforeKeys,\n            ...afterKeys\n        ]));\n        const changes = [];\n        for (const key of allKeys){\n            const beforeValue = before[key];\n            const afterValue = after[key];\n            if (!beforeKeys.includes(key)) {\n                changes.push({\n                    type: \"added\",\n                    key,\n                    value: afterValue\n                });\n            } else if (!afterKeys.includes(key)) {\n                changes.push({\n                    type: \"removed\",\n                    key,\n                    value: beforeValue\n                });\n            } else if (JSON.stringify(beforeValue) !== JSON.stringify(afterValue)) {\n                changes.push({\n                    type: \"modified\",\n                    key,\n                    before: beforeValue,\n                    after: afterValue\n                });\n            }\n        }\n        if (changes.length === 0) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-gray-500 italic\",\n                children: \"No changes in object\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                lineNumber: 571,\n                columnNumber: 14\n            }, this);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-3\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm font-medium text-gray-700\",\n                    children: [\n                        \"Object Changes (\",\n                        beforeKeys.length,\n                        \" → \",\n                        afterKeys.length,\n                        \" properties)\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                    lineNumber: 576,\n                    columnNumber: 9\n                }, this),\n                changes.map((change, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border border-gray-200 rounded p-3\",\n                        children: [\n                            change.type === \"added\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 text-sm font-medium text-green-700\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"w-2 h-2 bg-green-500 rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                lineNumber: 584,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Added property: \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                className: \"bg-gray-100 px-1 rounded\",\n                                                children: change.key\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                lineNumber: 585,\n                                                columnNumber: 35\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                        lineNumber: 583,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-green-50 border border-green-200 rounded p-2\",\n                                        children: renderValue(change.value)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                        lineNumber: 587,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                lineNumber: 582,\n                                columnNumber: 15\n                            }, this),\n                            change.type === \"removed\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 text-sm font-medium text-red-700\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"w-2 h-2 bg-red-500 rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                lineNumber: 595,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Removed property: \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                className: \"bg-gray-100 px-1 rounded\",\n                                                children: change.key\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                lineNumber: 596,\n                                                columnNumber: 37\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                        lineNumber: 594,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-red-50 border border-red-200 rounded p-2\",\n                                        children: renderValue(change.value)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                        lineNumber: 598,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                lineNumber: 593,\n                                columnNumber: 15\n                            }, this),\n                            change.type === \"modified\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 text-sm font-medium text-blue-700\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"w-2 h-2 bg-blue-500 rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                lineNumber: 606,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Modified property: \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                className: \"bg-gray-100 px-1 rounded\",\n                                                children: change.key\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                lineNumber: 607,\n                                                columnNumber: 38\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                        lineNumber: 605,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs font-medium text-red-700 mb-1\",\n                                                        children: \"Before\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                        lineNumber: 611,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-red-50 border border-red-200 rounded p-2\",\n                                                        children: renderValue(change.before)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                        lineNumber: 612,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                lineNumber: 610,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs font-medium text-green-700 mb-1\",\n                                                        children: \"After\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                        lineNumber: 617,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-green-50 border border-green-200 rounded p-2\",\n                                                        children: renderValue(change.after)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                        lineNumber: 618,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                lineNumber: 616,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                        lineNumber: 609,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                lineNumber: 604,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, idx, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                        lineNumber: 580,\n                        columnNumber: 11\n                    }, this))\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n            lineNumber: 575,\n            columnNumber: 7\n        }, this);\n    };\n    // Render delta changes in a user-friendly format\n    const renderDeltaChanges = (changes)=>{\n        const changeEntries = Object.entries(changes);\n        if (changeEntries.length === 0) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-500 text-sm\",\n                    children: \"No changes detected\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                    lineNumber: 638,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                lineNumber: 637,\n                columnNumber: 9\n            }, this);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: changeEntries.map((param)=>{\n                let [field, change] = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border border-gray-200 rounded-lg p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                            className: \"font-medium text-gray-900 mb-4 capitalize\",\n                            children: field.replace(/([A-Z])/g, \" $1\").replace(/^./, (str)=>str.toUpperCase())\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                            lineNumber: 648,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700\",\n                                    children: \"� Change Analysis\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 654,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-500 mb-3\",\n                                    children: \"Intelligent analysis showing what was added, removed, or modified\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 657,\n                                    columnNumber: 17\n                                }, this),\n                                renderSophisticatedChanges(change.before, change.after)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                            lineNumber: 653,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, field, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                    lineNumber: 647,\n                    columnNumber: 13\n                }, this);\n            })\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n            lineNumber: 644,\n            columnNumber: 7\n        }, this);\n    };\n    if (!isOpen || !log) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n        onClick: handleOverlayClick,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg max-w-6xl w-full mx-4 max-h-[90vh] overflow-hidden flex flex-col\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 border-b border-gray-200 flex-shrink-0 flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-bold text-gray-900\",\n                                    children: \"Log Details\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 677,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600 mt-1\",\n                                    children: \"View detailed information about this log entry\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 680,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                            lineNumber: 676,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"p-2 rounded-full hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors\",\n                            title: \"Close\",\n                            \"aria-label\": \"Close modal\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCalendar_FiChevronDown_FiChevronRight_FiDatabase_FiFileText_FiTag_FiUser_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiX, {\n                                className: \"h-5 w-5 text-gray-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                lineNumber: 690,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                            lineNumber: 684,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                    lineNumber: 675,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 overflow-y-auto p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                    children: \"Basic Information\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 698,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2 flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCalendar_FiChevronDown_FiChevronRight_FiDatabase_FiFileText_FiTag_FiUser_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiTag, {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                            lineNumber: 704,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"Operation\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                    lineNumber: 703,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border \".concat(getOperationColor(log.operation)),\n                                                    children: log.operation\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                    lineNumber: 707,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                            lineNumber: 702,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2 flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCalendar_FiChevronDown_FiChevronRight_FiDatabase_FiFileText_FiTag_FiUser_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiDatabase, {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                            lineNumber: 715,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"Entity\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                    lineNumber: 714,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-900 font-medium\",\n                                                    children: log.entity\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                    lineNumber: 718,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                            lineNumber: 713,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2 flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCalendar_FiChevronDown_FiChevronRight_FiDatabase_FiFileText_FiTag_FiUser_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiUser, {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                            lineNumber: 724,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"User\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                    lineNumber: 723,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-900 font-medium\",\n                                                    children: userName || \"Unknown User\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                    lineNumber: 727,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-500 mt-1\",\n                                                    children: log.userUuid\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                    lineNumber: 730,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                            lineNumber: 722,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2 flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCalendar_FiChevronDown_FiChevronRight_FiDatabase_FiFileText_FiTag_FiUser_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiCalendar, {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                            lineNumber: 736,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"Timestamp\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                    lineNumber: 735,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-900\",\n                                                    children: formatDate(log.createdAt)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                    lineNumber: 739,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                            lineNumber: 734,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 700,\n                                    columnNumber: 13\n                                }, this),\n                                log.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2 flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCalendar_FiChevronDown_FiChevronRight_FiDatabase_FiFileText_FiTag_FiUser_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiFileText, {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                    lineNumber: 747,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Description\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                            lineNumber: 746,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-900 bg-gray-50 p-3 rounded-lg border\",\n                                            children: log.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                            lineNumber: 750,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 745,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                            lineNumber: 697,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                    children: log.data && hasDeltaChanges(log.data) ? \"Changes\" : \"Additional Data\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 759,\n                                    columnNumber: 13\n                                }, this),\n                                log.data ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: hasDeltaChanges(log.data) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-3\",\n                                                        children: [\n                                                            \"Field Changes (\",\n                                                            log.data.changeCount || 0,\n                                                            \" changes)\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                        lineNumber: 769,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    renderDeltaChanges(log.data.changes)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                lineNumber: 768,\n                                                columnNumber: 21\n                                            }, this),\n                                            Object.keys(log.data).some((key)=>![\n                                                    \"changes\",\n                                                    \"changedFields\",\n                                                    \"changeCount\"\n                                                ].includes(key)) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                        children: \"Additional Information\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                        lineNumber: 778,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gray-900 text-green-400 p-4 rounded-lg border overflow-x-auto\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                            className: \"text-sm font-mono whitespace-pre-wrap\",\n                                                            children: formatJsonData(Object.fromEntries(Object.entries(log.data).filter((param)=>{\n                                                                let [key] = param;\n                                                                return ![\n                                                                    \"changes\",\n                                                                    \"changedFields\",\n                                                                    \"changeCount\"\n                                                                ].includes(key);\n                                                            })))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                            lineNumber: 782,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                        lineNumber: 781,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                lineNumber: 777,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                        lineNumber: 766,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                children: \"JSON Data\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                lineNumber: 797,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-900 text-green-400 p-4 rounded-lg border overflow-x-auto\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                    className: \"text-sm font-mono whitespace-pre-wrap\",\n                                                    children: formatJsonData(log.data)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                    lineNumber: 801,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                lineNumber: 800,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                        lineNumber: 796,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 764,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCalendar_FiChevronDown_FiChevronRight_FiDatabase_FiFileText_FiTag_FiUser_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiDatabase, {\n                                            className: \"w-12 h-12 text-gray-300 mx-auto mb-3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                            lineNumber: 810,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-500 text-sm\",\n                                            children: \"No additional data available\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                            lineNumber: 811,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 809,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                            lineNumber: 758,\n                            columnNumber: 11\n                        }, this),\n                        log.data && hasDeltaChanges(log.data) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                    children: \"Raw Data\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 819,\n                                    columnNumber: 15\n                                }, this),\n                                extractOldData(log.data) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4 border border-gray-200 rounded-lg overflow-hidden\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowOldDataTab(!showOldDataTab),\n                                            className: \"w-full px-4 py-3 bg-gray-50 hover:bg-gray-100 flex items-center justify-between text-left transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        showOldDataTab ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCalendar_FiChevronDown_FiChevronRight_FiDatabase_FiFileText_FiTag_FiUser_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiChevronDown, {\n                                                            className: \"w-4 h-4 text-gray-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                            lineNumber: 830,\n                                                            columnNumber: 25\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCalendar_FiChevronDown_FiChevronRight_FiDatabase_FiFileText_FiTag_FiUser_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiChevronRight, {\n                                                            className: \"w-4 h-4 text-gray-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                            lineNumber: 832,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-gray-900\",\n                                                            children: \"Raw Old Data (Before Changes)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                            lineNumber: 834,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                    lineNumber: 828,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-gray-500 bg-red-100 px-2 py-1 rounded\",\n                                                    children: \"Original Values\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                    lineNumber: 836,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                            lineNumber: 824,\n                                            columnNumber: 19\n                                        }, this),\n                                        showOldDataTab && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-4 bg-gray-900 text-green-400 overflow-x-auto\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                className: \"text-sm font-mono whitespace-pre-wrap\",\n                                                children: formatJsonData(extractOldData(log.data) || {})\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                lineNumber: 842,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                            lineNumber: 841,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 823,\n                                    columnNumber: 17\n                                }, this),\n                                extractNewData(log.data) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4 border border-gray-200 rounded-lg overflow-hidden\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowNewDataTab(!showNewDataTab),\n                                            className: \"w-full px-4 py-3 bg-gray-50 hover:bg-gray-100 flex items-center justify-between text-left transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        showNewDataTab ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCalendar_FiChevronDown_FiChevronRight_FiDatabase_FiFileText_FiTag_FiUser_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiChevronDown, {\n                                                            className: \"w-4 h-4 text-gray-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                            lineNumber: 859,\n                                                            columnNumber: 25\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCalendar_FiChevronDown_FiChevronRight_FiDatabase_FiFileText_FiTag_FiUser_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiChevronRight, {\n                                                            className: \"w-4 h-4 text-gray-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                            lineNumber: 861,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-gray-900\",\n                                                            children: \"Raw New Data (After Changes)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                            lineNumber: 863,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                    lineNumber: 857,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-gray-500 bg-green-100 px-2 py-1 rounded\",\n                                                    children: \"Updated Values\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                    lineNumber: 865,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                            lineNumber: 853,\n                                            columnNumber: 19\n                                        }, this),\n                                        showNewDataTab && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-4 bg-gray-900 text-green-400 overflow-x-auto\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                className: \"text-sm font-mono whitespace-pre-wrap\",\n                                                children: formatJsonData(extractNewData(log.data) || {})\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                                lineNumber: 871,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                            lineNumber: 870,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 852,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                            lineNumber: 818,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"pt-6 border-t border-gray-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"Log ID\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 883,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                    className: \"text-xs text-gray-600 bg-gray-100 px-2 py-1 rounded\",\n                                    children: log.id\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                                    lineNumber: 886,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                            lineNumber: 882,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                    lineNumber: 695,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 border-t border-gray-200 flex-shrink-0 flex justify-end\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onClose,\n                        className: \"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-400 transition-colors\",\n                        children: \"Close\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                        lineNumber: 894,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n                    lineNumber: 893,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n            lineNumber: 673,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\dido-distribution\\\\frontend\\\\app\\\\logs\\\\components\\\\LogDetailsModal.tsx\",\n        lineNumber: 672,\n        columnNumber: 5\n    }, this);\n}\n_s(LogDetailsModal, \"3gNnT4s7QjbDbG3nIYQIGUhCzM8=\");\n_c = LogDetailsModal;\nvar _c;\n$RefreshReg$(_c, \"LogDetailsModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9sb2dzL2NvbXBvbmVudHMvTG9nRGV0YWlsc01vZGFsLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQW1EO0FBQ29FO0FBVWhILFNBQVNXLGdCQUFnQixLQUF3RDtRQUF4RCxFQUFFQyxNQUFNLEVBQUVDLEdBQUcsRUFBRUMsT0FBTyxFQUFFQyxRQUFRLEVBQXdCLEdBQXhEOztJQUM5Qiw0QkFBNEI7SUFDNUIsTUFBTSxDQUFDQyxnQkFBZ0JDLGtCQUFrQixHQUFHZiwrQ0FBUUEsQ0FBQztJQUNyRCxNQUFNLENBQUNnQixnQkFBZ0JDLGtCQUFrQixHQUFHakIsK0NBQVFBLENBQUM7SUFFckQsbUNBQW1DO0lBQ25DRCxnREFBU0EsQ0FBQztRQUNSLElBQUksQ0FBQ1csUUFBUTtRQUViLE1BQU1RLGdCQUFnQixDQUFDQztZQUNyQixJQUFJQSxFQUFFQyxHQUFHLEtBQUssVUFBVTtnQkFDdEJELEVBQUVFLGNBQWM7Z0JBQ2hCVDtZQUNGO1FBQ0Y7UUFFQVUsT0FBT0MsZ0JBQWdCLENBQUMsV0FBV0w7UUFDbkMsT0FBTyxJQUFNSSxPQUFPRSxtQkFBbUIsQ0FBQyxXQUFXTjtJQUNyRCxHQUFHO1FBQUNSO1FBQVFFO0tBQVE7SUFFcEIseUNBQXlDO0lBQ3pDLE1BQU1hLHFCQUFxQixDQUFDTjtRQUMxQixJQUFJQSxFQUFFTyxNQUFNLEtBQUtQLEVBQUVRLGFBQWEsRUFBRTtZQUNoQ2Y7UUFDRjtJQUNGO0lBRUEsMEJBQTBCO0lBQzFCLE1BQU1nQixhQUFhLENBQUNDO1FBQ2xCLE1BQU1DLFVBQVUsT0FBT0QsU0FBUyxXQUFXLElBQUlFLEtBQUtGLFFBQVFBO1FBQzVELE9BQU9DLFFBQVFFLGNBQWMsQ0FBQyxTQUFTO1lBQ3JDQyxNQUFNO1lBQ05DLE9BQU87WUFDUEMsS0FBSztZQUNMQyxNQUFNO1lBQ05DLFFBQVE7WUFDUkMsUUFBUTtRQUNWO0lBQ0Y7SUFFQSw4Q0FBOEM7SUFDOUMsTUFBTUMsb0JBQW9CLENBQUNDO1FBQ3pCLE1BQU1DLEtBQUtELFVBQVVFLFdBQVc7UUFDaEMsSUFBSUQsR0FBR0UsUUFBUSxDQUFDLGFBQWFGLEdBQUdFLFFBQVEsQ0FBQyxRQUFRO1lBQy9DLE9BQU87UUFDVCxPQUFPLElBQUlGLEdBQUdFLFFBQVEsQ0FBQyxhQUFhRixHQUFHRSxRQUFRLENBQUMsV0FBV0YsR0FBR0UsUUFBUSxDQUFDLFdBQVc7WUFDaEYsT0FBTztRQUNULE9BQU8sSUFBSUYsR0FBR0UsUUFBUSxDQUFDLGFBQWFGLEdBQUdFLFFBQVEsQ0FBQyxXQUFXO1lBQ3pELE9BQU87UUFDVCxPQUFPLElBQUlGLEdBQUdFLFFBQVEsQ0FBQyxhQUFhRixHQUFHRSxRQUFRLENBQUMsV0FBVztZQUN6RCxPQUFPO1FBQ1QsT0FBTyxJQUFJRixHQUFHRSxRQUFRLENBQUMsY0FBY0YsR0FBR0UsUUFBUSxDQUFDLGFBQWE7WUFDNUQsT0FBTztRQUNULE9BQU8sSUFBSUYsR0FBR0UsUUFBUSxDQUFDLGVBQWVGLEdBQUdFLFFBQVEsQ0FBQyxTQUFTO1lBQ3pELE9BQU87UUFDVCxPQUFPLElBQUlGLEdBQUdFLFFBQVEsQ0FBQyxhQUFhRixHQUFHRSxRQUFRLENBQUMsV0FBVztZQUN6RCxPQUFPO1FBQ1QsT0FBTztZQUNMLE9BQU87UUFDVDtJQUNGO0lBRUEsK0JBQStCO0lBQy9CLE1BQU1DLGlCQUFpQixDQUFDQztRQUN0QixJQUFJO1lBQ0YsT0FBT0MsS0FBS0MsU0FBUyxDQUFDRixNQUFNLE1BQU07UUFDcEMsRUFBRSxPQUFPRyxPQUFPO1lBQ2QsT0FBTztRQUNUO0lBQ0Y7SUFFQSwrQ0FBK0M7SUFDL0MsTUFBTUMsa0JBQWtCLENBQUNKO1FBQ3ZCLE9BQU9BLFFBQVFBLEtBQUtLLE9BQU8sSUFBSSxPQUFPTCxLQUFLSyxPQUFPLEtBQUs7SUFDekQ7SUFFQSwwQ0FBMEM7SUFDMUMsTUFBTUMsaUJBQWlCLENBQUNOO1FBQ3RCLElBQUksQ0FBQ0ksZ0JBQWdCSixPQUFPLE9BQU87UUFFbkMsTUFBTU8sVUFBK0IsQ0FBQztRQUN0Q0MsT0FBT0MsT0FBTyxDQUFDVCxLQUFLSyxPQUFPLEVBQUVLLE9BQU8sQ0FBQztnQkFBQyxDQUFDQyxPQUFPQyxPQUFzQjtZQUNsRSxJQUFJQSxVQUFVLE9BQU9BLFdBQVcsWUFBWSxZQUFZQSxRQUFRO2dCQUM5REwsT0FBTyxDQUFDSSxNQUFNLEdBQUdDLE9BQU9DLE1BQU07WUFDaEM7UUFDRjtRQUNBLE9BQU9MLE9BQU9NLElBQUksQ0FBQ1AsU0FBU1EsTUFBTSxHQUFHLElBQUlSLFVBQVU7SUFDckQ7SUFFQSwwQ0FBMEM7SUFDMUMsTUFBTVMsaUJBQWlCLENBQUNoQjtRQUN0QixJQUFJLENBQUNJLGdCQUFnQkosT0FBTyxPQUFPO1FBRW5DLE1BQU1pQixVQUErQixDQUFDO1FBQ3RDVCxPQUFPQyxPQUFPLENBQUNULEtBQUtLLE9BQU8sRUFBRUssT0FBTyxDQUFDO2dCQUFDLENBQUNDLE9BQU9DLE9BQXNCO1lBQ2xFLElBQUlBLFVBQVUsT0FBT0EsV0FBVyxZQUFZLFdBQVdBLFFBQVE7Z0JBQzdESyxPQUFPLENBQUNOLE1BQU0sR0FBR0MsT0FBT00sS0FBSztZQUMvQjtRQUNGO1FBQ0EsT0FBT1YsT0FBT00sSUFBSSxDQUFDRyxTQUFTRixNQUFNLEdBQUcsSUFBSUUsVUFBVTtJQUNyRDtJQUlBLHdGQUF3RjtJQUN4RixNQUFNRSw2QkFBNkIsQ0FBQ04sUUFBYUs7UUFDL0MsOEJBQThCO1FBQzlCLElBQUlMLFdBQVcsUUFBUUssVUFBVSxNQUFNO1lBQ3JDLHFCQUFPLDhEQUFDRTtnQkFBSUMsV0FBVTswQkFBdUI7Ozs7OztRQUMvQztRQUVBLElBQUlSLFdBQVcsTUFBTTtZQUNuQixxQkFDRSw4REFBQ087Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDRDt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNDO2dDQUFLRCxXQUFVOzs7Ozs7NEJBQTJDOzs7Ozs7O2tDQUc3RCw4REFBQ0Q7d0JBQUlDLFdBQVU7a0NBQ1pFLFlBQVlMOzs7Ozs7Ozs7Ozs7UUFJckI7UUFFQSxJQUFJQSxVQUFVLE1BQU07WUFDbEIscUJBQ0UsOERBQUNFO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ0Q7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDQztnQ0FBS0QsV0FBVTs7Ozs7OzRCQUF5Qzs7Ozs7OztrQ0FHM0QsOERBQUNEO3dCQUFJQyxXQUFVO2tDQUNaRSxZQUFZVjs7Ozs7Ozs7Ozs7O1FBSXJCO1FBRUEsb0NBQW9DO1FBQ3BDLE1BQU1XLGFBQWFDLE1BQU1DLE9BQU8sQ0FBQ2IsVUFBVSxVQUFVLE9BQU9BO1FBQzVELE1BQU1jLFlBQVlGLE1BQU1DLE9BQU8sQ0FBQ1IsU0FBUyxVQUFVLE9BQU9BO1FBRTFELGVBQWU7UUFDZixJQUFJTSxlQUFlRyxXQUFXO1lBQzVCLHFCQUNFLDhEQUFDUDtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNEO3dCQUFJQyxXQUFVOzs0QkFBc0M7NEJBQWVHOzRCQUFXOzRCQUFJRzs7Ozs7OztrQ0FDbkYsOERBQUNQO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0Q7O2tEQUNDLDhEQUFDQTt3Q0FBSUMsV0FBVTs7NENBQXdDOzRDQUFTRzs0Q0FBVzs7Ozs7OztrREFDM0UsOERBQUNKO3dDQUFJQyxXQUFVO2tEQUNaRSxZQUFZVjs7Ozs7Ozs7Ozs7OzBDQUdqQiw4REFBQ087O2tEQUNDLDhEQUFDQTt3Q0FBSUMsV0FBVTs7NENBQTBDOzRDQUFRTTs0Q0FBVTs7Ozs7OztrREFDM0UsOERBQUNQO3dDQUFJQyxXQUFVO2tEQUNaRSxZQUFZTDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O1FBTXpCO1FBRUEsMENBQTBDO1FBQzFDLElBQUlNLGVBQWUsU0FBUztZQUMxQixPQUFPSSxtQkFBbUJmLFFBQVFLO1FBQ3BDLE9BQU8sSUFBSU0sZUFBZSxVQUFVO1lBQ2xDLE9BQU9LLG9CQUFvQmhCLFFBQVFLO1FBQ3JDLE9BQU87WUFDTCxtQkFBbUI7WUFDbkIsSUFBSUwsV0FBV0ssT0FBTztnQkFDcEIscUJBQU8sOERBQUNFO29CQUFJQyxXQUFVOzhCQUF1Qjs7Ozs7O1lBQy9DO1lBQ0EsT0FBT1Msc0JBQXNCakIsUUFBUUs7UUFDdkM7SUFDRjtJQUVBLGdFQUFnRTtJQUNoRSxNQUFNSyxjQUFjLENBQUNRO1FBQ25CLElBQUlBLFVBQVUsTUFBTSxxQkFBTyw4REFBQ1Q7WUFBS0QsV0FBVTtzQkFBdUI7Ozs7OztRQUNsRSxJQUFJVSxVQUFVQyxXQUFXLHFCQUFPLDhEQUFDVjtZQUFLRCxXQUFVO3NCQUF1Qjs7Ozs7O1FBQ3ZFLElBQUksT0FBT1UsVUFBVSxVQUFVLHFCQUFPLDhEQUFDVDtZQUFLRCxXQUFVOztnQkFBZ0I7Z0JBQUVVO2dCQUFNOzs7Ozs7O1FBQzlFLElBQUksT0FBT0EsVUFBVSxVQUFVLHFCQUFPLDhEQUFDVDtZQUFLRCxXQUFVO3NCQUFtQlU7Ozs7OztRQUN6RSxJQUFJLE9BQU9BLFVBQVUsV0FBVyxxQkFBTyw4REFBQ1Q7WUFBS0QsV0FBVTtzQkFBbUJZLE9BQU9GOzs7Ozs7UUFDakYsSUFBSU4sTUFBTUMsT0FBTyxDQUFDSyxRQUFRO1lBQ3hCLHFCQUNFLDhEQUFDWDtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNDO3dCQUFLRCxXQUFVO2tDQUFnQjs7Ozs7O29CQUMvQlUsTUFBTWhCLE1BQU0sS0FBSyxrQkFDaEIsOERBQUNPO3dCQUFLRCxXQUFVO2tDQUF1Qjs7Ozs7NkNBRXZDLDhEQUFDQzt3QkFBS0QsV0FBVTs7NEJBQWlCVSxNQUFNaEIsTUFBTTs0QkFBQzs7Ozs7OztrQ0FFaEQsOERBQUNPO3dCQUFLRCxXQUFVO2tDQUFnQjs7Ozs7Ozs7Ozs7O1FBR3RDO1FBQ0EsSUFBSSxPQUFPVSxVQUFVLFVBQVU7WUFDN0IsTUFBTWpCLE9BQU9OLE9BQU9NLElBQUksQ0FBQ2lCO1lBQ3pCLHFCQUNFLDhEQUFDWDtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNDO3dCQUFLRCxXQUFVO2tDQUFpQjs7Ozs7O29CQUNoQ1AsS0FBS0MsTUFBTSxLQUFLLGtCQUNmLDhEQUFDTzt3QkFBS0QsV0FBVTtrQ0FBdUI7Ozs7OzZDQUV2Qyw4REFBQ0M7d0JBQUtELFdBQVU7OzRCQUFpQlAsS0FBS0MsTUFBTTs0QkFBQzs7Ozs7OztrQ0FFL0MsOERBQUNPO3dCQUFLRCxXQUFVO2tDQUFpQjs7Ozs7Ozs7Ozs7O1FBR3ZDO1FBQ0EscUJBQU8sOERBQUNDO1lBQUtELFdBQVU7c0JBQWlCWSxPQUFPRjs7Ozs7O0lBQ2pEO0lBRUEsc0NBQXNDO0lBQ3RDLE1BQU1ELHdCQUF3QixDQUFDakIsUUFBYUs7UUFDMUMscUJBQ0UsOERBQUNFO1lBQUlDLFdBQVU7OzhCQUNiLDhEQUFDRDs7c0NBQ0MsOERBQUNBOzRCQUFJQyxXQUFVO3NDQUF3Qzs7Ozs7O3NDQUN2RCw4REFBQ0Q7NEJBQUlDLFdBQVU7c0NBQ1pFLFlBQVlWOzs7Ozs7Ozs7Ozs7OEJBR2pCLDhEQUFDTzs7c0NBQ0MsOERBQUNBOzRCQUFJQyxXQUFVO3NDQUEwQzs7Ozs7O3NDQUN6RCw4REFBQ0Q7NEJBQUlDLFdBQVU7c0NBQ1pFLFlBQVlMOzs7Ozs7Ozs7Ozs7Ozs7Ozs7SUFLdkI7SUFFQSxrRkFBa0Y7SUFDbEYsTUFBTWdCLHVCQUF1QixDQUFDQztRQUM1QixJQUFJLENBQUNBLE9BQU9BLElBQUlwQixNQUFNLEtBQUssR0FBRyxPQUFPO1FBQ3JDLE1BQU1xQixZQUFZRCxHQUFHLENBQUMsRUFBRTtRQUN4QixPQUFPLE9BQU9DLGNBQWMsWUFDckJBLGNBQWMsUUFDYixTQUFRQSxhQUFhLFVBQVVBLGFBQWEsV0FBV0EsYUFBYSxjQUFjQSxTQUFRO0lBQ3BHO0lBRUEsb0RBQW9EO0lBQ3BELE1BQU1DLHFCQUFxQixDQUFDQztRQUMxQixJQUFJLENBQUNBLFFBQVEsT0FBT0EsU0FBUyxVQUFVO1lBQ3JDLE9BQU9mLFlBQVllO1FBQ3JCO1FBRUEsTUFBTSxFQUFFQyxFQUFFLEVBQUVDLElBQUksRUFBRUMsS0FBSyxFQUFFQyxRQUFRLEVBQUUsR0FBR0MsWUFBWSxHQUFHTDtRQUVyRCxxQkFDRSw4REFBQ2xCO1lBQUlDLFdBQVU7OzhCQUNiLDhEQUFDRDtvQkFBSUMsV0FBVTs7d0JBQ1ptQixzQkFDQyw4REFBQ3BCOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0M7b0NBQUtELFdBQVU7OENBQXdCOzs7Ozs7OENBQ3hDLDhEQUFDQztvQ0FBS0QsV0FBVTs4Q0FBNkJtQjs7Ozs7Ozs7Ozs7O3dCQUdoREUsYUFBYVYsMkJBQ1osOERBQUNaOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0M7b0NBQUtELFdBQVU7OENBQXdCOzs7Ozs7OENBQ3hDLDhEQUFDQztvQ0FBS0QsV0FBVTs4Q0FBK0JxQjs7Ozs7Ozs7Ozs7O3dCQUdsREQsVUFBVVQsMkJBQ1QsOERBQUNaOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0M7b0NBQUtELFdBQVU7OENBQXdCOzs7Ozs7OENBQ3hDLDhEQUFDQztvQ0FBS0QsV0FBVTs7d0NBQTZCO3dDQUFFb0I7Ozs7Ozs7Ozs7Ozs7d0JBR2xERixPQUFPUCwyQkFDTiw4REFBQ1o7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDQztvQ0FBS0QsV0FBVTs4Q0FBd0I7Ozs7Ozs4Q0FDeEMsOERBQUNDO29DQUFLRCxXQUFVOzhDQUFtQ2tCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Z0JBSXhEL0IsT0FBT00sSUFBSSxDQUFDNkIsWUFBWTVCLE1BQU0sR0FBRyxtQkFDaEMsOERBQUNLO29CQUFJQyxXQUFVOzt3QkFBd0I7d0JBQ25DYixPQUFPTSxJQUFJLENBQUM2QixZQUFZNUIsTUFBTTt3QkFBQzs7Ozs7Ozs7Ozs7OztJQUszQztJQUVBLDZDQUE2QztJQUM3QyxNQUFNNkIsbUJBQW1CLENBQUNOLE1BQVdPO1FBQ25DLElBQUksQ0FBQ1AsUUFBUSxDQUFDTyxPQUFPLE9BQU87UUFFNUIsMkJBQTJCO1FBQzNCLElBQUlQLEtBQUtDLEVBQUUsS0FBS1AsV0FBVztZQUN6QixPQUFPYSxNQUFNQyxJQUFJLENBQUNDLENBQUFBLFVBQVdBLFdBQVdBLFFBQVFSLEVBQUUsS0FBS0QsS0FBS0MsRUFBRTtRQUNoRTtRQUVBLDBCQUEwQjtRQUMxQixPQUFPTSxNQUFNQyxJQUFJLENBQUNDLENBQUFBLFVBQVc5QyxLQUFLQyxTQUFTLENBQUM2QyxhQUFhOUMsS0FBS0MsU0FBUyxDQUFDb0M7SUFDMUU7SUFFQSwwREFBMEQ7SUFDMUQsTUFBTVYscUJBQXFCLENBQUNmLFFBQWVLO1FBQ3pDLE1BQU04QixrQkFBa0JkLHFCQUFxQnJCLFdBQVdxQixxQkFBcUJoQjtRQUU3RSxJQUFJOEIsaUJBQWlCO1lBQ25CLE9BQU9DLDJCQUEyQnBDLFFBQVFLO1FBQzVDO1FBRUEsaURBQWlEO1FBQ2pELE1BQU1nQyxZQUFZQyxLQUFLQyxHQUFHLENBQUN2QyxPQUFPRSxNQUFNLEVBQUVHLE1BQU1ILE1BQU07UUFDdEQsTUFBTVYsVUFBVSxFQUFFO1FBRWxCLElBQUssSUFBSWdELElBQUksR0FBR0EsSUFBSUgsV0FBV0csSUFBSztZQUNsQyxNQUFNQyxhQUFhRCxJQUFJeEMsT0FBT0UsTUFBTSxHQUFHRixNQUFNLENBQUN3QyxFQUFFLEdBQUdyQjtZQUNuRCxNQUFNdUIsWUFBWUYsSUFBSW5DLE1BQU1ILE1BQU0sR0FBR0csS0FBSyxDQUFDbUMsRUFBRSxHQUFHckI7WUFFaEQsSUFBSXNCLGVBQWV0QixXQUFXO2dCQUM1QjNCLFFBQVFtRCxJQUFJLENBQUM7b0JBQUVDLE1BQU07b0JBQVNDLE9BQU9MO29CQUFHdEIsT0FBT3dCO2dCQUFVO1lBQzNELE9BQU8sSUFBSUEsY0FBY3ZCLFdBQVc7Z0JBQ2xDM0IsUUFBUW1ELElBQUksQ0FBQztvQkFBRUMsTUFBTTtvQkFBV0MsT0FBT0w7b0JBQUd0QixPQUFPdUI7Z0JBQVc7WUFDOUQsT0FBTyxJQUFJckQsS0FBS0MsU0FBUyxDQUFDb0QsZ0JBQWdCckQsS0FBS0MsU0FBUyxDQUFDcUQsWUFBWTtnQkFDbkVsRCxRQUFRbUQsSUFBSSxDQUFDO29CQUFFQyxNQUFNO29CQUFZQyxPQUFPTDtvQkFBR3hDLFFBQVF5QztvQkFBWXBDLE9BQU9xQztnQkFBVTtZQUNsRjtRQUNGO1FBRUEsSUFBSWxELFFBQVFVLE1BQU0sS0FBSyxHQUFHO1lBQ3hCLHFCQUFPLDhEQUFDSztnQkFBSUMsV0FBVTswQkFBdUI7Ozs7OztRQUMvQztRQUVBLHFCQUNFLDhEQUFDRDtZQUFJQyxXQUFVOzs4QkFDYiw4REFBQ0Q7b0JBQUlDLFdBQVU7O3dCQUFvQzt3QkFDakNSLE9BQU9FLE1BQU07d0JBQUM7d0JBQUlHLE1BQU1ILE1BQU07d0JBQUM7Ozs7Ozs7Z0JBRWhEVixRQUFRc0QsR0FBRyxDQUFDLENBQUMvQyxRQUFRZ0Qsb0JBQ3BCLDhEQUFDeEM7d0JBQWNDLFdBQVU7OzRCQUN0QlQsT0FBTzZDLElBQUksS0FBSyx5QkFDZiw4REFBQ3JDO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0Q7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDQztnREFBS0QsV0FBVTs7Ozs7OzRDQUEyQzs0Q0FDM0NULE9BQU84QyxLQUFLOzs7Ozs7O2tEQUU5Qiw4REFBQ3RDO3dDQUFJQyxXQUFVO2tEQUNaRSxZQUFZWCxPQUFPbUIsS0FBSzs7Ozs7Ozs7Ozs7OzRCQUk5Qm5CLE9BQU82QyxJQUFJLEtBQUssMkJBQ2YsOERBQUNyQztnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNEO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ0M7Z0RBQUtELFdBQVU7Ozs7Ozs0Q0FBeUM7NENBQ3JDVCxPQUFPOEMsS0FBSzs7Ozs7OztrREFFbEMsOERBQUN0Qzt3Q0FBSUMsV0FBVTtrREFDWkUsWUFBWVgsT0FBT21CLEtBQUs7Ozs7Ozs7Ozs7Ozs0QkFJOUJuQixPQUFPNkMsSUFBSSxLQUFLLDRCQUNmLDhEQUFDckM7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNDO2dEQUFLRCxXQUFVOzs7Ozs7NENBQTBDOzRDQUN2Q1QsT0FBTzhDLEtBQUs7Ozs7Ozs7a0RBRWpDLDhEQUFDdEM7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDRDs7a0VBQ0MsOERBQUNBO3dEQUFJQyxXQUFVO2tFQUF3Qzs7Ozs7O2tFQUN2RCw4REFBQ0Q7d0RBQUlDLFdBQVU7a0VBQ1pFLFlBQVlYLE9BQU9DLE1BQU07Ozs7Ozs7Ozs7OzswREFHOUIsOERBQUNPOztrRUFDQyw4REFBQ0E7d0RBQUlDLFdBQVU7a0VBQTBDOzs7Ozs7a0VBQ3pELDhEQUFDRDt3REFBSUMsV0FBVTtrRUFDWkUsWUFBWVgsT0FBT00sS0FBSzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozt1QkF2QzNCMEM7Ozs7Ozs7Ozs7O0lBaURsQjtJQUVBLHVFQUF1RTtJQUN2RSxNQUFNWCw2QkFBNkIsQ0FBQ3BDLFFBQWVLO1FBQ2pELE1BQU1iLFVBQVUsRUFBRTtRQUNsQixNQUFNd0Qsc0JBQXNCLElBQUlDO1FBRWhDLGtDQUFrQztRQUNsQyxLQUFLLE1BQU1SLGNBQWN6QyxPQUFRO1lBQy9CLE1BQU1rRCxvQkFBb0JuQixpQkFBaUJVLFlBQVlwQztZQUV2RCxJQUFJLENBQUM2QyxtQkFBbUI7Z0JBQ3RCMUQsUUFBUW1ELElBQUksQ0FBQztvQkFBRUMsTUFBTTtvQkFBV25CLE1BQU1nQjtnQkFBVztZQUNuRCxPQUFPO2dCQUNMTyxvQkFBb0JHLEdBQUcsQ0FBQzlDLE1BQU0rQyxPQUFPLENBQUNGO2dCQUN0QyxJQUFJOUQsS0FBS0MsU0FBUyxDQUFDb0QsZ0JBQWdCckQsS0FBS0MsU0FBUyxDQUFDNkQsb0JBQW9CO29CQUNwRTFELFFBQVFtRCxJQUFJLENBQUM7d0JBQUVDLE1BQU07d0JBQVk1QyxRQUFReUM7d0JBQVlwQyxPQUFPNkM7b0JBQWtCO2dCQUNoRjtZQUNGO1FBQ0Y7UUFFQSxtQkFBbUI7UUFDbkIsSUFBSyxJQUFJVixJQUFJLEdBQUdBLElBQUluQyxNQUFNSCxNQUFNLEVBQUVzQyxJQUFLO1lBQ3JDLElBQUksQ0FBQ1Esb0JBQW9CSyxHQUFHLENBQUNiLElBQUk7Z0JBQy9CLE1BQU1FLFlBQVlyQyxLQUFLLENBQUNtQyxFQUFFO2dCQUMxQixNQUFNYyxxQkFBcUJ2QixpQkFBaUJXLFdBQVcxQztnQkFDdkQsSUFBSSxDQUFDc0Qsb0JBQW9CO29CQUN2QjlELFFBQVFtRCxJQUFJLENBQUM7d0JBQUVDLE1BQU07d0JBQVNuQixNQUFNaUI7b0JBQVU7Z0JBQ2hEO1lBQ0Y7UUFDRjtRQUVBLElBQUlsRCxRQUFRVSxNQUFNLEtBQUssR0FBRztZQUN4QixxQkFBTyw4REFBQ0s7Z0JBQUlDLFdBQVU7MEJBQXVCOzs7Ozs7UUFDL0M7UUFFQSxxQkFDRSw4REFBQ0Q7WUFBSUMsV0FBVTs7OEJBQ2IsOERBQUNEO29CQUFJQyxXQUFVOzt3QkFBb0M7d0JBQ2pDUixPQUFPRSxNQUFNO3dCQUFDO3dCQUFJRyxNQUFNSCxNQUFNO3dCQUFDOzs7Ozs7O2dCQUVoRFYsUUFBUXNELEdBQUcsQ0FBQyxDQUFDL0MsUUFBUWdELG9CQUNwQiw4REFBQ3hDO3dCQUFjQyxXQUFVOzs0QkFDdEJULE9BQU82QyxJQUFJLEtBQUsseUJBQ2YsOERBQUNyQztnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNEO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ0M7Z0RBQUtELFdBQVU7Ozs7Ozs0Q0FBMkM7Ozs7Ozs7a0RBRzdELDhEQUFDRDt3Q0FBSUMsV0FBVTtrREFDWmdCLG1CQUFtQnpCLE9BQU8wQixJQUFJOzs7Ozs7Ozs7Ozs7NEJBSXBDMUIsT0FBTzZDLElBQUksS0FBSywyQkFDZiw4REFBQ3JDO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0Q7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDQztnREFBS0QsV0FBVTs7Ozs7OzRDQUF5Qzs7Ozs7OztrREFHM0QsOERBQUNEO3dDQUFJQyxXQUFVO2tEQUNaZ0IsbUJBQW1CekIsT0FBTzBCLElBQUk7Ozs7Ozs7Ozs7Ozs0QkFJcEMxQixPQUFPNkMsSUFBSSxLQUFLLDRCQUNmLDhEQUFDckM7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNDO2dEQUFLRCxXQUFVOzs7Ozs7NENBQTBDOzRDQUMzQ1QsT0FBT0MsTUFBTSxDQUFDMkIsSUFBSSxJQUFJLElBQXVCLE9BQW5CNUIsT0FBT0MsTUFBTSxDQUFDMkIsSUFBSSxFQUFDOzs7Ozs7O29DQUU3RDRCLDBCQUEwQnhELE9BQU9DLE1BQU0sRUFBRUQsT0FBT00sS0FBSzs7Ozs7Ozs7dUJBN0JsRDBDOzs7Ozs7Ozs7OztJQW9DbEI7SUFFQSxpREFBaUQ7SUFDakQsTUFBTVEsNEJBQTRCLENBQUN2RCxRQUFhSztRQUM5QyxNQUFNbUQsYUFBYTdELE9BQU9NLElBQUksQ0FBQ0Q7UUFDL0IsTUFBTXlELFlBQVk5RCxPQUFPTSxJQUFJLENBQUNJO1FBQzlCLE1BQU1xRCxVQUFVOUMsTUFBTStDLElBQUksQ0FBQyxJQUFJVixJQUFJO2VBQUlPO2VBQWVDO1NBQVU7UUFFaEUsTUFBTWpFLFVBQVUsRUFBRTtRQUVsQixLQUFLLE1BQU05QixPQUFPZ0csUUFBUztZQUN6QixNQUFNRSxjQUFjNUQsTUFBTSxDQUFDdEMsSUFBSTtZQUMvQixNQUFNbUcsYUFBYXhELEtBQUssQ0FBQzNDLElBQUk7WUFFN0IsSUFBSSxDQUFDOEYsV0FBV3ZFLFFBQVEsQ0FBQ3ZCLE1BQU07Z0JBQzdCOEIsUUFBUW1ELElBQUksQ0FBQztvQkFBRUMsTUFBTTtvQkFBU2xGO29CQUFLd0QsT0FBTzJDO2dCQUFXO1lBQ3ZELE9BQU8sSUFBSSxDQUFDSixVQUFVeEUsUUFBUSxDQUFDdkIsTUFBTTtnQkFDbkM4QixRQUFRbUQsSUFBSSxDQUFDO29CQUFFQyxNQUFNO29CQUFXbEY7b0JBQUt3RCxPQUFPMEM7Z0JBQVk7WUFDMUQsT0FBTyxJQUFJeEUsS0FBS0MsU0FBUyxDQUFDdUUsaUJBQWlCeEUsS0FBS0MsU0FBUyxDQUFDd0UsYUFBYTtnQkFDckVyRSxRQUFRbUQsSUFBSSxDQUFDO29CQUFFQyxNQUFNO29CQUFZbEY7b0JBQUtzQyxRQUFRNEQ7b0JBQWF2RCxPQUFPd0Q7Z0JBQVc7WUFDL0U7UUFDRjtRQUVBLElBQUlyRSxRQUFRVSxNQUFNLEtBQUssR0FBRztZQUN4QixxQkFBTyw4REFBQ0s7Z0JBQUlDLFdBQVU7MEJBQXVCOzs7Ozs7UUFDL0M7UUFFQSxxQkFDRSw4REFBQ0Q7WUFBSUMsV0FBVTtzQkFDWmhCLFFBQVFzRCxHQUFHLENBQUMsQ0FBQy9DLFFBQVFnRCxvQkFDcEIsOERBQUN4QztvQkFBY0MsV0FBVTs7d0JBQ3RCVCxPQUFPNkMsSUFBSSxLQUFLLDRCQUNmOzs4Q0FDRSw4REFBQ25DO29DQUFLRCxXQUFVOzt3Q0FBd0NULE9BQU9yQyxHQUFHO3dDQUFDOzs7Ozs7OzhDQUNuRSw4REFBQzZDO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ0M7NENBQUtELFdBQVU7c0RBQTZCc0Qsa0JBQWtCL0QsT0FBT0MsTUFBTTs7Ozs7O3NEQUM1RSw4REFBQ1M7NENBQUtELFdBQVU7c0RBQWdCOzs7Ozs7c0RBQ2hDLDhEQUFDQzs0Q0FBS0QsV0FBVTtzREFBOEJzRCxrQkFBa0IvRCxPQUFPTSxLQUFLOzs7Ozs7Ozs7Ozs7Ozt3QkFJakZOLE9BQU82QyxJQUFJLEtBQUsseUJBQ2Y7OzhDQUNFLDhEQUFDbkM7b0NBQUtELFdBQVU7O3dDQUF3Q1QsT0FBT3JDLEdBQUc7d0NBQUM7Ozs7Ozs7OENBQ25FLDhEQUFDK0M7b0NBQUtELFdBQVU7O3dDQUE2Qjt3Q0FBRXNELGtCQUFrQi9ELE9BQU9tQixLQUFLOzs7Ozs7Ozs7d0JBR2hGbkIsT0FBTzZDLElBQUksS0FBSywyQkFDZjs7OENBQ0UsOERBQUNuQztvQ0FBS0QsV0FBVTs7d0NBQXdDVCxPQUFPckMsR0FBRzt3Q0FBQzs7Ozs7Ozs4Q0FDbkUsOERBQUMrQztvQ0FBS0QsV0FBVTs7d0NBQTRCO3dDQUFFc0Qsa0JBQWtCL0QsT0FBT21CLEtBQUs7Ozs7Ozs7Ozs7bUJBcEJ4RTZCOzs7Ozs7Ozs7O0lBMkJsQjtJQUVBLDhDQUE4QztJQUM5QyxNQUFNZSxvQkFBb0IsQ0FBQzVDO1FBQ3pCLElBQUlBLFVBQVUsTUFBTSxPQUFPO1FBQzNCLElBQUlBLFVBQVVDLFdBQVcsT0FBTztRQUNoQyxJQUFJLE9BQU9ELFVBQVUsVUFBVSxPQUFPLElBQVUsT0FBTkEsT0FBTTtRQUNoRCxJQUFJLE9BQU9BLFVBQVUsVUFBVSxPQUFPQSxNQUFNNkMsUUFBUTtRQUNwRCxJQUFJLE9BQU83QyxVQUFVLFdBQVcsT0FBT0EsTUFBTTZDLFFBQVE7UUFDckQsSUFBSW5ELE1BQU1DLE9BQU8sQ0FBQ0ssUUFBUSxPQUFPLElBQWlCLE9BQWJBLE1BQU1oQixNQUFNLEVBQUM7UUFDbEQsSUFBSSxPQUFPZ0IsVUFBVSxVQUFVLE9BQU8sSUFBOEIsT0FBMUJ2QixPQUFPTSxJQUFJLENBQUNpQixPQUFPaEIsTUFBTSxFQUFDO1FBQ3BFLE9BQU9rQixPQUFPRjtJQUNoQjtJQUVBLDRFQUE0RTtJQUM1RSxNQUFNRixzQkFBc0IsQ0FBQ2hCLFFBQTZCSztRQUN4RCxNQUFNbUQsYUFBYTdELE9BQU9NLElBQUksQ0FBQ0Q7UUFDL0IsTUFBTXlELFlBQVk5RCxPQUFPTSxJQUFJLENBQUNJO1FBQzlCLE1BQU1xRCxVQUFVOUMsTUFBTStDLElBQUksQ0FBQyxJQUFJVixJQUFJO2VBQUlPO2VBQWVDO1NBQVU7UUFFaEUsTUFBTWpFLFVBQVUsRUFBRTtRQUVsQixLQUFLLE1BQU05QixPQUFPZ0csUUFBUztZQUN6QixNQUFNRSxjQUFjNUQsTUFBTSxDQUFDdEMsSUFBSTtZQUMvQixNQUFNbUcsYUFBYXhELEtBQUssQ0FBQzNDLElBQUk7WUFFN0IsSUFBSSxDQUFDOEYsV0FBV3ZFLFFBQVEsQ0FBQ3ZCLE1BQU07Z0JBQzdCOEIsUUFBUW1ELElBQUksQ0FBQztvQkFBRUMsTUFBTTtvQkFBU2xGO29CQUFLd0QsT0FBTzJDO2dCQUFXO1lBQ3ZELE9BQU8sSUFBSSxDQUFDSixVQUFVeEUsUUFBUSxDQUFDdkIsTUFBTTtnQkFDbkM4QixRQUFRbUQsSUFBSSxDQUFDO29CQUFFQyxNQUFNO29CQUFXbEY7b0JBQUt3RCxPQUFPMEM7Z0JBQVk7WUFDMUQsT0FBTyxJQUFJeEUsS0FBS0MsU0FBUyxDQUFDdUUsaUJBQWlCeEUsS0FBS0MsU0FBUyxDQUFDd0UsYUFBYTtnQkFDckVyRSxRQUFRbUQsSUFBSSxDQUFDO29CQUFFQyxNQUFNO29CQUFZbEY7b0JBQUtzQyxRQUFRNEQ7b0JBQWF2RCxPQUFPd0Q7Z0JBQVc7WUFDL0U7UUFDRjtRQUVBLElBQUlyRSxRQUFRVSxNQUFNLEtBQUssR0FBRztZQUN4QixxQkFBTyw4REFBQ0s7Z0JBQUlDLFdBQVU7MEJBQXVCOzs7Ozs7UUFDL0M7UUFFQSxxQkFDRSw4REFBQ0Q7WUFBSUMsV0FBVTs7OEJBQ2IsOERBQUNEO29CQUFJQyxXQUFVOzt3QkFBb0M7d0JBQ2hDZ0QsV0FBV3RELE1BQU07d0JBQUM7d0JBQUl1RCxVQUFVdkQsTUFBTTt3QkFBQzs7Ozs7OztnQkFFekRWLFFBQVFzRCxHQUFHLENBQUMsQ0FBQy9DLFFBQVFnRCxvQkFDcEIsOERBQUN4Qzt3QkFBY0MsV0FBVTs7NEJBQ3RCVCxPQUFPNkMsSUFBSSxLQUFLLHlCQUNmLDhEQUFDckM7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNDO2dEQUFLRCxXQUFVOzs7Ozs7NENBQTJDOzBEQUMzQyw4REFBQ3dEO2dEQUFLeEQsV0FBVTswREFBNEJULE9BQU9yQyxHQUFHOzs7Ozs7Ozs7Ozs7a0RBRXhFLDhEQUFDNkM7d0NBQUlDLFdBQVU7a0RBQ1pFLFlBQVlYLE9BQU9tQixLQUFLOzs7Ozs7Ozs7Ozs7NEJBSTlCbkIsT0FBTzZDLElBQUksS0FBSywyQkFDZiw4REFBQ3JDO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0Q7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDQztnREFBS0QsV0FBVTs7Ozs7OzRDQUF5QzswREFDdkMsOERBQUN3RDtnREFBS3hELFdBQVU7MERBQTRCVCxPQUFPckMsR0FBRzs7Ozs7Ozs7Ozs7O2tEQUUxRSw4REFBQzZDO3dDQUFJQyxXQUFVO2tEQUNaRSxZQUFZWCxPQUFPbUIsS0FBSzs7Ozs7Ozs7Ozs7OzRCQUk5Qm5CLE9BQU82QyxJQUFJLEtBQUssNEJBQ2YsOERBQUNyQztnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNEO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ0M7Z0RBQUtELFdBQVU7Ozs7Ozs0Q0FBMEM7MERBQ3ZDLDhEQUFDd0Q7Z0RBQUt4RCxXQUFVOzBEQUE0QlQsT0FBT3JDLEdBQUc7Ozs7Ozs7Ozs7OztrREFFM0UsOERBQUM2Qzt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNEOztrRUFDQyw4REFBQ0E7d0RBQUlDLFdBQVU7a0VBQXdDOzs7Ozs7a0VBQ3ZELDhEQUFDRDt3REFBSUMsV0FBVTtrRUFDWkUsWUFBWVgsT0FBT0MsTUFBTTs7Ozs7Ozs7Ozs7OzBEQUc5Qiw4REFBQ087O2tFQUNDLDhEQUFDQTt3REFBSUMsV0FBVTtrRUFBMEM7Ozs7OztrRUFDekQsOERBQUNEO3dEQUFJQyxXQUFVO2tFQUNaRSxZQUFZWCxPQUFPTSxLQUFLOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3VCQXZDM0IwQzs7Ozs7Ozs7Ozs7SUFpRGxCO0lBRUEsaURBQWlEO0lBQ2pELE1BQU1rQixxQkFBcUIsQ0FBQ3pFO1FBQzFCLE1BQU0wRSxnQkFBZ0J2RSxPQUFPQyxPQUFPLENBQUNKO1FBRXJDLElBQUkwRSxjQUFjaEUsTUFBTSxLQUFLLEdBQUc7WUFDOUIscUJBQ0UsOERBQUNLO2dCQUFJQyxXQUFVOzBCQUNiLDRFQUFDMkQ7b0JBQUUzRCxXQUFVOzhCQUF3Qjs7Ozs7Ozs7Ozs7UUFHM0M7UUFFQSxxQkFDRSw4REFBQ0Q7WUFBSUMsV0FBVTtzQkFDWjBELGNBQWNwQixHQUFHLENBQUM7b0JBQUMsQ0FBQ2hELE9BQU9DLE9BQU87Z0JBQ2pDLHFCQUNFLDhEQUFDUTtvQkFBZ0JDLFdBQVU7O3NDQUN6Qiw4REFBQzREOzRCQUFHNUQsV0FBVTtzQ0FDWFYsTUFBTXVFLE9BQU8sQ0FBQyxZQUFZLE9BQU9BLE9BQU8sQ0FBQyxNQUFNQyxDQUFBQSxNQUFPQSxJQUFJQyxXQUFXOzs7Ozs7c0NBSXhFLDhEQUFDaEU7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDZ0U7b0NBQU1oRSxXQUFVOzhDQUEwQzs7Ozs7OzhDQUczRCw4REFBQzJEO29DQUFFM0QsV0FBVTs4Q0FBNkI7Ozs7OztnQ0FHekNGLDJCQUEyQlAsT0FBT0MsTUFBTSxFQUFFRCxPQUFPTSxLQUFLOzs7Ozs7OzttQkFiakRQOzs7OztZQWlCZDs7Ozs7O0lBR047SUFFQSxJQUFJLENBQUM5QyxVQUFVLENBQUNDLEtBQUssT0FBTztJQUU1QixxQkFDRSw4REFBQ3NEO1FBQUlDLFdBQVU7UUFBNkVpRSxTQUFTMUc7a0JBQ25HLDRFQUFDd0M7WUFBSUMsV0FBVTs7OEJBRWIsOERBQUNEO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ0Q7OzhDQUNDLDhEQUFDbUU7b0NBQUdsRSxXQUFVOzhDQUFrQzs7Ozs7OzhDQUdoRCw4REFBQzJEO29DQUFFM0QsV0FBVTs4Q0FBNkI7Ozs7Ozs7Ozs7OztzQ0FJNUMsOERBQUNtRTs0QkFDQ0YsU0FBU3ZIOzRCQUNUc0QsV0FBVTs0QkFDVm9FLE9BQU07NEJBQ05DLGNBQVc7c0NBRVgsNEVBQUN0SSxxSkFBR0E7Z0NBQUNpRSxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs4QkFLbkIsOERBQUNEO29CQUFJQyxXQUFVOztzQ0FFYiw4REFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDc0U7b0NBQUd0RSxXQUFVOzhDQUEyQzs7Ozs7OzhDQUV6RCw4REFBQ0Q7b0NBQUlDLFdBQVU7O3NEQUViLDhEQUFDRDs7OERBQ0MsOERBQUNpRTtvREFBTWhFLFdBQVU7O3NFQUNmLDhEQUFDOUQsdUpBQUtBOzREQUFDOEQsV0FBVTs7Ozs7O3dEQUFZOzs7Ozs7OzhEQUcvQiw4REFBQ0M7b0RBQUtELFdBQVcsOEVBQStHLE9BQWpDM0Isa0JBQWtCNUIsSUFBSTZCLFNBQVM7OERBQzNIN0IsSUFBSTZCLFNBQVM7Ozs7Ozs7Ozs7OztzREFLbEIsOERBQUN5Qjs7OERBQ0MsOERBQUNpRTtvREFBTWhFLFdBQVU7O3NFQUNmLDhEQUFDNUQsNEpBQVVBOzREQUFDNEQsV0FBVTs7Ozs7O3dEQUFZOzs7Ozs7OzhEQUdwQyw4REFBQ0M7b0RBQUtELFdBQVU7OERBQTZCdkQsSUFBSThILE1BQU07Ozs7Ozs7Ozs7OztzREFJekQsOERBQUN4RTs7OERBQ0MsOERBQUNpRTtvREFBTWhFLFdBQVU7O3NFQUNmLDhEQUFDaEUsd0pBQU1BOzREQUFDZ0UsV0FBVTs7Ozs7O3dEQUFZOzs7Ozs7OzhEQUdoQyw4REFBQ0M7b0RBQUtELFdBQVU7OERBQ2JyRCxZQUFZOzs7Ozs7OERBRWYsOERBQUNnSDtvREFBRTNELFdBQVU7OERBQThCdkQsSUFBSStILFFBQVE7Ozs7Ozs7Ozs7OztzREFJekQsOERBQUN6RTs7OERBQ0MsOERBQUNpRTtvREFBTWhFLFdBQVU7O3NFQUNmLDhEQUFDL0QsNEpBQVVBOzREQUFDK0QsV0FBVTs7Ozs7O3dEQUFZOzs7Ozs7OzhEQUdwQyw4REFBQ0M7b0RBQUtELFdBQVU7OERBQWlCdEMsV0FBV2pCLElBQUlnSSxTQUFTOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Z0NBSzVEaEksSUFBSWlJLFdBQVcsa0JBQ2QsOERBQUMzRTtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNnRTs0Q0FBTWhFLFdBQVU7OzhEQUNmLDhEQUFDN0QsNEpBQVVBO29EQUFDNkQsV0FBVTs7Ozs7O2dEQUFZOzs7Ozs7O3NEQUdwQyw4REFBQzJEOzRDQUFFM0QsV0FBVTtzREFDVnZELElBQUlpSSxXQUFXOzs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBT3hCLDhEQUFDM0U7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDc0U7b0NBQUd0RSxXQUFVOzhDQUNYdkQsSUFBSWtDLElBQUksSUFBSUksZ0JBQWdCdEMsSUFBSWtDLElBQUksSUFBSSxZQUFZOzs7Ozs7Z0NBR3REbEMsSUFBSWtDLElBQUksaUJBQ1AsOERBQUNvQjs4Q0FDRWhCLGdCQUFnQnRDLElBQUlrQyxJQUFJLGtCQUN2Qiw4REFBQ29COzswREFFQyw4REFBQ0E7Z0RBQUlDLFdBQVU7O2tFQUNiLDhEQUFDZ0U7d0RBQU1oRSxXQUFVOzs0REFBK0M7NERBQzlDdkQsSUFBSWtDLElBQUksQ0FBQ2dHLFdBQVcsSUFBSTs0REFBRTs7Ozs7OztvREFFM0NsQixtQkFBbUJoSCxJQUFJa0MsSUFBSSxDQUFDSyxPQUFPOzs7Ozs7OzRDQUlyQ0csT0FBT00sSUFBSSxDQUFDaEQsSUFBSWtDLElBQUksRUFBRWlHLElBQUksQ0FBQzFILENBQUFBLE1BQU8sQ0FBQztvREFBQztvREFBVztvREFBaUI7aURBQWMsQ0FBQ3VCLFFBQVEsQ0FBQ3ZCLHVCQUN2Riw4REFBQzZDO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQ2dFO3dEQUFNaEUsV0FBVTtrRUFBK0M7Ozs7OztrRUFHaEUsOERBQUNEO3dEQUFJQyxXQUFVO2tFQUNiLDRFQUFDNkU7NERBQUk3RSxXQUFVO3NFQUNadEIsZUFDQ1MsT0FBTzJGLFdBQVcsQ0FDaEIzRixPQUFPQyxPQUFPLENBQUMzQyxJQUFJa0MsSUFBSSxFQUFFb0csTUFBTSxDQUFDO29FQUFDLENBQUM3SCxJQUFJO3VFQUNwQyxDQUFDO29FQUFDO29FQUFXO29FQUFpQjtpRUFBYyxDQUFDdUIsUUFBUSxDQUFDdkI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzZEQVV0RSw4REFBQzZDOzswREFDQyw4REFBQ2lFO2dEQUFNaEUsV0FBVTswREFBK0M7Ozs7OzswREFHaEUsOERBQUNEO2dEQUFJQyxXQUFVOzBEQUNiLDRFQUFDNkU7b0RBQUk3RSxXQUFVOzhEQUNadEIsZUFBZWpDLElBQUlrQyxJQUFJOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7eURBT2xDLDhEQUFDb0I7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDNUQsNEpBQVVBOzRDQUFDNEQsV0FBVTs7Ozs7O3NEQUN0Qiw4REFBQzJEOzRDQUFFM0QsV0FBVTtzREFBd0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozt3QkFNMUN2RCxJQUFJa0MsSUFBSSxJQUFJSSxnQkFBZ0J0QyxJQUFJa0MsSUFBSSxtQkFDbkMsOERBQUNvQjs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNzRTtvQ0FBR3RFLFdBQVU7OENBQTJDOzs7Ozs7Z0NBR3hEZixlQUFleEMsSUFBSWtDLElBQUksbUJBQ3RCLDhEQUFDb0I7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDbUU7NENBQ0NGLFNBQVMsSUFBTXBILGtCQUFrQixDQUFDRDs0Q0FDbENvRCxXQUFVOzs4REFFViw4REFBQ0Q7b0RBQUlDLFdBQVU7O3dEQUNacEQsK0JBQ0MsOERBQUNQLCtKQUFhQTs0REFBQzJELFdBQVU7Ozs7O2lGQUV6Qiw4REFBQzFELGdLQUFjQTs0REFBQzBELFdBQVU7Ozs7OztzRUFFNUIsOERBQUNDOzREQUFLRCxXQUFVO3NFQUE0Qjs7Ozs7Ozs7Ozs7OzhEQUU5Qyw4REFBQ0M7b0RBQUtELFdBQVU7OERBQXFEOzs7Ozs7Ozs7Ozs7d0NBSXRFcEQsZ0NBQ0MsOERBQUNtRDs0Q0FBSUMsV0FBVTtzREFDYiw0RUFBQzZFO2dEQUFJN0UsV0FBVTswREFDWnRCLGVBQWVPLGVBQWV4QyxJQUFJa0MsSUFBSSxLQUFLLENBQUM7Ozs7Ozs7Ozs7Ozs7Ozs7O2dDQVF0RGdCLGVBQWVsRCxJQUFJa0MsSUFBSSxtQkFDdEIsOERBQUNvQjtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNtRTs0Q0FDQ0YsU0FBUyxJQUFNbEgsa0JBQWtCLENBQUNEOzRDQUNsQ2tELFdBQVU7OzhEQUVWLDhEQUFDRDtvREFBSUMsV0FBVTs7d0RBQ1psRCwrQkFDQyw4REFBQ1QsK0pBQWFBOzREQUFDMkQsV0FBVTs7Ozs7aUZBRXpCLDhEQUFDMUQsZ0tBQWNBOzREQUFDMEQsV0FBVTs7Ozs7O3NFQUU1Qiw4REFBQ0M7NERBQUtELFdBQVU7c0VBQTRCOzs7Ozs7Ozs7Ozs7OERBRTlDLDhEQUFDQztvREFBS0QsV0FBVTs4REFBdUQ7Ozs7Ozs7Ozs7Ozt3Q0FJeEVsRCxnQ0FDQyw4REFBQ2lEOzRDQUFJQyxXQUFVO3NEQUNiLDRFQUFDNkU7Z0RBQUk3RSxXQUFVOzBEQUNadEIsZUFBZWlCLGVBQWVsRCxJQUFJa0MsSUFBSSxLQUFLLENBQUM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQVUzRCw4REFBQ29COzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ2dFO29DQUFNaEUsV0FBVTs4Q0FBK0M7Ozs7Ozs4Q0FHaEUsOERBQUN3RDtvQ0FBS3hELFdBQVU7OENBQ2J2RCxJQUFJeUUsRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhCQU1iLDhEQUFDbkI7b0JBQUlDLFdBQVU7OEJBQ2IsNEVBQUNtRTt3QkFDQ0YsU0FBU3ZIO3dCQUNUc0QsV0FBVTtrQ0FDWDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU9YO0dBNTNCZ0J6RDtLQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9hcHAvbG9ncy9jb21wb25lbnRzL0xvZ0RldGFpbHNNb2RhbC50c3g/YjJkNCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUmVhY3QsIHsgdXNlRWZmZWN0LCB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IEZpWCwgRmlVc2VyLCBGaUNhbGVuZGFyLCBGaVRhZywgRmlGaWxlVGV4dCwgRmlEYXRhYmFzZSwgRmlDaGV2cm9uRG93biwgRmlDaGV2cm9uUmlnaHQgfSBmcm9tICdyZWFjdC1pY29ucy9maSc7XG5pbXBvcnQgdHlwZSB7IExvZyB9IGZyb20gJy4uL2xvZ3NBcGknO1xuXG5leHBvcnQgaW50ZXJmYWNlIExvZ0RldGFpbHNNb2RhbFByb3BzIHtcbiAgaXNPcGVuOiBib29sZWFuO1xuICBsb2c6IExvZyB8IG51bGw7XG4gIG9uQ2xvc2U6ICgpID0+IHZvaWQ7XG4gIHVzZXJOYW1lPzogc3RyaW5nO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gTG9nRGV0YWlsc01vZGFsKHsgaXNPcGVuLCBsb2csIG9uQ2xvc2UsIHVzZXJOYW1lIH06IExvZ0RldGFpbHNNb2RhbFByb3BzKSB7XG4gIC8vIFN0YXRlIGZvciBleHBhbmRhYmxlIHRhYnNcbiAgY29uc3QgW3Nob3dPbGREYXRhVGFiLCBzZXRTaG93T2xkRGF0YVRhYl0gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtzaG93TmV3RGF0YVRhYiwgc2V0U2hvd05ld0RhdGFUYWJdID0gdXNlU3RhdGUoZmFsc2UpO1xuXG4gIC8vIEhhbmRsZSBlc2NhcGUga2V5IHRvIGNsb3NlIG1vZGFsXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKCFpc09wZW4pIHJldHVybjtcbiAgICBcbiAgICBjb25zdCBoYW5kbGVLZXlEb3duID0gKGU6IEtleWJvYXJkRXZlbnQpID0+IHtcbiAgICAgIGlmIChlLmtleSA9PT0gJ0VzY2FwZScpIHtcbiAgICAgICAgZS5wcmV2ZW50RGVmYXVsdCgpO1xuICAgICAgICBvbkNsb3NlKCk7XG4gICAgICB9XG4gICAgfTtcbiAgICBcbiAgICB3aW5kb3cuYWRkRXZlbnRMaXN0ZW5lcigna2V5ZG93bicsIGhhbmRsZUtleURvd24pO1xuICAgIHJldHVybiAoKSA9PiB3aW5kb3cucmVtb3ZlRXZlbnRMaXN0ZW5lcigna2V5ZG93bicsIGhhbmRsZUtleURvd24pO1xuICB9LCBbaXNPcGVuLCBvbkNsb3NlXSk7XG5cbiAgLy8gSGFuZGxlIGNsaWNraW5nIG91dHNpZGUgbW9kYWwgdG8gY2xvc2VcbiAgY29uc3QgaGFuZGxlT3ZlcmxheUNsaWNrID0gKGU6IFJlYWN0Lk1vdXNlRXZlbnQpID0+IHtcbiAgICBpZiAoZS50YXJnZXQgPT09IGUuY3VycmVudFRhcmdldCkge1xuICAgICAgb25DbG9zZSgpO1xuICAgIH1cbiAgfTtcblxuICAvLyBGb3JtYXQgZGF0ZSBmb3IgZGlzcGxheVxuICBjb25zdCBmb3JtYXREYXRlID0gKGRhdGU6IERhdGUgfCBzdHJpbmcpID0+IHtcbiAgICBjb25zdCBkYXRlT2JqID0gdHlwZW9mIGRhdGUgPT09ICdzdHJpbmcnID8gbmV3IERhdGUoZGF0ZSkgOiBkYXRlO1xuICAgIHJldHVybiBkYXRlT2JqLnRvTG9jYWxlU3RyaW5nKCdlbi1VUycsIHtcbiAgICAgIHllYXI6ICdudW1lcmljJyxcbiAgICAgIG1vbnRoOiAnbG9uZycsXG4gICAgICBkYXk6ICdudW1lcmljJyxcbiAgICAgIGhvdXI6ICcyLWRpZ2l0JyxcbiAgICAgIG1pbnV0ZTogJzItZGlnaXQnLFxuICAgICAgc2Vjb25kOiAnMi1kaWdpdCcsXG4gICAgfSk7XG4gIH07XG5cbiAgLy8gR2V0IG9wZXJhdGlvbiBjb2xvciBiYXNlZCBvbiBvcGVyYXRpb24gdHlwZVxuICBjb25zdCBnZXRPcGVyYXRpb25Db2xvciA9IChvcGVyYXRpb246IHN0cmluZykgPT4ge1xuICAgIGNvbnN0IG9wID0gb3BlcmF0aW9uLnRvTG93ZXJDYXNlKCk7XG4gICAgaWYgKG9wLmluY2x1ZGVzKCdjcmVhdGUnKSB8fCBvcC5pbmNsdWRlcygnYWRkJykpIHtcbiAgICAgIHJldHVybiAnYmctZ3JlZW4tMTAwIHRleHQtZ3JlZW4tODAwIGJvcmRlci1ncmVlbi0yMDAnO1xuICAgIH0gZWxzZSBpZiAob3AuaW5jbHVkZXMoJ3VwZGF0ZScpIHx8IG9wLmluY2x1ZGVzKCdlZGl0JykgfHwgb3AuaW5jbHVkZXMoJ21vZGlmeScpKSB7XG4gICAgICByZXR1cm4gJ2JnLWJsdWUtMTAwIHRleHQtYmx1ZS04MDAgYm9yZGVyLWJsdWUtMjAwJztcbiAgICB9IGVsc2UgaWYgKG9wLmluY2x1ZGVzKCdkZWxldGUnKSB8fCBvcC5pbmNsdWRlcygncmVtb3ZlJykpIHtcbiAgICAgIHJldHVybiAnYmctcmVkLTEwMCB0ZXh0LXJlZC04MDAgYm9yZGVyLXJlZC0yMDAnO1xuICAgIH0gZWxzZSBpZiAob3AuaW5jbHVkZXMoJ2NhbmNlbCcpIHx8IG9wLmluY2x1ZGVzKCdyZWplY3QnKSkge1xuICAgICAgcmV0dXJuICdiZy1vcmFuZ2UtMTAwIHRleHQtb3JhbmdlLTgwMCBib3JkZXItb3JhbmdlLTIwMCc7XG4gICAgfSBlbHNlIGlmIChvcC5pbmNsdWRlcygnYXBwcm92ZScpIHx8IG9wLmluY2x1ZGVzKCdjb21wbGV0ZScpKSB7XG4gICAgICByZXR1cm4gJ2JnLXB1cnBsZS0xMDAgdGV4dC1wdXJwbGUtODAwIGJvcmRlci1wdXJwbGUtMjAwJztcbiAgICB9IGVsc2UgaWYgKG9wLmluY2x1ZGVzKCd0cmFuc2ZlcicpIHx8IG9wLmluY2x1ZGVzKCdtb3ZlJykpIHtcbiAgICAgIHJldHVybiAnYmctaW5kaWdvLTEwMCB0ZXh0LWluZGlnby04MDAgYm9yZGVyLWluZGlnby0yMDAnO1xuICAgIH0gZWxzZSBpZiAob3AuaW5jbHVkZXMoJ2FkanVzdCcpIHx8IG9wLmluY2x1ZGVzKCdtb2RpZnknKSkge1xuICAgICAgcmV0dXJuICdiZy15ZWxsb3ctMTAwIHRleHQteWVsbG93LTgwMCBib3JkZXIteWVsbG93LTIwMCc7XG4gICAgfSBlbHNlIHtcbiAgICAgIHJldHVybiAnYmctZ3JheS0xMDAgdGV4dC1ncmF5LTgwMCBib3JkZXItZ3JheS0yMDAnO1xuICAgIH1cbiAgfTtcblxuICAvLyBGb3JtYXQgSlNPTiBkYXRhIGZvciBkaXNwbGF5XG4gIGNvbnN0IGZvcm1hdEpzb25EYXRhID0gKGRhdGE6IFJlY29yZDxzdHJpbmcsIGFueT4pID0+IHtcbiAgICB0cnkge1xuICAgICAgcmV0dXJuIEpTT04uc3RyaW5naWZ5KGRhdGEsIG51bGwsIDIpO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICByZXR1cm4gJ0ludmFsaWQgSlNPTiBkYXRhJztcbiAgICB9XG4gIH07XG5cbiAgLy8gQ2hlY2sgaWYgdGhlIGxvZyBkYXRhIGNvbnRhaW5zIGRlbHRhIGNoYW5nZXNcbiAgY29uc3QgaGFzRGVsdGFDaGFuZ2VzID0gKGRhdGE6IFJlY29yZDxzdHJpbmcsIGFueT4pID0+IHtcbiAgICByZXR1cm4gZGF0YSAmJiBkYXRhLmNoYW5nZXMgJiYgdHlwZW9mIGRhdGEuY2hhbmdlcyA9PT0gJ29iamVjdCc7XG4gIH07XG5cbiAgLy8gRXh0cmFjdCByYXcgb2xkIGRhdGEgZnJvbSBkZWx0YSBjaGFuZ2VzXG4gIGNvbnN0IGV4dHJhY3RPbGREYXRhID0gKGRhdGE6IFJlY29yZDxzdHJpbmcsIGFueT4pID0+IHtcbiAgICBpZiAoIWhhc0RlbHRhQ2hhbmdlcyhkYXRhKSkgcmV0dXJuIG51bGw7XG5cbiAgICBjb25zdCBvbGREYXRhOiBSZWNvcmQ8c3RyaW5nLCBhbnk+ID0ge307XG4gICAgT2JqZWN0LmVudHJpZXMoZGF0YS5jaGFuZ2VzKS5mb3JFYWNoKChbZmllbGQsIGNoYW5nZV06IFtzdHJpbmcsIGFueV0pID0+IHtcbiAgICAgIGlmIChjaGFuZ2UgJiYgdHlwZW9mIGNoYW5nZSA9PT0gJ29iamVjdCcgJiYgJ2JlZm9yZScgaW4gY2hhbmdlKSB7XG4gICAgICAgIG9sZERhdGFbZmllbGRdID0gY2hhbmdlLmJlZm9yZTtcbiAgICAgIH1cbiAgICB9KTtcbiAgICByZXR1cm4gT2JqZWN0LmtleXMob2xkRGF0YSkubGVuZ3RoID4gMCA/IG9sZERhdGEgOiBudWxsO1xuICB9O1xuXG4gIC8vIEV4dHJhY3QgcmF3IG5ldyBkYXRhIGZyb20gZGVsdGEgY2hhbmdlc1xuICBjb25zdCBleHRyYWN0TmV3RGF0YSA9IChkYXRhOiBSZWNvcmQ8c3RyaW5nLCBhbnk+KSA9PiB7XG4gICAgaWYgKCFoYXNEZWx0YUNoYW5nZXMoZGF0YSkpIHJldHVybiBudWxsO1xuXG4gICAgY29uc3QgbmV3RGF0YTogUmVjb3JkPHN0cmluZywgYW55PiA9IHt9O1xuICAgIE9iamVjdC5lbnRyaWVzKGRhdGEuY2hhbmdlcykuZm9yRWFjaCgoW2ZpZWxkLCBjaGFuZ2VdOiBbc3RyaW5nLCBhbnldKSA9PiB7XG4gICAgICBpZiAoY2hhbmdlICYmIHR5cGVvZiBjaGFuZ2UgPT09ICdvYmplY3QnICYmICdhZnRlcicgaW4gY2hhbmdlKSB7XG4gICAgICAgIG5ld0RhdGFbZmllbGRdID0gY2hhbmdlLmFmdGVyO1xuICAgICAgfVxuICAgIH0pO1xuICAgIHJldHVybiBPYmplY3Qua2V5cyhuZXdEYXRhKS5sZW5ndGggPiAwID8gbmV3RGF0YSA6IG51bGw7XG4gIH07XG5cblxuXG4gIC8vIFNvcGhpc3RpY2F0ZWQgY2hhbmdlIHZpZXdlciB0aGF0IGFuYWx5emVzIGRhdGEgdHlwZXMgYW5kIHNob3dzIG1lYW5pbmdmdWwgZGlmZmVyZW5jZXNcbiAgY29uc3QgcmVuZGVyU29waGlzdGljYXRlZENoYW5nZXMgPSAoYmVmb3JlOiBhbnksIGFmdGVyOiBhbnkpID0+IHtcbiAgICAvLyBIYW5kbGUgbnVsbC91bmRlZmluZWQgY2FzZXNcbiAgICBpZiAoYmVmb3JlID09PSBudWxsICYmIGFmdGVyID09PSBudWxsKSB7XG4gICAgICByZXR1cm4gPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNTAwIGl0YWxpY1wiPk5vIGNoYW5nZSAoYm90aCBudWxsKTwvZGl2PjtcbiAgICB9XG5cbiAgICBpZiAoYmVmb3JlID09PSBudWxsKSB7XG4gICAgICByZXR1cm4gKFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTIgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyZWVuLTcwMFwiPlxuICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidy0yIGgtMiBiZy1ncmVlbi01MDAgcm91bmRlZC1mdWxsXCI+PC9zcGFuPlxuICAgICAgICAgICAgQWRkZWRcbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyZWVuLTUwIGJvcmRlciBib3JkZXItZ3JlZW4tMjAwIHJvdW5kZWQgcC0zXCI+XG4gICAgICAgICAgICB7cmVuZGVyVmFsdWUoYWZ0ZXIpfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgICk7XG4gICAgfVxuXG4gICAgaWYgKGFmdGVyID09PSBudWxsKSB7XG4gICAgICByZXR1cm4gKFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTIgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LXJlZC03MDBcIj5cbiAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInctMiBoLTIgYmctcmVkLTUwMCByb3VuZGVkLWZ1bGxcIj48L3NwYW4+XG4gICAgICAgICAgICBEZWxldGVkXG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1yZWQtNTAgYm9yZGVyIGJvcmRlci1yZWQtMjAwIHJvdW5kZWQgcC0zXCI+XG4gICAgICAgICAgICB7cmVuZGVyVmFsdWUoYmVmb3JlKX1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICApO1xuICAgIH1cblxuICAgIC8vIEJvdGggdmFsdWVzIGV4aXN0IC0gYW5hbHl6ZSB0eXBlc1xuICAgIGNvbnN0IGJlZm9yZVR5cGUgPSBBcnJheS5pc0FycmF5KGJlZm9yZSkgPyAnYXJyYXknIDogdHlwZW9mIGJlZm9yZTtcbiAgICBjb25zdCBhZnRlclR5cGUgPSBBcnJheS5pc0FycmF5KGFmdGVyKSA/ICdhcnJheScgOiB0eXBlb2YgYWZ0ZXI7XG5cbiAgICAvLyBUeXBlIGNoYW5nZWRcbiAgICBpZiAoYmVmb3JlVHlwZSAhPT0gYWZ0ZXJUeXBlKSB7XG4gICAgICByZXR1cm4gKFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktM1wiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LW9yYW5nZS03MDBcIj5UeXBlIENoYW5nZWQ6IHtiZWZvcmVUeXBlfSDihpIge2FmdGVyVHlwZX08L2Rpdj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgZ2FwLTNcIj5cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC14cyBmb250LW1lZGl1bSB0ZXh0LXJlZC03MDAgbWItMVwiPkJlZm9yZSAoe2JlZm9yZVR5cGV9KTwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXJlZC01MCBib3JkZXIgYm9yZGVyLXJlZC0yMDAgcm91bmRlZCBwLTNcIj5cbiAgICAgICAgICAgICAgICB7cmVuZGVyVmFsdWUoYmVmb3JlKX1cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC14cyBmb250LW1lZGl1bSB0ZXh0LWdyZWVuLTcwMCBtYi0xXCI+QWZ0ZXIgKHthZnRlclR5cGV9KTwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyZWVuLTUwIGJvcmRlciBib3JkZXItZ3JlZW4tMjAwIHJvdW5kZWQgcC0zXCI+XG4gICAgICAgICAgICAgICAge3JlbmRlclZhbHVlKGFmdGVyKX1cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICApO1xuICAgIH1cblxuICAgIC8vIFNhbWUgdHlwZSAtIGhhbmRsZSBzcGVjaWZpYyBjb21wYXJpc29uc1xuICAgIGlmIChiZWZvcmVUeXBlID09PSAnYXJyYXknKSB7XG4gICAgICByZXR1cm4gcmVuZGVyQXJyYXlDaGFuZ2VzKGJlZm9yZSwgYWZ0ZXIpO1xuICAgIH0gZWxzZSBpZiAoYmVmb3JlVHlwZSA9PT0gJ29iamVjdCcpIHtcbiAgICAgIHJldHVybiByZW5kZXJPYmplY3RDaGFuZ2VzKGJlZm9yZSwgYWZ0ZXIpO1xuICAgIH0gZWxzZSB7XG4gICAgICAvLyBQcmltaXRpdmUgdmFsdWVzXG4gICAgICBpZiAoYmVmb3JlID09PSBhZnRlcikge1xuICAgICAgICByZXR1cm4gPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNTAwIGl0YWxpY1wiPk5vIGNoYW5nZTwvZGl2PjtcbiAgICAgIH1cbiAgICAgIHJldHVybiByZW5kZXJQcmltaXRpdmVDaGFuZ2UoYmVmb3JlLCBhZnRlcik7XG4gICAgfVxuICB9O1xuXG4gIC8vIEhlbHBlciBmdW5jdGlvbiB0byByZW5kZXIgYSB2YWx1ZSB3aXRoIGFwcHJvcHJpYXRlIGZvcm1hdHRpbmdcbiAgY29uc3QgcmVuZGVyVmFsdWUgPSAodmFsdWU6IGFueSkgPT4ge1xuICAgIGlmICh2YWx1ZSA9PT0gbnVsbCkgcmV0dXJuIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JheS01MDAgaXRhbGljXCI+bnVsbDwvc3Bhbj47XG4gICAgaWYgKHZhbHVlID09PSB1bmRlZmluZWQpIHJldHVybiA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNTAwIGl0YWxpY1wiPnVuZGVmaW5lZDwvc3Bhbj47XG4gICAgaWYgKHR5cGVvZiB2YWx1ZSA9PT0gJ3N0cmluZycpIHJldHVybiA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWJsdWUtNjAwXCI+XCJ7dmFsdWV9XCI8L3NwYW4+O1xuICAgIGlmICh0eXBlb2YgdmFsdWUgPT09ICdudW1iZXInKSByZXR1cm4gPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1wdXJwbGUtNjAwXCI+e3ZhbHVlfTwvc3Bhbj47XG4gICAgaWYgKHR5cGVvZiB2YWx1ZSA9PT0gJ2Jvb2xlYW4nKSByZXR1cm4gPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1vcmFuZ2UtNjAwXCI+e1N0cmluZyh2YWx1ZSl9PC9zcGFuPjtcbiAgICBpZiAoQXJyYXkuaXNBcnJheSh2YWx1ZSkpIHtcbiAgICAgIHJldHVybiAoXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZm9udC1tb25vIHRleHQtc21cIj5cbiAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNTAwXCI+Wzwvc3Bhbj5cbiAgICAgICAgICB7dmFsdWUubGVuZ3RoID09PSAwID8gKFxuICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTQwMCBpdGFsaWNcIj5lbXB0eTwvc3Bhbj5cbiAgICAgICAgICApIDogKFxuICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMFwiPnt2YWx1ZS5sZW5ndGh9IGl0ZW1zPC9zcGFuPlxuICAgICAgICAgICl9XG4gICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTUwMFwiPl08L3NwYW4+XG4gICAgICAgIDwvZGl2PlxuICAgICAgKTtcbiAgICB9XG4gICAgaWYgKHR5cGVvZiB2YWx1ZSA9PT0gJ29iamVjdCcpIHtcbiAgICAgIGNvbnN0IGtleXMgPSBPYmplY3Qua2V5cyh2YWx1ZSk7XG4gICAgICByZXR1cm4gKFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZvbnQtbW9ubyB0ZXh0LXNtXCI+XG4gICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTUwMFwiPnsneyd9PC9zcGFuPlxuICAgICAgICAgIHtrZXlzLmxlbmd0aCA9PT0gMCA/IChcbiAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JheS00MDAgaXRhbGljXCI+ZW1wdHk8L3NwYW4+XG4gICAgICAgICAgKSA6IChcbiAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDBcIj57a2V5cy5sZW5ndGh9IHByb3BlcnRpZXM8L3NwYW4+XG4gICAgICAgICAgKX1cbiAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNTAwXCI+eyd9J308L3NwYW4+XG4gICAgICAgIDwvZGl2PlxuICAgICAgKTtcbiAgICB9XG4gICAgcmV0dXJuIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDBcIj57U3RyaW5nKHZhbHVlKX08L3NwYW4+O1xuICB9O1xuXG4gIC8vIFJlbmRlciBjaGFuZ2VzIGZvciBwcmltaXRpdmUgdmFsdWVzXG4gIGNvbnN0IHJlbmRlclByaW1pdGl2ZUNoYW5nZSA9IChiZWZvcmU6IGFueSwgYWZ0ZXI6IGFueSkgPT4ge1xuICAgIHJldHVybiAoXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgZ2FwLTNcIj5cbiAgICAgICAgPGRpdj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQteHMgZm9udC1tZWRpdW0gdGV4dC1yZWQtNzAwIG1iLTFcIj5CZWZvcmU8L2Rpdj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXJlZC01MCBib3JkZXIgYm9yZGVyLXJlZC0yMDAgcm91bmRlZCBwLTNcIj5cbiAgICAgICAgICAgIHtyZW5kZXJWYWx1ZShiZWZvcmUpfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgICAgPGRpdj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQteHMgZm9udC1tZWRpdW0gdGV4dC1ncmVlbi03MDAgbWItMVwiPkFmdGVyPC9kaXY+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmVlbi01MCBib3JkZXIgYm9yZGVyLWdyZWVuLTIwMCByb3VuZGVkIHAtM1wiPlxuICAgICAgICAgICAge3JlbmRlclZhbHVlKGFmdGVyKX1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICApO1xuICB9O1xuXG4gIC8vIENoZWNrIGlmIGFuIGFycmF5IGNvbnRhaW5zIGJ1c2luZXNzIGl0ZW1zIChoYXMgaWQsIG5hbWUsIHByaWNlLCBxdWFudGl0eSwgZXRjLilcbiAgY29uc3QgaXNCdXNpbmVzc0l0ZW1zQXJyYXkgPSAoYXJyOiBhbnlbXSkgPT4ge1xuICAgIGlmICghYXJyIHx8IGFyci5sZW5ndGggPT09IDApIHJldHVybiBmYWxzZTtcbiAgICBjb25zdCBmaXJzdEl0ZW0gPSBhcnJbMF07XG4gICAgcmV0dXJuIHR5cGVvZiBmaXJzdEl0ZW0gPT09ICdvYmplY3QnICYmXG4gICAgICAgICAgIGZpcnN0SXRlbSAhPT0gbnVsbCAmJlxuICAgICAgICAgICAoJ2lkJyBpbiBmaXJzdEl0ZW0gfHwgJ25hbWUnIGluIGZpcnN0SXRlbSB8fCAncHJpY2UnIGluIGZpcnN0SXRlbSB8fCAncXVhbnRpdHknIGluIGZpcnN0SXRlbSk7XG4gIH07XG5cbiAgLy8gUmVuZGVyIGJ1c2luZXNzIGl0ZW0gd2l0aCBrZXkgZGV0YWlscyBoaWdobGlnaHRlZFxuICBjb25zdCByZW5kZXJCdXNpbmVzc0l0ZW0gPSAoaXRlbTogYW55KSA9PiB7XG4gICAgaWYgKCFpdGVtIHx8IHR5cGVvZiBpdGVtICE9PSAnb2JqZWN0Jykge1xuICAgICAgcmV0dXJuIHJlbmRlclZhbHVlKGl0ZW0pO1xuICAgIH1cblxuICAgIGNvbnN0IHsgaWQsIG5hbWUsIHByaWNlLCBxdWFudGl0eSwgLi4ub3RoZXJQcm9wcyB9ID0gaXRlbTtcblxuICAgIHJldHVybiAoXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC13cmFwIGdhcC0zXCI+XG4gICAgICAgICAge25hbWUgJiYgKFxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMVwiPlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS01MDBcIj5OYW1lOjwvc3Bhbj5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMFwiPntuYW1lfTwvc3Bhbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICl9XG4gICAgICAgICAge3F1YW50aXR5ICE9PSB1bmRlZmluZWQgJiYgKFxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMVwiPlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS01MDBcIj5RdHk6PC9zcGFuPlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJmb250LW1lZGl1bSB0ZXh0LXB1cnBsZS02MDBcIj57cXVhbnRpdHl9PC9zcGFuPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgKX1cbiAgICAgICAgICB7cHJpY2UgIT09IHVuZGVmaW5lZCAmJiAoXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0xXCI+XG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTUwMFwiPlByaWNlOjwvc3Bhbj5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gdGV4dC1ncmVlbi02MDBcIj4ke3ByaWNlfTwvc3Bhbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICl9XG4gICAgICAgICAge2lkICE9PSB1bmRlZmluZWQgJiYgKFxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMVwiPlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS01MDBcIj5JRDo8L3NwYW4+XG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImZvbnQtbW9ubyB0ZXh0LXhzIHRleHQtZ3JheS02MDBcIj57aWR9PC9zcGFuPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgKX1cbiAgICAgICAgPC9kaXY+XG4gICAgICAgIHtPYmplY3Qua2V5cyhvdGhlclByb3BzKS5sZW5ndGggPiAwICYmIChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTUwMFwiPlxuICAgICAgICAgICAgK3tPYmplY3Qua2V5cyhvdGhlclByb3BzKS5sZW5ndGh9IG90aGVyIHByb3BlcnRpZXNcbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKX1cbiAgICAgIDwvZGl2PlxuICAgICk7XG4gIH07XG5cbiAgLy8gQ29tcGFyZSBidXNpbmVzcyBpdGVtcyBieSBJRCBvciBieSBjb250ZW50XG4gIGNvbnN0IGZpbmRNYXRjaGluZ0l0ZW0gPSAoaXRlbTogYW55LCBhcnJheTogYW55W10pID0+IHtcbiAgICBpZiAoIWl0ZW0gfHwgIWFycmF5KSByZXR1cm4gbnVsbDtcblxuICAgIC8vIFRyeSB0byBtYXRjaCBieSBJRCBmaXJzdFxuICAgIGlmIChpdGVtLmlkICE9PSB1bmRlZmluZWQpIHtcbiAgICAgIHJldHVybiBhcnJheS5maW5kKGFyckl0ZW0gPT4gYXJySXRlbSAmJiBhcnJJdGVtLmlkID09PSBpdGVtLmlkKTtcbiAgICB9XG5cbiAgICAvLyBGYWxsYmFjayB0byBleGFjdCBtYXRjaFxuICAgIHJldHVybiBhcnJheS5maW5kKGFyckl0ZW0gPT4gSlNPTi5zdHJpbmdpZnkoYXJySXRlbSkgPT09IEpTT04uc3RyaW5naWZ5KGl0ZW0pKTtcbiAgfTtcblxuICAvLyBSZW5kZXIgY2hhbmdlcyBmb3IgYXJyYXlzIC0gZW5oYW5jZWQgZm9yIGJ1c2luZXNzIGl0ZW1zXG4gIGNvbnN0IHJlbmRlckFycmF5Q2hhbmdlcyA9IChiZWZvcmU6IGFueVtdLCBhZnRlcjogYW55W10pID0+IHtcbiAgICBjb25zdCBpc0J1c2luZXNzSXRlbXMgPSBpc0J1c2luZXNzSXRlbXNBcnJheShiZWZvcmUpIHx8IGlzQnVzaW5lc3NJdGVtc0FycmF5KGFmdGVyKTtcblxuICAgIGlmIChpc0J1c2luZXNzSXRlbXMpIHtcbiAgICAgIHJldHVybiByZW5kZXJCdXNpbmVzc0l0ZW1zQ2hhbmdlcyhiZWZvcmUsIGFmdGVyKTtcbiAgICB9XG5cbiAgICAvLyBGb3Igbm9uLWJ1c2luZXNzIGFycmF5cywgdXNlIHNpbXBsZSBjb21wYXJpc29uXG4gICAgY29uc3QgbWF4TGVuZ3RoID0gTWF0aC5tYXgoYmVmb3JlLmxlbmd0aCwgYWZ0ZXIubGVuZ3RoKTtcbiAgICBjb25zdCBjaGFuZ2VzID0gW107XG5cbiAgICBmb3IgKGxldCBpID0gMDsgaSA8IG1heExlbmd0aDsgaSsrKSB7XG4gICAgICBjb25zdCBiZWZvcmVJdGVtID0gaSA8IGJlZm9yZS5sZW5ndGggPyBiZWZvcmVbaV0gOiB1bmRlZmluZWQ7XG4gICAgICBjb25zdCBhZnRlckl0ZW0gPSBpIDwgYWZ0ZXIubGVuZ3RoID8gYWZ0ZXJbaV0gOiB1bmRlZmluZWQ7XG5cbiAgICAgIGlmIChiZWZvcmVJdGVtID09PSB1bmRlZmluZWQpIHtcbiAgICAgICAgY2hhbmdlcy5wdXNoKHsgdHlwZTogJ2FkZGVkJywgaW5kZXg6IGksIHZhbHVlOiBhZnRlckl0ZW0gfSk7XG4gICAgICB9IGVsc2UgaWYgKGFmdGVySXRlbSA9PT0gdW5kZWZpbmVkKSB7XG4gICAgICAgIGNoYW5nZXMucHVzaCh7IHR5cGU6ICdyZW1vdmVkJywgaW5kZXg6IGksIHZhbHVlOiBiZWZvcmVJdGVtIH0pO1xuICAgICAgfSBlbHNlIGlmIChKU09OLnN0cmluZ2lmeShiZWZvcmVJdGVtKSAhPT0gSlNPTi5zdHJpbmdpZnkoYWZ0ZXJJdGVtKSkge1xuICAgICAgICBjaGFuZ2VzLnB1c2goeyB0eXBlOiAnbW9kaWZpZWQnLCBpbmRleDogaSwgYmVmb3JlOiBiZWZvcmVJdGVtLCBhZnRlcjogYWZ0ZXJJdGVtIH0pO1xuICAgICAgfVxuICAgIH1cblxuICAgIGlmIChjaGFuZ2VzLmxlbmd0aCA9PT0gMCkge1xuICAgICAgcmV0dXJuIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTUwMCBpdGFsaWNcIj5ObyBjaGFuZ2VzIGluIGFycmF5PC9kaXY+O1xuICAgIH1cblxuICAgIHJldHVybiAoXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktM1wiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMFwiPlxuICAgICAgICAgIEFycmF5IENoYW5nZXMgKHtiZWZvcmUubGVuZ3RofSDihpIge2FmdGVyLmxlbmd0aH0gaXRlbXMpXG4gICAgICAgIDwvZGl2PlxuICAgICAgICB7Y2hhbmdlcy5tYXAoKGNoYW5nZSwgaWR4KSA9PiAoXG4gICAgICAgICAgPGRpdiBrZXk9e2lkeH0gY2xhc3NOYW1lPVwiYm9yZGVyIGJvcmRlci1ncmF5LTIwMCByb3VuZGVkIHAtM1wiPlxuICAgICAgICAgICAge2NoYW5nZS50eXBlID09PSAnYWRkZWQnICYmIChcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmVlbi03MDBcIj5cbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInctMiBoLTIgYmctZ3JlZW4tNTAwIHJvdW5kZWQtZnVsbFwiPjwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgIEFkZGVkIGF0IGluZGV4IHtjaGFuZ2UuaW5kZXh9XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmVlbi01MCBib3JkZXIgYm9yZGVyLWdyZWVuLTIwMCByb3VuZGVkIHAtMlwiPlxuICAgICAgICAgICAgICAgICAge3JlbmRlclZhbHVlKGNoYW5nZS52YWx1ZSl9XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIHtjaGFuZ2UudHlwZSA9PT0gJ3JlbW92ZWQnICYmIChcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1yZWQtNzAwXCI+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ3LTIgaC0yIGJnLXJlZC01MDAgcm91bmRlZC1mdWxsXCI+PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgUmVtb3ZlZCBmcm9tIGluZGV4IHtjaGFuZ2UuaW5kZXh9XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1yZWQtNTAgYm9yZGVyIGJvcmRlci1yZWQtMjAwIHJvdW5kZWQgcC0yXCI+XG4gICAgICAgICAgICAgICAgICB7cmVuZGVyVmFsdWUoY2hhbmdlLnZhbHVlKX1cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApfVxuICAgICAgICAgICAge2NoYW5nZS50eXBlID09PSAnbW9kaWZpZWQnICYmIChcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ibHVlLTcwMFwiPlxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidy0yIGgtMiBiZy1ibHVlLTUwMCByb3VuZGVkLWZ1bGxcIj48L3NwYW4+XG4gICAgICAgICAgICAgICAgICBNb2RpZmllZCBhdCBpbmRleCB7Y2hhbmdlLmluZGV4fVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMiBnYXAtMlwiPlxuICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhzIGZvbnQtbWVkaXVtIHRleHQtcmVkLTcwMCBtYi0xXCI+QmVmb3JlPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctcmVkLTUwIGJvcmRlciBib3JkZXItcmVkLTIwMCByb3VuZGVkIHAtMlwiPlxuICAgICAgICAgICAgICAgICAgICAgIHtyZW5kZXJWYWx1ZShjaGFuZ2UuYmVmb3JlKX1cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC14cyBmb250LW1lZGl1bSB0ZXh0LWdyZWVuLTcwMCBtYi0xXCI+QWZ0ZXI8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmVlbi01MCBib3JkZXIgYm9yZGVyLWdyZWVuLTIwMCByb3VuZGVkIHAtMlwiPlxuICAgICAgICAgICAgICAgICAgICAgIHtyZW5kZXJWYWx1ZShjaGFuZ2UuYWZ0ZXIpfVxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICkpfVxuICAgICAgPC9kaXY+XG4gICAgKTtcbiAgfTtcblxuICAvLyBTcGVjaWFsaXplZCByZW5kZXJlciBmb3IgYnVzaW5lc3MgaXRlbXMgKHByb2R1Y3RzLCBsaW5lIGl0ZW1zLCBldGMuKVxuICBjb25zdCByZW5kZXJCdXNpbmVzc0l0ZW1zQ2hhbmdlcyA9IChiZWZvcmU6IGFueVtdLCBhZnRlcjogYW55W10pID0+IHtcbiAgICBjb25zdCBjaGFuZ2VzID0gW107XG4gICAgY29uc3QgcHJvY2Vzc2VkQWZ0ZXJJdGVtcyA9IG5ldyBTZXQoKTtcblxuICAgIC8vIEZpbmQgcmVtb3ZlZCBhbmQgbW9kaWZpZWQgaXRlbXNcbiAgICBmb3IgKGNvbnN0IGJlZm9yZUl0ZW0gb2YgYmVmb3JlKSB7XG4gICAgICBjb25zdCBtYXRjaGluZ0FmdGVySXRlbSA9IGZpbmRNYXRjaGluZ0l0ZW0oYmVmb3JlSXRlbSwgYWZ0ZXIpO1xuXG4gICAgICBpZiAoIW1hdGNoaW5nQWZ0ZXJJdGVtKSB7XG4gICAgICAgIGNoYW5nZXMucHVzaCh7IHR5cGU6ICdyZW1vdmVkJywgaXRlbTogYmVmb3JlSXRlbSB9KTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHByb2Nlc3NlZEFmdGVySXRlbXMuYWRkKGFmdGVyLmluZGV4T2YobWF0Y2hpbmdBZnRlckl0ZW0pKTtcbiAgICAgICAgaWYgKEpTT04uc3RyaW5naWZ5KGJlZm9yZUl0ZW0pICE9PSBKU09OLnN0cmluZ2lmeShtYXRjaGluZ0FmdGVySXRlbSkpIHtcbiAgICAgICAgICBjaGFuZ2VzLnB1c2goeyB0eXBlOiAnbW9kaWZpZWQnLCBiZWZvcmU6IGJlZm9yZUl0ZW0sIGFmdGVyOiBtYXRjaGluZ0FmdGVySXRlbSB9KTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cblxuICAgIC8vIEZpbmQgYWRkZWQgaXRlbXNcbiAgICBmb3IgKGxldCBpID0gMDsgaSA8IGFmdGVyLmxlbmd0aDsgaSsrKSB7XG4gICAgICBpZiAoIXByb2Nlc3NlZEFmdGVySXRlbXMuaGFzKGkpKSB7XG4gICAgICAgIGNvbnN0IGFmdGVySXRlbSA9IGFmdGVyW2ldO1xuICAgICAgICBjb25zdCBtYXRjaGluZ0JlZm9yZUl0ZW0gPSBmaW5kTWF0Y2hpbmdJdGVtKGFmdGVySXRlbSwgYmVmb3JlKTtcbiAgICAgICAgaWYgKCFtYXRjaGluZ0JlZm9yZUl0ZW0pIHtcbiAgICAgICAgICBjaGFuZ2VzLnB1c2goeyB0eXBlOiAnYWRkZWQnLCBpdGVtOiBhZnRlckl0ZW0gfSk7XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9XG5cbiAgICBpZiAoY2hhbmdlcy5sZW5ndGggPT09IDApIHtcbiAgICAgIHJldHVybiA8ZGl2IGNsYXNzTmFtZT1cInRleHQtZ3JheS01MDAgaXRhbGljXCI+Tm8gY2hhbmdlcyBpbiBpdGVtczwvZGl2PjtcbiAgICB9XG5cbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTNcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDBcIj5cbiAgICAgICAgICBJdGVtcyBDaGFuZ2VzICh7YmVmb3JlLmxlbmd0aH0g4oaSIHthZnRlci5sZW5ndGh9IGl0ZW1zKVxuICAgICAgICA8L2Rpdj5cbiAgICAgICAge2NoYW5nZXMubWFwKChjaGFuZ2UsIGlkeCkgPT4gKFxuICAgICAgICAgIDxkaXYga2V5PXtpZHh9IGNsYXNzTmFtZT1cImJvcmRlciBib3JkZXItZ3JheS0yMDAgcm91bmRlZCBwLTNcIj5cbiAgICAgICAgICAgIHtjaGFuZ2UudHlwZSA9PT0gJ2FkZGVkJyAmJiAoXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMiB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JlZW4tNzAwXCI+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ3LTIgaC0yIGJnLWdyZWVuLTUwMCByb3VuZGVkLWZ1bGxcIj48L3NwYW4+XG4gICAgICAgICAgICAgICAgICBBZGRlZCBJdGVtXG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmVlbi01MCBib3JkZXIgYm9yZGVyLWdyZWVuLTIwMCByb3VuZGVkIHAtM1wiPlxuICAgICAgICAgICAgICAgICAge3JlbmRlckJ1c2luZXNzSXRlbShjaGFuZ2UuaXRlbSl9XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIHtjaGFuZ2UudHlwZSA9PT0gJ3JlbW92ZWQnICYmIChcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1yZWQtNzAwXCI+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ3LTIgaC0yIGJnLXJlZC01MDAgcm91bmRlZC1mdWxsXCI+PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgUmVtb3ZlZCBJdGVtXG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1yZWQtNTAgYm9yZGVyIGJvcmRlci1yZWQtMjAwIHJvdW5kZWQgcC0zXCI+XG4gICAgICAgICAgICAgICAgICB7cmVuZGVyQnVzaW5lc3NJdGVtKGNoYW5nZS5pdGVtKX1cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApfVxuICAgICAgICAgICAge2NoYW5nZS50eXBlID09PSAnbW9kaWZpZWQnICYmIChcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ibHVlLTcwMFwiPlxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidy0yIGgtMiBiZy1ibHVlLTUwMCByb3VuZGVkLWZ1bGxcIj48L3NwYW4+XG4gICAgICAgICAgICAgICAgICBNb2RpZmllZCBJdGVtIHtjaGFuZ2UuYmVmb3JlLm5hbWUgJiYgYFwiJHtjaGFuZ2UuYmVmb3JlLm5hbWV9XCJgfVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIHtyZW5kZXJCdXNpbmVzc0l0ZW1DaGFuZ2VzKGNoYW5nZS5iZWZvcmUsIGNoYW5nZS5hZnRlcil9XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKSl9XG4gICAgICA8L2Rpdj5cbiAgICApO1xuICB9O1xuXG4gIC8vIFJlbmRlciBkZXRhaWxlZCBjaGFuZ2VzIHdpdGhpbiBhIGJ1c2luZXNzIGl0ZW1cbiAgY29uc3QgcmVuZGVyQnVzaW5lc3NJdGVtQ2hhbmdlcyA9IChiZWZvcmU6IGFueSwgYWZ0ZXI6IGFueSkgPT4ge1xuICAgIGNvbnN0IGJlZm9yZUtleXMgPSBPYmplY3Qua2V5cyhiZWZvcmUpO1xuICAgIGNvbnN0IGFmdGVyS2V5cyA9IE9iamVjdC5rZXlzKGFmdGVyKTtcbiAgICBjb25zdCBhbGxLZXlzID0gQXJyYXkuZnJvbShuZXcgU2V0KFsuLi5iZWZvcmVLZXlzLCAuLi5hZnRlcktleXNdKSk7XG5cbiAgICBjb25zdCBjaGFuZ2VzID0gW107XG5cbiAgICBmb3IgKGNvbnN0IGtleSBvZiBhbGxLZXlzKSB7XG4gICAgICBjb25zdCBiZWZvcmVWYWx1ZSA9IGJlZm9yZVtrZXldO1xuICAgICAgY29uc3QgYWZ0ZXJWYWx1ZSA9IGFmdGVyW2tleV07XG5cbiAgICAgIGlmICghYmVmb3JlS2V5cy5pbmNsdWRlcyhrZXkpKSB7XG4gICAgICAgIGNoYW5nZXMucHVzaCh7IHR5cGU6ICdhZGRlZCcsIGtleSwgdmFsdWU6IGFmdGVyVmFsdWUgfSk7XG4gICAgICB9IGVsc2UgaWYgKCFhZnRlcktleXMuaW5jbHVkZXMoa2V5KSkge1xuICAgICAgICBjaGFuZ2VzLnB1c2goeyB0eXBlOiAncmVtb3ZlZCcsIGtleSwgdmFsdWU6IGJlZm9yZVZhbHVlIH0pO1xuICAgICAgfSBlbHNlIGlmIChKU09OLnN0cmluZ2lmeShiZWZvcmVWYWx1ZSkgIT09IEpTT04uc3RyaW5naWZ5KGFmdGVyVmFsdWUpKSB7XG4gICAgICAgIGNoYW5nZXMucHVzaCh7IHR5cGU6ICdtb2RpZmllZCcsIGtleSwgYmVmb3JlOiBiZWZvcmVWYWx1ZSwgYWZ0ZXI6IGFmdGVyVmFsdWUgfSk7XG4gICAgICB9XG4gICAgfVxuXG4gICAgaWYgKGNoYW5nZXMubGVuZ3RoID09PSAwKSB7XG4gICAgICByZXR1cm4gPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNTAwIGl0YWxpY1wiPk5vIHByb3BlcnR5IGNoYW5nZXM8L2Rpdj47XG4gICAgfVxuXG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctYmx1ZS01MCBib3JkZXIgYm9yZGVyLWJsdWUtMjAwIHJvdW5kZWQgcC0zIHNwYWNlLXktMlwiPlxuICAgICAgICB7Y2hhbmdlcy5tYXAoKGNoYW5nZSwgaWR4KSA9PiAoXG4gICAgICAgICAgPGRpdiBrZXk9e2lkeH0gY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIHRleHQtc21cIj5cbiAgICAgICAgICAgIHtjaGFuZ2UudHlwZSA9PT0gJ21vZGlmaWVkJyAmJiAoXG4gICAgICAgICAgICAgIDw+XG4gICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBjYXBpdGFsaXplXCI+e2NoYW5nZS5rZXl9Ojwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXJlZC02MDAgbGluZS10aHJvdWdoXCI+e3JlbmRlcklubGluZVZhbHVlKGNoYW5nZS5iZWZvcmUpfTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JheS00MDBcIj7ihpI8L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyZWVuLTYwMCBmb250LW1lZGl1bVwiPntyZW5kZXJJbmxpbmVWYWx1ZShjaGFuZ2UuYWZ0ZXIpfTwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC8+XG4gICAgICAgICAgICApfVxuICAgICAgICAgICAge2NoYW5nZS50eXBlID09PSAnYWRkZWQnICYmIChcbiAgICAgICAgICAgICAgPD5cbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIGNhcGl0YWxpemVcIj57Y2hhbmdlLmtleX06PC9zcGFuPlxuICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JlZW4tNjAwIGZvbnQtbWVkaXVtXCI+K3tyZW5kZXJJbmxpbmVWYWx1ZShjaGFuZ2UudmFsdWUpfTwvc3Bhbj5cbiAgICAgICAgICAgICAgPC8+XG4gICAgICAgICAgICApfVxuICAgICAgICAgICAge2NoYW5nZS50eXBlID09PSAncmVtb3ZlZCcgJiYgKFxuICAgICAgICAgICAgICA8PlxuICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgY2FwaXRhbGl6ZVwiPntjaGFuZ2Uua2V5fTo8L3NwYW4+XG4gICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1yZWQtNjAwIGxpbmUtdGhyb3VnaFwiPi17cmVuZGVySW5saW5lVmFsdWUoY2hhbmdlLnZhbHVlKX08L3NwYW4+XG4gICAgICAgICAgICAgIDwvPlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKSl9XG4gICAgICA8L2Rpdj5cbiAgICApO1xuICB9O1xuXG4gIC8vIFJlbmRlciBhIHZhbHVlIGlubGluZSAoZm9yIGNvbXBhY3QgZGlzcGxheSlcbiAgY29uc3QgcmVuZGVySW5saW5lVmFsdWUgPSAodmFsdWU6IGFueSkgPT4ge1xuICAgIGlmICh2YWx1ZSA9PT0gbnVsbCkgcmV0dXJuICdudWxsJztcbiAgICBpZiAodmFsdWUgPT09IHVuZGVmaW5lZCkgcmV0dXJuICd1bmRlZmluZWQnO1xuICAgIGlmICh0eXBlb2YgdmFsdWUgPT09ICdzdHJpbmcnKSByZXR1cm4gYFwiJHt2YWx1ZX1cImA7XG4gICAgaWYgKHR5cGVvZiB2YWx1ZSA9PT0gJ251bWJlcicpIHJldHVybiB2YWx1ZS50b1N0cmluZygpO1xuICAgIGlmICh0eXBlb2YgdmFsdWUgPT09ICdib29sZWFuJykgcmV0dXJuIHZhbHVlLnRvU3RyaW5nKCk7XG4gICAgaWYgKEFycmF5LmlzQXJyYXkodmFsdWUpKSByZXR1cm4gYFske3ZhbHVlLmxlbmd0aH0gaXRlbXNdYDtcbiAgICBpZiAodHlwZW9mIHZhbHVlID09PSAnb2JqZWN0JykgcmV0dXJuIGB7JHtPYmplY3Qua2V5cyh2YWx1ZSkubGVuZ3RofSBwcm9wc31gO1xuICAgIHJldHVybiBTdHJpbmcodmFsdWUpO1xuICB9O1xuXG4gIC8vIFJlbmRlciBjaGFuZ2VzIGZvciBvYmplY3RzIC0gc2hvdyBhZGRlZCwgcmVtb3ZlZCwgYW5kIG1vZGlmaWVkIHByb3BlcnRpZXNcbiAgY29uc3QgcmVuZGVyT2JqZWN0Q2hhbmdlcyA9IChiZWZvcmU6IFJlY29yZDxzdHJpbmcsIGFueT4sIGFmdGVyOiBSZWNvcmQ8c3RyaW5nLCBhbnk+KSA9PiB7XG4gICAgY29uc3QgYmVmb3JlS2V5cyA9IE9iamVjdC5rZXlzKGJlZm9yZSk7XG4gICAgY29uc3QgYWZ0ZXJLZXlzID0gT2JqZWN0LmtleXMoYWZ0ZXIpO1xuICAgIGNvbnN0IGFsbEtleXMgPSBBcnJheS5mcm9tKG5ldyBTZXQoWy4uLmJlZm9yZUtleXMsIC4uLmFmdGVyS2V5c10pKTtcblxuICAgIGNvbnN0IGNoYW5nZXMgPSBbXTtcblxuICAgIGZvciAoY29uc3Qga2V5IG9mIGFsbEtleXMpIHtcbiAgICAgIGNvbnN0IGJlZm9yZVZhbHVlID0gYmVmb3JlW2tleV07XG4gICAgICBjb25zdCBhZnRlclZhbHVlID0gYWZ0ZXJba2V5XTtcblxuICAgICAgaWYgKCFiZWZvcmVLZXlzLmluY2x1ZGVzKGtleSkpIHtcbiAgICAgICAgY2hhbmdlcy5wdXNoKHsgdHlwZTogJ2FkZGVkJywga2V5LCB2YWx1ZTogYWZ0ZXJWYWx1ZSB9KTtcbiAgICAgIH0gZWxzZSBpZiAoIWFmdGVyS2V5cy5pbmNsdWRlcyhrZXkpKSB7XG4gICAgICAgIGNoYW5nZXMucHVzaCh7IHR5cGU6ICdyZW1vdmVkJywga2V5LCB2YWx1ZTogYmVmb3JlVmFsdWUgfSk7XG4gICAgICB9IGVsc2UgaWYgKEpTT04uc3RyaW5naWZ5KGJlZm9yZVZhbHVlKSAhPT0gSlNPTi5zdHJpbmdpZnkoYWZ0ZXJWYWx1ZSkpIHtcbiAgICAgICAgY2hhbmdlcy5wdXNoKHsgdHlwZTogJ21vZGlmaWVkJywga2V5LCBiZWZvcmU6IGJlZm9yZVZhbHVlLCBhZnRlcjogYWZ0ZXJWYWx1ZSB9KTtcbiAgICAgIH1cbiAgICB9XG5cbiAgICBpZiAoY2hhbmdlcy5sZW5ndGggPT09IDApIHtcbiAgICAgIHJldHVybiA8ZGl2IGNsYXNzTmFtZT1cInRleHQtZ3JheS01MDAgaXRhbGljXCI+Tm8gY2hhbmdlcyBpbiBvYmplY3Q8L2Rpdj47XG4gICAgfVxuXG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0zXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwXCI+XG4gICAgICAgICAgT2JqZWN0IENoYW5nZXMgKHtiZWZvcmVLZXlzLmxlbmd0aH0g4oaSIHthZnRlcktleXMubGVuZ3RofSBwcm9wZXJ0aWVzKVxuICAgICAgICA8L2Rpdj5cbiAgICAgICAge2NoYW5nZXMubWFwKChjaGFuZ2UsIGlkeCkgPT4gKFxuICAgICAgICAgIDxkaXYga2V5PXtpZHh9IGNsYXNzTmFtZT1cImJvcmRlciBib3JkZXItZ3JheS0yMDAgcm91bmRlZCBwLTNcIj5cbiAgICAgICAgICAgIHtjaGFuZ2UudHlwZSA9PT0gJ2FkZGVkJyAmJiAoXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMiB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JlZW4tNzAwXCI+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ3LTIgaC0yIGJnLWdyZWVuLTUwMCByb3VuZGVkLWZ1bGxcIj48L3NwYW4+XG4gICAgICAgICAgICAgICAgICBBZGRlZCBwcm9wZXJ0eTogPGNvZGUgY2xhc3NOYW1lPVwiYmctZ3JheS0xMDAgcHgtMSByb3VuZGVkXCI+e2NoYW5nZS5rZXl9PC9jb2RlPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctZ3JlZW4tNTAgYm9yZGVyIGJvcmRlci1ncmVlbi0yMDAgcm91bmRlZCBwLTJcIj5cbiAgICAgICAgICAgICAgICAgIHtyZW5kZXJWYWx1ZShjaGFuZ2UudmFsdWUpfVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgICB7Y2hhbmdlLnR5cGUgPT09ICdyZW1vdmVkJyAmJiAoXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMiB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtcmVkLTcwMFwiPlxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidy0yIGgtMiBiZy1yZWQtNTAwIHJvdW5kZWQtZnVsbFwiPjwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgIFJlbW92ZWQgcHJvcGVydHk6IDxjb2RlIGNsYXNzTmFtZT1cImJnLWdyYXktMTAwIHB4LTEgcm91bmRlZFwiPntjaGFuZ2Uua2V5fTwvY29kZT5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXJlZC01MCBib3JkZXIgYm9yZGVyLXJlZC0yMDAgcm91bmRlZCBwLTJcIj5cbiAgICAgICAgICAgICAgICAgIHtyZW5kZXJWYWx1ZShjaGFuZ2UudmFsdWUpfVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgICB7Y2hhbmdlLnR5cGUgPT09ICdtb2RpZmllZCcgJiYgKFxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTIgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWJsdWUtNzAwXCI+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ3LTIgaC0yIGJnLWJsdWUtNTAwIHJvdW5kZWQtZnVsbFwiPjwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgIE1vZGlmaWVkIHByb3BlcnR5OiA8Y29kZSBjbGFzc05hbWU9XCJiZy1ncmF5LTEwMCBweC0xIHJvdW5kZWRcIj57Y2hhbmdlLmtleX08L2NvZGU+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0yIGdhcC0yXCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQteHMgZm9udC1tZWRpdW0gdGV4dC1yZWQtNzAwIG1iLTFcIj5CZWZvcmU8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1yZWQtNTAgYm9yZGVyIGJvcmRlci1yZWQtMjAwIHJvdW5kZWQgcC0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAge3JlbmRlclZhbHVlKGNoYW5nZS5iZWZvcmUpfVxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhzIGZvbnQtbWVkaXVtIHRleHQtZ3JlZW4tNzAwIG1iLTFcIj5BZnRlcjwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyZWVuLTUwIGJvcmRlciBib3JkZXItZ3JlZW4tMjAwIHJvdW5kZWQgcC0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAge3JlbmRlclZhbHVlKGNoYW5nZS5hZnRlcil9XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKSl9XG4gICAgICA8L2Rpdj5cbiAgICApO1xuICB9O1xuXG4gIC8vIFJlbmRlciBkZWx0YSBjaGFuZ2VzIGluIGEgdXNlci1mcmllbmRseSBmb3JtYXRcbiAgY29uc3QgcmVuZGVyRGVsdGFDaGFuZ2VzID0gKGNoYW5nZXM6IFJlY29yZDxzdHJpbmcsIHsgYmVmb3JlOiBhbnk7IGFmdGVyOiBhbnkgfT4pID0+IHtcbiAgICBjb25zdCBjaGFuZ2VFbnRyaWVzID0gT2JqZWN0LmVudHJpZXMoY2hhbmdlcyk7XG5cbiAgICBpZiAoY2hhbmdlRW50cmllcy5sZW5ndGggPT09IDApIHtcbiAgICAgIHJldHVybiAoXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgcHktNFwiPlxuICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS01MDAgdGV4dC1zbVwiPk5vIGNoYW5nZXMgZGV0ZWN0ZWQ8L3A+XG4gICAgICAgIDwvZGl2PlxuICAgICAgKTtcbiAgICB9XG5cbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTZcIj5cbiAgICAgICAge2NoYW5nZUVudHJpZXMubWFwKChbZmllbGQsIGNoYW5nZV0pID0+IHtcbiAgICAgICAgICByZXR1cm4gKFxuICAgICAgICAgICAgPGRpdiBrZXk9e2ZpZWxkfSBjbGFzc05hbWU9XCJib3JkZXIgYm9yZGVyLWdyYXktMjAwIHJvdW5kZWQtbGcgcC00XCI+XG4gICAgICAgICAgICAgIDxoNSBjbGFzc05hbWU9XCJmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwIG1iLTQgY2FwaXRhbGl6ZVwiPlxuICAgICAgICAgICAgICAgIHtmaWVsZC5yZXBsYWNlKC8oW0EtWl0pL2csICcgJDEnKS5yZXBsYWNlKC9eLi8sIHN0ciA9PiBzdHIudG9VcHBlckNhc2UoKSl9XG4gICAgICAgICAgICAgIDwvaDU+XG5cbiAgICAgICAgICAgICAgey8qIFNvcGhpc3RpY2F0ZWQgQ2hhbmdlIEFuYWx5c2lzICovfVxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDBcIj5cbiAgICAgICAgICAgICAgICAgIO+/vSBDaGFuZ2UgQW5hbHlzaXNcbiAgICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTUwMCBtYi0zXCI+XG4gICAgICAgICAgICAgICAgICBJbnRlbGxpZ2VudCBhbmFseXNpcyBzaG93aW5nIHdoYXQgd2FzIGFkZGVkLCByZW1vdmVkLCBvciBtb2RpZmllZFxuICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICB7cmVuZGVyU29waGlzdGljYXRlZENoYW5nZXMoY2hhbmdlLmJlZm9yZSwgY2hhbmdlLmFmdGVyKX1cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICApO1xuICAgICAgICB9KX1cbiAgICAgIDwvZGl2PlxuICAgICk7XG4gIH07XG5cbiAgaWYgKCFpc09wZW4gfHwgIWxvZykgcmV0dXJuIG51bGw7XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cImZpeGVkIGluc2V0LTAgYmctYmxhY2sgYmctb3BhY2l0eS01MCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciB6LTUwXCIgb25DbGljaz17aGFuZGxlT3ZlcmxheUNsaWNrfT5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgcm91bmRlZC1sZyBzaGFkb3ctbGcgbWF4LXctNnhsIHctZnVsbCBteC00IG1heC1oLVs5MHZoXSBvdmVyZmxvdy1oaWRkZW4gZmxleCBmbGV4LWNvbFwiPlxuICAgICAgICB7LyogSGVhZGVyICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtNiBib3JkZXItYiBib3JkZXItZ3JheS0yMDAgZmxleC1zaHJpbmstMCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1ib2xkIHRleHQtZ3JheS05MDBcIj5cbiAgICAgICAgICAgICAgTG9nIERldGFpbHNcbiAgICAgICAgICAgIDwvaDM+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDAgbXQtMVwiPlxuICAgICAgICAgICAgICBWaWV3IGRldGFpbGVkIGluZm9ybWF0aW9uIGFib3V0IHRoaXMgbG9nIGVudHJ5XG4gICAgICAgICAgICA8L3A+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgb25DbGljaz17b25DbG9zZX1cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cInAtMiByb3VuZGVkLWZ1bGwgaG92ZXI6YmctZ3JheS0xMDAgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLWJsdWUtNTAwIHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgICAgICAgIHRpdGxlPVwiQ2xvc2VcIlxuICAgICAgICAgICAgYXJpYS1sYWJlbD1cIkNsb3NlIG1vZGFsXCJcbiAgICAgICAgICA+XG4gICAgICAgICAgICA8RmlYIGNsYXNzTmFtZT1cImgtNSB3LTUgdGV4dC1ncmF5LTUwMFwiIC8+XG4gICAgICAgICAgPC9idXR0b24+XG4gICAgICAgIDwvZGl2PlxuICAgICAgICBcbiAgICAgICAgey8qIENvbnRlbnQgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIG92ZXJmbG93LXktYXV0byBwLTZcIj5cbiAgICAgICAgICB7LyogQmFzaWMgSW5mb3JtYXRpb24gLSBDb21wYWN0IExheW91dCAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTZcIj5cbiAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTkwMCBtYi00XCI+QmFzaWMgSW5mb3JtYXRpb248L2g0PlxuICAgICAgICAgICAgXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgbGc6Z3JpZC1jb2xzLTQgZ2FwLTRcIj5cbiAgICAgICAgICAgICAgey8qIE9wZXJhdGlvbiAqL31cbiAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIG1iLTIgZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cbiAgICAgICAgICAgICAgICAgIDxGaVRhZyBjbGFzc05hbWU9XCJ3LTQgaC00XCIgLz5cbiAgICAgICAgICAgICAgICAgIE9wZXJhdGlvblxuICAgICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPXtgaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIHB4LTMgcHktMSByb3VuZGVkLWZ1bGwgdGV4dC1zbSBmb250LW1lZGl1bSBib3JkZXIgJHtnZXRPcGVyYXRpb25Db2xvcihsb2cub3BlcmF0aW9uKX1gfT5cbiAgICAgICAgICAgICAgICAgIHtsb2cub3BlcmF0aW9ufVxuICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgey8qIEVudGl0eSAqL31cbiAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIG1iLTIgZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cbiAgICAgICAgICAgICAgICAgIDxGaURhdGFiYXNlIGNsYXNzTmFtZT1cInctNCBoLTRcIiAvPlxuICAgICAgICAgICAgICAgICAgRW50aXR5XG4gICAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktOTAwIGZvbnQtbWVkaXVtXCI+e2xvZy5lbnRpdHl9PC9zcGFuPlxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICB7LyogVXNlciAqL31cbiAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIG1iLTIgZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cbiAgICAgICAgICAgICAgICAgIDxGaVVzZXIgY2xhc3NOYW1lPVwidy00IGgtNFwiIC8+XG4gICAgICAgICAgICAgICAgICBVc2VyXG4gICAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktOTAwIGZvbnQtbWVkaXVtXCI+XG4gICAgICAgICAgICAgICAgICB7dXNlck5hbWUgfHwgJ1Vua25vd24gVXNlcid9XG4gICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTUwMCBtdC0xXCI+e2xvZy51c2VyVXVpZH08L3A+XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIHsvKiBUaW1lc3RhbXAgKi99XG4gICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBtYi0yIGZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XG4gICAgICAgICAgICAgICAgICA8RmlDYWxlbmRhciBjbGFzc05hbWU9XCJ3LTQgaC00XCIgLz5cbiAgICAgICAgICAgICAgICAgIFRpbWVzdGFtcFxuICAgICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTkwMFwiPntmb3JtYXREYXRlKGxvZy5jcmVhdGVkQXQpfTwvc3Bhbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgey8qIERlc2NyaXB0aW9uIC0gRnVsbCBXaWR0aCAqL31cbiAgICAgICAgICAgIHtsb2cuZGVzY3JpcHRpb24gJiYgKFxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTRcIj5cbiAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIG1iLTIgZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cbiAgICAgICAgICAgICAgICAgIDxGaUZpbGVUZXh0IGNsYXNzTmFtZT1cInctNCBoLTRcIiAvPlxuICAgICAgICAgICAgICAgICAgRGVzY3JpcHRpb25cbiAgICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS05MDAgYmctZ3JheS01MCBwLTMgcm91bmRlZC1sZyBib3JkZXJcIj5cbiAgICAgICAgICAgICAgICAgIHtsb2cuZGVzY3JpcHRpb259XG4gICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7LyogRGF0YSBTZWN0aW9uIC0gRnVsbCBXaWR0aCAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTZcIj5cbiAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTkwMCBtYi00XCI+XG4gICAgICAgICAgICAgIHtsb2cuZGF0YSAmJiBoYXNEZWx0YUNoYW5nZXMobG9nLmRhdGEpID8gJ0NoYW5nZXMnIDogJ0FkZGl0aW9uYWwgRGF0YSd9XG4gICAgICAgICAgICA8L2g0PlxuXG4gICAgICAgICAgICB7bG9nLmRhdGEgPyAoXG4gICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAge2hhc0RlbHRhQ2hhbmdlcyhsb2cuZGF0YSkgPyAoXG4gICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICB7LyogRGVsdGEgQ2hhbmdlcyAqL31cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi02XCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBtYi0zXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICBGaWVsZCBDaGFuZ2VzICh7bG9nLmRhdGEuY2hhbmdlQ291bnQgfHwgMH0gY2hhbmdlcylcbiAgICAgICAgICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgIHtyZW5kZXJEZWx0YUNoYW5nZXMobG9nLmRhdGEuY2hhbmdlcyl9XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgIHsvKiBBZGRpdGlvbmFsIG1ldGFkYXRhICovfVxuICAgICAgICAgICAgICAgICAgICB7T2JqZWN0LmtleXMobG9nLmRhdGEpLnNvbWUoa2V5ID0+ICFbJ2NoYW5nZXMnLCAnY2hhbmdlZEZpZWxkcycsICdjaGFuZ2VDb3VudCddLmluY2x1ZGVzKGtleSkpICYmIChcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTZcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgbWItMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICBBZGRpdGlvbmFsIEluZm9ybWF0aW9uXG4gICAgICAgICAgICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmF5LTkwMCB0ZXh0LWdyZWVuLTQwMCBwLTQgcm91bmRlZC1sZyBib3JkZXIgb3ZlcmZsb3cteC1hdXRvXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxwcmUgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1vbm8gd2hpdGVzcGFjZS1wcmUtd3JhcFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtmb3JtYXRKc29uRGF0YShcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIE9iamVjdC5mcm9tRW50cmllcyhcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgT2JqZWN0LmVudHJpZXMobG9nLmRhdGEpLmZpbHRlcigoW2tleV0pID0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIVsnY2hhbmdlcycsICdjaGFuZ2VkRmllbGRzJywgJ2NoYW5nZUNvdW50J10uaW5jbHVkZXMoa2V5KVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICApXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9wcmU+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIG1iLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICBKU09OIERhdGFcbiAgICAgICAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmF5LTkwMCB0ZXh0LWdyZWVuLTQwMCBwLTQgcm91bmRlZC1sZyBib3JkZXIgb3ZlcmZsb3cteC1hdXRvXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPHByZSBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbW9ubyB3aGl0ZXNwYWNlLXByZS13cmFwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICB7Zm9ybWF0SnNvbkRhdGEobG9nLmRhdGEpfVxuICAgICAgICAgICAgICAgICAgICAgIDwvcHJlPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBweS04XCI+XG4gICAgICAgICAgICAgICAgPEZpRGF0YWJhc2UgY2xhc3NOYW1lPVwidy0xMiBoLTEyIHRleHQtZ3JheS0zMDAgbXgtYXV0byBtYi0zXCIgLz5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNTAwIHRleHQtc21cIj5ObyBhZGRpdGlvbmFsIGRhdGEgYXZhaWxhYmxlPC9wPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7LyogUmF3IERhdGEgVGFicyAtIE9ubHkgc2hvdyBpZiBkZWx0YSBjaGFuZ2VzIGV4aXN0ICovfVxuICAgICAgICAgIHtsb2cuZGF0YSAmJiBoYXNEZWx0YUNoYW5nZXMobG9nLmRhdGEpICYmIChcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItNlwiPlxuICAgICAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtZ3JheS05MDAgbWItNFwiPlJhdyBEYXRhPC9oND5cblxuICAgICAgICAgICAgICB7LyogT2xkIERhdGEgVGFiICovfVxuICAgICAgICAgICAgICB7ZXh0cmFjdE9sZERhdGEobG9nLmRhdGEpICYmIChcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTQgYm9yZGVyIGJvcmRlci1ncmF5LTIwMCByb3VuZGVkLWxnIG92ZXJmbG93LWhpZGRlblwiPlxuICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTaG93T2xkRGF0YVRhYighc2hvd09sZERhdGFUYWIpfVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgcHgtNCBweS0zIGJnLWdyYXktNTAgaG92ZXI6YmctZ3JheS0xMDAgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIHRleHQtbGVmdCB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICB7c2hvd09sZERhdGFUYWIgPyAoXG4gICAgICAgICAgICAgICAgICAgICAgICA8RmlDaGV2cm9uRG93biBjbGFzc05hbWU9XCJ3LTQgaC00IHRleHQtZ3JheS02MDBcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICAgICAgICA8RmlDaGV2cm9uUmlnaHQgY2xhc3NOYW1lPVwidy00IGgtNCB0ZXh0LWdyYXktNjAwXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDBcIj5SYXcgT2xkIERhdGEgKEJlZm9yZSBDaGFuZ2VzKTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTUwMCBiZy1yZWQtMTAwIHB4LTIgcHktMSByb3VuZGVkXCI+XG4gICAgICAgICAgICAgICAgICAgICAgT3JpZ2luYWwgVmFsdWVzXG4gICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAge3Nob3dPbGREYXRhVGFiICYmIChcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTQgYmctZ3JheS05MDAgdGV4dC1ncmVlbi00MDAgb3ZlcmZsb3cteC1hdXRvXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPHByZSBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbW9ubyB3aGl0ZXNwYWNlLXByZS13cmFwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICB7Zm9ybWF0SnNvbkRhdGEoZXh0cmFjdE9sZERhdGEobG9nLmRhdGEpIHx8IHt9KX1cbiAgICAgICAgICAgICAgICAgICAgICA8L3ByZT5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICApfVxuXG4gICAgICAgICAgICAgIHsvKiBOZXcgRGF0YSBUYWIgKi99XG4gICAgICAgICAgICAgIHtleHRyYWN0TmV3RGF0YShsb2cuZGF0YSkgJiYgKFxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItNCBib3JkZXIgYm9yZGVyLWdyYXktMjAwIHJvdW5kZWQtbGcgb3ZlcmZsb3ctaGlkZGVuXCI+XG4gICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNob3dOZXdEYXRhVGFiKCFzaG93TmV3RGF0YVRhYil9XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBweC00IHB5LTMgYmctZ3JheS01MCBob3ZlcjpiZy1ncmF5LTEwMCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gdGV4dC1sZWZ0IHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAgICAgICAgICAgIHtzaG93TmV3RGF0YVRhYiA/IChcbiAgICAgICAgICAgICAgICAgICAgICAgIDxGaUNoZXZyb25Eb3duIGNsYXNzTmFtZT1cInctNCBoLTQgdGV4dC1ncmF5LTYwMFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgICAgICAgIDxGaUNoZXZyb25SaWdodCBjbGFzc05hbWU9XCJ3LTQgaC00IHRleHQtZ3JheS02MDBcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMFwiPlJhdyBOZXcgRGF0YSAoQWZ0ZXIgQ2hhbmdlcyk8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS01MDAgYmctZ3JlZW4tMTAwIHB4LTIgcHktMSByb3VuZGVkXCI+XG4gICAgICAgICAgICAgICAgICAgICAgVXBkYXRlZCBWYWx1ZXNcbiAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICB7c2hvd05ld0RhdGFUYWIgJiYgKFxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtNCBiZy1ncmF5LTkwMCB0ZXh0LWdyZWVuLTQwMCBvdmVyZmxvdy14LWF1dG9cIj5cbiAgICAgICAgICAgICAgICAgICAgICA8cHJlIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tb25vIHdoaXRlc3BhY2UtcHJlLXdyYXBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtmb3JtYXRKc29uRGF0YShleHRyYWN0TmV3RGF0YShsb2cuZGF0YSkgfHwge30pfVxuICAgICAgICAgICAgICAgICAgICAgIDwvcHJlPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICl9XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICApfVxuXG4gICAgICAgICAgey8qIExvZyBJRCAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInB0LTYgYm9yZGVyLXQgYm9yZGVyLWdyYXktMjAwXCI+XG4gICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIG1iLTJcIj5cbiAgICAgICAgICAgICAgTG9nIElEXG4gICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgPGNvZGUgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNjAwIGJnLWdyYXktMTAwIHB4LTIgcHktMSByb3VuZGVkXCI+XG4gICAgICAgICAgICAgIHtsb2cuaWR9XG4gICAgICAgICAgICA8L2NvZGU+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBGb290ZXIgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC02IGJvcmRlci10IGJvcmRlci1ncmF5LTIwMCBmbGV4LXNocmluay0wIGZsZXgganVzdGlmeS1lbmRcIj5cbiAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICBvbkNsaWNrPXtvbkNsb3NlfVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwicHgtNCBweS0yIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBiZy1ncmF5LTEwMCByb3VuZGVkLWxnIGhvdmVyOmJnLWdyYXktMjAwIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1ncmF5LTQwMCB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgPlxuICAgICAgICAgICAgQ2xvc2VcbiAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKTtcbn0gIl0sIm5hbWVzIjpbIlJlYWN0IiwidXNlRWZmZWN0IiwidXNlU3RhdGUiLCJGaVgiLCJGaVVzZXIiLCJGaUNhbGVuZGFyIiwiRmlUYWciLCJGaUZpbGVUZXh0IiwiRmlEYXRhYmFzZSIsIkZpQ2hldnJvbkRvd24iLCJGaUNoZXZyb25SaWdodCIsIkxvZ0RldGFpbHNNb2RhbCIsImlzT3BlbiIsImxvZyIsIm9uQ2xvc2UiLCJ1c2VyTmFtZSIsInNob3dPbGREYXRhVGFiIiwic2V0U2hvd09sZERhdGFUYWIiLCJzaG93TmV3RGF0YVRhYiIsInNldFNob3dOZXdEYXRhVGFiIiwiaGFuZGxlS2V5RG93biIsImUiLCJrZXkiLCJwcmV2ZW50RGVmYXVsdCIsIndpbmRvdyIsImFkZEV2ZW50TGlzdGVuZXIiLCJyZW1vdmVFdmVudExpc3RlbmVyIiwiaGFuZGxlT3ZlcmxheUNsaWNrIiwidGFyZ2V0IiwiY3VycmVudFRhcmdldCIsImZvcm1hdERhdGUiLCJkYXRlIiwiZGF0ZU9iaiIsIkRhdGUiLCJ0b0xvY2FsZVN0cmluZyIsInllYXIiLCJtb250aCIsImRheSIsImhvdXIiLCJtaW51dGUiLCJzZWNvbmQiLCJnZXRPcGVyYXRpb25Db2xvciIsIm9wZXJhdGlvbiIsIm9wIiwidG9Mb3dlckNhc2UiLCJpbmNsdWRlcyIsImZvcm1hdEpzb25EYXRhIiwiZGF0YSIsIkpTT04iLCJzdHJpbmdpZnkiLCJlcnJvciIsImhhc0RlbHRhQ2hhbmdlcyIsImNoYW5nZXMiLCJleHRyYWN0T2xkRGF0YSIsIm9sZERhdGEiLCJPYmplY3QiLCJlbnRyaWVzIiwiZm9yRWFjaCIsImZpZWxkIiwiY2hhbmdlIiwiYmVmb3JlIiwia2V5cyIsImxlbmd0aCIsImV4dHJhY3ROZXdEYXRhIiwibmV3RGF0YSIsImFmdGVyIiwicmVuZGVyU29waGlzdGljYXRlZENoYW5nZXMiLCJkaXYiLCJjbGFzc05hbWUiLCJzcGFuIiwicmVuZGVyVmFsdWUiLCJiZWZvcmVUeXBlIiwiQXJyYXkiLCJpc0FycmF5IiwiYWZ0ZXJUeXBlIiwicmVuZGVyQXJyYXlDaGFuZ2VzIiwicmVuZGVyT2JqZWN0Q2hhbmdlcyIsInJlbmRlclByaW1pdGl2ZUNoYW5nZSIsInZhbHVlIiwidW5kZWZpbmVkIiwiU3RyaW5nIiwiaXNCdXNpbmVzc0l0ZW1zQXJyYXkiLCJhcnIiLCJmaXJzdEl0ZW0iLCJyZW5kZXJCdXNpbmVzc0l0ZW0iLCJpdGVtIiwiaWQiLCJuYW1lIiwicHJpY2UiLCJxdWFudGl0eSIsIm90aGVyUHJvcHMiLCJmaW5kTWF0Y2hpbmdJdGVtIiwiYXJyYXkiLCJmaW5kIiwiYXJySXRlbSIsImlzQnVzaW5lc3NJdGVtcyIsInJlbmRlckJ1c2luZXNzSXRlbXNDaGFuZ2VzIiwibWF4TGVuZ3RoIiwiTWF0aCIsIm1heCIsImkiLCJiZWZvcmVJdGVtIiwiYWZ0ZXJJdGVtIiwicHVzaCIsInR5cGUiLCJpbmRleCIsIm1hcCIsImlkeCIsInByb2Nlc3NlZEFmdGVySXRlbXMiLCJTZXQiLCJtYXRjaGluZ0FmdGVySXRlbSIsImFkZCIsImluZGV4T2YiLCJoYXMiLCJtYXRjaGluZ0JlZm9yZUl0ZW0iLCJyZW5kZXJCdXNpbmVzc0l0ZW1DaGFuZ2VzIiwiYmVmb3JlS2V5cyIsImFmdGVyS2V5cyIsImFsbEtleXMiLCJmcm9tIiwiYmVmb3JlVmFsdWUiLCJhZnRlclZhbHVlIiwicmVuZGVySW5saW5lVmFsdWUiLCJ0b1N0cmluZyIsImNvZGUiLCJyZW5kZXJEZWx0YUNoYW5nZXMiLCJjaGFuZ2VFbnRyaWVzIiwicCIsImg1IiwicmVwbGFjZSIsInN0ciIsInRvVXBwZXJDYXNlIiwibGFiZWwiLCJvbkNsaWNrIiwiaDMiLCJidXR0b24iLCJ0aXRsZSIsImFyaWEtbGFiZWwiLCJoNCIsImVudGl0eSIsInVzZXJVdWlkIiwiY3JlYXRlZEF0IiwiZGVzY3JpcHRpb24iLCJjaGFuZ2VDb3VudCIsInNvbWUiLCJwcmUiLCJmcm9tRW50cmllcyIsImZpbHRlciJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/logs/components/LogDetailsModal.tsx\n"));

/***/ })

});