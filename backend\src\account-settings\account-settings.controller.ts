import {
  Controller,
  Get,
  Post,
  Put,
  Body,
  Param,
  Delete,
  BadRequestException,
  UseGuards,
  ParseUUIDPipe,
} from "@nestjs/common";
import { Uuid7 } from "../utils/uuid7";
import { AccountSettingsService } from "./account-settings.service";
import {
  ApiTags,
  ApiResponse,
  ApiBody,
  ApiOperation,
  ApiParam,
} from "@nestjs/swagger";
import { CreateAccountSettingsDto } from "./dto/create-account-settings.dto";
import { UpdateAccountSettingsDto } from "./dto/update-account-settings.dto";
import { AccountSettingsResponseDto } from "./dto/account-settings-response.dto";
@ApiTags("account-settings")
@Controller("account-settings")
export class AccountSettingsController {
  constructor(
    private readonly accountSettingsService: AccountSettingsService,
  ) {}

  @Post()
  @ApiOperation({ summary: "Create new account settings" })
  @ApiBody({ type: CreateAccountSettingsDto })
  @ApiResponse({
    status: 201,
    description: "Account settings created successfully",
    type: AccountSettingsResponseDto,
  })
  @ApiResponse({ status: 400, description: "Invalid input or user not found" })
  @ApiResponse({ status: 404, description: "User not found" })
  async create(
    @Body() createAccountSettingsDto: CreateAccountSettingsDto,
  ): Promise<AccountSettingsResponseDto> {
    return this.accountSettingsService.create(createAccountSettingsDto);
  }

  @Get("list")
  @ApiOperation({ summary: "List all account settings (excluding deleted)" })
  @ApiResponse({
    status: 200,
    description: "List of all account settings",
    type: [AccountSettingsResponseDto],
  })
  async findAll(): Promise<AccountSettingsResponseDto[]> {
    return this.accountSettingsService.findAll();
  }

  @Get("by-user/:userUuid")
  @ApiOperation({ summary: "Get account settings for a specific user" })
  @ApiParam({
    name: "userUuid",
    description: "User UUID to get account settings for",
    type: String,
    example: "018ea2bb-6b8d-7e9a-8a2a-7e9a8a2a7e9a",
  })
  @ApiResponse({
    status: 200,
    description: "Account settings for the specified user",
    type: [AccountSettingsResponseDto],
  })
  @ApiResponse({ status: 400, description: "Invalid user UUID" })
  async findByUserUuid(
    @Param("userUuid", ParseUUIDPipe) userUuid: string,
  ): Promise<AccountSettingsResponseDto[]> {
    try {
      new Uuid7(userUuid);
    } catch {
      throw new BadRequestException("Invalid user UUID");
    }
    return this.accountSettingsService.findByUserUuid(userUuid);
  }

  @Get(":uuid")
  @ApiOperation({ summary: "Get account settings by UUID" })
  @ApiParam({
    name: "uuid",
    description: "Account settings UUID",
    type: String,
    example: "018ea2bb-6b8d-7e9a-8a2a-7e9a8a2a7e9a",
  })
  @ApiResponse({
    status: 200,
    description: "Account settings found",
    type: AccountSettingsResponseDto,
  })
  @ApiResponse({ status: 404, description: "Account settings not found" })
  @ApiResponse({ status: 400, description: "Invalid account settings UUID" })
  async findOne(
    @Param("uuid", ParseUUIDPipe) uuid: string,
  ): Promise<AccountSettingsResponseDto> {
    try {
      new Uuid7(uuid);
    } catch {
      throw new BadRequestException("Invalid account settings UUID");
    }
    return this.accountSettingsService.findOne(uuid);
  }

  @Put(":uuid")
  @ApiOperation({ summary: "Update account settings by UUID" })
  @ApiParam({
    name: "uuid",
    description: "Account settings UUID",
    type: String,
    example: "018ea2bb-6b8d-7e9a-8a2a-7e9a8a2a7e9a",
  })
  @ApiBody({ type: UpdateAccountSettingsDto })
  @ApiResponse({
    status: 200,
    description: "Account settings updated successfully",
    type: AccountSettingsResponseDto,
  })
  @ApiResponse({ status: 404, description: "Account settings not found" })
  @ApiResponse({ status: 400, description: "Invalid input" })
  async update(
    @Param("uuid", ParseUUIDPipe) uuid: string,
    @Body() updateAccountSettingsDto: UpdateAccountSettingsDto,
  ): Promise<AccountSettingsResponseDto> {
    try {
      new Uuid7(uuid);
    } catch {
      throw new BadRequestException("Invalid account settings UUID");
    }
    return this.accountSettingsService.update(uuid, updateAccountSettingsDto);
  }

  @Delete(":uuid")
  @ApiOperation({ summary: "Delete account settings by UUID (soft delete)" })
  @ApiParam({
    name: "uuid",
    description: "Account settings UUID",
    type: String,
    example: "018ea2bb-6b8d-7e9a-8a2a-7e9a8a2a7e9a",
  })
  @ApiResponse({
    status: 200,
    description: "Account settings soft deleted successfully",
    schema: {
      example: {
        message: "Account settings soft deleted successfully",
      },
    },
  })
  @ApiResponse({ status: 404, description: "Account settings not found" })
  @ApiResponse({ status: 400, description: "Invalid account settings UUID" })
  async remove(@Param("uuid", ParseUUIDPipe) uuid: string): Promise<{ message: string }> {
    try {
      new Uuid7(uuid);
    } catch {
      throw new BadRequestException("Invalid account settings UUID");
    }
    await this.accountSettingsService.remove(uuid);
    return { message: "Account settings deleted successfully" };
  }

  @Delete("all")
  @ApiOperation({ summary: "Hard delete all account settings (irreversible)" })
  @ApiResponse({
    status: 200,
    description: "All account settings hard deleted successfully",
    schema: {
      example: {
        message: "All account settings hard deleted successfully",
        deletedCount: 5,
      },
    },
  })
  async deleteAllAccountSettings(): Promise<{
    message: string;
    deletedCount: number;
  }> {
    return this.accountSettingsService.deleteAllAccountSettings();
  }
}
