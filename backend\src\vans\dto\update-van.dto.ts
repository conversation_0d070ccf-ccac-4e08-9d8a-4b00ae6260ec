import { ApiProperty } from "@nestjs/swagger";
import { IsOptional, IsString, IsNumber } from "class-validator";

export class UpdateVanDto {
  @ApiProperty({ example: "Van 001", description: "Van name", required: false })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiProperty({
    example: "ABC-123",
    description: "Van license plate",
    required: false,
  })
  @IsString()
  @IsOptional()
  licensePlate?: string;

  @ApiProperty({
    example: "Ford Transit",
    description: "Van model",
    required: false,
  })
  @IsString()
  @IsOptional()
  model?: string;

  @ApiProperty({ example: 2020, description: "Van year", required: false })
  @IsNumber()
  @IsOptional()
  year?: number;
}
