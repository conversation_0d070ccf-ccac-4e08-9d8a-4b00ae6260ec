import { RoutesService } from "../src/routes/routes.service.typeorm";
import { Repository } from "typeorm";

describe("RoutesService", () => {
  let routesService: RoutesService;
  let mockRouteRepository: jest.Mocked<Repository<any>>;

  beforeEach(() => {
    mockRouteRepository = {
      create: jest.fn(),
      save: jest.fn(),
      findOne: jest.fn(),
      find: jest.fn(),
      createQueryBuilder: jest.fn(),
      delete: jest.fn(),
    } as any;

    routesService = new RoutesService(mockRouteRepository);
  });

  describe("Route computation logic", () => {
    it("should create a computed route successfully", async () => {
      // Arrange
      const computeRouteDto = {
        name: "Test Route",
        description: "Test Description",
        customerLocations: [
          { latitude: 40.7128, longitude: -74.006, customerName: "A" },
          { latitude: 40.7589, longitude: -73.9851, customerName: "B" },
        ],
        warehouseUuid: "test-warehouse-uuid",
      };

      const mockRoute = {
        id: "test-route-id",
        name: "Test Route",
        description: "Test Description",
        customerLocations: computeRouteDto.customerLocations,
        optimizedRoute: computeRouteDto.customerLocations.map((loc, index) => ({
          ...loc,
          order: index + 1,
        })),
        totalDistance: 0,
        warehouseId: "test-warehouse-uuid",
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockRouteRepository.create.mockReturnValue(mockRoute);
      mockRouteRepository.save.mockResolvedValue(mockRoute);

      // Act
      const result = await routesService.computeRoute(computeRouteDto);

      // Assert
      expect(mockRouteRepository.create).toHaveBeenCalled();
      expect(mockRouteRepository.save).toHaveBeenCalled();
      expect(result.name).toBe("Test Route");
      expect(result.optimizedRoute).toHaveLength(2);
    });
  });
});
