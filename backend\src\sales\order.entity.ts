import { Entity, PrimaryColumn, Column, CreateDateColumn, UpdateDateColumn, Index, OneToMany } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Uuid7 } from '../utils/uuid7';
import { OrderItem } from './order-item.entity';

export enum OrderStatus {
  DRAFT = "draft",
  PENDING = "pending",
  CONFIRMED = "confirmed",
  PROCESSING = "processing",
  SHIPPED = "shipped",
  DELIVERED = "delivered",
  CANCELLED = "cancelled",
  RETURNED = "returned",
}

export enum OrderPriority {
  LOW = "low",
  NORMAL = "normal",
  HIGH = "high",
  URGENT = "urgent",
}

@Entity('orders')
export class Order {
  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the order (primary key)",
  })
  @PrimaryColumn('uuid')
  id: string;

  @ApiProperty({
    example: "ORD-20250115-001",
    description: "Unique order number",
  })
  @Column({ unique: true })
  orderNumber: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the customer",
  })
  @Column('uuid')
  @Index()
  customerUuid: string;

  @ApiProperty({
    example: "Customer Name",
    description: "Customer name at time of order",
    required: false,
  })
  @Column({ nullable: true })
  customerName?: string;

  @ApiProperty({
    example: "12345678901",
    description: "Customer fiscal ID at time of order",
    required: false,
  })
  @Column({ nullable: true })
  customerFiscalId?: string;

  @ApiProperty({
    example: "RC123456",
    description: "Customer RC at time of order",
    required: false,
  })
  @Column({ nullable: true })
  customerRc?: string;

  @ApiProperty({
    example: "ART001",
    description: "Customer article number at time of order",
    required: false,
  })
  @Column({ nullable: true })
  customerArticleNumber?: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the warehouse",
  })
  @Column('uuid')
  @Index()
  warehouseUuid: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the quote this order was created from",
    required: false,
  })
  @Column('uuid', { nullable: true })
  @Index()
  quoteUuid?: string;

  @ApiProperty({ example: 150.0, description: "Subtotal amount before tax" })
  @Column('decimal', { precision: 10, scale: 2, default: 0 })
  subtotal: number;

  @ApiProperty({
    example: true,
    description: "Whether tax is applied to this order",
  })
  @Column({ default: false })
  useTax: boolean;

  @ApiProperty({
    example: 0.1,
    description: "Tax rate as decimal (e.g., 0.1 for 10%)",
  })
  @Column('decimal', { precision: 5, scale: 4, default: 0.1 })
  taxRate: number;

  @ApiProperty({ example: 15.0, description: "Total tax amount" })
  @Column('decimal', { precision: 10, scale: 2, default: 0 })
  taxAmount: number;

  @ApiProperty({ example: 165.0, description: "Total amount including tax" })
  @Column('decimal', { precision: 10, scale: 2 })
  totalAmount: number;

  @ApiProperty({
    example: "2025-01-15T10:30:00.000Z",
    description: "Order date",
  })
  @Column()
  orderDate: Date;

  @ApiProperty({
    example: "2025-01-20T10:00:00.000Z",
    description: "Requested delivery date",
    required: false,
  })
  @Column({ nullable: true })
  requestedDeliveryDate?: Date;

  @ApiProperty({
    example: "2025-01-22T14:30:00.000Z",
    description: "Actual delivery date",
    required: false,
  })
  @Column({ nullable: true })
  actualDeliveryDate?: Date;

  @ApiProperty({
    example: OrderStatus.PENDING,
    description: "Current status of the order",
    enum: OrderStatus,
  })
  @Column({
    type: 'enum',
    enum: OrderStatus,
    default: OrderStatus.DRAFT,
  })
  status: OrderStatus;

  @ApiProperty({
    example: OrderPriority.NORMAL,
    description: "Order priority level",
    enum: OrderPriority,
  })
  @Column({
    type: 'enum',
    enum: OrderPriority,
    default: OrderPriority.NORMAL,
  })
  priority: OrderPriority;

  @ApiProperty({
    example: "Special delivery instructions",
    description: "Order notes",
    required: false,
  })
  @Column({ nullable: true })
  notes?: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the user who created the order",
  })
  @Column('uuid')
  @Index()
  createdBy: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the user who last updated the order",
  })
  @Column('uuid')
  @Index()
  updatedBy: string;

  @ApiProperty({
    example: false,
    description: "Whether the order has been soft deleted",
  })
  @Column({ default: false })
  isDeleted: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Relationship to OrderItems
  @OneToMany(() => OrderItem, orderItem => orderItem.order)
  orderItems: OrderItem[];

  // Helper method to generate UUID
  static generateId(): string {
    return new Uuid7().toString();
  }

  // Helper method to generate UUID
  static fromBinary(binary: any): string {
    if (!binary) return null;
    try {
      return new Uuid7(binary).toString();
    } catch {
      return null;
    }
  }
} 