import React, { forwardRef } from 'react';
import { FiSearch, FiX } from 'react-icons/fi';
import { posStyles } from '../styles/posStyles';
import type { SearchBarProps } from '../types';

export const SearchBar = forwardRef<HTMLInputElement, SearchBarProps>(
  ({ value, onChange, placeholder = "Search products...", autoFocus = false, disabled = false }, ref) => {
    
    const handleClear = () => {
      if (disabled) return;
      onChange('');
      // Focus back to input after clearing
      if (ref && typeof ref === 'object' && ref.current) {
        ref.current.focus();
      }
    };

    const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
      if (disabled) return;
      // Escape key clears the search
      if (e.key === 'Escape' && value) {
        e.preventDefault();
        handleClear();
      }
    };

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      if (disabled) return;
      const newValue = e.target.value;
      onChange(newValue);
    };

    return (
      <div className={posStyles.search.container}>
        <div className={posStyles.search.inputWrapper}>
          <FiSearch className={posStyles.search.icon} />
          <input
            ref={ref}
            type="text"
            value={value}
            onChange={handleChange}
            onKeyDown={handleKeyDown}
            placeholder={placeholder}
            className={`${posStyles.search.input} ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
            autoFocus={autoFocus}
            disabled={disabled}
            aria-label="Search products"
            title="Search by product name, SKU, or barcode"
          />
          {value && !disabled && (
            <button
              type="button"
              onClick={handleClear}
              className={posStyles.search.clearButton}
              aria-label="Clear search"
              title="Clear search (Esc)"
            >
              <FiX className="h-4 w-4" />
            </button>
          )}
        </div>
        <div className="text-xs text-gray-500 mt-1 flex items-center space-x-3">
          <span className="flex items-center space-x-1">
            <span className="w-1 h-1 bg-gray-400 rounded-full"></span>
            <span>Name</span>
          </span>
          <span className="flex items-center space-x-1">
            <span className="w-1 h-1 bg-gray-400 rounded-full"></span>
            <span>SKU</span>
          </span>
          <span className="flex items-center space-x-1">
            <span className="w-1 h-1 bg-gray-400 rounded-full"></span>
            <span>Barcode</span>
          </span>
        </div>
      </div>
    );
  }
);

SearchBar.displayName = 'SearchBar'; 