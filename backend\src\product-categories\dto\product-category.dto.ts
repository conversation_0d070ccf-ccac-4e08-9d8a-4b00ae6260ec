import { ApiProperty } from "@nestjs/swagger";

export class ProductCategoryDto {
  @ApiProperty({
    example: "018ea2bb-6b8d-7e9a-8a2a-7e9a8a2a7e9a",
    description: "UUID of the product category",
  })
  uuid: string;

  @ApiProperty({
    example: "Electronics",
    description: "Name of the product category",
  })
  name: string;

  @ApiProperty({
    example: "018ea2bb-6b8d-7e9a-8a2a-7e9a8a2a7e9a",
    description: "UUID of the warehouse this category belongs to",
  })
  warehouseUuidString?: string;

  @ApiProperty({
    example: "018ea2bb-6b8d-7e9a-8a2a-7e9a8a2a7e9a",
    description: "UUID of the user who created the category",
  })
  createdByString?: string;

  @ApiProperty({
    example: "John Doe",
    description: "Name of the user who created the category",
  })
  createdByName?: string;

  @ApiProperty({
    example: "018ea2bb-6b8d-7e9a-8a2a-7e9a8a2a7e9a",
    description: "UUID of the user who last updated the category",
  })
  updatedByString?: string;

  @ApiProperty({
    example: "Jane Smith",
    description: "Name of the user who last updated the category",
  })
  updatedByName?: string;

  @ApiProperty({
    example: "2024-01-15T10:30:00.000Z",
    description: "Creation timestamp",
  })
  createdAt: Date;

  @ApiProperty({
    example: "2024-01-15T10:30:00.000Z",
    description: "Last update timestamp",
  })
  updatedAt: Date;
}

export function toProductCategoryDto(productCategory: any): ProductCategoryDto {
  return {
    uuid: productCategory.id || productCategory.uuid,
    name: productCategory.name,
    warehouseUuidString: productCategory.warehouseUuid,
    createdByString: productCategory.createdBy,
    updatedByString: productCategory.updatedBy,
    createdAt: productCategory.createdAt,
    updatedAt: productCategory.updatedAt,
  };
}
