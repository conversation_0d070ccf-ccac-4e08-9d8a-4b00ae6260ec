import {
  <PERSON><PERSON><PERSON>,
  PrimaryColumn,
  <PERSON>umn,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from "typeorm";
import { ApiProperty } from "@nestjs/swagger";
import { Uuid7 } from "../utils/uuid7";
import { Warehouse } from "../warehouses/warehouse.entity";

@Entity("routes")
export class Route {
  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the route (primary key)",
  })
  @PrimaryColumn("uuid")
  id: string;

  @ApiProperty({
    example: "Morning Delivery Route",
    description: "Route name (required)",
  })
  @Column({ type: "varchar", nullable: false })
  name: string;

  @ApiProperty({
    example: "Optimized route for morning deliveries in downtown area",
    description: "Route description",
    required: false,
  })
  @Column({ type: "text", nullable: true })
  description?: string;

  @ApiProperty({
    example: [
      { latitude: 40.7128, longitude: -74.006, customerName: "Customer A" },
      { latitude: 40.7589, longitude: -73.9851, customerName: "Customer B" },
    ],
    description: "Original customer locations before optimization",
    type: "array",
  })
  @Column({ type: "jsonb", nullable: false })
  customerLocations: Array<{
    latitude: number;
    longitude: number;
    customerName?: string;
  }>;

  @ApiProperty({
    example: [
      {
        latitude: 40.7128,
        longitude: -74.006,
        customerName: "Customer A",
        order: 1,
      },
      {
        latitude: 40.7589,
        longitude: -73.9851,
        customerName: "Customer B",
        order: 2,
      },
    ],
    description: "Optimized route with visit order",
    type: "array",
  })
  @Column({ type: "jsonb", nullable: false })
  optimizedRoute: Array<{
    latitude: number;
    longitude: number;
    customerName?: string;
    order: number;
  }>;

  @ApiProperty({
    example: 15.5,
    description: "Total distance of the optimized route in kilometers",
  })
  @Column({ type: "decimal", precision: 10, scale: 2, nullable: false })
  totalDistance: number;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the warehouse this route belongs to (required)",
  })
  @Column({ type: "uuid", nullable: false })
  warehouseId: string;

  @ApiProperty({ example: false, description: "Soft delete flag" })
  @Column({ type: "boolean", default: false })
  isDeleted: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Relations
  @ManyToOne(() => Warehouse, { nullable: false })
  @JoinColumn({ name: "warehouseId" })
  warehouse: Warehouse;

  // Helper method to generate UUID
  static generateId(): string {
    return new Uuid7().toString();
  }
} 