import { IsOptional, Is<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>UUI<PERSON> } from "class-validator";
import { ApiProperty } from "@nestjs/swagger";

export class FilterPurchaseDto {
  @ApiProperty({
    required: false,
    example: "uuid-v7-string",
    description: "Warehouse UUID to filter by",
  })
  @IsOptional()
  @IsString()
  @IsUUID("all")
  warehouseUuid?: string;

  @ApiProperty({
    required: false,
    example: "uuid-v7-string",
    description: "Supplier UUID to filter by",
  })
  @IsOptional()
  @IsString()
  @IsUUID("all")
  supplierUuid?: string;

  @ApiProperty({
    required: false,
    example: "PAID",
    description: "Purchase status to filter by",
  })
  @IsOptional()
  @IsString()
  status?: string;

  @ApiProperty({
    required: false,
    example: "2024-01-01T00:00:00.000Z",
    description: "Filter purchases created after this date (ISO 8601)",
  })
  @IsOptional()
  @IsString()
  createdFrom?: string; // ISO date string

  @ApiProper<PERSON>({
    required: false,
    example: "2024-12-31T23:59:59.999Z",
    description: "Filter purchases created before this date (ISO 8601)",
  })
  @IsOptional()
  @IsString()
  createdTo?: string; // ISO date string

  // Add more fields as needed for advanced filtering
}
