import React from 'react';
import { Sale } from '../salesApi';

interface SalesCancelModalProps {
  isOpen: boolean;
  saleToCancel: Sale | null;
  isCancelling: boolean;
  onConfirm: () => void;
  onClose: () => void;
}

export const SalesCancelModal: React.FC<SalesCancelModalProps> = ({
  isOpen,
  saleToCancel,
  isCancelling,
  onConfirm,
  onClose
}) => {
  if (!isOpen || !saleToCancel) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full m-4">
        <div className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Cancel Sale</h3>
          <p className="text-sm text-gray-600 mb-6">
            Are you sure you want to cancel sale <strong>{saleToCancel.invoiceNumber}</strong>? 
            This action cannot be undone.
          </p>
          <div className="flex justify-end space-x-3">
            <button
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
              disabled={isCancelling}
            >
              Cancel
            </button>
            <button
              onClick={onConfirm}
              className="px-4 py-2 text-sm font-medium text-white bg-red-600 rounded-md hover:bg-red-700 disabled:opacity-50"
              disabled={isCancelling}
            >
              {isCancelling ? 'Cancelling...' : 'Cancel Sale'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}; 