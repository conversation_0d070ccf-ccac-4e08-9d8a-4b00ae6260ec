import { PassportStrategy } from "@nestjs/passport";
import { Injectable, Inject } from "@nestjs/common";
import { UsersService } from "../users/users.service";
import { RolesService } from "../roles/roles.service";

import { Strategy, VerifyCallback } from "passport-google-oauth20";

@Injectable()
export class GoogleStrategy extends PassportStrategy(Strategy, "google") {
  constructor(
    private readonly usersService: UsersService,
    private readonly rolesService: RolesService,
  ) {
    super({
      clientID: process.env.GOOGLE_CLIENT_ID,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET,
      callbackURL: process.env.GOOGLE_CALLBACK_URL,
      scope: ["profile", "email"],
    });
  }

  async validate(
    accessToken: string,
    refreshToken: string,
    profile: any,
    done: VerifyCallback,
  ): Promise<any> {
    const { name, emails, photos } = profile;
    const email = emails[0].value;
    try {
      let user = await this.usersService.findByEmail(email);
      if (!user) {
        // Create user with proper logic - let the service handle warehouse creation and role assignment
        user = await this.usersService.create(
          undefined,
          email,
          name.givenName + " " + name.familyName,
          undefined,
          "super",
        );
      } else {
        // Check if existing user has a role assigned
        if (!user.roleUuidString) {
          // SECURITY FIX: Don't automatically assign admin role
          // Instead, throw an error requiring explicit role assignment
          const error = new Error(
            `User ${email} exists but has no role assigned. Please contact an administrator to assign appropriate permissions.`
          );
          error.name = 'ROLE_REQUIRED';
          done(error);
          return;
        }
      }
      // Optionally update user info here if needed
      done(null, user);
    } catch (err) {
      // Provide detailed error information for debugging
      done(
        new Error(`Google OAuth user validation failed: ${err.message || err}`),
      );
    }
  }
}
