import { ApiProperty } from "@nestjs/swagger";
import { StorageType } from "../storage_type.enum";
import { IsNotEmpty, IsString, IsUUID, IsEnum } from "class-validator";

export class CreateStorageDto {
  @ApiProperty({ enum: StorageType, example: StorageType.WAREHOUSE })
  @IsNotEmpty()
  @IsEnum(StorageType)
  type: StorageType;
  @ApiProperty({ example: "Main Warehouse" })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    example: "d1e2f3a4-5678-4bcd-9e21-abcdef123456",
    description: "Warehouse UUID (required)",
  })
  @IsUUID()
  @IsNotEmpty()
  warehouseUuid: string;

  @ApiProperty({ example: "c3b2a1d4-9876-4f1e-8a34-123456abcdef" })
  @IsUUID()
  @IsNotEmpty()
  userUuid: string;
}
