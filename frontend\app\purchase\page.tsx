"use client";

import ProtectedRoute from "@/components/ProtectedRoute";
import TopTaskBar from '@/components/TopTaskBar/TopTaskBar';
import SideTaskBar from '@/components/SideTaskBar/SideTaskBar';
import { FiShoppingCart, FiClock } from 'react-icons/fi';

export default function PurchasePage() {
  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-gray-50">
        {/* Top Task Bar */}
        <TopTaskBar />
        <div className="flex">
          {/* Side Task Bar */}
          <SideTaskBar />
          
          {/* Main Content */}
          <main className="flex-1 p-8">
            <div className="max-w-4xl mx-auto">
              {/* Header */}
              <div className="mb-8">
                <div className="flex items-center gap-3 mb-4">
                  <FiShoppingCart className="text-3xl text-blue-600" />
                  <h1 className="text-3xl font-bold text-gray-900">Purchase</h1>
                </div>
                <p className="text-gray-600">Manage your purchase orders and inventory procurement</p>
              </div>

              {/* Coming Soon Content */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-12 text-center">
                <div className="flex justify-center mb-6">
                  <div className="bg-blue-100 p-4 rounded-full">
                    <FiClock className="text-4xl text-blue-600" />
                  </div>
                </div>
                
                <h2 className="text-2xl font-semibold text-gray-900 mb-4">
                  Coming Soon
                </h2>
                
                <p className="text-gray-600 mb-6 max-w-md mx-auto">
                  The purchase management system is currently under development. 
                  This feature will allow you to create purchase orders, manage suppliers, 
                  and track inventory procurement.
                </p>
                
                <div className="bg-gray-50 rounded-lg p-6 max-w-lg mx-auto">
                  <h3 className="font-semibold text-gray-900 mb-3">Planned Features:</h3>
                  <ul className="text-sm text-gray-600 space-y-2 text-left">
                    <li className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                      Create and manage purchase orders
                    </li>
                    <li className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                      Supplier management and selection
                    </li>
                    <li className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                      Product catalog integration
                    </li>
                    <li className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                      Payment tracking and management
                    </li>
                    <li className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                      Purchase history and reporting
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </main>
        </div>
      </div>
    </ProtectedRoute>
  );
} 