import { ApiProperty } from "@nestjs/swagger";
import { AccountPlanIdentifier } from "../enums/account-plan.enum";
import { FeatureIdentifier } from "../enums/feature.enum";

export class AccountPlanDto {
  @ApiProperty({
    example: "FREE",
    description: "The identifier of the account plan",
    enum: AccountPlanIdentifier,
  })
  identifier: AccountPlanIdentifier;

  @ApiProperty({
    example: "Free Plan",
    description: "The display name of the account plan",
  })
  name: string;

  @ApiProperty({
    example: ["INVENTORY_TRACKING", "SALES_ORDERS", "CUSTOMER_MANAGEMENT"],
    description: "List of feature identifiers included in this plan",
    enum: FeatureIdentifier,
    isArray: true,
  })
  features: FeatureIdentifier[];
}
