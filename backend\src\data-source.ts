import { DataSource } from "typeorm";
import * as dotenv from "dotenv";

// Load environment variables
dotenv.config({ path: ".env" });

export const AppDataSource = new DataSource({
  type: "postgres",
  host: process.env.YUGABYTE_HOST || "localhost",
  port: parseInt(process.env.YUGABYTE_PORT || "5433"),
  database: process.env.YUGABYTE_DATABASE || "yugabyte",
  username: process.env.YUGABYTE_USER || "yugabyte",
  password: process.env.YUGABYTE_PASSWORD || "yugabyte",
  synchronize: false, // Disable synchronize for migrations
  logging: ["error", "warn"],
  ssl: false,
  entities: ["src/**/*.entity.ts"],
  migrations: ["migration/*.ts"],
  subscribers: [],
}); 