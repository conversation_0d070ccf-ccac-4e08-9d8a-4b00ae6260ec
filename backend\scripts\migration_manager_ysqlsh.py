#!/usr/bin/env python3
"""
TypeORM Migration Manager with Automated Backup (API-Based)

This script provides a safe way to run TypeORM migrations with automatic backup
creation and rollback capabilities using the YugabyteDB Management API.

Usage:
    python migration_manager_ysqlsh.py run          # Run pending migrations with backup
    python migration_manager_ysqlsh.py revert       # Revert last migration
    python migration_manager_ysqlsh.py status       # Show migration status (with database connection)
    python migration_manager_ysqlsh.py api-status   # Show detailed API and system status
    python migration_manager_ysqlsh.py backup       # Create backup only
    python migration_manager_ysqlsh.py restore      # Restore from latest backup
    python migration_manager_ysqlsh.py list         # List all available backups
    python migration_manager_ysqlsh.py restore-from <backup-name>  # Restore from specific backup
    python migration_manager_ysqlsh.py delete-backup <backup-name> # Delete specific backup
    python migration_manager_ysqlsh.py clear-db     # Clear database (development only)
    python migration_manager_ysqlsh.py db-info      # Show database information
    python migration_manager_ysqlsh.py monitor      # Show comprehensive monitoring data
"""

import sys
import subprocess
import os
import json
import time
import re
import requests
from pathlib import Path
from datetime import datetime
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

BACKEND_DIR = Path(__file__).resolve().parent.parent
MIGRATIONS_DIR = BACKEND_DIR / "migration"
ENV_PATH = BACKEND_DIR / ".env"

# API configuration
BACKUP_API_BASE_URL = os.environ.get("BACKUP_API_BASE_URL", "http://localhost:5000")

def find_npm():
    """Find npm executable in common locations."""
    npm_paths = [
        "npm",  # Try PATH first
        "C:\\Program Files\\nodejs\\npm.cmd",
        "C:\\Program Files\\nodejs\\npm.ps1",
        "C:\\Program Files (x86)\\nodejs\\npm.cmd",
        "C:\\Program Files (x86)\\nodejs\\npm.ps1"
    ]
    
    for path in npm_paths:
        try:
            subprocess.run([path, "--version"], capture_output=True, check=True)
            return path
        except (subprocess.CalledProcessError, FileNotFoundError):
            continue
    
    return None

def validate_environment():
    """Validate that the backup API is accessible."""
    try:
        response = requests.get(f"{BACKUP_API_BASE_URL}/health/", timeout=10)
        if response.status_code == 200:
            health_data = response.json()
            if health_data.get("status") == "healthy":
                print("[OK] Backup API is healthy and accessible")
                return True
            else:
                print(f"[WARNING] Backup API status: {health_data.get('status')}")
                return True
        else:
            print(f"[ERROR] Backup API health check failed: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"[ERROR] Cannot connect to backup API at {BACKUP_API_BASE_URL}: {e}")
        print(f"Please ensure the backup backend is running on {BACKUP_API_BASE_URL}")
        return False

def api_request(method, endpoint, data=None, timeout=30):
    """Make a request to the backup API."""
    url = f"{BACKUP_API_BASE_URL}{endpoint}"
    headers = {"Content-Type": "application/json"}
    
    try:
        if method.upper() == "GET":
            response = requests.get(url, headers=headers, timeout=timeout)
        elif method.upper() == "POST":
            response = requests.post(url, headers=headers, json=data, timeout=timeout)
        elif method.upper() == "DELETE":
            response = requests.delete(url, headers=headers, timeout=timeout)
        else:
            raise ValueError(f"Unsupported HTTP method: {method}")
        
        response.raise_for_status()
        return response.json()
        
    except requests.exceptions.RequestException as e:
        print(f"[ERROR] API request failed: {e}")
        if hasattr(e, 'response') and e.response is not None:
            try:
                error_data = e.response.json()
                print(f"[ERROR] API Error: {error_data}")
            except:
                print(f"[ERROR] HTTP Status: {e.response.status_code}")
        return None

def create_backup(prefix="migration"):
    """Create a database backup using the API."""
    print(f"[CREATING] Creating backup with prefix: {prefix}")
    
    data = {"prefix": prefix}
    result = api_request("POST", "/backup/", data)
    
    if result and result.get("status") == "success":
        backup_data = result.get("data", {})
        backup_name = backup_data.get("backup_name")
        timestamp = backup_data.get("timestamp")
        size_bytes = backup_data.get("size_bytes", 0)
        size_mb = size_bytes / (1024 * 1024)
        
        print(f"[OK] Backup created successfully!")
        print(f"   [NAME] {backup_name}")
        print(f"   [TIME] {timestamp}")
        print(f"   [SIZE] {size_mb:.2f} MB")
        
        return backup_name
    else:
        print(f"[ERROR] Backup creation failed: {result}")
        return None

def list_backups():
    """List all available backups using the API."""
    print("[INFO] Available Backups:")
    print("=" * 50)
    
    result = api_request("GET", "/backup/list")
    
    if result and result.get("status") == "success":
        backups = result.get("data", [])
        
        if not backups:
            print("[INFO] No backups found")
            return []
        
        print(f"Found {len(backups)} backup(s):")
        print()
        
        for i, backup in enumerate(backups, 1):
            name = backup.get("name", "Unknown")
            size_bytes = backup.get("size_bytes", 0)
            size_mb = size_bytes / (1024 * 1024)
            created_at = backup.get("created_at", "Unknown")
            modified_at = backup.get("modified_at", "Unknown")
            
            print(f"{i:2d}. [OK] {name}")
            print(f"    [DATE] Created: {created_at}")
            print(f"    [SIZE] Size: {size_mb:.2f} MB")
            print()
        
        print("[HELP] To restore the latest backup, use:")
        print("   python scripts/migration_manager_ysqlsh.py restore")
        print()
        print("[HELP] To restore a specific backup, use:")
        print("   python scripts/migration_manager_ysqlsh.py restore-from <backup-name>")
        print()
        print("[HELP] To delete a backup, use:")
        print("   python scripts/migration_manager_ysqlsh.py delete-backup <backup-name>")
        
        return backups
    else:
        print(f"[ERROR] Failed to list backups: {result}")
        return []

def delete_backup(backup_name):
    """Delete a specific backup using the API."""
    print(f"[DELETING] Deleting backup: {backup_name}")
    
    # Confirm deletion
    confirm = input(f"[WARNING] This will permanently delete backup '{backup_name}'. Continue? (yes/no): ")
    if confirm.lower() != 'yes':
        print("[CANCELLED] Deletion cancelled")
        return False
    
    result = api_request("DELETE", f"/backup/{backup_name}")
    
    if result and result.get("status") == "success":
        print(f"[SUCCESS] Backup '{backup_name}' deleted successfully!")
        return True
    else:
        print(f"[ERROR] Backup deletion failed: {result}")
        return False

def restore_from_backup():
    """Restore database from the latest backup using the API."""
    print("[RESTORING] Getting latest backup...")
    
    result = api_request("GET", "/backup/list")
    
    if not result or result.get("status") != "success":
        print(f"[ERROR] Failed to get backup list: {result}")
        return False
    
    backups = result.get("data", [])
    if not backups:
        print("[ERROR] No backups found")
        return False
    
    latest_backup = backups[0]
    backup_name = latest_backup.get("name")
    
    print(f"[RESTORING] Restoring from backup: {backup_name}")
    
    # Confirm restoration
    confirm = input("[WARNING] This will overwrite the current database. Continue? (yes/no): ")
    if confirm.lower() != 'yes':
        print("[CANCELLED] Restoration cancelled")
        return False
    
    data = {"backup_name": backup_name}
    result = api_request("POST", "/backup/restore", data)
    
    if result and result.get("status") == "success":
        print("[SUCCESS] Database restored successfully!")
        return True
    else:
        print(f"[ERROR] Restoration failed: {result}")
        return False

def restore_from_specific_backup(backup_name):
    """Restore database from a specific backup using the API."""
    print(f"[RESTORING] Restoring from specific backup: {backup_name}")
    
    # Confirm restoration
    confirm = input("[WARNING] This will overwrite the current database. Continue? (yes/no): ")
    if confirm.lower() != 'yes':
        print("[CANCELLED] Restoration cancelled")
        return False
    
    data = {"backup_name": backup_name}
    result = api_request("POST", "/backup/restore", data)
    
    if result and result.get("status") == "success":
        print("[SUCCESS] Database restored successfully!")
        return True
    else:
        print(f"[ERROR] Restoration failed: {result}")
        return False

def clear_database():
    """Clear all data from the database using the API."""
    print("[CLEARING] Clearing database...")
    
    # Confirm clearing
    confirm = input("[WARNING] This will delete ALL data from the database. Continue? (yes/no): ")
    if confirm.lower() != 'yes':
        print("[CANCELLED] Database clearing cancelled")
        return False
    
    result = api_request("POST", "/database/clear")
    
    if result and result.get("status") == "success":
        print("[SUCCESS] Database cleared successfully!")
        return True
    else:
        print(f"[ERROR] Database clearing failed: {result}")
        return False

def get_database_info():
    """Get database information using the API."""
    print("[INFO] Database Information:")
    print("=" * 50)
    
    result = api_request("GET", "/database/info")
    
    if result:
        print(f"Database Name: {result.get('database_name', 'Unknown')}")
        print(f"Host: {result.get('host', 'Unknown')}")
        print(f"Port: {result.get('port', 'Unknown')}")
        print(f"User: {result.get('user', 'Unknown')}")
        print(f"Version: {result.get('version', 'Unknown')}")
        print(f"Type: {result.get('type', 'Unknown')}")
    else:
        print("[ERROR] Failed to get database information")

def run_typeorm_command(command, description):
    """Run a TypeORM CLI command."""
    print(f"[RUNNING] {description}...")
    
    try:
        npm_cmd = find_npm()
        if not npm_cmd:
            raise FileNotFoundError("npm command not found in PATH or common locations")
        
        result = subprocess.run([
            npm_cmd, "run", command
        ], cwd=BACKEND_DIR, capture_output=True, text=True, check=True)
        
        print(f"[OK] {description} completed successfully")
        if result.stdout:
            print(result.stdout)
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"[ERROR] {description} failed:")
        print(f"   Error: {e}")
        if e.stdout:
            print(f"   Stdout: {e.stdout}")
        if e.stderr:
            print(f"   Stderr: {e.stderr}")
        return False
    except FileNotFoundError as e:
        print(f"[ERROR] {description} failed: {e}")
        return False

def get_migration_status():
    """Get current migration status with database connection."""
    try:
        npm_cmd = find_npm()
        if not npm_cmd:
            raise FileNotFoundError("npm command not found in PATH or common locations")
        
        result = subprocess.run([
            npm_cmd, "run", "typeorm", "migration:show", "--", "-d", "src/data-source.ts"
        ], cwd=BACKEND_DIR, capture_output=True, text=True, check=True)
        
        return result.stdout
        
    except subprocess.CalledProcessError as e:
        print(f"[ERROR] Failed to get migration status: {e}")
        return None
    except FileNotFoundError as e:
        print(f"[ERROR] Failed to get migration status: {e}")
        return None

def list_pending_migrations():
    """List pending migrations."""
    try:
        npm_cmd = find_npm()
        if not npm_cmd:
            raise FileNotFoundError("npm command not found in PATH or common locations")
        
        result = subprocess.run([
            npm_cmd, "run", "typeorm", "migration:show", "--", "-d", "src/data-source.ts"
        ], cwd=BACKEND_DIR, capture_output=True, text=True, check=True)
        
        # Parse the output to find pending migrations
        lines = result.stdout.split('\n')
        pending = []
        
        for line in lines:
            line = line.strip()
            # Look for lines that contain [ ] (pending migrations) - handle ANSI escape codes
            if '[ ] ' in line:
                # Extract the migration name from the line
                # Find the position of '[ ] ' and extract everything after it
                start_pos = line.find('[ ] ') + 4
                migration_name = line[start_pos:].strip()
                # Remove any ANSI escape codes from the migration name
                import re
                migration_name = re.sub(r'\x1b\[[0-9;]*[a-zA-Z]', '', migration_name)
                pending.append(migration_name)
        
        return pending
        
    except subprocess.CalledProcessError as e:
        print(f"[ERROR] Failed to list pending migrations: {e}")
        return []
    except FileNotFoundError as e:
        print(f"[ERROR] Failed to list pending migrations: {e}")
        return []

def run_migrations():
    """Run pending migrations with backup."""
    print("[RUNNING] Running migrations with backup...")
    
    # Check for pending migrations
    pending = list_pending_migrations()
    if not pending:
        print("[OK] No pending migrations found")
        return
    
    print(f"[INFO] Found {len(pending)} pending migration(s):")
    for migration in pending:
        print(f"   - {migration}")
    
    # Create backup with migration prefix
    backup_name = create_backup("migration")
    if not backup_name:
        print("[ERROR] Failed to create backup. Aborting migration.")
        sys.exit(1)
    
    # Run migrations using the unsafe command to avoid infinite loop
    success = run_typeorm_command("migration:run-unsafe", "Running migrations")
    
    if success:
        print("[SUCCESS] Migration completed successfully!")
        print(f"[BACKUP] Backup available: {backup_name}")
    else:
        print("[ERROR] Migration failed!")
        print(f"[BACKUP] Backup available: {backup_name}")
        print("[RESTORE] You can restore from backup using: python migration_manager_ysqlsh.py restore")
        sys.exit(1)

def revert_migration():
    """Revert the last migration."""
    print("[RUNNING] Reverting last migration...")
    
    # Create backup with revert prefix
    backup_name = create_backup("revert")
    if not backup_name:
        print("[ERROR] Failed to create backup. Aborting revert.")
        sys.exit(1)
    
    # Revert migration using the unsafe command to avoid infinite loop
    success = run_typeorm_command("migration:revert-unsafe", "Reverting migration")
    
    if success:
        print("[OK] Migration reverted successfully!")
        print(f"[BACKUP] Backup available: {backup_name}")
    else:
        print("[ERROR] Migration revert failed!")
        print(f"[BACKUP] Backup available: {backup_name}")
        sys.exit(1)

def show_status():
    """Show migration status with full database connection."""
    print("[STATUS] Migration Status (with database connection):")
    print("=" * 50)
    
    # Use full database connection status
    status = get_migration_status()
    if status:
        print(status)
    else:
        print("[ERROR] Could not retrieve migration status")

def show_api_status():
    """Show backup API status."""
    print("[STATUS] Backup API Status:")
    print("=" * 50)
    
    # Health check
    health_result = api_request("GET", "/health/")
    if health_result:
        print(f"Health Status: {health_result.get('status', 'Unknown')}")
        if health_result.get('database'):
            print(f"Database: {health_result.get('database')}")
        if health_result.get('host'):
            print(f"Host: {health_result.get('host')}")
        if health_result.get('port'):
            print(f"Port: {health_result.get('port')}")
        if health_result.get('yugabyte_tools_available') is not None:
            print(f"YugabyteDB Tools Available: {health_result.get('yugabyte_tools_available')}")
    
    # System status
    status_result = api_request("GET", "/health/status")
    if status_result:
        env_status = status_result.get('environment', {})
        tools_status = status_result.get('yugabyte_tools', {})
        backups_count = status_result.get('backups_count', 0)
        backups_directory = status_result.get('backups_directory', 'Unknown')
        
        print(f"Environment Valid: {env_status.get('valid', 'Unknown')}")
        print(f"YugabyteDB Tools Available: {tools_status.get('available', 'Unknown')}")
        print(f"Total Backups: {backups_count}")
        print(f"Backups Directory: {backups_directory}")
    
    # Connection test
    connection_result = api_request("GET", "/monitoring/connection")
    if connection_result:
        connection_status = connection_result.get('status', 'Unknown')
        response_time = connection_result.get('response_time_ms', 0)
        print(f"Database Connection: {connection_status}")
        print(f"Response Time: {response_time:.2f}ms")

def show_monitoring_data():
    """Show comprehensive monitoring data."""
    print("[MONITORING] Comprehensive System Monitoring:")
    print("=" * 60)
    
    # Get monitoring overview
    overview_result = api_request("GET", "/monitoring/overview")
    
    if overview_result:
        timestamp = overview_result.get('timestamp', 'Unknown')
        print(f"Timestamp: {timestamp}")
        print()
        
        # Connection info
        connection = overview_result.get('connection', {})
        if connection:
            print("Database Connection:")
            print(f"  Status: {connection.get('status', 'Unknown')}")
            print(f"  Host: {connection.get('host', 'Unknown')}")
            print(f"  Port: {connection.get('port', 'Unknown')}")
            print(f"  Database: {connection.get('database', 'Unknown')}")
            print(f"  Response Time: {connection.get('response_time_ms', 0):.2f}ms")
            print()
        
        # Cluster info
        cluster = overview_result.get('cluster', {})
        if cluster:
            print("Cluster Health:")
            print(f"  Overall Status: {cluster.get('overall_status', 'Unknown')}")
            print(f"  Total Nodes: {cluster.get('total_nodes', 0)}")
            print(f"  Healthy Nodes: {cluster.get('healthy_nodes', 0)}")
            print(f"  Unhealthy Nodes: {cluster.get('unhealthy_nodes', 0)}")
            print()
        
        # System info
        system = overview_result.get('system', {})
        if system:
            print("System Resources:")
            print(f"  CPU Usage: {system.get('cpu_usage_percent', 0):.1f}%")
            print(f"  Memory Usage: {system.get('memory_usage_percent', 0):.1f}%")
            print(f"  Disk Usage: {system.get('disk_usage_percent', 0):.1f}%")
            print()
        
        # Database info
        database = overview_result.get('database', {})
        if database:
            print("Database Statistics:")
            print(f"  Active Connections: {database.get('active_connections', 0)}")
            print(f"  Total Connections: {database.get('total_connections', 0)}")
            print(f"  Database Size: {database.get('database_size_mb', 0):.1f} MB")
            print(f"  Table Count: {database.get('table_count', 0)}")
            print(f"  Index Count: {database.get('index_count', 0)}")
            print(f"  Slow Queries: {database.get('slow_queries', 0)}")
    else:
        print("[ERROR] Failed to get monitoring data")

def main():
    """Main function."""
    if len(sys.argv) < 2:
        print(__doc__)
        sys.exit(1)
    
    # Validate environment and API connectivity
    if not validate_environment():
        print(f"\n[TROUBLESHOOTING] Troubleshooting tips:")
        print(f"1. Ensure the backup backend is running on {BACKUP_API_BASE_URL}")
        print(f"2. Check if the backup backend is accessible: curl {BACKUP_API_BASE_URL}/health/")
        print(f"3. Verify network connectivity to the backup backend")
        print(f"4. Check backup backend logs for any errors")
        print(f"5. Verify environment variables are set correctly")
        sys.exit(1)
    
    command = sys.argv[1].lower()
    
    if command == "run":
        run_migrations()
    elif command == "revert":
        revert_migration()
    elif command == "status":
        show_status()
    elif command == "api-status":
        show_api_status()
    elif command == "backup":
        create_backup()
    elif command == "restore":
        restore_from_backup()
    elif command == "list":
        list_backups()
    elif command == "restore-from":
        if len(sys.argv) < 3:
            print("[ERROR] Missing backup name")
            print("Usage: python scripts/migration_manager_ysqlsh.py restore-from <backup-name>")
            print("\n[HELP] Available backups:")
            list_backups()
            sys.exit(1)
        backup_name = sys.argv[2]
        restore_from_specific_backup(backup_name)
    elif command == "delete-backup":
        if len(sys.argv) < 3:
            print("[ERROR] Missing backup name")
            print("Usage: python scripts/migration_manager_ysqlsh.py delete-backup <backup-name>")
            print("\n[HELP] Available backups:")
            list_backups()
            sys.exit(1)
        backup_name = sys.argv[2]
        delete_backup(backup_name)
    elif command == "clear-db":
        clear_database()
    elif command == "db-info":
        get_database_info()
    elif command == "monitor":
        show_monitoring_data()
    else:
        print(f"[ERROR] Unknown command: {command}")
        print(__doc__)
        sys.exit(1)

if __name__ == "__main__":
    main() 