import { Entity, PrimaryColumn, Column, CreateDateColumn, UpdateDateColumn, Index, OneToMany } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Uuid7 } from '../utils/uuid7';
import { QuoteItem } from './quote-item.entity';

export enum QuoteStatus {
  DRAFT = "draft",
  SENT = "sent",
  ACCEPTED = "accepted",
  REJECTED = "rejected",
  EXPIRED = "expired",
  CONVERTED = "converted",
}

@Entity('quotes')
export class Quote {
  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the quote (primary key)",
  })
  @PrimaryColumn('uuid')
  id: string;

  @ApiProperty({
    example: "QT-20250115-001",
    description: "Unique quote number",
  })
  @Column({ unique: true })
  quoteNumber: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the customer",
  })
  @Column('uuid')
  @Index()
  customerUuid: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the warehouse",
  })
  @Column('uuid')
  @Index()
  warehouseUuid: string;

  @ApiProperty({ example: 165.0, description: "Total amount" })
  @Column('decimal', { precision: 10, scale: 2 })
  totalAmount: number;

  @ApiProperty({
    example: QuoteStatus.DRAFT,
    description: "Current status of the quote",
    enum: QuoteStatus,
  })
  @Column({
    type: 'enum',
    enum: QuoteStatus,
    default: QuoteStatus.DRAFT,
  })
  status: QuoteStatus;

  @ApiProperty({
    example: "2025-01-15T10:30:00.000Z",
    description: "Quote date",
  })
  @Column()
  quoteDate: Date;

  @ApiProperty({
    example: "2025-02-14T10:30:00.000Z",
    description: "Quote expiry date",
  })
  @Column()
  expiryDate: Date;

  @ApiProperty({
    example: "Special pricing for bulk order",
    description: "Quote notes",
    required: false,
  })
  @Column({ nullable: true })
  notes?: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the converted order",
    required: false,
  })
  @Column('uuid', { nullable: true })
  @Index()
  convertedToOrderUuid?: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the user who created the quote",
  })
  @Column('uuid')
  @Index()
  createdBy: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the user who last updated the quote",
  })
  @Column('uuid')
  @Index()
  updatedBy: string;

  @ApiProperty({
    example: false,
    description: "Whether the quote has been soft deleted",
  })
  @Column({ default: false })
  isDeleted: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Relationship to QuoteItems
  @OneToMany(() => QuoteItem, quoteItem => quoteItem.quote)
  quoteItems: QuoteItem[];

  // Helper method to generate UUID
  static generateId(): string {
    return new Uuid7().toString();
  }

  // Helper method to generate UUID
  static fromBinary(binary: any): string {
    if (!binary) return null;
    try {
      return new Uuid7(binary).toString();
    } catch {
      return null;
    }
  }
} 