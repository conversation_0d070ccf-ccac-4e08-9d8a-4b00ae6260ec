# Setup & Installation Guide

This document contains all setup, installation, and environment configuration instructions for the Dido Distribution backend.

---

## Prerequisites
- Node.js (v18+ recommended)
- npm or yarn
- YugabyteDB (local or remote instance)

---

## Dependencies
- bcrypt
- @nestjs/passport
- passport-google-oauth20
- @nestjs/typeorm
- typeorm
- pg

### Install Dependencies
If not already installed, run:
```bash
npm install bcrypt @nestjs/passport passport passport-google-oauth20 @nestjs/typeorm typeorm pg
```

---

## 1. <PERSON><PERSON> & Install
```bash
git clone <repo-url>
cd backend
npm install
```

---

## 2. Environment Setup
Create a `.env` file in the root:
```
YUGABYTE_HOST=localhost
YUGABYTE_PORT=5433
YUGABYTE_DATABASE=yugabyte
YUGABYTE_USER=yugabyte
YUGABYTE_PASSWORD=password
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GOOGLE_CALLBACK_URL=http://localhost:8000/auth/google/callback
```

---

## 3. Running the App

### Development (auto-reload):
```bash
npm run dev
```
### Production:
```bash
npm run start
```
- API: [http://localhost:8000](http://localhost:8000)
- Swagger Docs: [http://localhost:8000/docs](http://localhost:8000/docs)

---

## 4. YugabyteDB Setup

Ensure YugabyteDB is running and accessible. The application will automatically create the required tables using TypeORM's synchronize feature.

For local development, you can use Docker:
```bash
docker run -d --name yugabyte -p 7000:7000 -p 9000:9000 -p 5433:5433 -p 9042:9042 yugabytedb/yugabyte:latest bin/yugabyted start --daemon=false
```

---

## 🔑 Authentication Modules

### Password Auth (`password.js`)
Provides secure password hashing and verification using bcrypt. Install `bcrypt` as a dependency and use the helper functions in your services as needed.

### Google OAuth2
Supports authentication via Google using Passport.js. Add your Google credentials to `.env` and install the required dependencies. Register the `AuthModule` in your main app module.

#### Example: Register AuthModule
In your `app.module.ts`:
```ts
import { AuthModule } from './auth/auth.module';
@Module({
  imports: [AuthModule],
})
export class AppModule {}
```

---

## 🧑‍💻 Development Workflow
- **Lint:** `npm run lint`
- **Test:** `npm run test`
- **Build:** `npm run build`
- **Dev:** `npm run dev`

---

## 📝 Usage Notes
- YugabyteDB must be running and accessible at the configured host and port.
- Swagger docs are always at `/docs`.
- All user IDs use UUIDv7 (polyfill).
- Database tables are automatically created using TypeORM synchronize.

---

## 🛠️ Troubleshooting
- **YugabyteDB connection errors:** Check your YugabyteDB configuration and server status.
- **bcrypt not installed:** Run `npm install bcrypt`.
- **Google OAuth issues:** Double-check `.env` values and Google Cloud Console settings.
- **UUIDv7 errors:** Ensure you use the provided polyfill in `src/utils/uuid7.ts`.
- **TypeORM errors:** Check that all entities are properly imported in `app.module.ts`.
