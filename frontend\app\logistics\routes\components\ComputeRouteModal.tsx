"use client";

import React, { useState, useEffect } from "react";
import { Customer } from "../../../sales/customers/customersApi";
import { Route, ComputeRouteDto } from "../api";
import { Route as RouteIcon, X, Save, Loader2 } from "lucide-react";

interface ComputeRouteModalProps {
  isOpen: boolean;
  onClose: () => void;
  selectedCustomers: Customer[];
  onComputeRoute: (data: ComputeRouteDto) => Promise<Route>;
  warehouseUuid: string;
}

const ComputeRouteModal: React.FC<ComputeRouteModalProps> = ({
  isOpen,
  onClose,
  selectedCustomers,
  onComputeRoute,
  warehouseUuid
}) => {
  const [routeName, setRouteName] = useState("");
  const [routeDescription, setRouteDescription] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");

  useEffect(() => {
    if (isOpen) {
      // Auto-generate route name based on selected customers
      if (selectedCustomers.length > 0) {
        const customerNames = selectedCustomers.map(c => c.name).slice(0, 3);
        const name = customerNames.length > 1 
          ? `${customerNames.join(", ")}${selectedCustomers.length > 3 ? "..." : ""} Route`
          : `${customerNames[0]} Route`;
        setRouteName(name);
      }
      
      // Auto-generate description
      const description = `Optimized route for ${selectedCustomers.length} customer${selectedCustomers.length !== 1 ? 's' : ''}`;
      setRouteDescription(description);
    }
  }, [isOpen, selectedCustomers]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!routeName.trim()) {
      setError("Route name is required");
      return;
    }

    if (selectedCustomers.length < 2) {
      setError("At least 2 customers are required to compute a route");
      return;
    }

    setError("");
    setIsLoading(true);

    try {
      const customerLocations = selectedCustomers.map(customer => ({
        latitude: customer.latitude!,
        longitude: customer.longitude!,
        customerName: customer.name
      }));

      const computeData: ComputeRouteDto = {
        name: routeName.trim(),
        description: routeDescription.trim() || undefined,
        customerLocations,
        warehouseUuid
      };

      await onComputeRoute(computeData);
      onClose();
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to compute route");
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Escape") {
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div 
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[10000] p-4"
      style={{ position: 'fixed', zIndex: 10000 }}
      onClick={(e) => {
        if (e.target === e.currentTarget) {
          onClose();
        }
      }}
    >
      <div 
        className="bg-white rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto"
        onKeyDown={handleKeyDown}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
              <RouteIcon className="w-5 h-5 text-blue-600" />
            </div>
            <div>
              <h2 className="text-lg font-semibold text-gray-900">Compute Route</h2>
              <p className="text-sm text-gray-500">
                Create optimized route for {selectedCustomers.length} customer{selectedCustomers.length !== 1 ? 's' : ''}
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 rounded-lg hover:bg-gray-100 transition-colors"
            disabled={isLoading}
          >
            <X className="w-5 h-5 text-gray-500" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Error Message */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-center space-x-2">
                <div className="w-5 h-5 bg-red-100 rounded-full flex items-center justify-center">
                  <X className="w-3 h-3 text-red-600" />
                </div>
                <span className="text-sm text-red-700">{error}</span>
              </div>
            </div>
          )}

          {/* Route Name */}
          <div>
            <label htmlFor="routeName" className="block text-sm font-medium text-gray-700 mb-2">
              Route Name <span className="text-red-500">*</span>
            </label>
            <input
              id="routeName"
              type="text"
              value={routeName}
              onChange={(e) => setRouteName(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter route name"
              required
              disabled={isLoading}
            />
          </div>

          {/* Route Description */}
          <div>
            <label htmlFor="routeDescription" className="block text-sm font-medium text-gray-700 mb-2">
              Description
            </label>
            <textarea
              id="routeDescription"
              value={routeDescription}
              onChange={(e) => setRouteDescription(e.target.value)}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter route description (optional)"
              disabled={isLoading}
            />
          </div>

          {/* Selected Customers Summary */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Selected Customers ({selectedCustomers.length})
            </label>
            <div className="bg-gray-50 rounded-lg p-4 max-h-40 overflow-y-auto">
              {selectedCustomers.length === 0 ? (
                <p className="text-gray-500 text-sm">No customers selected</p>
              ) : (
                <div className="space-y-2">
                  {selectedCustomers.map((customer, index) => (
                    <div key={customer.uuid} className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                          <span className="text-xs font-medium text-blue-600">{index + 1}</span>
                        </div>
                        <div>
                          <p className="text-sm font-medium text-gray-900">{customer.name}</p>
                          <p className="text-xs text-gray-500">{customer.customerType}</p>
                        </div>
                      </div>
                      {customer.latitude && customer.longitude && (
                        <div className="text-xs text-gray-400">
                          {customer.latitude.toFixed(4)}, {customer.longitude.toFixed(4)}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Validation Messages */}
          {selectedCustomers.length < 2 && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <div className="flex items-center space-x-2">
                <div className="w-5 h-5 bg-yellow-100 rounded-full flex items-center justify-center">
                  <span className="text-xs font-medium text-yellow-600">!</span>
                </div>
                <span className="text-sm text-yellow-700">
                  At least 2 customers with coordinates are required to compute a route
                </span>
              </div>
            </div>
          )}

          {/* Buttons */}
          <div className="flex space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors disabled:opacity-50"
              disabled={isLoading}
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isLoading || selectedCustomers.length < 2}
              className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
            >
              {isLoading ? (
                <>
                  <Loader2 className="w-4 h-4 animate-spin" />
                  <span>Computing...</span>
                </>
              ) : (
                <>
                  <Save className="w-4 h-4" />
                  <span>Compute Route</span>
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ComputeRouteModal; 