"use client";
import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { SecurityStatus } from '@/contexts/AuthContext';

interface SecurityStatusProps {
  showDetails?: boolean;
  className?: string;
}

export default function SecurityStatusComponent({ showDetails = false, className = '' }: SecurityStatusProps) {
  const { getSecurityStatus } = useAuth();
  const [securityStatus, setSecurityStatus] = useState<SecurityStatus | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchSecurityStatus = async () => {
    setLoading(true);
    setError(null);
    try {
      const status = await getSecurityStatus();
      setSecurityStatus(status);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load security status');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchSecurityStatus();
  }, []);

  if (loading) {
    return (
      <div className={`flex items-center space-x-2 text-sm text-gray-600 ${className}`}>
        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
        <span>Checking security status...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`text-sm text-red-600 ${className}`}>
        <button 
          onClick={fetchSecurityStatus}
          className="underline hover:no-underline"
        >
          Security status unavailable. Click to retry.
        </button>
      </div>
    );
  }

  if (!securityStatus) {
    return null;
  }

  // If account is locked, show prominent warning
  if (securityStatus.isLocked) {
    return (
      <div className={`bg-red-50 border border-red-200 rounded-lg p-3 ${className}`}>
        <div className="flex items-center space-x-2">
          <svg className="w-5 h-5 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 15.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
          <div>
            <p className="text-sm font-medium text-red-800">
              Account temporarily locked
            </p>
            {securityStatus.lockedUntil && (
              <p className="text-xs text-red-600">
                Unlocks at {new Date(securityStatus.lockedUntil).toLocaleString()}
              </p>
            )}
          </div>
        </div>
      </div>
    );
  }

  // Show security status summary
  return (
    <div className={`space-y-2 ${className}`}>
      {/* Security Status Summary */}
      <div className="flex items-center justify-between text-sm">
        <span className="text-gray-600">Security Status:</span>
        <div className="flex items-center space-x-2">
          {securityStatus.suspiciousActivityDetected && (
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
              <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
              Suspicious Activity
            </span>
          )}
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
            <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
            </svg>
            Secure
          </span>
        </div>
      </div>

      {/* Login Attempts */}
      <div className="flex items-center justify-between text-sm">
        <span className="text-gray-600">Remaining login attempts:</span>
        <span className={`font-medium ${
          securityStatus.remainingAttempts <= 2 ? 'text-red-600' : 
          securityStatus.remainingAttempts <= 3 ? 'text-yellow-600' : 
          'text-green-600'
        }`}>
          {securityStatus.remainingAttempts}
        </span>
      </div>

      {/* Show details if requested */}
      {showDetails && securityStatus.recentEvents.length > 0 && (
        <div className="mt-3 pt-3 border-t border-gray-200">
          <h4 className="text-sm font-medium text-gray-900 mb-2">Recent Security Events</h4>
          <div className="space-y-1">
            {securityStatus.recentEvents.slice(0, 3).map((event, index) => (
              <div key={index} className="flex items-center justify-between text-xs">
                <div className="flex items-center space-x-2">
                  <div className={`w-2 h-2 rounded-full ${
                    event.success ? 'bg-green-500' : 'bg-red-500'
                  }`}></div>
                  <span className="text-gray-600">{event.eventType.replace('_', ' ')}</span>
                </div>
                <span className="text-gray-500">
                  {new Date(event.timestamp).toLocaleDateString()}
                </span>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Refresh button */}
      <button
        onClick={fetchSecurityStatus}
        className="text-xs text-blue-600 hover:text-blue-800 underline"
      >
        Refresh status
      </button>
    </div>
  );
} 