import { Modu<PERSON> } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { UserAccountPlansController } from "./user-account-plans.controller";
import { UserAccountPlansService } from "./user-account-plans.service.typeorm";
import { AccountPlan } from "./account-plan.entity";
import { Feature } from "./feature.entity";

@Module({
  imports: [
    TypeOrmModule.forFeature([AccountPlan, Feature]),
  ],
  controllers: [UserAccountPlansController],
  providers: [UserAccountPlansService],
  exports: [UserAccountPlansService],
})
export class UserAccountPlansModule {}
