import React, { useState, useEffect } from "react";

export interface RegionFormValues {
  name: string;
  description?: string;
  latitude?: number;
  longitude?: number;
}

interface RegionFormProps {
  initialValues: RegionFormValues;
  onSubmit: (values: RegionFormValues) => Promise<void>;
  onCancel: () => void;
  isSubmitting: boolean;
  error?: string | null;
}

const RegionForm: React.FC<RegionFormProps> = ({
  initialValues,
  onSubmit,
  onCancel,
  isSubmitting,
  error,
}) => {
  const [values, setValues] = useState<RegionFormValues>(initialValues);
  const [errors, setErrors] = useState<Partial<RegionFormValues>>({});

  // Reset form when initialValues change (e.g., when switching between add/edit)
  useEffect(() => {
    setValues(initialValues);
    setErrors({});
  }, [initialValues]);

  const validateForm = (): boolean => {
    const newErrors: Partial<RegionFormValues> = {};

    if (!values.name?.trim()) {
      newErrors.name = "Name is required";
    }

    // Validate latitude if provided
    if (values.latitude !== undefined && values.latitude !== null) {
      const lat = Number(values.latitude);
      if (isNaN(lat) || lat < -90 || lat > 90) {
        newErrors.latitude = "Latitude must be between -90 and 90";
      }
    }

    // Validate longitude if provided
    if (values.longitude !== undefined && values.longitude !== null) {
      const lng = Number(values.longitude);
      if (isNaN(lng) || lng < -180 || lng > 180) {
        newErrors.longitude = "Longitude must be between -180 and 180";
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!validateForm()) return;

    // Convert coordinate strings to numbers or undefined
    const submitValues: RegionFormValues = {
      name: values.name.trim(),
      description: values.description?.trim() || undefined,
      latitude: values.latitude !== null && values.latitude !== undefined && values.latitude !== "" 
        ? Number(values.latitude) 
        : undefined,
      longitude: values.longitude !== null && values.longitude !== undefined && values.longitude !== "" 
        ? Number(values.longitude) 
        : undefined,
    };

    await onSubmit(submitValues);
  };

  const handleInputChange = (field: keyof RegionFormValues, value: string | number) => {
    setValues(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-xl p-6 w-full max-w-md mx-auto">
      <h2 className="text-xl font-bold mb-2 text-gray-800">
        {initialValues.name ? "Edit Region" : "Add Region"}
      </h2>
      <p className="text-gray-600 mb-4">
        {initialValues.name ? "Update region information." : "Create a new geographical region."}
      </p>

      {error && (
        <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
          <p className="text-red-700 text-sm">{error}</p>
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-4">
        {/* Name Field */}
        <div>
          <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
            Name <span className="text-red-500">*</span>
          </label>
          <input
            id="name"
            type="text"
            value={values.name}
            onChange={(e) => handleInputChange("name", e.target.value)}
            placeholder="Enter region name"
            className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              errors.name ? "border-red-500" : "border-gray-300"
            }`}
            required
            autoFocus
          />
          {errors.name && (
            <p className="mt-1 text-sm text-red-600 flex items-center">
              <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
              {errors.name}
            </p>
          )}
        </div>

        {/* Description Field */}
        <div>
          <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
            Description
          </label>
          <textarea
            id="description"
            value={values.description || ""}
            onChange={(e) => handleInputChange("description", e.target.value)}
            placeholder="Enter region description (optional)"
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        {/* Latitude Field */}
        <div>
          <label htmlFor="latitude" className="block text-sm font-medium text-gray-700 mb-1">
            Latitude
          </label>
          <input
            id="latitude"
            type="number"
            step="any"
            value={values.latitude !== undefined ? values.latitude : ""}
            onChange={(e) => handleInputChange("latitude", e.target.value === "" ? "" : Number(e.target.value))}
            placeholder="Enter latitude (-90 to 90)"
            className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              errors.latitude ? "border-red-500" : "border-gray-300"
            }`}
          />
          {errors.latitude && (
            <p className="mt-1 text-sm text-red-600 flex items-center">
              <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
              {errors.latitude}
            </p>
          )}
        </div>

        {/* Longitude Field */}
        <div>
          <label htmlFor="longitude" className="block text-sm font-medium text-gray-700 mb-1">
            Longitude
          </label>
          <input
            id="longitude"
            type="number"
            step="any"
            value={values.longitude !== undefined ? values.longitude : ""}
            onChange={(e) => handleInputChange("longitude", e.target.value === "" ? "" : Number(e.target.value))}
            placeholder="Enter longitude (-180 to 180)"
            className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              errors.longitude ? "border-red-500" : "border-gray-300"
            }`}
          />
          {errors.longitude && (
            <p className="mt-1 text-sm text-red-600 flex items-center">
              <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
              {errors.longitude}
            </p>
          )}
        </div>

        {/* Action Buttons */}
        <div className="space-y-3 pt-4">
          <button
            type="submit"
            disabled={isSubmitting}
            className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
          >
            {isSubmitting ? (
              <>
                <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                {initialValues.name ? "Updating..." : "Creating..."}
              </>
            ) : (
              initialValues.name ? "Update Region" : "Create Region"
            )}
          </button>
          <button
            type="button"
            onClick={onCancel}
            className="w-full bg-gray-300 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500"
          >
            Cancel
          </button>
        </div>
      </form>
    </div>
  );
};

export default RegionForm; 