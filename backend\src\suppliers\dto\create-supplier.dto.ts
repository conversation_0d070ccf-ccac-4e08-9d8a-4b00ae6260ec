import { ApiProperty } from "@nestjs/swagger";
import { IsString, IsOptional, IsNumber, IsUUID } from "class-validator";

export class CreateSupplierDto {
  @ApiProperty({
    example: "Acme Supplies Ltd.",
    description: "Supplier name (required)",
  })
  @IsString()
  name: string;

  @ApiProperty({
    example: "12345678901",
    description: "Fiscal identification number (tax ID)",
    required: false,
  })
  @IsOptional()
  @IsString()
  fiscalId?: string;

  @ApiProperty({
    example: "RC123456789",
    description: "Registration Certificate number",
    required: false,
  })
  @IsOptional()
  @IsString()
  rc?: string;

  @ApiProperty({
    example: "ART-001-2024",
    description: "Article Number assigned by the supplier",
    required: false,
  })
  @IsOptional()
  @IsString()
  articleNumber?: string;

  @ApiProperty({ example: "<EMAIL>", required: false })
  @IsOptional()
  @IsString()
  email?: string;

  @ApiProperty({ example: "+1234567890", required: false })
  @IsOptional()
  @IsString()
  phone?: string;

  @ApiProperty({ example: "123 Main St, City, Country", required: false })
  @IsOptional()
  @IsString()
  address?: string;

  @ApiProperty({
    example: "SUP-001",
    description: "Supplier code",
    required: false,
  })
  @IsOptional()
  @IsString()
  code?: string;

  @ApiProperty({
    example: "Premium supplier for electronics",
    description: "Supplier description",
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    example: 40.7128,
    description: "Latitude coordinate for supplier location",
    required: false,
  })
  @IsOptional()
  @IsNumber()
  latitude?: number;

  @ApiProperty({
    example: -74.006,
    description: "Longitude coordinate for supplier location",
    required: false,
  })
  @IsOptional()
  @IsNumber()
  longitude?: number;

  @ApiProperty({ example: "Supplier notes or remarks.", required: false })
  @IsOptional()
  @IsString()
  notes?: string;

  @ApiProperty({
    example: "018e6b7c-9c2a-7b73-bb6d-0d1e8e2f4b1a",
    description: "UUIDv7 string of the warehouseUuid",
    required: false,
  })
  @IsOptional()
  @IsUUID("all")
  warehouseUuid?: string;

  @ApiProperty({
    example: "018e6b7c-9c2a-7b73-bb6d-0d1e8e2f4b1a",
    description: "UUIDv7 string of the user who created/owns this supplier",
  })
  @IsUUID("all")
  userUuid: string;
}
