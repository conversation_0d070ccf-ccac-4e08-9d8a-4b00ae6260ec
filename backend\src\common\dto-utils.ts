/**
 * Common DTO utilities for consistent mapping across entities
 */

/**
 * Maps entity ID to UUID field for frontend compatibility
 * @param entity - The entity object
 * @returns The UUID value (either entity.uuid or entity.id)
 */
export function mapEntityIdToUuid(entity: any): string {
  return entity.uuid || entity.id;
}

/**
 * Maps entity UUID fields to string format for frontend compatibility
 * @param entity - The entity object
 * @param fieldName - The base field name (e.g., 'warehouseUuid')
 * @returns The UUID string value
 */
export function mapUuidFieldToString(entity: any, fieldName: string): string | undefined {
  const stringField = `${fieldName}String`;
  return entity[stringField] ?? entity[fieldName] ?? undefined;
}

/**
 * Creates a standardized UUID mapping for common entity fields
 * @param entity - The entity object
 * @returns Object with mapped UUID fields
 */
export function mapCommonUuidFields(entity: any) {
  return {
    uuid: mapEntityIdToUuid(entity),
    warehouseUuidString: mapUuidFieldToString(entity, 'warehouseUuid'),
    vanUuidString: mapUuidFieldToString(entity, 'vanUuid'),
    roleUuidString: mapUuidFieldToString(entity, 'roleUuid'),
    userUuidString: mapUuidFieldToString(entity, 'userUuid'),
    mainStorageUuidString: mapUuidFieldToString(entity, 'mainStorageUuid'),
  };
} 