import {
  Injectable,
  NotFoundException,
  BadRequestException,
  Inject,
  ForbiddenException,
} from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";
import { Role } from "./role.entity";
import { User } from "./user.entity";
import { RoleDto, toRoleDto, toRoleDtoArray } from "./dto/role.dto";
import { UpdateRoleDto } from "./dto/update-role.dto";
import { Permission } from "./user_permissions";
import { MOBILE_SALE_AGENT } from "../users/default_roles";

@Injectable()
export class UserRolesService {
  constructor(
    @InjectRepository(Role)
    private roleRepository: Repository<Role>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
  ) {}

  /**
   * Finds a role by name and warehouseUuid (UUIDv7 string). Returns the Role entity or null.
   */
  async findRoleByNameAndWarehouseUuid(name: string, warehouseUuid: string) {
    return this.roleRepository.findOne({
      where: {
        name,
        warehouseUuid,
        isDeleted: false,
      },
    });
  }

  async createRole(
    name: string,
    permissions: string[] | undefined,
    warehouseUuid: string,
  ): Promise<RoleDto> {
    if (!warehouseUuid) {
      throw new BadRequestException("warehouseUuid is required");
    }
    const existingRole = await this.roleRepository.findOne({
      where: {
        name,
        warehouseUuid,
      },
    });
    if (existingRole) {
      throw new BadRequestException(
        "Role with this name already exists for this warehouse.",
      );
    }
    const newRole = this.roleRepository.create({
      id: Role.generateId(),
      name,
      permissions: permissions ?? [],
      warehouseUuid,
      isDeleted: false,
    });
    const saved = await this.roleRepository.save(newRole);
    return toRoleDto(saved);
  }

  async getRoleByUuid(uuid: string): Promise<RoleDto> {
    const role = await this.roleRepository.findOne({
      where: { id: uuid, isDeleted: false },
    });
    if (!role) {
      throw new NotFoundException("Role not found");
    }
    return toRoleDto(role);
  }

  async listRolesByWarehouse(warehouseUuid: string): Promise<RoleDto[]> {
    const roles = await this.roleRepository.find({
      where: { warehouseUuid, isDeleted: false },
    });
    return toRoleDtoArray(roles);
  }

  async listRoles(): Promise<RoleDto[]> {
    const roles = await this.roleRepository.find({
      where: { isDeleted: false },
    });
    return toRoleDtoArray(roles);
  }

  async listRolesRaw(): Promise<RoleDto[]> {
    const roles = await this.roleRepository.find();
    return toRoleDtoArray(roles);
  }

  async updateRoleByUuid(uuid: string, body: UpdateRoleDto): Promise<RoleDto> {
    const role = await this.roleRepository.findOne({
      where: { id: uuid, isDeleted: false },
    });
    if (!role) {
      throw new NotFoundException("Role not found");
    }
    Object.assign(role, body);
    const saved = await this.roleRepository.save(role);
    return toRoleDto(saved);
  }

  async deleteRoleByUuid(uuid: string): Promise<{ success: boolean }> {
    const role = await this.roleRepository.findOne({
      where: { id: uuid, isDeleted: false },
    });
    if (!role) {
      throw new NotFoundException("Role not found or already deleted");
    }
    role.isDeleted = true;
    await this.roleRepository.save(role);
    return { success: true };
  }

  async assignRoleToUser(
    uuid: string,
    roleUuid: string,
    assignerUuid: string,
  ): Promise<{ success: boolean }> {
    // Validate and fetch the assigner
    const assigner = await this.userRepository.findOne({
      where: { id: assignerUuid, isDeleted: false },
    });
    if (!assigner) throw new NotFoundException("Assigner user not found");
    
    // Fetch assigner's role and permissions
    if (!assigner.roleUuid) {
      throw new ForbiddenException("Assigner does not have a role assigned");
    }
    const assignerRole = await this.roleRepository.findOne({
      where: { id: assigner.roleUuid, isDeleted: false },
    });
    if (!assignerRole) {
      throw new ForbiddenException("Assigner role not found");
    }
    
    // Only allow users with the actual 'admin' role to assign roles
    const adminRoleForAssigner = await this.findRoleByNameAndWarehouseUuid(
      "admin",
      assigner.warehouseUuid || "",
    );
    if (!adminRoleForAssigner) {
      throw new NotFoundException(
        "Admin role not found for assigner's warehouse",
      );
    }
    const isAssignerAdminStrict =
      assigner.roleUuid === adminRoleForAssigner.id;
    if (!isAssignerAdminStrict) {
      throw new ForbiddenException(
        "Only users with the admin role can assign roles",
      );
    }

    // Proceed with assignment
    const user = await this.userRepository.findOne({
      where: { id: uuid, isDeleted: false },
    });
    if (!user) throw new NotFoundException("User not found");

    // --- MOBILE_SALE_AGENT GUARD: Target user cannot have this role, nor can this role be assigned ---
    // Find warehouseUuid for the target user
    const warehouseUuidString = user.warehouseUuid;
    if (!warehouseUuidString) {
      throw new BadRequestException("Target user has no warehouseUuid");
    }
    
    // Find admin role for this warehouse
    const adminRole = await this.findRoleByNameAndWarehouseUuid(
      "admin",
      warehouseUuidString,
    );
    if (!adminRole) {
      throw new NotFoundException("Admin role not found for this warehouse");
    }
    
    // Get MOBILE_SALE_AGENT role for this warehouse
    const mobileSaleAgentRole = await this.findRoleByNameAndWarehouseUuid(
      MOBILE_SALE_AGENT,
      warehouseUuidString,
    );

    // Prevent changing FROM MOBILE_SALE_AGENT
    if (
      mobileSaleAgentRole &&
      user.roleUuid === mobileSaleAgentRole.id
    ) {
      throw new ForbiddenException(
        "Cannot change the role of a MOBILE_SALE_AGENT user",
      );
    }
    
    // Prevent changing TO MOBILE_SALE_AGENT
    if (
      mobileSaleAgentRole &&
      roleUuid === mobileSaleAgentRole.id
    ) {
      throw new ForbiddenException(
        "Assignment of the MOBILE_SALE_AGENT role is forbidden",
      );
    }
    // --- END MOBILE_SALE_AGENT GUARD ---

    // --- ADMIN-TO-ADMIN ROLE CHANGE GUARD ---
    // Compare assigner.roleUuid and user.roleUuid to adminRole.id
    const isAssignerAdmin = assigner.roleUuid === adminRole.id;
    const isTargetAdmin = user.roleUuid === adminRole.id;
    if (isAssignerAdmin && isTargetAdmin) {
      throw new ForbiddenException(
        "Admins cannot change the role of other admin users",
      );
    }
    // --- END GUARD ---

    const roleDoc = await this.roleRepository.findOne({
      where: { id: roleUuid, isDeleted: false },
    });
    if (!roleDoc)
      throw new NotFoundException(`Role with UUID "${roleUuid}" not found`);

    // Forbid assignment of the admin role to any user
    if (roleUuid === adminRole.id) {
      throw new ForbiddenException(
        "Assignment of the admin role to any user is forbidden",
      );
    }

    user.roleUuid = roleUuid;
    await this.userRepository.save(user);
    return { success: true };
  }
}
