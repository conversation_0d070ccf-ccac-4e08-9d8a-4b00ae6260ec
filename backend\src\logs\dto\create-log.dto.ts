import { IsString, IsUUID } from "class-validator";
import { ApiProperty } from "@nestjs/swagger";

export class CreateLogDto {
  @ApiProperty({
    description: "UUID of the user who performed the action",
    example: "01890b6e-7b8c-7e6b-8e2e-7b8c7e6b8e2e",
  })
  @IsString()
  @IsUUID('all', { message: "userUuid must be a valid UUID" })
  userUuid: string;

  @ApiProperty({
    description: "The operation performed (e.g., 'created', 'updated', 'deleted', 'cancelled')",
    example: "deleted",
  })
  @IsString()
  operation: string;

  @ApiProperty({
    description: "The entity that was affected (e.g., 'sale123', 'product456', 'customer789')",
    example: "sale123",
  })
  @IsString()
  entity: string;

  @ApiProperty({
    description: "Human-readable description of the action performed",
    example: "User <PERSON> deleted sale order #12345",
    required: false,
  })
  @IsString()
  description?: string;

  @ApiProperty({
    description: "Additional JSON data related to the action",
    example: { "orderId": "12345", "amount": 150.00, "customerName": "John Doe" },
    required: false,
  })
  data?: Record<string, any>;
} 