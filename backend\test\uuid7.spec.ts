import { Uuid7 } from "../src/utils/uuid7";

describe("Uuid7", () => {
  it("should roundtrip buffer -> string -> buffer", () => {
    const uuid = Uuid7.generate();
    const buffer = Buffer.from(uuid.toString().replace(/-/g, ""), "hex");
    const string = uuid.toString();
    const uuidFromString = Uuid7.fromString(string);
    const buffer2 = Buffer.from(uuidFromString.toString().replace(/-/g, ""), "hex");
    expect(buffer2.equals(buffer)).toBe(true);
  });

  it("should roundtrip string -> buffer -> string", () => {
    const uuid = Uuid7.generate();
    const string = uuid.toString();
    const uuidFromString = Uuid7.fromString(string);
    const string2 = uuidFromString.toString();
    expect(string2).toBe(string);
  });

  it("should roundtrip buffer -> string -> buffer (using constructor)", () => {
    const uuid = Uuid7.generate();
    const buffer = Buffer.from(uuid.toString().replace(/-/g, ""), "hex");
    const string = uuid.toString();
    const uuidFromString = new Uuid7(string);
    const buffer2 = Buffer.from(uuidFromString.toString().replace(/-/g, ""), "hex");
    expect(buffer2.equals(buffer)).toBe(true);
  });

  it("should throw if buffer is not 16 bytes", () => {
    expect(() => new Uuid7(Buffer.alloc(15))).toThrow();
    expect(() => new Uuid7(Buffer.alloc(17))).toThrow();
  });

  it("should throw if string is invalid", () => {
    expect(() => Uuid7.fromString("not-a-uuid")).toThrow();
  });

  it("should check equality", () => {
    const uuid1 = Uuid7.generate();
    const uuid2 = Uuid7.fromString(uuid1.toString());
    expect(uuid1.equals(uuid2)).toBe(true);
    const uuid3 = Uuid7.generate();
    expect(uuid1.equals(uuid3)).toBe(false);
  });

  it("should serialize to JSON as string", () => {
    const uuid = Uuid7.generate();
    expect(JSON.stringify({ id: uuid })).toContain(uuid.toString());
  });
});
