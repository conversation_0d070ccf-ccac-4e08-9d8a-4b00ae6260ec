"use client";
import React, { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/contexts/AuthContext";
import { googleTokenAuth } from "../authApi";
import NetworkStatusChecker from "@/components/NetworkStatusChecker";
import BackendStatusChecker from "@/components/BackendStatusChecker";

export default function AuthCallbackPage() {
  const router = useRouter();
  const { login } = useAuth();
  const [error, setError] = useState<string | null>(null);
  const [isRetrying, setIsRetrying] = useState(false);
  const [timeoutId, setTimeoutId] = useState<NodeJS.Timeout | null>(null);
  const [debugInfo, setDebugInfo] = useState<string[]>([]);
  const [networkStatus, setNetworkStatus] = useState<'checking' | 'good' | 'slow' | 'unstable' | 'offline'>('checking');
  const [backendStatus, setBackendStatus] = useState<'checking' | 'online' | 'offline' | 'error'>('checking');
  const [authStep, setAuthStep] = useState<'initializing' | 'checking_backend' | 'processing_auth' | 'completed' | 'failed'>('initializing');
  const [retryCount, setRetryCount] = useState(0);
  const [showDebugInfo, setShowDebugInfo] = useState(false);

  const MAX_RETRIES = 3;
  const AUTH_TIMEOUT = 30000; // 30 seconds
  const BACKEND_TIMEOUT = 10000; // 10 seconds

  const addDebugInfo = (info: string) => {
    console.log(`[Google Auth Debug] ${info}`);
    setDebugInfo(prev => [...prev, `${new Date().toLocaleTimeString()}: ${info}`]);
  };

  const clearAllTimeouts = () => {
    if (timeoutId) {
      clearTimeout(timeoutId);
      setTimeoutId(null);
    }
  };

  const handleAuth = async (isRetry = false) => {
    if (isRetry) {
      setIsRetrying(true);
      setError(null);
      setDebugInfo([]);
      setAuthStep('initializing');
      setRetryCount(prev => prev + 1);
    }

    addDebugInfo(`Starting Google authentication process... ${isRetry ? `(Retry #${retryCount + 1})` : ''}`);
    setAuthStep('checking_backend');

    // Clear any existing timeouts
    clearAllTimeouts();

    try {
      // Check backend connectivity first
      addDebugInfo("Checking backend connectivity...");
      try {
        const healthCheck = await fetch('/api/auth/health', {
          method: 'GET',
          headers: { 'Content-Type': 'application/json' },
          signal: AbortSignal.timeout(BACKEND_TIMEOUT)
        });
        
        if (!healthCheck.ok) {
          throw new Error(`Backend health check failed: ${healthCheck.status}`);
        }
        
        addDebugInfo("Backend is accessible, proceeding with authentication");
        setAuthStep('processing_auth');
      } catch (healthError) {
        addDebugInfo(`Backend health check failed: ${healthError.message}`);
        throw new Error("Backend server is not accessible. Please ensure:\n\n1. Backend server is running on localhost:8000\n2. No firewall is blocking the connection\n3. Check backend logs for any startup errors");
      }

      // Set main authentication timeout
      const timeout = setTimeout(() => {
        addDebugInfo("Authentication timeout reached (30 seconds)");
        setAuthStep('failed');
        setError("Authentication is taking longer than expected. This could be due to:\n\n1. Slow or unstable internet connection\n2. Backend server not responding\n3. Google OAuth configuration issues\n\nPlease try refreshing the page or check your connection.");
        
        // Auto-retry if we haven't exceeded max retries
        if (retryCount < MAX_RETRIES) {
          addDebugInfo(`Auto-retrying in 5 seconds... (${retryCount + 1}/${MAX_RETRIES})`);
          setTimeout(() => {
            handleAuth(true);
          }, 5000);
        } else {
          addDebugInfo("Max retries reached, redirecting to auth page");
          setTimeout(() => {
            router.replace("/auth?error=callback_failed");
          }, 3000);
        }
      }, AUTH_TIMEOUT);

      setTimeoutId(timeout);

      // Try to extract token and user from query params first
      const params = new URLSearchParams(window.location.search);
      addDebugInfo(`URL parameters found: ${Array.from(params.keys()).join(', ')}`);

      if (params.has("token") && params.has("user")) {
        addDebugInfo("Found token and user in URL parameters");
        const token = params.get("token")!;
        const userData = JSON.parse(decodeURIComponent(params.get("user")!));
        
        // Clear timeout since we got the data
        clearAllTimeouts();
        
        addDebugInfo("Successfully parsed user data from URL");
        console.log("Received user data:", userData);
        
        // Handle both legacy and enhanced response formats
        if (userData.accessToken && userData.refreshToken) {
          addDebugInfo("Using enhanced response format");
          login(userData.user, userData.accessToken, userData.refreshToken);
        } else {
          addDebugInfo("Using legacy response format");
          // Legacy response format - convert to new format
          const legacyUser = {
            uuid: userData.uuid,
            email: userData.email,
            firstName: userData.name ? userData.name.split(' ')[0] : null,
            lastName: userData.name ? userData.name.split(' ').slice(1).join(' ') : null,
            phone: null,
            isActive: !userData.isDeleted,
            roles: [], // Will be fetched later if needed
            warehouseUuid: userData.warehouseUuidString || userData.warehouseUuid || null,
            warehouseUuidString: userData.warehouseUuidString || userData.warehouseUuid || null, // Ensure both fields are set
            vanUuid: userData.vanUuidString || userData.vanUuid || null,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            // Legacy support fields
            name: userData.name,
            userType: userData.userType,
            warehouseName: null, // Will be fetched later
          };
          
          console.log("Converted user data:", legacyUser);
          login(legacyUser, token, ''); // No refresh token in legacy format
        }
        
        addDebugInfo("Login successful, redirecting to dashboard");
        setAuthStep('completed');
        router.replace("/dashboard");
        return;
      }

      // If no query params, try to fetch from backend callback endpoint
      addDebugInfo("No URL parameters found, trying backend callback endpoint");
      
      const controller = new AbortController();
      const fetchTimeout = setTimeout(() => {
        addDebugInfo("Backend fetch timeout reached");
        controller.abort();
      }, BACKEND_TIMEOUT);

      let res;
      try {
        res = await fetch("/api/auth/google/callback", {
          credentials: "include",
          signal: controller.signal,
        });
      } catch (fetchError) {
        addDebugInfo(`Primary callback endpoint failed: ${fetchError.message}`);
        addDebugInfo("Trying JSON fallback endpoint...");
        
        // Try the JSON fallback endpoint
        try {
          res = await fetch("/api/auth/google/callback/json", {
            credentials: "include",
            signal: controller.signal,
          });
          addDebugInfo("JSON fallback endpoint response received");
        } catch (fallbackError) {
          addDebugInfo(`JSON fallback endpoint also failed: ${fallbackError.message}`);
          throw fetchError; // Throw the original error
        }
      }

      clearTimeout(fetchTimeout);
      clearAllTimeouts();

      addDebugInfo(`Backend response status: ${res.status} ${res.statusText}`);

      if (res.ok) {
        const data = await res.json();
        addDebugInfo("Successfully received data from backend");
        if (data.user && data.accessToken) {
          // Handle enhanced response format
          if (data.refreshToken) {
            addDebugInfo("Using enhanced response format from backend");
            login(data.user, data.accessToken, data.refreshToken);
          } else {
            addDebugInfo("Using legacy response format from backend");
            // Legacy format
            login(data.user, data.accessToken, '');
          }
          addDebugInfo("Login successful, redirecting to dashboard");
          setAuthStep('completed');
          router.replace("/dashboard");
          return;
        } else {
          addDebugInfo("Backend response missing user or accessToken");
          throw new Error("Invalid response from backend: missing user or access token");
        }
      } else {
        addDebugInfo(`Backend returned error status: ${res.status}`);
        const errorText = await res.text();
        addDebugInfo(`Backend error response: ${errorText}`);
        throw new Error(`Backend authentication failed: ${res.status} ${res.statusText}`);
      }

    } catch (e) {
      console.error("Authentication error:", e);
      
      // Clear any existing timeout
      clearAllTimeouts();
      
      const errorMessage = e instanceof Error ? e.message : "Authentication failed. Please try again.";
      addDebugInfo(`Authentication failed: ${errorMessage}`);
      setError(errorMessage);
      setAuthStep('failed');
      
      // Auto-retry if we haven't exceeded max retries
      if (retryCount < MAX_RETRIES) {
        addDebugInfo(`Auto-retrying in 5 seconds... (${retryCount + 1}/${MAX_RETRIES})`);
        setTimeout(() => {
          handleAuth(true);
        }, 5000);
      } else {
        addDebugInfo("Max retries reached, redirecting to auth page");
        setTimeout(() => {
          router.replace("/auth?error=callback_failed");
        }, 3000);
      }
    } finally {
      setIsRetrying(false);
    }
  };

  useEffect(() => {
    addDebugInfo("Callback page mounted");
    handleAuth();
    
    // Global safety timeout - if nothing happens within 20 seconds, redirect to auth page
    const safetyTimeout = setTimeout(() => {
      addDebugInfo("Global safety timeout reached - redirecting to auth page");
      router.replace("/auth?error=timeout");
    }, 20000); // 20 seconds
    
    // Cleanup timeout on unmount
    return () => {
      clearAllTimeouts();
      clearTimeout(safetyTimeout);
    };
  }, []);

  const handleRetry = () => {
    clearAllTimeouts();
    handleAuth(true);
  };

  const handleCancel = () => {
    clearAllTimeouts();
    addDebugInfo("User cancelled authentication");
    router.replace("/auth");
  };

  const handleBackendCheck = async () => {
    addDebugInfo("Checking backend connectivity...");
    try {
      const res = await fetch("/api/auth/health", {
        method: "GET",
        headers: { "Content-Type": "application/json" },
      });
      addDebugInfo(`Backend connectivity check: ${res.status} ${res.statusText}`);
      if (res.ok) {
        const data = await res.json();
        setError(`Backend is accessible (uptime: ${Math.floor(data.uptime)}s). The issue might be with Google OAuth configuration.`);
      } else {
        setError("Backend is accessible but returned an error. Check Google OAuth setup.");
      }
    } catch (err) {
      addDebugInfo(`Backend connectivity check failed: ${err.message}`);
      setError("Cannot connect to backend server. Please ensure the backend is running on localhost:8000");
    }
  };

  const getStepMessage = () => {
    switch (authStep) {
      case 'initializing':
        return 'Initializing authentication process...';
      case 'checking_backend':
        return 'Checking backend server connectivity...';
      case 'processing_auth':
        return 'Processing Google authentication...';
      case 'completed':
        return 'Authentication completed successfully!';
      case 'failed':
        return 'Authentication failed. Please try again.';
      default:
        return 'Processing authentication...';
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="bg-white rounded-lg shadow-lg p-8 max-w-2xl w-full text-center">
        {error ? (
          <>
            <div className="mb-4">
              <svg className="w-16 h-16 text-red-500 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 15.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
              <h2 className="text-xl font-semibold text-gray-900 mb-2">Authentication Error</h2>
              <p className="text-gray-600 mb-6 whitespace-pre-line text-left">{error}</p>
              
              {/* Retry status */}
              {retryCount > 0 && retryCount < MAX_RETRIES && (
                <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                  <p className="text-sm text-blue-800">
                    Retry #{retryCount} of {MAX_RETRIES} - Auto-retrying in 5 seconds...
                  </p>
                </div>
              )}
            </div>
            
            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              <button
                onClick={handleRetry}
                disabled={isRetrying}
                className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isRetrying ? "Retrying..." : "Try Again"}
              </button>
              <button
                onClick={handleBackendCheck}
                className="px-6 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
              >
                Check Backend
              </button>
              <button
                onClick={handleCancel}
                className="px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700"
              >
                Cancel
              </button>
            </div>
          </>
        ) : (
          <>
            <div className="mb-4">
              <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <h2 className="text-xl font-semibold text-gray-900 mb-2">Signing you in with Google...</h2>
              <p className="text-gray-600 mb-4">{getStepMessage()}</p>
              <p className="text-sm text-gray-500 mb-4">This process may take up to 30 seconds for unstable connections.</p>
              <p className="text-xs text-gray-400 mb-4">If your internet is slow, this may take longer. Please be patient.</p>
              
              {/* Retry status */}
              {retryCount > 0 && retryCount < MAX_RETRIES && (
                <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                  <p className="text-sm text-blue-800">
                    Retry #{retryCount} of {MAX_RETRIES} - Auto-retrying in 5 seconds...
                  </p>
                </div>
              )}
              
              {/* Status Indicators */}
              <div className="mt-4 space-y-2">
                <div className="p-3 rounded-lg text-sm bg-gray-50">
                  <BackendStatusChecker 
                    onStatusChange={setBackendStatus}
                    showDetails={true}
                  />
                </div>
                <div className="p-3 rounded-lg text-sm bg-gray-50">
                  <NetworkStatusChecker 
                    onStatusChange={setNetworkStatus}
                    showDetails={true}
                  />
                </div>
              </div>
            </div>
            
            <button
              onClick={handleCancel}
              className="px-6 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
            >
              Cancel
            </button>
          </>
        )}
        
        {/* Debug Information */}
        <div className="mt-6 text-left">
          <button
            onClick={() => setShowDebugInfo(!showDebugInfo)}
            className="flex items-center text-sm text-gray-500 hover:text-gray-700"
          >
            <svg className={`w-4 h-4 mr-1 transition-transform ${showDebugInfo ? 'rotate-90' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
            Debug Information ({debugInfo.length} entries)
          </button>
          
          {showDebugInfo && (
            <div className="mt-2 p-3 bg-gray-100 rounded text-xs font-mono max-h-40 overflow-y-auto">
              {debugInfo.map((info, index) => (
                <div key={index} className="mb-1">{info}</div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
