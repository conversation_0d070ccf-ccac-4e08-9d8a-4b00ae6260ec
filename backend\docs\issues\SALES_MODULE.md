# Sales Module Documentation

**Location:** `backend/src/sales/`

## Purpose
Manages sales transactions, orders, quotes, and customer billing with comprehensive financial tracking.

## Structure
```
sales/
├── sales.controller.ts       # Sales CRUD operations
├── sales.service.ts          # Sales business logic
├── sales.schema.ts           # Sales data model
├── sales.module.ts           # Module configuration
├── sales.constants.ts        # Sales constants
├── orders.controller.ts      # Order CRUD operations
├── orders.service.ts         # Order business logic
├── order.schema.ts           # Order data model
├── order.constants.ts        # Order constants
├── quote.controller.ts       # Quote CRUD operations
├── quote.service.ts          # Quote business logic
├── quote.schema.ts           # Quote data model
└── dto/                      # Data transfer objects
```

## Key Features
- Sales transaction management
- Order processing and tracking
- Quote management and conversion
- Payment tracking and status management
- Tax calculation and management
- Customer billing and invoicing
- Financial reporting capabilities
- Multi-payment method support

## Endpoints

### Sales Management
- `POST /sales` - Create sale
- `GET /sales` - List sales (with filtering and pagination)
- `GET /sales/:uuid` - Get sale by UUID
- `PUT /sales/:uuid` - Update sale
- `DELETE /sales/:uuid` - Soft delete sale

### Orders Management
- `POST /orders` - Create order
- `GET /orders` - List orders (with filtering and pagination)
- `GET /orders/:uuid` - Get order by UUID
- `PUT /orders/:uuid` - Update order
- `DELETE /orders/:uuid` - Soft delete order
- `GET /orders/warehouse/:warehouseUuid` - List orders by warehouse

### Quotes Management
- `POST /quotes` - Create quote
- `GET /quotes` - List quotes (with filtering and pagination)
- `GET /quotes/:uuid` - Get quote by UUID
- `PUT /quotes/:uuid` - Update quote
- `DELETE /quotes/:uuid` - Soft delete quote

## Schema Fields

### Sale Schema
```typescript
{
  _id: Binary,                    // Primary key (UUIDv7)
  invoiceNumber: string,          // Unique invoice number
  customerUuid: Binary,           // Customer reference (indexed)
  customerName?: string,          // Customer name snapshot
  customerFiscalId?: string,      // Customer fiscal ID snapshot
  customerRc?: string,            // Customer RC snapshot
  customerArticleNumber?: string, // Customer article number snapshot
  orderUuid?: Binary,             // Order reference (indexed)
  itemsSnapshot: SaleItemSnapshot[], // Sale items snapshot
  subtotal: number,               // Subtotal before tax
  useTax: boolean,                // Tax usage flag
  taxRate: number,                // Tax rate (default: 0.1)
  taxAmount: number,              // Total tax amount
  totalAmount: number,            // Total amount after tax
  amountPaid: number,             // Amount already paid
  balanceDue: number,             // Balance due
  paymentMethod: PaymentMethods,  // Payment method
  paymentDate?: Date[],           // Payment dates array
  invoiceDate: Date,              // Invoice date
  dueDate: Date,                  // Due date
  status: SaleStatus,             // Sale status
  createdBy: Binary,              // Creator user reference (indexed)
  updatedBy: Binary,              // Last updater user reference (indexed)
  isDeleted: boolean,             // Soft delete flag
  createdAt: Date,                // Creation timestamp
  updatedAt: Date                 // Update timestamp
}
```

### Order Schema
```typescript
{
  _id: Binary,                    // Primary key (UUIDv7)
  orderNumber: string,            // Unique order number
  customerUuid: Binary,           // Customer reference (indexed)
  warehouseUuid: Binary,          // Warehouse reference (indexed)
  items: OrderItem[],             // Order items
  totalAmount: number,            // Total amount
  status: OrderStatus,            // Order status
  createdBy: Binary,              // Creator user reference (indexed)
  updatedBy: Binary,              // Last updater user reference (indexed)
  isDeleted: boolean,             // Soft delete flag
  createdAt: Date,                // Creation timestamp
  updatedAt: Date                 // Update timestamp
}
```

### Quote Schema
```typescript
{
  _id: Binary,                    // Primary key (UUIDv7)
  quoteNumber: string,            // Unique quote number
  customerUuid: Binary,           // Customer reference (indexed)
  warehouseUuid: Binary,          // Warehouse reference (indexed)
  items: QuoteItem[],             // Quote items
  totalAmount: number,            // Total amount
  validUntil: Date,               // Quote validity date
  status: QuoteStatus,            // Quote status
  createdBy: Binary,              // Creator user reference (indexed)
  updatedBy: Binary,              // Last updater user reference (indexed)
  isDeleted: boolean,             // Soft delete flag
  createdAt: Date,                // Creation timestamp
  updatedAt: Date                 // Update timestamp
}
```

## Enums and Constants

### Sale Status
```typescript
enum SaleStatus {
  PAID = "paid",
  PARTIALLY_PAID = "partially_paid",
  UNPAID = "unpaid",
  CANCELLED = "cancelled"
}
```

### Payment Methods
```typescript
enum PaymentMethods {
  CASH = "cash",
  CREDIT_CARD = "credit_card",
  BANK_TRANSFER = "bank_transfer",
  MOBILE_PAYMENT = "mobile_payment",
  CHEQUE = "cheque",
  OTHER = "other"
}
```

### Order Status
```typescript
enum OrderStatus {
  PENDING = "pending",
  CONFIRMED = "confirmed",
  IN_PROGRESS = "in_progress",
  SHIPPED = "shipped",
  DELIVERED = "delivered",
  CANCELLED = "cancelled"
}
```

### Quote Status
```typescript
enum QuoteStatus {
  DRAFT = "draft",
  SENT = "sent",
  ACCEPTED = "accepted",
  REJECTED = "rejected",
  EXPIRED = "expired"
}
```

## Virtual Properties

### Sale Virtuals
- `uuid` - String representation of _id
- `customerUuidString` - String representation of customerUuid
- `orderUuidString` - String representation of orderUuid
- `createdByString` - String representation of createdBy
- `updatedByString` - String representation of updatedBy

### Order Virtuals
- `uuid` - String representation of _id
- `customerUuidString` - String representation of customerUuid
- `warehouseUuidString` - String representation of warehouseUuid
- `createdByString` - String representation of createdBy
- `updatedByString` - String representation of updatedBy

### Quote Virtuals
- `uuid` - String representation of _id
- `customerUuidString` - String representation of customerUuid
- `warehouseUuidString` - String representation of warehouseUuid
- `createdByString` - String representation of createdBy
- `updatedByString` - String representation of updatedBy

## Business Logic

### Sales Processing
1. **Invoice Generation:** Creates unique invoice numbers
2. **Customer Snapshot:** Stores customer data at time of sale
3. **Item Snapshot:** Stores product data at time of sale
4. **Tax Calculation:** Applies tax based on configuration
5. **Payment Tracking:** Manages partial and full payments
6. **Status Management:** Tracks sale status changes

### Order Management
1. **Order Creation:** Creates orders from quotes or direct input
2. **Status Tracking:** Manages order lifecycle
3. **Warehouse Assignment:** Links orders to specific warehouses
4. **Item Management:** Tracks order items and quantities

### Quote Management
1. **Quote Creation:** Creates quotes for customers
2. **Validity Period:** Manages quote expiration
3. **Conversion:** Converts quotes to orders
4. **Status Tracking:** Manages quote status changes

## Financial Features
- **Multi-tier Pricing:** Supports different customer types
- **Tax Management:** Configurable tax rates and application
- **Payment Tracking:** Multiple payment methods and dates
- **Balance Management:** Tracks outstanding balances
- **Financial Reporting:** Comprehensive financial data

## Dependencies
- `@nestjs/mongoose` - MongoDB integration
- `../utils/uuid7` - UUID generation and conversion
- `../customers` - Customer data
- `../products` - Product data
- `../warehouses` - Warehouse data
- `../users` - User data

## Related Modules
- **Customers Module** - Customer data and management
- **Products Module** - Product data and pricing
- **Warehouses Module** - Warehouse data
- **Users Module** - User data for tracking
- **Inventory Module** - Stock management

## Issues Found

### ✅ Good Practices
1. **Complete Entity Fields**
   - Has `createdBy`, `updatedBy` fields
   - Has proper timestamps
   - Follows entity standards

2. **Comprehensive Schema**
   - Well-structured data models
   - Proper relationships
   - Good virtual implementations

### 🟡 Medium Priority Issues
1. **Complex Schema**
   - Issue: Very complex with many fields
   - Impact: Maintenance complexity

2. **Business Logic Complexity**
   - Issue: Complex business rules
   - Impact: Testing and debugging challenges

## Database Indexes
- `customerUuid` - For customer-specific queries
- `warehouseUuid` - For warehouse-specific queries
- `orderUuid` - For order references
- `createdBy` - For user tracking
- `updatedBy` - For user tracking
- `status` - For status filtering
- `invoiceNumber` - For invoice lookups
- `orderNumber` - For order lookups
- `quoteNumber` - For quote lookups

## Performance Considerations
- Indexed fields for efficient querying
- Pagination for large datasets
- Soft delete for data retention
- Snapshot data for historical accuracy

## Security Considerations
- UUIDv7 for secure identifiers
- Warehouse scoping for data isolation
- User tracking for all operations
- Input validation for all fields
- Financial data protection

## Future Improvements
1. Implement invoice generation
2. Add payment gateway integration
3. Implement email notifications
4. Add sales analytics and reporting
5. Implement discount management
6. Add multi-currency support
7. Implement sales forecasting
8. Add customer credit management 