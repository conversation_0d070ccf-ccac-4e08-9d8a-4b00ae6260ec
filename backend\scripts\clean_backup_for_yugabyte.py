#!/usr/bin/env python3
"""
Clean Backup for YugabyteDB Compatibility (API-Based)

This script removes PostgreSQL-specific configuration parameters that are not
supported by YugabyteDB from backup files using the backend API.

Usage:
    python scripts/clean_backup_for_yugabyte.py [backup-name]
    
    backup-name: Optional specific backup name to clean (default: latest backup)
"""

import sys
import re
import requests
import os
from pathlib import Path
from datetime import datetime
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

BACKEND_DIR = Path(__file__).resolve().parent.parent
ENV_PATH = BACKEND_DIR / ".env"

# API configuration
BACKUP_API_BASE_URL = os.environ.get("BACKUP_API_BASE_URL", "http://localhost:5000")

def validate_environment():
    """Validate that the backup API is accessible."""
    try:
        response = requests.get(f"{BACKUP_API_BASE_URL}/health/", timeout=10)
        if response.status_code == 200:
            health_data = response.json()
            if health_data.get("status") == "healthy":
                print("[OK] Backup API is healthy and accessible")
                return True
            else:
                print(f"[WARNING] Backup API status: {health_data.get('status')}")
                return True
        else:
            print(f"[ERROR] Backup API health check failed: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"[ERROR] Cannot connect to backup API at {BACKUP_API_BASE_URL}: {e}")
        print(f"Please ensure the backup backend is running on {BACKUP_API_BASE_URL}")
        return False

def api_request(method, endpoint, data=None, timeout=30):
    """Make a request to the backup API."""
    url = f"{BACKUP_API_BASE_URL}{endpoint}"
    headers = {"Content-Type": "application/json"}
    
    try:
        if method.upper() == "GET":
            response = requests.get(url, headers=headers, timeout=timeout)
        elif method.upper() == "POST":
            response = requests.post(url, headers=headers, json=data, timeout=timeout)
        elif method.upper() == "DELETE":
            response = requests.delete(url, headers=headers, timeout=timeout)
        else:
            raise ValueError(f"Unsupported HTTP method: {method}")
        
        response.raise_for_status()
        return response.json()
        
    except requests.exceptions.RequestException as e:
        print(f"[ERROR] API request failed: {e}")
        if hasattr(e, 'response') and e.response is not None:
            try:
                error_data = e.response.json()
                print(f"[ERROR] API Error: {error_data}")
            except:
                print(f"[ERROR] HTTP Status: {e.response.status_code}")
        return None

def get_latest_backup():
    """Get the latest backup using the API."""
    print("[INFO] Getting latest backup...")
    
    result = api_request("GET", "/backup/list")
    
    if not result or result.get("status") != "success":
        print(f"[ERROR] Failed to get backup list: {result}")
        return None
    
    backups = result.get("data", [])
    if not backups:
        print("[ERROR] No backups found")
        return None
    
    latest_backup = backups[0]
    backup_name = latest_backup.get("name")
    
    print(f"[INFO] Latest backup: {backup_name}")
    return backup_name

def clean_backup_content(backup_content):
    """
    Clean backup content to remove YugabyteDB-incompatible parameters.
    
    Args:
        backup_content: The raw backup content as string
    
    Returns:
        tuple: (cleaned_content, removed_lines_count)
    """
    # Parameters to remove (YugabyteDB doesn't support these)
    unsupported_params = [
        r'^SET transaction_timeout = .*;',
        r'^SET idle_in_transaction_session_timeout = .*;',
        r'^SET lock_timeout = .*;',
        r'^SET statement_timeout = .*;',
        r'^SET row_security = .*;',
        r'^SET xmloption = .*;',
        r'^SET client_min_messages = .*;',
        r'^SET check_function_bodies = .*;',
        r'^SELECT pg_catalog\.set_config\(\'search_path\', \'\', false\);',
    ]
    
    lines = backup_content.split('\n')
    cleaned_lines = []
    removed_lines = []
    
    for line_num, line in enumerate(lines, 1):
        line_stripped = line.strip()
        
        # Skip empty lines
        if not line_stripped:
            cleaned_lines.append(line)
            continue
        
        # Check if this is an unsupported parameter
        is_unsupported = False
        for pattern in unsupported_params:
            if re.match(pattern, line_stripped):
                removed_lines.append((line_num, line_stripped))
                is_unsupported = True
                break
        
        if not is_unsupported:
            cleaned_lines.append(line)
    
    return '\n'.join(cleaned_lines), len(removed_lines)

def clean_backup_via_api(backup_name):
    """
    Clean a backup using the API.
    
    Args:
        backup_name: Name of the backup to clean
    """
    print(f"[CLEANING] Cleaning backup: {backup_name}")
    
    # Request backup cleaning via API
    data = {"backup_name": backup_name}
    result = api_request("POST", "/backup/clean", data)
    
    if result and result.get("status") == "success":
        cleaned_data = result.get("data", {})
        cleaned_backup_name = cleaned_data.get("cleaned_backup_name")
        removed_lines = cleaned_data.get("removed_lines", 0)
        
        print(f"[SUCCESS] Backup cleaned successfully!")
        print(f"   [CLEANED_BACKUP] {cleaned_backup_name}")
        print(f"   [REMOVED_LINES] {removed_lines} unsupported parameter(s)")
        
        return cleaned_backup_name
    else:
        print(f"[ERROR] Backup cleaning failed: {result}")
        return None

def main():
    """Main function."""
    print("🧹 Clean Backup for YugabyteDB (API-Based)")
    print("=" * 50)
    
    # Validate environment and API connectivity
    if not validate_environment():
        print(f"\n[TROUBLESHOOTING] Troubleshooting tips:")
        print(f"1. Ensure the backup backend is running on {BACKUP_API_BASE_URL}")
        print(f"2. Check if the backup backend is accessible: curl {BACKUP_API_BASE_URL}/health/")
        print(f"3. Verify network connectivity to the backup backend")
        print(f"4. Check backup backend logs for any errors")
        print(f"5. Verify environment variables are set correctly")
        sys.exit(1)
    
    # Get backup name from command line or use latest
    if len(sys.argv) > 1:
        backup_name = sys.argv[1]
        print(f"[INFO] Using specified backup: {backup_name}")
    else:
        backup_name = get_latest_backup()
        if not backup_name:
            print("[ERROR] No backup found to clean")
            sys.exit(1)
    
    # Clean the backup
    cleaned_backup_name = clean_backup_via_api(backup_name)
    
    if cleaned_backup_name:
        print(f"\n🎉 Backup cleaned successfully!")
        print(f"📁 Cleaned backup: {cleaned_backup_name}")
        print(f"\n💡 You can now restore using:")
        print(f"   python scripts/migration_manager_ysqlsh.py restore-from {cleaned_backup_name}")
    else:
        print("❌ Failed to clean backup")
        sys.exit(1)

if __name__ == "__main__":
    main() 