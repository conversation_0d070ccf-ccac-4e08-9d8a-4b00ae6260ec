> ⚠️ **MANDATORY:** If you are interacting with schemas, entity models, or anything involving UUIDs, you MUST read and follow [`docs/UUID_USAGE_GUIDELINES.md`](./UUID_USAGE_GUIDELINES.md) before making any changes. Failure to do so will result in broken APIs, data corruption, and rejected pull requests.

Make sure to always update the documentation when adding new endpoints or modifying existing ones. When adding new response fields (e.g., productSnapshot for inventory items), update both the DTO and documentation accordingly. For example, inventory item endpoints now support filtering by product name and include a product snapshot in the response.

- The products module supports filtering/listing by warehouseUuid via `GET /products/warehouse/{warehouseUuid}`. This endpoint expects a UUIDv7 string and returns only products for the given warehouse, following all DTO/virtual rules per UUID guidelines.

- For each new endpoint (e.g., PATCH /users/{userUuid}/warehouse), ensure:
  - The endpoint is documented in the relevant docs file.
  - Do not write endpoints in this file.
  - A DTO is created for new request bodies.
  - Swagger decorators are used for grouping and documentation.

- **Example:** The `DELETE /products/:uuid (requires only uuid): Soft deletes a product by setting isDeleted: true.` for request validation and Swagger documentation. See `src/products/dto/delete-product.dto.ts` and `docs/PRODUCTS_ENDPOINTS.md` for reference. The delete endpoint does not use warehouseUuid.
Make sure to always fix potential violations of backend and api standards
For each new entity, make sure we can list, create, and delete
When creating new endpoint make sure to make it swagger proof and do DTO



## 📝 Backend API Conventions

- **Foreign Key UUID Pattern:**
  - All foreign key references (e.g., roleUuid in User, warehouseUuid in Role, etc.) must be UUIDv7 strings as documented in [UUID_USAGE_GUIDELINES.md](./UUID_USAGE_GUIDELINES.md#21-foreign-key-uuid-pattern-mandatory).
  - Never use name strings for foreign keys. Always use UUIDv7 strings in both entities and DTOs.

- **Change User Warehouse UUID:**
  - `PATCH /users/{userUuid}/warehouse` allows updating the `warehouseUuid` for a user. Requires a JSON body `{ "warehouseUuid": "uuid-v7-string" }`. See `docs/USERS_ENDPOINTS.md` for details.

- **UUID7 for All Entities:**
  - **You MUST consult [`docs/UUID_USAGE_GUIDELINES.md`](./UUID_USAGE_GUIDELINES.md) before making any changes to schemas or UUID-related logic.**
  - All primary keys and references in the backend use UUID7. The `id` field is mandatory for all entities, stored as PostgreSQL UUID type (string). The `uuid` string is exposed as a property for API output.
  - All endpoints and schemas expect and return UUID7 values for entity references.
  - **Entity Creation:** The backend will automatically generate the UUID7. **Clients should NOT provide the UUID7 as a parameter in the request body.**
  - **Important:** For full details and code patterns for UUID storage, conversion, and querying, see [`docs/UUID_USAGE_GUIDELINES.md`](./UUID_USAGE_GUIDELINES.md). Do not use entity names as references.

- **Swagger Grouping:** All controllers must use the `@ApiTags('...')` decorator from `@nestjs/swagger` to ensure their endpoints are grouped correctly in the Swagger UI. If omitted, endpoints will appear under the default group.

- **UUID Handling (including UUIDv7 for Primary Keys):**
  - For all details on UUID generation, storage, and usage in DTOs/controllers/services, see [`docs/UUID_USAGE_GUIDELINES.md`](./UUID_USAGE_GUIDELINES.md). Do not duplicate this guidance here.

- **DTO Validation (Mandatory Fields):**
  - All required fields in backend Data Transfer Objects (DTOs) are **mandatory** for API requests. For example, when creating a quote, every field marked as required in `CreateQuoteDto` must be present in the request body.
  - If any required field is missing or invalid, the API will respond with a 400 error and a detailed validation message.

- **Soft Delete:** All delete endpoints, except for `DELETE /products/all`, perform soft deletes (set `isDeleted: true`) for traceability. No data is actually removed from the database; instead, records are marked as deleted and excluded from normal queries.
- `DELETE /products/all`: Hard deletes all products by removing them from the database. This action is irreversible. No request body is required. The response will indicate the number of products deleted.

- **Numeric Filter Handling:** When implementing numeric filters (e.g., `minAmount`, `maxAmount`), always handle empty values, NaN, and provide appropriate fallbacks. See [`docs/API_LAYER.md`](./API_LAYER.md#numeric-filter-handling) for implementation patterns and constants usage.


## API Design Guidelines

- **DTOs are mandatory**: All controller endpoints must use DTOs (Data Transfer Objects) for request and response bodies. This is required for proper OpenAPI/Swagger documentation generation.
- Use `class-validator` decorators (e.g., `@IsString`, `@IsUUID`, etc.) and `@nestjs/swagger` decorators (e.g., `@ApiProperty`) in DTOs to ensure validation and API docs coverage.
- **🚨 EVERY property in EVERY DTO _must_ use `@ApiProperty` with an `example` field and a `description`.**
  - This is crucial for correct Swagger/OpenAPI documentation and for onboarding new developers.
  - **Do NOT omit the `example` field!**
  - Example:
    ```typescript
    @ApiProperty({ example: 'Sample Name', description: 'Name of the entity' })
    name: string;
    ```
- Do NOT use raw entity classes or inline types for request/response payloads in controllers.

### UUID-safe Entity DTO Pattern

- **Never expose internal fields (such as id or warehouseUuid) as anything but strings in API responses.**
- **Always map entities to plain DTOs** before returning from any controller endpoint. Use a utility function (e.g., `toEntityDto`) that extracts only the allowed fields and omits any internal fields.
- This pattern ensures API consumers receive only string UUIDs and never raw binary data, and is required for all entity endpoints.
- See [`docs/UUID_USAGE_GUIDELINES.md`](./UUID_USAGE_GUIDELINES.md) for rationale and implementation details.
- Example (generic, do not use entity names):
  ```typescript
  export interface EntityDto {
    uuid: string;
    // ...other allowed fields
  }

  export function toEntityDto(entity: any): EntityDto {
    return {
      uuid: entity.uuid,
      // ...map other allowed fields
    };
  }
  ```
- Apply this pattern for all list, single, create, and update endpoints for any entity.

---

**Note:** All UUID validation and transformation is performed at the application layer. PostgreSQL/YugabyteDB stores UUIDs as type `uuid`, but does not enforce the UUID version. Ensure your application logic enforces UUIDv7 compliance. Also, `@IsUUID('7')` and `ParseUUIDPipe({ version: '7' })` require custom implementations for UUIDv7 support.