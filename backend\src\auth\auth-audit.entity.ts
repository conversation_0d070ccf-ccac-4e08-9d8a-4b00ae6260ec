import { Enti<PERSON>, PrimaryGeneratedColumn, Column, CreateDateColumn, Index } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';

@Entity('auth_audits')
export class AuthAudit {
  @ApiProperty({
    example: 1,
    description: "Auto-generated primary key",
  })
  @PrimaryGeneratedColumn()
  id: number;

  @ApiProperty({
    example: "login",
    description: "Type of authentication event",
  })
  @Column()
  eventType: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "User ID associated with the event",
    required: false,
  })
  @Column('uuid', { nullable: true })
  @Index()
  userId?: string;

  @ApiProperty({
    example: "<EMAIL>",
    description: "User email associated with the event",
    required: false,
  })
  @Column({ nullable: true })
  userEmail?: string;

  @ApiProperty({
    example: "***********",
    description: "IP address of the request",
    required: false,
  })
  @Column({ nullable: true })
  ipAddress?: string;

  @ApiProperty({
    example: "Mozilla/5.0...",
    description: "User agent string",
    required: false,
  })
  @Column({ nullable: true })
  userAgent?: string;

  @ApiProperty({
    example: "2025-01-15T10:30:00.000Z",
    description: "Timestamp of the event",
  })
  @CreateDateColumn()
  timestamp: Date;

  @ApiProperty({
    example: { browser: "Chrome", os: "Windows" },
    description: "Additional event details",
    required: false,
  })
  @Column('jsonb', { nullable: true })
  details?: any;

  @ApiProperty({
    example: true,
    description: "Whether the authentication was successful",
  })
  @Column()
  success: boolean;

  @ApiProperty({
    example: "Invalid credentials",
    description: "Error message if authentication failed",
    required: false,
  })
  @Column({ nullable: true })
  errorMessage?: string;
} 