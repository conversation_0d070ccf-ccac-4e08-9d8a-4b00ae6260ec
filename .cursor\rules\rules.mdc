---
alwaysApply: true
---
 Only modify code directly relevant to the specific request. Avoid changing unrelated functionality.
- Never replace code with placeholders like `// ... rest of the processing ...`. Always include complete code.
- Break problems into smaller steps. Think through each step separately before implementing.
- Always provide a complete PLAN with REASONING based on evidence from code and logs before making changes.
- Explain your OBSERVATIONS clearly, then provide REASONING to identify the exact issue. Add console logs when needed to gather more information.
- Do not create documentation file that explain the code changes.
- Do not try to run the backend or frontend server.
