import { <PERSON>tity, PrimaryColumn, Column, CreateDateColumn, UpdateDateColumn, Index, OneToMany, JoinColumn } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Uuid7 } from '../utils/uuid7';
import { SaleItem } from './sale-item.entity';

export enum SaleStatus {
  PAID = "paid",
  PARTIALLY_PAID = "partially_paid",
  UNPAID = "unpaid",
  CANCELLED = "cancelled",
}

export enum PaymentMethods {
  CASH = "cash",
  CREDIT_CARD = "credit_card",
  BANK_TRANSFER = "bank_transfer",
  MOBILE_PAYMENT = "mobile_payment",
  CHEQUE = "cheque",
  OTHER = "other",
}

@Entity('sales')
export class Sale {
  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the sale (primary key)",
  })
  @PrimaryColumn('uuid')
  id: string;

  @ApiProperty({
    example: "INV-********-001",
    description: "Unique invoice number",
  })
  @Column({ unique: true })
  invoiceNumber: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the customer",
    required: false,
  })
  @Column('uuid', { nullable: true })
  @Index()
  customerUuid?: string;

  @ApiProperty({
    example: "Customer Name",
    description: "Customer name at time of sale",
    required: false,
  })
  @Column({ nullable: true })
  customerName?: string;

  @ApiProperty({
    example: "***********",
    description: "Customer fiscal ID at time of sale",
    required: false,
  })
  @Column({ nullable: true })
  customerFiscalId?: string;

  @ApiProperty({
    example: "RC123456",
    description: "Customer RC at time of sale",
    required: false,
  })
  @Column({ nullable: true })
  customerRc?: string;

  @ApiProperty({
    example: "ART001",
    description: "Customer article number at time of sale",
    required: false,
  })
  @Column({ nullable: true })
  customerArticleNumber?: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the order this sale was created from",
    required: false,
  })
  @Column('uuid', { nullable: true })
  @Index()
  orderUuid?: string;

  @ApiProperty({ example: 150.0, description: "Subtotal amount before tax" })
  @Column('decimal', { precision: 10, scale: 2, default: 0 })
  subtotal: number;

  @ApiProperty({
    example: true,
    description: "Whether tax is applied to this sale",
  })
  @Column({ default: false })
  useTax: boolean;

  @ApiProperty({
    example: 0.1,
    description: "Tax rate as decimal (e.g., 0.1 for 10%)",
  })
  @Column('decimal', { precision: 5, scale: 4, default: 0.1 })
  taxRate: number;

  @ApiProperty({ example: 15.0, description: "Total tax amount" })
  @Column('decimal', { precision: 10, scale: 2, default: 0 })
  taxAmount: number;

  @ApiProperty({ example: 165.0, description: "Total amount including tax" })
  @Column('decimal', { precision: 10, scale: 2 })
  totalAmount: number;

  @ApiProperty({ example: 100.0, description: "Amount paid so far" })
  @Column('decimal', { precision: 10, scale: 2, default: 0 })
  amountPaid: number;

  @ApiProperty({ example: 65.0, description: "Balance due" })
  @Column('decimal', { precision: 10, scale: 2, default: 0 })
  balanceDue: number;

  @ApiProperty({
    example: PaymentMethods.CASH,
    description: "Payment method used",
    enum: PaymentMethods,
  })
  @Column({
    type: 'enum',
    enum: PaymentMethods,
    default: PaymentMethods.CASH,
  })
  paymentMethod: PaymentMethods;

  @ApiProperty({
    example: ["2025-01-15T10:30:00.000Z"],
    description: "Array of payment dates",
    required: false,
  })
  @Column('jsonb', { nullable: true })
  paymentDate?: Date[];

  @ApiProperty({
    example: "2025-01-15T10:30:00.000Z",
    description: "Invoice date",
  })
  @Column()
  invoiceDate: Date;

  @ApiProperty({
    example: "2025-02-14T10:30:00.000Z",
    description: "Due date for payment",
  })
  @Column()
  dueDate: Date;

  @ApiProperty({
    example: SaleStatus.UNPAID,
    description: "Current status of the sale",
    enum: SaleStatus,
  })
  @Column({
    type: 'enum',
    enum: SaleStatus,
    default: SaleStatus.UNPAID,
  })
  status: SaleStatus;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the user who created the sale",
  })
  @Column('uuid')
  @Index()
  createdBy: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the user who last updated the sale",
  })
  @Column('uuid')
  @Index()
  updatedBy: string;

  @ApiProperty({
    example: false,
    description: "Whether the sale has been soft deleted",
  })
  @Column({ default: false })
  isDeleted: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Relationship to SaleItems
  @OneToMany(() => SaleItem, saleItem => saleItem.sale)
  saleItems: SaleItem[];

  // Helper method to generate UUID
  static generateId(): string {
    return new Uuid7().toString();
  }

  // Helper method to generate UUID
  static fromBinary(binary: any): string {
    if (!binary) return null;
    try {
      return new Uuid7(binary).toString();
    } catch {
      return null;
    }
  }
} 