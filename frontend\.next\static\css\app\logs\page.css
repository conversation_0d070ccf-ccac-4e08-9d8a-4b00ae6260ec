/*!*****************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[3]!./components/itemsTable/ItemsTable.fadein.css ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************/
/* Enhanced fade-in animation with bounce effect - reduced transform to prevent scrollbars */
.fade-in {
  opacity: 1;
  animation: fadeInBounce 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards;
}

/* Ensure table is visible after animation */
.fade-in-complete {
  opacity: 1 !important;
  transform: translateY(0) scale(1) !important;
}

/* Smooth transition for loading state changes */
.fade-in.loading {
  opacity: 0.7;
  animation: loadingPulse 2s ease-in-out infinite;
}

/* Enhanced loading skeleton animations with wave effect */
.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Cool bounce fade-in animation - minimal transforms to prevent scrollbars */
@keyframes fadeInBounce {
  0% {
    opacity: 0;
    transform: translateY(5px) scale(0.99);
  }
  60% {
    opacity: 0.8;
    transform: translateY(-1px) scale(1.005);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Ensure table stays visible after animation */
.fade-in {
  animation-fill-mode: both;
}

.fade-in:not(.loading) {
  opacity: 1;
  transform: translateY(0) scale(1);
}

/* Pulsing animation for loading container - removed scale to prevent scrollbars */
@keyframes loadingPulse {
  0%, 100% {
    opacity: 0.7;
  }
  50% {
    opacity: 0.9;
  }
}

/* Smooth transitions for table container - add overflow hidden during animations */
.table-container {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden; /* Prevent scrollbars during animations */
  contain: layout style; /* Isolate layout and style containment */
  position: relative;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
}

.table-container.loading {
  opacity: 0.8;
  animation: containerBreathing 3s ease-in-out infinite;
}

/* Reduced shadow changes to prevent layout shifts */
@keyframes containerBreathing {
  0%, 100% {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  }
  50% {
    box-shadow: 0 12px 18px -3px rgba(0, 0, 0, 0.1), 0 6px 8px -2px rgba(0, 0, 0, 0.05);
  }
}

/* Wave shimmer effect for skeleton rows */
.skeleton-row {
  position: relative;
  overflow: hidden;
  animation: skeletonFloat 2s ease-in-out infinite;
}

.skeleton-row::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.4) 50%,
    transparent 100%
  );
  animation: waveShimmer 2s ease-in-out infinite;
}

@keyframes waveShimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* Reduced float distance to prevent scrollbars */
@keyframes skeletonFloat {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-1px);
  }
}

/* Staggered fade-in for table rows - reduced translateX to prevent horizontal scrollbars */
.table-row {
  opacity: 0;
  animation: staggeredFadeIn 0.6s ease-out forwards;
}

/* Extended staggered animation for more rows */
.table-row:nth-child(1) { animation-delay: 0.05s; }
.table-row:nth-child(2) { animation-delay: 0.1s; }
.table-row:nth-child(3) { animation-delay: 0.15s; }
.table-row:nth-child(4) { animation-delay: 0.2s; }
.table-row:nth-child(5) { animation-delay: 0.25s; }
.table-row:nth-child(6) { animation-delay: 0.3s; }
.table-row:nth-child(7) { animation-delay: 0.35s; }
.table-row:nth-child(8) { animation-delay: 0.4s; }
.table-row:nth-child(9) { animation-delay: 0.45s; }
.table-row:nth-child(10) { animation-delay: 0.5s; }
.table-row:nth-child(11) { animation-delay: 0.55s; }
.table-row:nth-child(12) { animation-delay: 0.6s; }
.table-row:nth-child(13) { animation-delay: 0.65s; }
.table-row:nth-child(14) { animation-delay: 0.7s; }
.table-row:nth-child(15) { animation-delay: 0.75s; }
.table-row:nth-child(16) { animation-delay: 0.8s; }
.table-row:nth-child(17) { animation-delay: 0.85s; }
.table-row:nth-child(18) { animation-delay: 0.9s; }
.table-row:nth-child(19) { animation-delay: 0.95s; }
.table-row:nth-child(20) { animation-delay: 1s; }
.table-row:nth-child(n+21) { animation-delay: 1s; }

/* Reduced translateX to prevent horizontal scrollbars */
@keyframes staggeredFadeIn {
  from {
    opacity: 0;
    transform: translateX(-5px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Table rows - no hover effects */
.table-row {
  transition: none;
  position: relative;
}

@keyframes slideIn {
  from {
    height: 0;
    opacity: 0;
  }
  to {
    height: 100%;
    opacity: 1;
  }
}

/* Smooth hover effects during loading */
.loading tbody tr:hover {
  background-color: transparent !important;
  box-shadow: none !important;
}

/* Reset animation for loading state */
.loading .table-row {
  opacity: 1;
  animation: none;
}

/* Enhanced transitions for all table elements */
tbody tr {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Cool loading state skeleton styling with gradient animation */
.loading .animate-pulse div {
  background: linear-gradient(
    90deg,
    #f0f0f0 0%,
    #e0e0e0 25%,
    #d0d0d0 50%,
    #e0e0e0 75%,
    #f0f0f0 100%
  );
  background-size: 200% 100%;
  animation: gradientWave 2s ease-in-out infinite;
  border-radius: 4px;
  position: relative;
  overflow: hidden;
}

@keyframes gradientWave {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Sparkle effect for skeleton elements */
.loading .animate-pulse div::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.8) 50%,
    transparent 100%
  );
  animation: sparkle 3s ease-in-out infinite;
}

@keyframes sparkle {
  0%, 100% {
    left: -100%;
    opacity: 0;
  }
  50% {
    left: 100%;
    opacity: 1;
  }
}

/* Table header animation - reduced translateY to prevent scrollbars */
.table-header {
  animation: slideDown 0.6s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Action buttons hover animations */
.action-button {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.action-button:hover {
  transform: scale(1.05);
}

.action-button::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.3s ease;
}

.action-button:hover::before {
  width: 100%;
  height: 100%;
}

/* Loading spinner for individual cells */
.cell-loading {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Entrance animation for the entire table - reduced translateY to prevent scrollbars */
.table-entrance {
  animation: tableSlideUp 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

/* Minimal translateY distance to prevent scrollbars */
@keyframes tableSlideUp {
  from {
    opacity: 0;
    transform: translateY(8px) scale(0.99);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Ensure the outer container prevents scrollbars during animations */
.overflow-x-auto {
  position: relative;
  /* Prevent horizontal scrollbar flash after animations */
  scrollbar-width: thin;
}

/* Better control of horizontal overflow */
.overflow-x-auto:not(.animating) {
  overflow-x: auto;
  overflow-y: hidden;
  transition: overflow 0.3s ease-out;
}

/* Smooth transition for overflow changes */
.overflow-x-auto {
  transition: all 0.3s ease-out;
}

/* Completely prevent all scrollbars during animations */
.overflow-x-auto.animating {
  overflow: hidden !important;
  overflow-x: hidden !important;
  overflow-y: hidden !important;
}

/* Also apply to the table container during animations */
.table-container.loading,
.table-container.fade-in {
  overflow: hidden !important;
  contain: layout style;
}

/* Prevent any content from escaping during entrance animation */
.table-entrance {
  overflow: hidden !important;
  contain: layout;
}

/* Ultimate scrollbar prevention during animations */
.animating {
  scrollbar-width: none !important; /* Firefox */
  -ms-overflow-style: none !important; /* IE and Edge */
}

.animating::-webkit-scrollbar {
  display: none !important; /* Chrome, Safari and Opera */
}

/* Force no overflow on animated elements */
.animating * {
  overflow: visible !important;
}

.animating .table-container,
.animating .overflow-x-auto {
  overflow: hidden !important;
  max-width: 100% !important;
  max-height: 100% !important;
}

