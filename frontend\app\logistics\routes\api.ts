// routesApi.ts - API utilities for Routes endpoints
import axios from 'axios';
import { getAxiosAuthHeaders } from '@/utils/authHeaders';

const API_BASE = '/api';

// Route interfaces matching backend DTOs
export interface CustomerLocation {
  latitude: number;
  longitude: number;
  customerName?: string;
}

export interface RouteLocation extends CustomerLocation {}

export interface OptimizedRouteLocation extends RouteLocation {
  order: number;
}

export interface Route {
  uuid: string;
  name: string;
  description?: string;
  customerLocations: RouteLocation[];
  optimizedRoute: OptimizedRouteLocation[];
  totalDistance: number;
  warehouseUuidString: string;
  createdAt: Date;
  updatedAt: Date;
}

// Compute route DTO
export interface ComputeRouteDto {
  name: string;
  description?: string;
  customerLocations: CustomerLocation[];
  warehouseUuid: string;
}

// Update route DTO
export interface UpdateRouteDto {
  name?: string;
  description?: string;
  customerLocations?: CustomerLocation[];
  warehouseUuid?: string;
}

// Filter route DTO
export interface FilterRouteDto {
  warehouseUuid?: string;
  name?: string;
  description?: string;
}

// Pagination DTO
export interface PaginationQueryDto {
  page?: number;
  limit?: number;
}

// Paginated response DTO
export interface PaginatedResponseDto<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Hard delete response DTO
export interface HardDeleteResponseDto {
  deletedCount: number;
  message: string;
}

// Compute optimized route
export async function computeRoute(data: ComputeRouteDto): Promise<Route> {
  const res = await axios.post(`${API_BASE}/routes/compute`, data, {
    headers: getAxiosAuthHeaders(),
  });
  return res.data;
}

// Get all routes with pagination and filtering
export async function getRoutes(
  paginationQuery?: PaginationQueryDto,
  filter?: FilterRouteDto
): Promise<PaginatedResponseDto<Route>> {
  const params = new URLSearchParams();
  
  if (paginationQuery?.page) params.append('page', paginationQuery.page.toString());
  if (paginationQuery?.limit) params.append('limit', paginationQuery.limit.toString());
  
  if (filter?.warehouseUuid) params.append('warehouseUuid', filter.warehouseUuid);
  if (filter?.name) params.append('name', filter.name);
  if (filter?.description) params.append('description', filter.description);

  const queryString = params.toString();
  const url = `${API_BASE}/routes${queryString ? `?${queryString}` : ''}`;
  
  const res = await axios.get(url, {
    headers: getAxiosAuthHeaders(),
  });
  return res.data;
}

// Get single route by UUID
export async function getRouteByUuid(uuid: string): Promise<Route> {
  const res = await axios.get(`${API_BASE}/routes/${uuid}`, {
    headers: getAxiosAuthHeaders(),
  });
  return res.data;
}

// Update route
export async function updateRoute(uuid: string, data: UpdateRouteDto): Promise<Route> {
  const res = await axios.patch(`${API_BASE}/routes/${uuid}`, data, {
    headers: getAxiosAuthHeaders(),
  });
  return res.data;
}

// Delete route (soft delete)
export async function deleteRoute(uuid: string): Promise<void> {
  await axios.delete(`${API_BASE}/routes/${uuid}`, {
    headers: getAxiosAuthHeaders(),
  });
}

// Hard delete all routes
export async function hardDeleteAllRoutes(): Promise<HardDeleteResponseDto> {
  const res = await axios.delete(`${API_BASE}/routes/all`, {
    headers: getAxiosAuthHeaders(),
  });
  return res.data;
} 