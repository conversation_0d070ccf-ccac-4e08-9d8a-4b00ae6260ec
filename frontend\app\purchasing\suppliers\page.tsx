"use client";
import React, { useEffect, useState, useCallback } from 'react';
import ItemsTable, { ItemsTableColumn } from '@/components/itemsTable/ItemsTable';
import TableActionButtons from '@/components/itemsTable/TableActionButtons';
import SupplierModal from './components/SupplierModal';
import SupplierSearchBar from './components/SupplierSearchBar';
import { getSuppliers, filterSuppliers, createSupplier, updateSupplier, deleteSupplier } from './suppliersApi';
import { Supplier, CreateSupplierDto, UpdateSupplierDto } from './types';
import { useAuth } from '../../../contexts/AuthContext';

// Simple toast implementation (if no library is present)
function showToast(message: string, type: 'success' | 'error' = 'success') {
  const toast = document.createElement('div');
  toast.textContent = message;
  toast.className = `fixed top-6 right-6 z-[9999] px-4 py-2 rounded shadow text-white ${type === 'success' ? 'bg-green-600' : 'bg-red-600'}`;
  document.body.appendChild(toast);
  setTimeout(() => toast.remove(), 3000);
}

export default function Suppliers() {
  // Auth context for warehouseUuid/userUuid
  const { user } = useAuth();
  const warehouseUuid = user?.warehouseUuid;
  const userUuid = user?.uuid;

  const [suppliers, setSuppliers] = useState<Supplier[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalOpen, setModalOpen] = useState(false);
  const [modalMode, setModalMode] = useState<'add' | 'edit'>('add');
  const [modalInitial, setModalInitial] = useState<Partial<CreateSupplierDto & UpdateSupplierDto>>({});
  const [selectedSupplier, setSelectedSupplier] = useState<Supplier | null>(null);
  const [formLoading, setFormLoading] = useState(false);
  const [formError, setFormError] = useState<string | undefined>(undefined);
  const [error, setError] = useState<string | undefined>(undefined);

  // Fetch suppliers - use filter endpoint with warehouseUuid for proper filtering
  const fetchSuppliers = useCallback(async () => {
    if (!warehouseUuid) {
      setError('Warehouse context not available');
      return;
    }
    
    setLoading(true);
    setError(undefined);
    try {
      const data = await filterSuppliers({ warehouseUuid });
      setSuppliers(data);
    } catch (err: any) {
      const errorMessage = err?.response?.data?.message || 'Failed to load suppliers';
      setError(errorMessage);
      showToast(errorMessage, 'error');
    } finally {
      setLoading(false);
    }
  }, [warehouseUuid]);

  // Search/filter suppliers with improved functionality
  const handleSearch = async (query: string) => {
    if (!warehouseUuid) {
      setError('Warehouse context not available');
      return;
    }
    
    setLoading(true);
    setError(undefined);
    try {
      const data = await filterSuppliers({ 
        name: query.trim() || undefined, 
        warehouseUuid 
      });
      setSuppliers(data);
    } catch (err: any) {
      const errorMessage = err?.response?.data?.message || 'Failed to search suppliers';
      setError(errorMessage);
      showToast(errorMessage, 'error');
    } finally {
      setLoading(false);
    }
  };

  // Clear search and reload all suppliers
  const handleClearSearch = () => {
    fetchSuppliers();
  };

  useEffect(() => {
    fetchSuppliers();
  }, [fetchSuppliers]);

  // Keyboard shortcut: F2 to open add modal
  useEffect(() => {
    const handler = (e: KeyboardEvent) => {
      if (e.key === 'F2') {
        e.preventDefault();
        handleAdd();
      }
    };
    window.addEventListener('keydown', handler);
    return () => window.removeEventListener('keydown', handler);
  }, []);

  const handleAdd = () => {
    setModalMode('add');
    setModalInitial({});
    setModalOpen(true);
    setFormError(undefined);
  };

  const handleEdit = (supplier: Supplier) => {
    setSelectedSupplier(supplier);
    setModalMode('edit');
    setModalInitial(supplier);
    setModalOpen(true);
    setFormError(undefined);
  };

  const handleDelete = async (supplier: Supplier) => {
    if (!window.confirm(`Delete supplier "${supplier.name}"? This action cannot be undone.`)) return;
    setLoading(true);
    try {
      await deleteSupplier(supplier.uuid);
      showToast('Supplier deleted successfully', 'success');
      fetchSuppliers();
    } catch (err: any) {
      const errorMessage = err?.response?.data?.message || 'Failed to delete supplier';
      setError(errorMessage);
      showToast(errorMessage, 'error');
    } finally {
      setLoading(false);
    }
  };

  const handleModalSubmit = async (values: CreateSupplierDto | UpdateSupplierDto) => {
    setFormLoading(true);
    setFormError(undefined);
    if (!warehouseUuid) {
      const errorMessage = 'Missing warehouse context';
      setFormError(errorMessage);
      showToast(errorMessage, 'error');
      setFormLoading(false);
      return;
    }
    try {
      if (modalMode === 'add') {
        await createSupplier({ ...(values as CreateSupplierDto), warehouseUuid });
        showToast('Supplier added successfully', 'success');
      } else if (modalMode === 'edit' && selectedSupplier) {
        await updateSupplier(selectedSupplier.uuid, { ...(values as UpdateSupplierDto), warehouseUuid });
        showToast('Supplier updated successfully', 'success');
      }
      setModalOpen(false);
      fetchSuppliers();
    } catch (err: any) {
      const errorMessage = err?.response?.data?.message || 'Failed to save supplier';
      setFormError(errorMessage);
      showToast(errorMessage, 'error');
    } finally {
      setFormLoading(false);
    }
  };

  // Show error if warehouse context is missing
  if (!warehouseUuid) {
    return (
      <div className="p-6">
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <h2 className="text-red-800 font-semibold">Configuration Error</h2>
          <p className="text-red-600">Warehouse context is not available. Please check your user configuration.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      {/* Project Convention: warehouseUuid/userUuid injected from AuthContext (see CODE_GENERATION.md) */}
      <h1 className="text-2xl font-bold mb-6 flex items-center justify-between">
        <span>Suppliers</span>
        <button
          className="bg-blue-600 text-white rounded px-4 py-2 font-semibold shadow hover:bg-blue-700"
          onClick={handleAdd}
          tabIndex={0}
          title="Add Supplier (F2)"
        >
          Add Supplier <span className="ml-2 text-xs">(F2)</span>
        </button>
      </h1>
      <SupplierSearchBar 
        onSearch={handleSearch} 
        onClear={handleClearSearch}
        loading={loading} 
      />
      {error && (
        <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded text-red-600">
          {error}
        </div>
      )}
      <ItemsTable
        columns={[
          { 
            key: 'name', 
            header: 'Name',
            render: (value) => <span className="font-semibold">{value}</span>
          },
          { 
            key: 'fiscalId', 
            header: 'Fiscal ID', 
            render: (v) => v || '-' 
          },
          { 
            key: 'rc', 
            header: 'Registration Certificate', 
            render: (v) => v || '-' 
          },
          { 
            key: 'email', 
            header: 'Email', 
            render: (v) => v || '-' 
          },
          { 
            key: 'phone', 
            header: 'Phone', 
            render: (v) => v || '-' 
          },
          { 
            key: 'address', 
            header: 'Address', 
            render: (v) => v ? (v.length > 50 ? `${v.substring(0, 50)}...` : v) : '-',
            cellClassName: 'max-w-xs'
          },
          {
            key: 'actions',
            header: 'Actions',
            render: (_: any, row: Supplier) => (
              <TableActionButtons
                onEdit={() => handleEdit(row)}
                onDelete={() => handleDelete(row)}
              />
            ),
            headerClassName: 'text-center',
            cellClassName: 'text-center align-middle',
          },
        ]}
        data={suppliers}
        noDataText={loading ? 'Loading suppliers...' : 'No suppliers found.'}
        containerClassName="bg-white rounded-xl shadow-lg border border-gray-200 w-full"
      />
      <SupplierModal
        open={modalOpen}
        onClose={() => setModalOpen(false)}
        onSubmit={handleModalSubmit}
        initialValues={modalInitial}
        mode={modalMode}
        loading={formLoading}
        error={formError}
      />
    </div>
  );
}
