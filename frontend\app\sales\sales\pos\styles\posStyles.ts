// POS Styles - Tailwind CSS classes for the Point of Sale interface

export const posStyles = {
  layout: {
    container: "h-full bg-white rounded-lg shadow-sm flex flex-col",
    content: "flex flex-1 min-h-0",
    main: "flex-1 p-6 overflow-hidden flex flex-col",
    sidebar: "bg-gray-50 border-l border-gray-200 flex flex-col min-h-0 w-full lg:min-w-80 lg:max-w-96",
  },

  header: {
    container: "mb-4",
    title: "text-2xl font-bold text-gray-900 mb-1",
    subtitle: "text-sm text-gray-600",
  },



  search: {
    container: "mb-4",
    inputWrapper: "relative flex items-center group",
    input: "w-full pl-10 pr-10 py-2.5 text-base border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 placeholder-gray-400 hover:border-gray-400",
    icon: "absolute left-3 h-5 w-5 text-gray-400 pointer-events-none group-focus-within:text-blue-500 transition-colors",
    clearButton: "absolute right-2 p-1.5 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:bg-gray-100",
    placeholder: "Search products...",
  },

  productList: {
    container: "h-full overflow-y-auto",
    grid: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4",
    item: "p-4 border border-gray-200 rounded-lg cursor-pointer hover:bg-blue-50 hover:border-blue-300 transition-colors",
    itemSelected: "p-4 border-2 border-blue-500 rounded-lg cursor-pointer bg-blue-50 transition-colors",
    itemName: "font-semibold text-gray-900 mb-1",
    itemPrice: "text-lg font-bold text-green-600 mb-1",
    itemStock: "text-sm text-gray-500",
    itemDescription: "text-sm text-gray-600 mt-2",
    noResults: "text-center py-12 text-gray-500",
  },

  cart: {
    container: "h-full flex flex-col min-h-0",
    header: "p-4 border-b border-gray-200 flex-shrink-0",
    title: "text-xl font-bold text-gray-900",
    content: "flex-1 overflow-y-auto p-4 min-h-0",
    footer: "border-t border-gray-200 p-4 flex-shrink-0",
    
    // Updated for proper vertical scrolling with fixed height
    verticalContent: "flex-1 overflow-hidden p-4",
    verticalScroll: "cart-vertical-scroll overflow-y-auto overflow-x-hidden h-full pr-2",
    verticalContainer: "space-y-3",
    
    item: {
      container: "flex items-center justify-between p-3 border border-gray-200 rounded-lg mb-3",
      // Updated vertical layout styles with fixed height container
      verticalContainer: "p-3 rounded-lg transition-all duration-300 border border-gray-200",
      verticalContainerHighlighted: "p-3 rounded-lg transition-all duration-300 bg-yellow-50 border-yellow-400 border-2",
      info: "flex-1 min-w-0",
      name: "font-medium text-gray-900 truncate",
      price: "text-sm text-gray-600",
      quantity: "flex items-center gap-2 mt-2",
      quantityInput: "w-20 p-1 text-center border border-gray-300 rounded",
      priceInput: "w-24 p-1 text-center border border-gray-300 rounded",
      removeBtn: "p-1 text-red-500 hover:text-red-700 hover:bg-red-50 rounded",
      // Vertical styles for inputs with highlighting
      verticalQuantityInput: "w-20 p-1 text-center border rounded border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500",
      verticalQuantityInputHighlighted: "w-20 p-1 text-center border rounded border-yellow-400 border-2 bg-yellow-50 focus:ring-yellow-500 focus:border-yellow-500",
      verticalPriceInput: "w-24 p-1 text-center border rounded border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500",
      verticalPriceInputHighlighted: "w-24 p-1 text-center border rounded border-yellow-400 border-2 bg-yellow-50 focus:ring-yellow-500 focus:border-yellow-500",
    },

    summary: {
      container: "space-y-2 mb-4",
      row: "flex justify-between",
      label: "text-gray-600",
      value: "font-medium text-gray-900",
      total: "text-lg font-bold text-gray-900",
    },

    payment: {
      container: "mb-4",
      label: "block text-sm font-medium text-gray-700 mb-2",
      select: "w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",
    },

    notes: {
      container: "mb-4",
      label: "block text-sm font-medium text-gray-700 mb-2",
      textarea: "w-full p-3 border border-gray-300 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-blue-500",
    },

    actions: {
      container: "space-y-2",
      submitBtn: "w-full bg-green-600 text-white py-3 px-4 rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 font-medium disabled:opacity-50 disabled:cursor-not-allowed",
      clearBtn: "w-full bg-gray-600 text-white py-2 px-4 rounded-lg hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500",
    },
  },

  modal: {
    overlay: "fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",
    container: "bg-white rounded-lg shadow-lg max-w-2xl w-full mx-4 max-h-[80vh] overflow-hidden flex flex-col",
    header: "p-6 border-b border-gray-200 flex-shrink-0",
    title: "text-xl font-bold text-gray-900",
    content: "p-6 flex-1 overflow-y-auto",
    footer: "p-6 border-t border-gray-200 flex gap-3 flex-shrink-0",
    
    form: {
      field: "mb-4",
      label: "block text-sm font-medium text-gray-700 mb-1",
      input: "w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",
      textarea: "w-full p-3 border border-gray-300 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-blue-500",
      error: "text-red-500 text-sm mt-1",
    },

    buttons: {
      primary: "flex-1 bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 font-medium transition-colors",
      secondary: "flex-1 bg-gray-300 text-gray-700 py-3 px-6 rounded-lg hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500 font-medium transition-colors",
      danger: "flex-1 bg-red-600 text-white py-3 px-6 rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 font-medium transition-colors",
    },
  },

  quantity: {
    container: "space-y-4",
    productInfo: "text-center p-4 bg-gray-50 rounded-lg",
    productName: "font-semibold text-gray-900",
    productPrice: "text-lg font-bold text-green-600",
    inputs: "grid grid-cols-2 gap-4",
    inputGroup: "space-y-2",
    label: "block text-sm font-medium text-gray-700",
    input: "w-full p-3 text-center border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",
  },



  error: {
    container: "mb-4 p-4 bg-red-50 border border-red-200 rounded-lg",
    text: "text-red-700",
    icon: "text-red-500 mr-2",
  },

  loading: {
    container: "flex items-center justify-center py-8",
    spinner: "animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600",
    text: "ml-3 text-gray-600",
  },

  empty: {
    container: "text-center py-12",
    icon: "mx-auto h-12 w-12 text-gray-400 mb-4",
    text: "text-gray-500 text-lg",
    subtext: "text-gray-400 text-sm mt-2",
  },

  cartQuantity: {
    badge: "absolute -top-2 -right-2 bg-green-600 text-white text-xs font-bold rounded-full h-6 w-6 flex items-center justify-center shadow-lg border-2 border-white z-10",
    highlight: "bg-green-50 border-green-500 border-2",
    productWrapper: "relative",
  },

  cartItemHighlight: {
    container: "bg-yellow-50 border-yellow-400 border-2 transition-all duration-300",
    quantityInput: "border-yellow-400 border-2 bg-yellow-50 focus:ring-yellow-500 focus:border-yellow-500",
    priceInput: "border-yellow-400 border-2 bg-yellow-50 focus:ring-yellow-500 focus:border-yellow-500",
  },
}; 