import { authenticatedFetch } from '@/utils/authHeaders';

export interface Warehouse {
  uuid: string;
  name: string;
  isDeleted: boolean;
  userUuid?: string;
  userUuidString?: string;
}

export interface CreateWarehouseDto {
  name: string;
  description?: string;
  userUuid: string;
}

export interface UpdateWarehouseDto {
  name?: string;
  description?: string;
  userUuid?: string;
}

// Fetch all non-deleted warehouses for a specific user
export const fetchWarehouses = async (userUuid?: string): Promise<Warehouse[]> => {
  const url = userUuid ? `/api/warehouses/list?userUuid=${encodeURIComponent(userUuid)}` : '/api/warehouses/list';
  const res = await authenticatedFetch(url);
  if (!res.ok) {
    let message = 'Failed to fetch warehouses';
    try {
      const error = await res.json();
      if (error && error.message) message = error.message;
    } catch {}
    throw new Error(message);
  }
  const data = await res.json();
  return data.data || data; // Handle both paginated and non-paginated responses
};

// Fetch a single warehouse by UUID
export const fetchWarehouseById = async (uuid: string): Promise<Warehouse> => {
  const res = await authenticatedFetch(`/api/warehouses/${uuid}`);
  if (!res.ok) {
    let message = 'Failed to fetch warehouse';
    try {
      const error = await res.json();
      if (error && error.message) message = error.message;
    } catch {}
    throw new Error(message);
  }
  return res.json();
};

// Create a new warehouse
export const createWarehouse = async (data: CreateWarehouseDto): Promise<Warehouse> => {
  const res = await authenticatedFetch('/api/warehouses', {
    method: 'POST',
    body: JSON.stringify(data),
  });
  if (!res.ok) {
    let message = 'Failed to create warehouse';
    try {
      const error = await res.json();
      if (error && error.message) message = error.message;
    } catch {}
    throw new Error(message);
  }
  return res.json();
};

// Update a warehouse by UUID
export const updateWarehouse = async (uuid: string, data: UpdateWarehouseDto): Promise<Warehouse> => {
  const res = await authenticatedFetch(`/api/warehouses/${uuid}`, {
    method: 'PUT',
    body: JSON.stringify(data),
  });
  if (!res.ok) {
    let message = 'Failed to update warehouse';
    try {
      const error = await res.json();
      if (error && error.message) message = error.message;
    } catch {}
    throw new Error(message);
  }
  return res.json();
};

// Delete a warehouse by UUID (soft delete)
export const deleteWarehouse = async (uuid: string): Promise<void> => {
  const res = await authenticatedFetch(`/api/warehouses/${uuid}`, {
    method: 'DELETE',
  });
  if (!res.ok) {
    let message = 'Failed to delete warehouse';
    try {
      const error = await res.json();
      if (error && error.message) message = error.message;
    } catch {}
    throw new Error(message);
  }
};

// Filter warehouses by name and userUuid with pagination
export const filterWarehouses = async (params: {
  name?: string;
  userUuid?: string;
  page?: number;
  limit?: number;
}): Promise<{
  data: Warehouse[];
  total: number;
  page: number;
  limit: number;
  hasNext: boolean;
  hasPrev: boolean;
}> => {
  const searchParams = new URLSearchParams();
  if (params.name) searchParams.append('name', params.name);
  if (params.userUuid) searchParams.append('userUuid', params.userUuid);
  if (params.page) searchParams.append('page', params.page.toString());
  if (params.limit) searchParams.append('limit', params.limit.toString());

  const url = `/api/warehouses/filter?${searchParams.toString()}`;
  const res = await authenticatedFetch(url);
  if (!res.ok) {
    let message = 'Failed to filter warehouses';
    try {
      const error = await res.json();
      if (error && error.message) message = error.message;
    } catch {}
    throw new Error(message);
  }
  return res.json();
};

// Get warehouse count for a specific user
export const getWarehouseCount = async (userUuid?: string): Promise<number> => {
  const url = userUuid ? `/api/warehouses/count?userUuid=${encodeURIComponent(userUuid)}` : '/api/warehouses/count';
  const res = await authenticatedFetch(url);
  if (!res.ok) {
    let message = 'Failed to get warehouse count';
    try {
      const error = await res.json();
      if (error && error.message) message = error.message;
    } catch {}
    throw new Error(message);
  }
  const data = await res.json();
  return data.count || 0;
};

// Get main storage UUID for a warehouse
export const getWarehouseMainStorage = async (warehouseUuid: string): Promise<{ mainStorageUuid: string | null }> => {
  const res = await authenticatedFetch(`/api/warehouses/${warehouseUuid}/main-storage`);
  if (!res.ok) {
    let message = 'Failed to get warehouse main storage';
    try {
      const error = await res.json();
      if (error && error.message) message = error.message;
    } catch {}
    throw new Error(message);
  }
  const data = await res.json();
  return { mainStorageUuid: data.mainStorageUuid || null };
}; 