import { SideTaskBar } from '@/components/SideTaskBar/SideTaskBar';
import TopTaskBar from '@/components/TopTaskBar/TopTaskBar';

export default function LogsLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="flex h-screen bg-gray-50">
      <SideTaskBar />
      <div className="flex-1 flex flex-col overflow-hidden">
        <TopTaskBar />
        <main className="flex-1 overflow-y-auto">
          {children}
        </main>
      </div>
    </div>
  );
} 