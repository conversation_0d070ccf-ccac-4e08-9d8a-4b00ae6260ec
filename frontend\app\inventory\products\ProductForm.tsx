// ProductForm.tsx - Form for creating/editing a product
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import React from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { getAllProductCategoriesRaw, createProductCategory, ProductCategory } from './productCategoriesApi';
import { useAuth } from '../../../contexts/AuthContext';

/**
 * [Form UX Convention]
 * - Do NOT include warehouseUuid/userUuid in forms (inject from context in parent).
 * - The first field (Name) must be focused when the modal opens (handled by modal).
 * - Name is required field for product forms.
 * - Document these conventions for all similar forms.
 */
const productSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  description: z.string().optional(),
  sku: z.string().optional(),
  barcode: z.string().optional(),
  // Fixed: Use productCategoryUuid instead of productCategoryUuidString
  productCategoryUuid: z.string().optional(),
  // Simple price field (used when custom pricing is disabled) - string from form input
  price: z.string().optional(),
  // Updated pricing structure to match backend - all optional and strings from form
  retailPrice: z.string().optional(),
  wholesalePrice: z.string().optional(),
  midWholesalePrice: z.string().optional(),
  institutionalPrice: z.string().optional(),
  // Fixed: Make cost (purchase price) optional - string from form input
  cost: z.string().optional(),
});

export type ProductFormValues = z.infer<typeof productSchema>;

interface ProductFormProps {
  initialValues?: Partial<ProductFormValues>;
  onSubmit: (values: ProductFormValues) => void;
  onCancel?: () => void;
  submitLabel?: string;
  loading?: boolean;
  warehouseUuid: string;
}

// Enhanced Category Selector Component
const CategorySelector: React.FC<{
  value: string;
  onChange: (value: string) => void;
  categories: ProductCategory[];
  isLoading: boolean;
  warehouseUuid: string;
  userUuid: string;
}> = ({ value, onChange, categories, isLoading, warehouseUuid, userUuid }) => {
  const [isOpen, setIsOpen] = React.useState(false);
  const [searchTerm, setSearchTerm] = React.useState('');
  const [showCreateForm, setShowCreateForm] = React.useState(false);
  const [newCategoryName, setNewCategoryName] = React.useState('');
  const [creatingCategory, setCreatingCategory] = React.useState(false);
  const [createError, setCreateError] = React.useState<string | null>(null);
  const queryClient = useQueryClient();
  const dropdownRef = React.useRef<HTMLDivElement>(null);
  const createInputRef = React.useRef<HTMLInputElement>(null);

  // Close dropdown when clicking outside
  React.useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
        setShowCreateForm(false);
        setCreateError(null);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Auto-focus create input when create form opens
  React.useEffect(() => {
    if (showCreateForm && createInputRef.current) {
      createInputRef.current.focus();
    }
  }, [showCreateForm]);

  // Create category mutation
  const createCategoryMutation = useMutation({
    mutationFn: async (name: string) => {
      return createProductCategory({ name }, warehouseUuid, userUuid);
    },
    onSuccess: (newCategory) => {
      queryClient.invalidateQueries({ queryKey: ['productCategories', warehouseUuid] });
      onChange(newCategory.uuid);
      setNewCategoryName('');
      setShowCreateForm(false);
      setIsOpen(false);
      setCreateError(null);
      // Optional: You could add a success toast here if you have a toast system
    },
    onError: (error: any) => {
      setCreateError(error?.response?.data?.message || 'Failed to create category');
    },
  });

  // Filter categories based on search
  const filteredCategories = categories.filter(category =>
    category.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Get selected category for display
  const selectedCategory = categories.find(cat => cat.uuid === value);

  const handleCreateCategory = async (e?: React.FormEvent | React.KeyboardEvent) => {
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }
    
    if (!newCategoryName.trim()) {
      setCreateError('Category name is required');
      return;
    }
    
    setCreateError(null);
    setCreatingCategory(true);
    
    try {
      await createCategoryMutation.mutateAsync(newCategoryName.trim());
    } catch (error) {
      // Error is already handled by the mutation's onError callback
      console.error('Category creation failed:', error);
    } finally {
      setCreatingCategory(false);
    }
  };

  const handleAddNewClick = () => {
    setShowCreateForm(true);
    setSearchTerm('');
    setCreateError(null);
  };

  return (
    <div className="relative" ref={dropdownRef}>
      {/* Main selector button */}
      <button
        type="button"
        onClick={() => setIsOpen(!isOpen)}
        className="w-full rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-400 focus:border-blue-400 px-4 py-2 text-left text-base transition-all outline-none hover:bg-gray-50 flex items-center justify-between"
      >
        <span className={selectedCategory ? 'text-gray-900' : 'text-gray-500'}>
          {selectedCategory ? selectedCategory.name : 'Select a category...'}
        </span>
        <svg
          className={`w-5 h-5 text-gray-400 transition-transform ${isOpen ? 'rotate-180' : ''}`}
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </button>

      {/* Dropdown */}
      {isOpen && (
        <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-80 overflow-hidden">
          {/* Search input */}
          <div className="p-3 border-b border-gray-200">
            <input
              type="text"
              placeholder="Search categories..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-400 focus:border-blue-400 outline-none text-base"
            />
          </div>

          {/* Add new category button */}
          <div className="p-2 border-b border-gray-200 bg-gray-50">
            <button
              type="button"
              onClick={handleAddNewClick}
              className="w-full flex items-center gap-2 px-3 py-2 text-left text-blue-600 hover:bg-blue-50 rounded-md transition-colors text-sm font-medium"
            >
              <div className="flex items-center justify-center w-5 h-5 bg-blue-600 rounded-full">
                <svg className="w-3 h-3 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                </svg>
              </div>
              Add New Category
            </button>
          </div>

                     {/* Create category form */}
           {showCreateForm && (
             <div className="p-4 border-b border-gray-200 bg-blue-50">
               <div className="space-y-3">
                 <div>
                   <label className="block text-sm font-medium text-gray-700 mb-1">
                     Category Name <span className="text-red-500">*</span>
                   </label>
                   <input
                     ref={createInputRef}
                     type="text"
                     value={newCategoryName}
                     onChange={(e) => setNewCategoryName(e.target.value)}
                     placeholder="Enter category name"
                     className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-400 focus:border-blue-400 outline-none text-base"
                     disabled={creatingCategory}
                     onKeyDown={(e) => {
                       if (e.key === 'Enter') {
                         e.preventDefault();
                         e.stopPropagation();
                         handleCreateCategory(e);
                       }
                     }}
                   />
                   {createError && (
                     <div className="flex items-center gap-2 mt-1 text-red-600 text-sm">
                       <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                         <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01M21 12A9 9 0 1 1 3 12a9 9 0 0 1 18 0Z" />
                       </svg>
                       {createError}
                     </div>
                   )}
                 </div>
                 <div className="flex gap-2">
                   <button
                     type="button"
                     onClick={handleCreateCategory}
                     disabled={!newCategoryName.trim() || creatingCategory}
                     className="flex-1 px-3 py-2 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-400 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
                   >
                     {creatingCategory ? (
                       <>
                         <svg className="w-4 h-4 animate-spin" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                           <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                           <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v8z"></path>
                         </svg>
                         Creating...
                       </>
                     ) : (
                       'Create Category'
                     )}
                   </button>
                   <button
                     type="button"
                     onClick={() => {
                       setShowCreateForm(false);
                       setNewCategoryName('');
                       setCreateError(null);
                     }}
                     className="px-3 py-2 text-gray-600 text-sm border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-400"
                     disabled={creatingCategory}
                   >
                     Cancel
                   </button>
                 </div>
               </div>
             </div>
           )}

          {/* Categories list */}
          <div className="max-h-48 overflow-y-auto">
            {isLoading ? (
              <div className="p-4 text-center text-gray-500 text-sm">
                <div className="flex items-center justify-center gap-2">
                  <svg className="w-4 h-4 animate-spin" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v8z"></path>
                  </svg>
                  Loading categories...
                </div>
              </div>
            ) : filteredCategories.length === 0 ? (
              <div className="p-4 text-center text-gray-500 text-sm">
                {searchTerm ? `No categories found matching "${searchTerm}"` : 'No categories available'}
              </div>
            ) : (
              filteredCategories.map((category) => (
                <button
                  key={category.uuid}
                  type="button"
                  onClick={() => {
                    onChange(category.uuid);
                    setIsOpen(false);
                    setShowCreateForm(false);
                  }}
                  className={`w-full text-left px-4 py-2 hover:bg-gray-100 transition-colors text-sm ${
                    value === category.uuid ? 'bg-blue-50 text-blue-600 font-medium' : 'text-gray-900'
                  }`}
                >
                  {category.name}
                </button>
              ))
            )}
          </div>
        </div>
      )}
    </div>
  );
};

// Enhanced Number Input Component
const NumberInput: React.FC<{
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  error?: boolean;
}> = ({ value, onChange, placeholder = "0.00", className = "", error = false }) => {
  const [inputValue, setInputValue] = React.useState(value);

  // Update input value when prop changes
  React.useEffect(() => {
    setInputValue(value);
  }, [value]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setInputValue(newValue);
    
    // Only update the actual value if it's a valid number
    const numValue = parseFloat(newValue);
    if (!isNaN(numValue)) {
      onChange(newValue);
    } else if (newValue === '' || newValue === '.') {
      // Allow empty or decimal point for better UX
      onChange('0');
    }
  };

  const handleBlur = () => {
    // Format the value when user leaves the input
    const numValue = parseFloat(inputValue) || 0;
    const formattedValue = numValue.toFixed(2);
    setInputValue(formattedValue);
    onChange(formattedValue);
  };

  return (
    <input
      type="number"
      min="0"
      step="0.01"
      value={inputValue}
      onChange={handleChange}
      onBlur={handleBlur}
      placeholder={placeholder}
      className={`w-full rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-400 focus:border-blue-400 pl-8 pr-4 py-2 text-base transition-all outline-none [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none ${error ? 'border-red-400' : ''} ${className}`}
    />
  );
};

export default function ProductForm({ initialValues = {}, onSubmit, onCancel, submitLabel = 'Save', loading, warehouseUuid }: ProductFormProps) {
  // warehouseUuid is injected by the parent page from AuthContext and passed as prop
  const { user } = useAuth();
  const userUuid = user?.uuid || '';

  const nameInputRef = React.useRef<HTMLInputElement | null>(null);
  
  // Check if the four prices are different to determine default pricing mode
  const shouldShowCustomPricing = React.useMemo(() => {
    if (!initialValues.retailPrice && !initialValues.wholesalePrice && !initialValues.midWholesalePrice && !initialValues.institutionalPrice) {
      return false; // No prices set, use simple pricing
    }
    
    const retail = parseFloat(initialValues.retailPrice || '0');
    const wholesale = parseFloat(initialValues.wholesalePrice || '0');
    const midWholesale = parseFloat(initialValues.midWholesalePrice || '0');
    const institutional = parseFloat(initialValues.institutionalPrice || '0');
    
    // If all prices are the same, use simple pricing; otherwise use custom pricing
    return !(retail === wholesale && wholesale === midWholesale && midWholesale === institutional);
  }, [initialValues.retailPrice, initialValues.wholesalePrice, initialValues.midWholesalePrice, initialValues.institutionalPrice]);
  
  const [showAdditionalPrices, setShowAdditionalPrices] = React.useState(shouldShowCustomPricing);

  // Fetch product categories for dropdown
  const { data: categories = [], isLoading: categoriesLoading } = useQuery<ProductCategory[]>({
    queryKey: ['productCategories', warehouseUuid],
    queryFn: () => getAllProductCategoriesRaw(warehouseUuid),
    enabled: !!warehouseUuid,
  });

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors },
  } = useForm<ProductFormValues>({
    resolver: zodResolver(productSchema),
    defaultValues: {
      ...initialValues,
      // Set default values for price fields
      cost: initialValues.cost || '0',
      // If using custom pricing (different prices), set price to retail price, otherwise use price or '0'
      price: shouldShowCustomPricing ? (initialValues.retailPrice || '0') : (initialValues.price || '0'),
      retailPrice: initialValues.retailPrice || '0',
      wholesalePrice: initialValues.wholesalePrice || '0',
      midWholesalePrice: initialValues.midWholesalePrice || '0',
      institutionalPrice: initialValues.institutionalPrice || '0',
    },
  });

  React.useEffect(() => {
    if (nameInputRef.current) {
      nameInputRef.current.focus();
    }
  }, []);

  // Update pricing mode when initialValues change
  React.useEffect(() => {
    setShowAdditionalPrices(shouldShowCustomPricing);
    // When switching to custom pricing, set the simple price to retail price
    if (shouldShowCustomPricing && initialValues.retailPrice) {
      setValue('price', initialValues.retailPrice);
    }
  }, [shouldShowCustomPricing, initialValues.retailPrice, setValue]);

  // When simple pricing is used, update all price fields when the simple price changes
  React.useEffect(() => {
    if (!showAdditionalPrices) {
      const simplePrice = watch('price');
      if (simplePrice && simplePrice !== '0' && simplePrice !== '') {
        console.log('[ProductForm] Simple pricing: updating all price fields to:', simplePrice);
        setValue('retailPrice', simplePrice);
        setValue('wholesalePrice', simplePrice);
        setValue('midWholesalePrice', simplePrice);
        setValue('institutionalPrice', simplePrice);
      }
    }
  }, [watch('price'), showAdditionalPrices, setValue]);

  const selectedCategoryUuid = watch('productCategoryUuid');

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">

      <div className="mb-4">
        <label className="block text-sm font-medium mb-1">Name<span className="text-red-500">*</span></label>
        <input
          {...register('name')}
          className={`w-full rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-400 focus:border-blue-400 px-4 py-2 text-base transition-all outline-none ${errors.name ? 'border-red-400' : ''}`}
          ref={el => {
            register('name').ref(el);
            nameInputRef.current = el;
          }}
          placeholder="Enter product name"
        />
        {errors.name && <div className="flex items-center gap-2 mt-1 text-sm text-red-600"><svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01M21 12A9 9 0 1 1 3 12a9 9 0 0 1 18 0Z" /></svg>{errors.name.message}</div>}
      </div>

      <div className="mb-4">
        <label className="block text-sm font-medium mb-1">Description</label>
        <textarea {...register('description')} className="w-full rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-400 focus:border-blue-400 px-4 py-2 text-base transition-all outline-none min-h-[60px] resize-y" placeholder="Optional description" />
      </div>

      <div className="grid grid-cols-2 gap-4 mb-4">
        <div>
          <label className="block text-sm font-medium mb-1">SKU</label>
          <input {...register('sku')} className="w-full rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-400 focus:border-blue-400 px-4 py-2 text-base transition-all outline-none" placeholder="Stock keeping unit" />
        </div>
        <div>
          <label className="block text-sm font-medium mb-1">Barcode</label>
          <input {...register('barcode')} className="w-full rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-400 focus:border-blue-400 px-4 py-2 text-base transition-all outline-none" placeholder="Barcode" />
        </div>
      </div>

      <div className="mb-4">
        <label className="block text-sm font-medium mb-1">
          Category 
          <span className="text-gray-500 text-sm font-normal ml-1">(Optional - click + to create new)</span>
        </label>
        <CategorySelector
          value={selectedCategoryUuid || ''}
          onChange={(value) => setValue('productCategoryUuid', value)}
          categories={categories}
          isLoading={categoriesLoading}
          warehouseUuid={warehouseUuid}
          userUuid={userUuid}
        />
        {categoriesLoading && (
          <div className="text-sm text-gray-500 mt-1">Loading categories...</div>
        )}
      </div>
      
      {/* Purchase Price field - optional */}
      <div className="mb-4">
        <div>
          <label className="block text-sm font-medium mb-1">Purchase Price <span className="text-gray-500 text-sm font-normal">(What you pay to purchase this product)</span></label>
          <div className="relative">
            <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">$</span>
            <NumberInput
              value={watch('cost') || '0'}
              onChange={(value) => setValue('cost', value)}
              placeholder="0.00"
              error={!!errors.cost}
            />
          </div>
          {errors.cost && <div className="flex items-center gap-2 mt-1 text-sm text-red-600"><svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01M21 12A9 9 0 1 1 3 12a9 9 0 0 1 18 0Z" /></svg>{errors.cost.message}</div>}
        </div>
      </div>

      {/* Pricing Section */}
      <div className="mb-6">
        <button
          type="button"
          onClick={() => setShowAdditionalPrices(!showAdditionalPrices)}
          className="flex items-center gap-2 text-blue-600 hover:text-blue-700 font-medium text-sm mb-3 transition-colors"
        >
          <svg 
            className={`w-4 h-4 transition-transform ${showAdditionalPrices ? 'rotate-90' : ''}`} 
            fill="none" 
            viewBox="0 0 24 24" 
            stroke="currentColor"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
          </svg>
          {showAdditionalPrices ? 'Use Simple Pricing' : 'Use Custom Pricing'}
        </button>
        
        {!showAdditionalPrices ? (
          // Simple Price field
          <div>
            <label className="block text-sm font-medium mb-1">Price <span className="text-gray-500 text-sm font-normal">(Selling price for this product)</span></label>
            <div className="relative">
              <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">$</span>
              <NumberInput
                value={watch('price') || '0'}
                onChange={(value) => setValue('price', value)}
                placeholder="0.00"
                error={!!errors.price}
              />
            </div>
            {errors.price && <div className="flex items-center gap-2 mt-1 text-sm text-red-600"><svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01M21 12A9 9 0 1 1 3 12a9 9 0 0 1 18 0Z" /></svg>{errors.price.message}</div>}
          </div>
        ) : (
          // Custom pricing with 4 price types
          <div className="grid grid-cols-2 gap-4 p-4 bg-gray-50 rounded-lg border border-gray-200">
            <div>
              <label className="block text-sm font-medium mb-1">Retail Price <span className="text-gray-500 text-sm font-normal">(Individual customers)</span></label>
              <div className="relative">
                <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">$</span>
                <NumberInput
                  value={watch('retailPrice') || '0'}
                  onChange={(value) => setValue('retailPrice', value)}
                  placeholder="0.00"
                  error={!!errors.retailPrice}
                  className="bg-white"
                />
              </div>
              {errors.retailPrice && <div className="flex items-center gap-2 mt-1 text-sm text-red-600"><svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01M21 12A9 9 0 1 1 3 12a9 9 0 0 1 18 0Z" /></svg>{errors.retailPrice.message}</div>}
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">Wholesale Price <span className="text-gray-500 text-sm font-normal">(Bulk buyers)</span></label>
              <div className="relative">
                <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">$</span>
                <NumberInput
                  value={watch('wholesalePrice') || '0'}
                  onChange={(value) => setValue('wholesalePrice', value)}
                  placeholder="0.00"
                  error={!!errors.wholesalePrice}
                  className="bg-white"
                />
              </div>
              {errors.wholesalePrice && <div className="flex items-center gap-2 mt-1 text-sm text-red-600"><svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01M21 12A9 9 0 1 1 3 12a9 9 0 0 1 18 0Z" /></svg>{errors.wholesalePrice.message}</div>}
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">Mid-Wholesale Price <span className="text-gray-500 text-sm font-normal">(Medium volume buyers)</span></label>
              <div className="relative">
                <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">$</span>
                <NumberInput
                  value={watch('midWholesalePrice') || '0'}
                  onChange={(value) => setValue('midWholesalePrice', value)}
                  placeholder="0.00"
                  error={!!errors.midWholesalePrice}
                  className="bg-white"
                />
              </div>
              {errors.midWholesalePrice && <div className="flex items-center gap-2 mt-1 text-sm text-red-600"><svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01M21 12A9 9 0 1 1 3 12a9 9 0 0 1 18 0Z" /></svg>{errors.midWholesalePrice.message}</div>}
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">Institutional Price <span className="text-gray-500 text-sm font-normal">(Institutions & organizations)</span></label>
              <div className="relative">
                <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">$</span>
                <NumberInput
                  value={watch('institutionalPrice') || '0'}
                  onChange={(value) => setValue('institutionalPrice', value)}
                  placeholder="0.00"
                  error={!!errors.institutionalPrice}
                  className="bg-white"
                />
              </div>
              {errors.institutionalPrice && <div className="flex items-center gap-2 mt-1 text-sm text-red-600"><svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01M21 12A9 9 0 1 1 3 12a9 9 0 0 1 18 0Z" /></svg>{errors.institutionalPrice.message}</div>}
            </div>
          </div>
        )}
      </div>

      <div className="flex gap-3 mt-6">
        <button 
          type="button" 
          onClick={onCancel} 
          className="w-full bg-gray-200 text-gray-800 font-medium rounded-lg py-2.5 text-sm transition-colors hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-400"
        >
          Cancel
        </button>
        <button 
          type="submit" 
          className="w-full flex items-center justify-center gap-2 bg-blue-600 text-white font-medium rounded-lg py-2.5 text-sm transition-colors hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-400 disabled:opacity-60 disabled:cursor-not-allowed" 
          disabled={loading}
        >
          {loading ? (
            <svg className="w-4 h-4 animate-spin" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v8z"></path>
            </svg>
          ) : (
            <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
            </svg>
          )}
          {loading ? 'Saving...' : submitLabel}
        </button>
      </div>
    </form>
  );
}
