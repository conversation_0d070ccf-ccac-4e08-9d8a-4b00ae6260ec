import { ApiProperty } from "@nestjs/swagger";
import {
  IsString,
  IsUUID,
  IsOptional,
  IsDateString,
  IsEnum,
  IsBoolean,
  IsNumber,
  Min,
  <PERSON>,
} from "class-validator";
import { OrderPriority, OrderStatus } from "../order.entity";

export class CreateOrderDto {
  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the user creating the order",
  })
  @IsString()
  @IsUUID("7")
  userUuid: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the warehouse",
  })
  @IsString()
  @IsUUID("7")
  warehouseUuid: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the customer",
  })
  @IsString()
  @IsUUID("7")
  customerUuid: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the quote this order was created from",
    required: false,
  })
  @IsOptional()
  @IsString()
  @IsUUID("7")
  quoteUuid?: string;

  @ApiProperty({
    example: "2024-01-20T10:00:00.000Z",
    description: "Requested delivery date",
    required: false,
  })
  @IsOptional()
  @IsDateString()
  requestedDeliveryDate?: string;

  @ApiProperty({
    example: OrderPriority.NORMAL,
    description: "Order priority level",
    enum: OrderPriority,
    required: false,
  })
  @IsOptional()
  @IsEnum(OrderPriority)
  priority?: OrderPriority;

  @ApiProperty({
    example: "Special delivery instructions",
    description: "Order notes",
    required: false,
  })
  @IsOptional()
  @IsString()
  notes?: string;

  @ApiProperty({
    example: true,
    description: "Whether tax is applied to this order",
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  useTax?: boolean;

  @ApiProperty({
    example: 0.1,
    description: "Tax rate as decimal (e.g., 0.1 for 10%)",
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(1)
  taxRate?: number;

  @ApiProperty({
    example: OrderStatus.DRAFT,
    description: "Order status",
    enum: OrderStatus,
    required: false,
  })
  @IsOptional()
  @IsEnum(OrderStatus)
  status?: OrderStatus;

  @ApiProperty({
    type: [Object],
    description: "Order items",
    required: false,
  })
  @IsOptional()
  items?: any[];
}
