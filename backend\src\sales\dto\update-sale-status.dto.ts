import {
  IsString,
  IsUUID,
  IsE<PERSON>,
} from "class-validator";
import { ApiProperty } from "@nestjs/swagger";
import { SaleStatus } from "../sale.entity";

export class UpdateSaleStatusDto {
  @ApiProperty({
    description: "UUID of the user updating the sale status",
    example: "018ea2bb-6b8d-7e9a-8a2a-7e9a8a2a7e9a",
  })
  @IsString()
  @IsUUID('all', { message: "userUuid must be a valid UUID" })
  userUuid!: string;

  @ApiProperty({
    description: "New status for the sale",
    enum: SaleStatus,
    example: SaleStatus.PAID,
  })
  @IsString()
  @IsEnum(SaleStatus, { message: "status must be a valid sale status" })
  status!: SaleStatus;
} 