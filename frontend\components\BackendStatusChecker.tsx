"use client";
import React, { useState, useEffect } from 'react';

interface BackendStatusCheckerProps {
  onStatusChange?: (status: 'checking' | 'online' | 'offline' | 'error') => void;
  showDetails?: boolean;
  className?: string;
}

export default function BackendStatusChecker({ 
  onStatusChange, 
  showDetails = false, 
  className = "" 
}: BackendStatusCheckerProps) {
  const [status, setStatus] = useState<'checking' | 'online' | 'offline' | 'error'>('checking');
  const [details, setDetails] = useState<string>('');
  const [lastCheck, setLastCheck] = useState<Date | null>(null);
  const [checkTimeout, setCheckTimeout] = useState<NodeJS.Timeout | null>(null);

  const checkBackendStatus = async () => {
    setStatus('checking');
    setDetails('Checking backend connectivity...');
    
    // Clear any existing timeout
    if (checkTimeout) {
      clearTimeout(checkTimeout);
    }
    
    // Set a timeout for the check
    const timeout = setTimeout(() => {
      setStatus('offline');
      setDetails('Backend check timed out after 10 seconds. Server may be offline or unreachable.');
      onStatusChange?.('offline');
    }, 10000);
    
    setCheckTimeout(timeout);
    
    try {
      const startTime = Date.now();
      const response = await fetch('/api/auth/health', {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' },
        signal: AbortSignal.timeout(8000), // 8 second timeout for fetch
      });
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      // Clear the timeout since we got a response
      clearTimeout(timeout);
      setCheckTimeout(null);
      
      setLastCheck(new Date());
      
      if (response.ok) {
        const data = await response.json();
        setStatus('online');
        setDetails(`Backend is online (${responseTime}ms) - Uptime: ${Math.floor(data.uptime)}s`);
        onStatusChange?.('online');
      } else {
        setStatus('error');
        setDetails(`Backend returned error: ${response.status} ${response.statusText} (${responseTime}ms)`);
        onStatusChange?.('error');
      }
    } catch (error) {
      // Clear the timeout since we got an error
      clearTimeout(timeout);
      setCheckTimeout(null);
      
      setLastCheck(new Date());
      setStatus('offline');
      setDetails(`Cannot connect to backend: ${error instanceof Error ? error.message : 'Unknown error'}`);
      onStatusChange?.('offline');
    }
  };

  useEffect(() => {
    checkBackendStatus();
    
    // Cleanup timeout on unmount
    return () => {
      if (checkTimeout) {
        clearTimeout(checkTimeout);
      }
    };
  }, []);

  const getStatusIcon = () => {
    switch (status) {
      case 'checking':
        return (
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
        );
      case 'online':
        return (
          <svg className="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
          </svg>
        );
      case 'offline':
        return (
          <svg className="w-4 h-4 text-red-600" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
          </svg>
        );
      case 'error':
        return (
          <svg className="w-4 h-4 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
        );
    }
  };

  const getStatusText = () => {
    switch (status) {
      case 'checking':
        return 'Checking backend connection...';
      case 'online':
        return 'Backend server is online';
      case 'offline':
        return 'Backend server is offline';
      case 'error':
        return 'Backend server error';
    }
  };

  const getStatusColor = () => {
    switch (status) {
      case 'checking':
        return 'text-blue-600';
      case 'online':
        return 'text-green-600';
      case 'offline':
        return 'text-red-600';
      case 'error':
        return 'text-yellow-600';
    }
  };

  return (
    <div className={`flex items-center ${className}`}>
      <div className="mr-2">
        {getStatusIcon()}
      </div>
      <div className="flex-1">
        <div className={`text-sm font-medium ${getStatusColor()}`}>
          {getStatusText()}
        </div>
        {showDetails && details && (
          <div className="text-xs text-gray-500 mt-1">
            {details}
          </div>
        )}
        {showDetails && lastCheck && (
          <div className="text-xs text-gray-400 mt-1">
            Last checked: {lastCheck.toLocaleTimeString()}
          </div>
        )}
      </div>
      <button
        onClick={checkBackendStatus}
        disabled={status === 'checking'}
        className="ml-2 px-2 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded transition disabled:opacity-50"
        title="Refresh backend status"
      >
        {status === 'checking' ? 'Checking...' : 'Refresh'}
      </button>
    </div>
  );
} 