import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository, DataSource } from "typeorm";
import { CustomerPayment, PaymentMethod, PaymentStatus } from "./customer-payment.entity";
import { Customer } from "./customer.entity";
import { CreateCustomerPaymentDto } from "./dto/create-customer-payment.dto";
import { UpdateCustomerPaymentDto } from "./dto/update-customer-payment.dto";
import { FilterCustomerPaymentDto } from "./dto/filter-customer-payment.dto";
import {
  CustomerPaymentResponseDto,
  toCustomerPaymentResponseDto,
} from "./dto/customer-payment-response.dto";
import { Uuid7 } from "../utils/uuid7";
import { PaginatedResponseDto } from "../dto/pagination.dto";

@Injectable()
export class CustomerPaymentService {
  constructor(
    @InjectRepository(CustomerPayment)
    private customerPaymentRepository: Repository<CustomerPayment>,
    @InjectRepository(Customer)
    private customerRepository: Repository<Customer>,
    private dataSource: DataSource,
  ) {}

  async create(
    createPaymentDto: CreateCustomerPaymentDto,
  ): Promise<CustomerPaymentResponseDto> {
    // Verify customer exists
    const customer = await this.customerRepository.findOne({
      where: { id: createPaymentDto.customerUuid, isDeleted: false }
    });

    if (!customer) {
      throw new NotFoundException(`Customer with UUID ${createPaymentDto.customerUuid} not found`);
    }

    // Use transaction to ensure data consistency
    return await this.dataSource.transaction(async (manager) => {
      const payment = new CustomerPayment();
      payment.id = CustomerPayment.generateId();
      payment.customerUuid = createPaymentDto.customerUuid;
      payment.userUuid = createPaymentDto.userUuid;
      payment.warehouseUuid = createPaymentDto.warehouseUuid;
      payment.saleUuid = createPaymentDto.saleUuid;
      payment.paymentMethod = createPaymentDto.paymentMethod;
      payment.amount = createPaymentDto.amount;
      payment.status = PaymentStatus.PENDING; // Default status
      payment.description = createPaymentDto.description;
      payment.referenceNumber = createPaymentDto.referenceNumber;
      payment.processedAt = new Date(); // Set to current time
      payment.previousCreditBalance = customer.currentCredit;
      payment.newCreditBalance = customer.currentCredit + createPaymentDto.amount;

      // Save payment
      const savedPayment = await manager.save(CustomerPayment, payment);

      // Update customer credit
      customer.currentCredit = payment.newCreditBalance;
      await manager.save(Customer, customer);

      return toCustomerPaymentResponseDto(savedPayment);
    });
  }

  async findAll(
    filter?: FilterCustomerPaymentDto,
    page: number = 1,
    limit: number = 10,
  ): Promise<PaginatedResponseDto<CustomerPaymentResponseDto>> {
    const queryBuilder = this.customerPaymentRepository.createQueryBuilder('payment');

    if (filter) {
      if (filter.customerUuid) {
        queryBuilder.andWhere('payment.customerUuid = :customerUuid', { customerUuid: filter.customerUuid });
      }
      if (filter.userUuid) {
        queryBuilder.andWhere('payment.userUuid = :userUuid', { userUuid: filter.userUuid });
      }
      if (filter.warehouseUuid) {
        queryBuilder.andWhere('payment.warehouseUuid = :warehouseUuid', { warehouseUuid: filter.warehouseUuid });
      }
      if (filter.saleUuid) {
        queryBuilder.andWhere('payment.saleUuid = :saleUuid', { saleUuid: filter.saleUuid });
      }
      if (filter.paymentMethod) {
        queryBuilder.andWhere('payment.paymentMethod = :paymentMethod', { paymentMethod: filter.paymentMethod });
      }
      if (filter.status) {
        queryBuilder.andWhere('payment.status = :status', { status: filter.status });
      }
      if (filter.referenceNumber) {
        queryBuilder.andWhere('payment.referenceNumber ILIKE :referenceNumber', { referenceNumber: `%${filter.referenceNumber}%` });
      }
    }

    // Get total count
    const total = await queryBuilder.getCount();

    // Apply pagination
    const offset = (page - 1) * limit;
    queryBuilder.skip(offset).take(limit);

    // Apply ordering
    queryBuilder.orderBy('payment.createdAt', 'DESC');

    const data = await queryBuilder.getMany();

    return new PaginatedResponseDto(
      data.map(payment => toCustomerPaymentResponseDto(payment)),
      total,
      page,
      limit
    );
  }

  async findOne(uuid: string): Promise<CustomerPaymentResponseDto> {
    const payment = await this.customerPaymentRepository.findOne({
      where: { id: uuid }
    });

    if (!payment) {
      throw new NotFoundException(`Customer payment with UUID ${uuid} not found`);
    }

    return toCustomerPaymentResponseDto(payment);
  }

  async update(
    uuid: string,
    updateDto: UpdateCustomerPaymentDto,
  ): Promise<CustomerPaymentResponseDto> {
    const payment = await this.customerPaymentRepository.findOne({
      where: { id: uuid }
    });

    if (!payment) {
      throw new NotFoundException(`Customer payment with UUID ${uuid} not found`);
    }

    // Update fields
    if (updateDto.paymentMethod !== undefined) payment.paymentMethod = updateDto.paymentMethod;
    if (updateDto.amount !== undefined) payment.amount = updateDto.amount;
    if (updateDto.status !== undefined) payment.status = updateDto.status;
    if (updateDto.description !== undefined) payment.description = updateDto.description;
    if (updateDto.referenceNumber !== undefined) payment.referenceNumber = updateDto.referenceNumber;
    if (updateDto.processedAt !== undefined) payment.processedAt = new Date(updateDto.processedAt);

    const updated = await this.customerPaymentRepository.save(payment);
    return toCustomerPaymentResponseDto(updated);
  }

  async remove(uuid: string): Promise<void> {
    const payment = await this.customerPaymentRepository.findOne({
      where: { id: uuid }
    });

    if (!payment) {
      throw new NotFoundException(`Customer payment with UUID ${uuid} not found`);
    }

    await this.customerPaymentRepository.remove(payment);
  }

  async getCustomerPaymentHistory(
    customerUuid: string,
    page: number = 1,
    limit: number = 10,
  ): Promise<PaginatedResponseDto<CustomerPaymentResponseDto>> {
    const queryBuilder = this.customerPaymentRepository.createQueryBuilder('payment');
    queryBuilder.where('payment.customerUuid = :customerUuid', { customerUuid });

    // Get total count
    const total = await queryBuilder.getCount();

    // Apply pagination
    const offset = (page - 1) * limit;
    queryBuilder.skip(offset).take(limit);

    // Apply ordering
    queryBuilder.orderBy('payment.createdAt', 'DESC');

    const data = await queryBuilder.getMany();

    return new PaginatedResponseDto(
      data.map(payment => toCustomerPaymentResponseDto(payment)),
      total,
      page,
      limit
    );
  }

  async findByCustomer(
    customerUuid: string,
    page: number = 1,
    limit: number = 10,
  ): Promise<PaginatedResponseDto<CustomerPaymentResponseDto>> {
    return this.getCustomerPaymentHistory(customerUuid, page, limit);
  }

  async refundPayment(
    uuid: string,
    refundAmount: number,
    reason: string,
    userUuid: string,
  ): Promise<CustomerPaymentResponseDto> {
    const payment = await this.customerPaymentRepository.findOne({
      where: { id: uuid }
    });

    if (!payment) {
      throw new NotFoundException(`Customer payment with UUID ${uuid} not found`);
    }

    if (refundAmount > payment.amount) {
      throw new BadRequestException('Refund amount cannot exceed original payment amount');
    }

    // Create refund payment
    const refundPayment = new CustomerPayment();
    refundPayment.id = CustomerPayment.generateId();
    refundPayment.customerUuid = payment.customerUuid;
    refundPayment.userUuid = userUuid;
    refundPayment.warehouseUuid = payment.warehouseUuid;
    refundPayment.saleUuid = payment.saleUuid;
    refundPayment.paymentMethod = payment.paymentMethod;
    refundPayment.amount = -refundAmount; // Negative amount for refund
    refundPayment.status = PaymentStatus.COMPLETED;
    refundPayment.description = `Refund: ${reason}`;
    refundPayment.referenceNumber = `REF-${payment.referenceNumber}`;

    const savedRefund = await this.customerPaymentRepository.save(refundPayment);
    return toCustomerPaymentResponseDto(savedRefund);
  }

  async cancelPayment(uuid: string, userUuid: string): Promise<CustomerPaymentResponseDto> {
    const payment = await this.customerPaymentRepository.findOne({
      where: { id: uuid }
    });

    if (!payment) {
      throw new NotFoundException(`Customer payment with UUID ${uuid} not found`);
    }

    if (payment.status === PaymentStatus.CANCELLED) {
      throw new BadRequestException('Payment is already cancelled');
    }

    payment.status = PaymentStatus.CANCELLED;
    payment.description = payment.description ? `${payment.description} (Cancelled)` : 'Cancelled';

    const updatedPayment = await this.customerPaymentRepository.save(payment);
    return toCustomerPaymentResponseDto(updatedPayment);
  }
} 