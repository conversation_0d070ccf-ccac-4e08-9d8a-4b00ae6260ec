import React, { useState, useEffect } from 'react';
import { posStyles } from '../styles/posStyles';
import { getPaymentMethodName, formatCurrency } from '../utils/posHelpers';
import type { PaymentSelectorProps } from '../types';

const PAYMENT_METHODS = [
  { value: 'cash', label: 'Cash' },
  { value: 'credit_card', label: 'Credit/Debit Card' },
  { value: 'bank_transfer', label: 'Bank Transfer' },
  { value: 'mobile_payment', label: 'Mobile Payment' },
  { value: 'cheque', label: 'Cheque' },
  { value: 'other', label: 'Other' },
];

export function PaymentSelector({ selectedMethod, onMethodChange, total, amountPaid, onAmountPaidChange, disabled = false }: PaymentSelectorProps) {
  const change = amountPaid - total;
  
  // Separate state for input value to avoid cursor jumping
  const [inputValue, setInputValue] = useState(amountPaid.toString());

  // Update input value when amountPaid prop changes
  useEffect(() => {
    setInputValue(amountPaid.toString());
  }, [amountPaid]);

  const handleAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setInputValue(value);
    
    // Only update the actual amount if it's a valid number
    const numValue = parseFloat(value);
    if (!isNaN(numValue)) {
      onAmountPaidChange(numValue);
    } else if (value === '' || value === '.') {
      // Allow empty or decimal point for better UX
      onAmountPaidChange(0);
    }
  };

  const handleBlur = () => {
    // Format the value when user leaves the input
    const numValue = parseFloat(inputValue) || 0;
    setInputValue(numValue.toFixed(2));
    onAmountPaidChange(numValue);
  };

  return (
    <div className="mb-3 space-y-2">
      <label className="block text-xs font-medium text-gray-700">
        Payment Method
      </label>
      <select
        value={selectedMethod || 'cash'}
        onChange={(e) => onMethodChange(e.target.value)}
        disabled={disabled}
        className={`w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 text-xs ${
          disabled ? 'bg-gray-100 cursor-not-allowed opacity-50' : ''
        }`}
      >
        {PAYMENT_METHODS.map((method) => (
          <option key={method.value} value={method.value}>
            {method.label}
          </option>
        ))}
      </select>
      
      {/* Payment Amount Section */}
      <div className="space-y-2">
        <label className="block text-xs font-medium text-gray-700">
          Amount Paid
        </label>
        <div className="flex items-center space-x-2">
          <input
            type="number"
            min="0"
            step="0.01"
            value={inputValue}
            onChange={handleAmountChange}
            onBlur={handleBlur}
            disabled={disabled}
            className={`flex-1 p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 text-xs [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none ${
              disabled ? 'bg-gray-100 cursor-not-allowed opacity-50' : ''
            }`}
            placeholder="0.00"
          />
          <button
            type="button"
            onClick={() => {
              console.log('[PaymentSelector] Exact amount clicked:', { total });
              onAmountPaidChange(total);
            }}
            disabled={disabled}
            className={`px-2 py-2 text-xs font-medium rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 transition-colors whitespace-nowrap ${
              disabled 
                ? 'bg-gray-400 cursor-not-allowed' 
                : 'bg-blue-600 text-white hover:bg-blue-700'
            }`}
          >
            Exact Amount
          </button>
        </div>
        
        {/* Change Display */}
        {amountPaid > 0 && (
          <div className="flex justify-between items-center p-1.5 bg-gray-50 rounded-md">
            <span className="text-xs text-gray-600">Change:</span>
            <span className={`font-semibold text-xs ${change >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              {formatCurrency(Math.abs(change))}
              {change < 0 && ' (Insufficient)'}
            </span>
          </div>
        )}
        
        {/* Validation Message */}
        {amountPaid >= total && total > 0 && (
          <div className="text-xs text-green-600 bg-green-50 p-1.5 rounded-md">
            ✓ Payment amount covers the total. Change: {formatCurrency(amountPaid - total)}
          </div>
        )}
      </div>
      
      {selectedMethod && (
        <div className="mt-1 text-xs text-gray-600">
          Total to collect: <span className="font-semibold">{formatCurrency(total)}</span>
        </div>
      )}
    </div>
  );
} 