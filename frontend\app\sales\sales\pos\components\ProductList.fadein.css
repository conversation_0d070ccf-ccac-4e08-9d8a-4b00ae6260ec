/* Enhanced fade-in animation with bounce effect for ProductList */
.product-list-fade-in {
  opacity: 1;
  animation: productListFadeInBounce 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards;
}

/* Ensure product list is visible after animation */
.product-list-fade-in-complete {
  opacity: 1 !important;
  transform: translateY(0) scale(1) !important;
}

/* Cool bounce fade-in animation for product list */
@keyframes productListFadeInBounce {
  0% {
    opacity: 0;
    transform: translateY(5px) scale(0.99);
  }
  60% {
    opacity: 0.8;
    transform: translateY(-1px) scale(1.005);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Ensure product list stays visible after animation */
.product-list-fade-in {
  animation-fill-mode: both;
}

.product-list-fade-in:not(.loading) {
  opacity: 1;
  transform: translateY(0) scale(1);
}

/* Product list container with smooth transitions */
.product-list-container {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  contain: layout style;
  position: relative;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
}

/* Staggered fade-in for product items */
.product-item {
  opacity: 0;
  animation: productItemStaggeredFadeIn 0.6s ease-out forwards;
}

/* Extended staggered animation for more product items */
.product-item:nth-child(1) { animation-delay: 0.05s; }
.product-item:nth-child(2) { animation-delay: 0.1s; }
.product-item:nth-child(3) { animation-delay: 0.15s; }
.product-item:nth-child(4) { animation-delay: 0.2s; }
.product-item:nth-child(5) { animation-delay: 0.25s; }
.product-item:nth-child(6) { animation-delay: 0.3s; }
.product-item:nth-child(7) { animation-delay: 0.35s; }
.product-item:nth-child(8) { animation-delay: 0.4s; }
.product-item:nth-child(9) { animation-delay: 0.45s; }
.product-item:nth-child(10) { animation-delay: 0.5s; }
.product-item:nth-child(11) { animation-delay: 0.55s; }
.product-item:nth-child(12) { animation-delay: 0.6s; }
.product-item:nth-child(13) { animation-delay: 0.65s; }
.product-item:nth-child(14) { animation-delay: 0.7s; }
.product-item:nth-child(15) { animation-delay: 0.75s; }
.product-item:nth-child(16) { animation-delay: 0.8s; }
.product-item:nth-child(17) { animation-delay: 0.85s; }
.product-item:nth-child(18) { animation-delay: 0.9s; }
.product-item:nth-child(19) { animation-delay: 0.95s; }
.product-item:nth-child(20) { animation-delay: 1s; }
.product-item:nth-child(n+21) { animation-delay: 1s; }

/* Staggered fade-in animation for product items */
@keyframes productItemStaggeredFadeIn {
  from {
    opacity: 0;
    transform: translateX(-5px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Product items with smooth transitions */
.product-item {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

/* Product list entrance animation */
.product-list-entrance {
  animation: productListSlideUp 0.6s ease-out forwards;
}

@keyframes productListSlideUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Loading state for product list */
.product-list-loading {
  opacity: 0.7;
  animation: productListLoadingPulse 2s ease-in-out infinite;
}

@keyframes productListLoadingPulse {
  0%, 100% {
    opacity: 0.7;
  }
  50% {
    opacity: 0.9;
  }
}

/* Enhanced hover effects for product items */
.product-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Selected product item animation */
.product-item.selected {
  animation: productItemSelected 0.3s ease-out forwards;
}

@keyframes productItemSelected {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
  100% {
    transform: scale(1);
  }
}

/* Cart quantity badge animation */
.cart-quantity-badge {
  animation: cartBadgePop 0.3s ease-out forwards;
}

@keyframes cartBadgePop {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Product list container animations */
.product-list-container.animating {
  overflow: hidden;
}

.product-list-container.animating * {
  pointer-events: none;
}

.product-list-container.animating .product-item {
  pointer-events: auto;
} 