import 'package:json_annotation/json_annotation.dart';

part 'role.g.dart';

@JsonSerializable()
class Role {

  Role({
    required this.uuid,
    required this.name,
    required this.permissions,
    this.warehouseUuid,
    this.isDeleted,
    this.createdAt,
    this.updatedAt,
  });

  factory Role.fromJson(Map<String, dynamic> json) => _$RoleFromJson(json);
  final String uuid;
  final String name;
  final List<String> permissions;
  final String? warehouseUuid;
  final bool? isDeleted;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  Map<String, dynamic> toJson() => _$RoleToJson(this);

  /// Check if role has a specific permission
  bool hasPermission(String permission) => permissions.contains(permission);

  /// Check if role is admin
  bool get isAdmin => name == 'admin';

  /// Check if role is mobile sale agent
  bool get isMobileSaleAgent => name == 'mobile sale agent';

  /// Check if role is manager
  bool get isManager => name == 'manager';
} 