import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";

export class UpdateUserAndPasswordDto {
  @ApiPropertyOptional({
    example: "<PERSON>",
    description: "New name for the user",
  })
  name?: string;

  @ApiPropertyOptional({
    example: "oldPassword123",
    description: "Current password (required if changing password)",
  })
  oldPassword?: string;

  @ApiPropertyOptional({
    example: "newPassword456",
    description: "New password (required if changing password)",
  })
  newPassword?: string;
}
