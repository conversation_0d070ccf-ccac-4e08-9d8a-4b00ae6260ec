'use client';

import { ThemeProvider as ThemeProviderClient } from '@/contexts/ThemeContext';
import { AuthProvider } from '@/contexts/AuthContext';
import { ReactNode } from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: (failureCount, error: any) => {
        // Don't retry on 401 errors
        if (error?.response?.status === 401) {
          return false;
        }
        // Retry up to 2 times for other errors
        return failureCount < 2;
      },
      // Global error handler for queries
      onError: (error: any) => {
        if (error?.response?.status === 401) {
          console.log('Query failed with 401, clearing tokens');
          // Clear tokens on 401 errors
          localStorage.removeItem('dido_token');
          localStorage.removeItem('dido_refresh_token');
          localStorage.removeItem('dido_user');
          
          // Dispatch event to notify components
          if (typeof window !== 'undefined') {
            window.dispatchEvent(new CustomEvent('tokensCleared'));
          }
        }
      }
    },
    mutations: {
      retry: (failureCount, error: any) => {
        // Don't retry on 401 errors
        if (error?.response?.status === 401) {
          return false;
        }
        // Retry up to 1 time for other errors
        return failureCount < 1;
      },
      // Global error handler for mutations
      onError: (error: any) => {
        if (error?.response?.status === 401) {
          console.log('Mutation failed with 401, clearing tokens');
          // Clear tokens on 401 errors
          localStorage.removeItem('dido_token');
          localStorage.removeItem('dido_refresh_token');
          localStorage.removeItem('dido_user');
          
          // Dispatch event to notify components
          if (typeof window !== 'undefined') {
            window.dispatchEvent(new CustomEvent('tokensCleared'));
          }
        }
      }
    }
  }
});

export function Providers({ children }: { children: ReactNode }) {
  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <ThemeProviderClient>{children}</ThemeProviderClient>
      </AuthProvider>
    </QueryClientProvider>
  );
}
