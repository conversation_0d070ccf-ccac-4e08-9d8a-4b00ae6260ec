// Export all hooks from a central location
export { useKeyboardNavigation } from './useKeyboardNavigation';

// New centralized POS state management hooks
export { usePOSState } from './usePOSState';
export { usePOSOperations } from './usePOSOperations';
export { usePOSProducts } from './usePOSProducts';

// Re-export types for convenience
export type { POSSaleState, UsePOSStateReturn } from './usePOSState';
export type { UsePOSOperationsReturn } from './usePOSOperations';
export type { UsePOSProductsReturn } from './usePOSProducts'; 