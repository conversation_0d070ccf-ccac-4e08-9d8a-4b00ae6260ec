import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository, Like, ILike } from "typeorm";
import { Customer, CustomerType } from "./customer.entity";
import { CreateCustomerDto } from "./dto/create-customer.dto";
import { UpdateCustomerDto } from "./dto/update-customer.dto";
import { FilterCustomerDto } from "./dto/filter-customer.dto";
import {
  CustomerResponseDto,
  toCustomerResponseDto,
} from "./dto/customer-response.dto";
import { Uuid7 } from "../utils/uuid7";

@Injectable()
export class CustomerService {
  constructor(
    @InjectRepository(Customer)
    private customerRepository: Repository<Customer>,
  ) {}

  async create(
    createCustomerDto: CreateCustomerDto,
  ): Promise<CustomerResponseDto> {
    console.log('[CustomerService] create called with:', JSON.stringify(createCustomerDto, null, 2));
    
    const customer = new Customer();
    customer.id = Customer.generateId();
    customer.name = createCustomerDto.name;
    customer.fiscalId = createCustomerDto.fiscalId;
    customer.email = createCustomerDto.email;
    customer.phone = createCustomerDto.phone;
    customer.address = createCustomerDto.address;
    customer.rc = createCustomerDto.rc;
    customer.articleNumber = createCustomerDto.articleNumber;
    customer.customerType = createCustomerDto.customerType as CustomerType;
    customer.latitude = createCustomerDto.latitude;
    customer.longitude = createCustomerDto.longitude;
    customer.warehouseUuid = createCustomerDto.warehouseUuid;
    customer.regionUuid = createCustomerDto.regionUuid;
    customer.currentCredit = createCustomerDto.currentCredit || 0.0;
    customer.isDeleted = false;

    console.log('[CustomerService] customer object before save:', JSON.stringify(customer, null, 2));
    
    try {
      const saved = await this.customerRepository.save(customer);
      console.log('[CustomerService] customer saved successfully:', saved.id);
      return toCustomerResponseDto(saved);
    } catch (error) {
      console.error('[CustomerService] Error saving customer:', error);
      throw error;
    }
  }

  async findAll(
    filter?: FilterCustomerDto,
    page: number = 1,
    limit: number = 10,
  ): Promise<{
    data: Customer[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    console.log('[CustomerService] findAll called with:');
    console.log('[CustomerService] filter:', JSON.stringify(filter, null, 2));
    console.log('[CustomerService] page:', page, 'limit:', limit);
    
    const queryBuilder = this.customerRepository.createQueryBuilder('customer');

    // Base condition: not deleted
    queryBuilder.where('customer.isDeleted = :isDeleted', { isDeleted: false });

    if (filter) {
      if (filter.name) {
        queryBuilder.andWhere('customer.name ILIKE :name', { name: `%${filter.name}%` });
      }
      if (filter.email) {
        queryBuilder.andWhere('customer.email ILIKE :email', { email: `%${filter.email}%` });
      }
      if (filter.customerType) {
        queryBuilder.andWhere('customer.customerType = :customerType', { customerType: filter.customerType });
      }
      if (filter.warehouseUuid) {
        try {
          console.log(
            "CustomerService: Using warehouseUuid:",
            filter.warehouseUuid,
          );
          queryBuilder.andWhere('customer.warehouseUuid = :warehouseUuid', { warehouseUuid: filter.warehouseUuid });
        } catch (error) {
          console.error(
            "CustomerService: Error with warehouseUuid:",
            error,
          );
          // Return empty results when UUID is invalid
          return {
            data: [],
            total: 0,
            page,
            limit,
            totalPages: 0,
          };
        }
      }
      if (filter.regionUuid) {
        try {
          console.log(
            "CustomerService: Using regionUuid:",
            filter.regionUuid,
          );
          queryBuilder.andWhere('customer.regionUuid = :regionUuid', { regionUuid: filter.regionUuid });
        } catch (error) {
          console.error(
            "CustomerService: Error with regionUuid:",
            error,
          );
          return {
            data: [],
            total: 0,
            page,
            limit,
            totalPages: 0,
          };
        }
      }
    }

    // Get total count
    const total = await queryBuilder.getCount();

    // Apply pagination
    const offset = (page - 1) * limit;
    queryBuilder.skip(offset).take(limit);

    // Apply ordering
    queryBuilder.orderBy('customer.createdAt', 'DESC');

    const data = await queryBuilder.getMany();

    const totalPages = Math.ceil(total / limit);

    console.log('[CustomerService] Query results:');
    console.log('[CustomerService] Total:', total);
    console.log('[CustomerService] Data length:', data.length);
    console.log('[CustomerService] Page:', page, 'Total pages:', totalPages);

    return {
      data,
      total,
      page,
      limit,
      totalPages,
    };
  }

  async findOne(uuid: string): Promise<CustomerResponseDto> {
    const customer = await this.customerRepository.findOne({
      where: { id: uuid, isDeleted: false }
    });

    if (!customer) {
      throw new NotFoundException(`Customer with UUID ${uuid} not found`);
    }

    return toCustomerResponseDto(customer);
  }

  async getCustomerSales(
    uuid: string,
    page: number = 1,
    limit: number = 10,
  ): Promise<{
    data: any[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    // Verify customer exists
    const customer = await this.customerRepository.findOne({
      where: { id: uuid, isDeleted: false }
    });

    if (!customer) {
      throw new NotFoundException(`Customer with UUID ${uuid} not found`);
    }

    // For now, return empty sales data since we need to implement sales entity migration
    // This will be updated when sales module is migrated to TypeORM
    return {
      data: [],
      total: 0,
      page,
      limit,
      totalPages: 0,
    };
  }

  async update(
    uuid: string,
    updateDto: UpdateCustomerDto,
  ): Promise<CustomerResponseDto> {
    const customer = await this.customerRepository.findOne({
      where: { id: uuid, isDeleted: false }
    });

    if (!customer) {
      throw new NotFoundException(`Customer with UUID ${uuid} not found`);
    }

    // Update fields
    if (updateDto.name !== undefined) customer.name = updateDto.name;
    if (updateDto.fiscalId !== undefined) customer.fiscalId = updateDto.fiscalId;
    if (updateDto.email !== undefined) customer.email = updateDto.email;
    if (updateDto.phone !== undefined) customer.phone = updateDto.phone;
    if (updateDto.address !== undefined) customer.address = updateDto.address;
    if (updateDto.rc !== undefined) customer.rc = updateDto.rc;
    if (updateDto.articleNumber !== undefined) customer.articleNumber = updateDto.articleNumber;
    if (updateDto.customerType !== undefined) customer.customerType = updateDto.customerType as CustomerType;
    if (updateDto.latitude !== undefined) customer.latitude = updateDto.latitude;
    if (updateDto.longitude !== undefined) customer.longitude = updateDto.longitude;
    // warehouseUuid is intentionally excluded from updates as it cannot be changed
    if (updateDto.regionUuid !== undefined) customer.regionUuid = updateDto.regionUuid;
    if (updateDto.currentCredit !== undefined) customer.currentCredit = updateDto.currentCredit;

    const updated = await this.customerRepository.save(customer);
    return toCustomerResponseDto(updated);
  }

  async remove(uuid: string): Promise<void> {
    const customer = await this.customerRepository.findOne({
      where: { id: uuid, isDeleted: false }
    });

    if (!customer) {
      throw new NotFoundException(`Customer with UUID ${uuid} not found`);
    }

    customer.isDeleted = true;
    await this.customerRepository.save(customer);
  }

  async hardDeleteAll(): Promise<{ deletedCount: number }> {
    const result = await this.customerRepository.delete({});
    return { deletedCount: result.affected || 0 };
  }
} 