import { <PERSON><PERSON>ty, PrimaryColumn, Column, CreateDateColumn, UpdateDateColumn, Index } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';

@Entity('refresh_tokens')
export class RefreshToken {
  @ApiProperty({
    example: "uuid-v7-string",
    description: "Unique token identifier",
  })
  @PrimaryColumn('uuid')
  tokenId: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "User ID associated with the token",
  })
  @Column('uuid')
  @Index()
  userId: string;

  @ApiProperty({
    example: "<EMAIL>",
    description: "User email associated with the token",
  })
  @Column()
  userEmail: string;

  @ApiProperty({
    example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    description: "The refresh token value",
  })
  @Column()
  token: string;

  @ApiProperty({
    example: "2025-02-15T10:30:00.000Z",
    description: "Token expiration timestamp",
  })
  @Column()
  expiresAt: Date;

  @ApiProperty({
    example: "2025-01-15T10:30:00.000Z",
    description: "Token creation timestamp",
  })
  @CreateDateColumn()
  createdAt: Date;

  @ApiProperty({
    example: "2025-01-15T11:30:00.000Z",
    description: "Last time the token was used",
    required: false,
  })
  @Column({ nullable: true })
  lastUsedAt?: Date;

  @ApiProperty({
    example: false,
    description: "Whether the token has been revoked",
  })
  @Column({ default: false })
  isRevoked: boolean;

  @ApiProperty({
    example: "***********",
    description: "IP address where the token was created",
    required: false,
  })
  @Column({ nullable: true })
  ipAddress?: string;

  @ApiProperty({
    example: "Mozilla/5.0...",
    description: "User agent where the token was created",
    required: false,
  })
  @Column({ nullable: true })
  userAgent?: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "Family ID for token rotation",
    required: false,
  })
  @Column('uuid', { nullable: true })
  familyId?: string;
} 