// ordersApi.ts - API utilities for Orders endpoints
import axios from 'axios';
import { getAxiosAuthHeaders } from '@/utils/authHeaders';

const API_BASE = '/api/orders';

export interface Order {
  uuid: string;
  customerUuid: string;
  warehouseUuid: string;
  userUuid: string;
  items: OrderItem[];
  subtotal: number;
  taxAmount: number;
  totalAmount: number;
  status: 'pending' | 'processing' | 'completed' | 'cancelled';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  createdAt: string;
  updatedAt: string;
}

export interface OrderItem {
  uuid: string;
  orderUuid: string;
  productUuid: string;
  productName: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
}

export interface CreateOrderDto {
  customerUuid: string;
  warehouseUuid: string;
  userUuid: string;
  items: Array<{
    productUuid: string;
    quantity: number;
    unitPrice: number;
  }>;
  priority?: 'low' | 'medium' | 'high' | 'urgent';
}

export interface UpdateOrderDto {
  customerUuid?: string;
  priority?: 'low' | 'medium' | 'high' | 'urgent';
  status?: 'pending' | 'processing' | 'completed' | 'cancelled';
}

export interface FilterOrderDto {
  warehouseUuid?: string;
  customerUuid?: string;
  status?: string;
  priority?: string;
  createdFrom?: string;
  createdTo?: string;
}

/**
 * Create a new order
 */
export async function createOrder(data: CreateOrderDto): Promise<Order> {
  const res = await axios.post(API_BASE, data, {
    headers: getAxiosAuthHeaders(),
  });
  return res.data;
}

/**
 * Create order from quote
 */
export async function createOrderFromQuote(quoteUuid: string, userUuid: string): Promise<Order> {
  const res = await axios.post(`${API_BASE}/from-quote/${quoteUuid}`, { userUuid }, {
    headers: getAxiosAuthHeaders(),
  });
  return res.data;
}

/**
 * Add products to an existing order
 */
export async function addProductsToOrder(orderUuid: string, items: Array<{ productUuid: string; quantity: number; unitPrice: number }>): Promise<Order> {
  const res = await axios.post(`${API_BASE}/${orderUuid}/products`, { items }, {
    headers: getAxiosAuthHeaders(),
  });
  return res.data;
}

/**
 * Get all orders with optional filtering
 */
export async function getOrders(filter?: FilterOrderDto): Promise<Order[]> {
  const params = new URLSearchParams();
  if (filter?.warehouseUuid) params.append('warehouseUuid', filter.warehouseUuid);
  if (filter?.customerUuid) params.append('customerUuid', filter.customerUuid);
  if (filter?.status) params.append('status', filter.status);
  if (filter?.priority) params.append('priority', filter.priority);
  if (filter?.createdFrom) params.append('createdFrom', filter.createdFrom);
  if (filter?.createdTo) params.append('createdTo', filter.createdTo);

  const url = `${API_BASE}?${params.toString()}`;
  const res = await axios.get(url, {
    headers: getAxiosAuthHeaders(),
  });
  return res.data;
}

/**
 * Get a single order by UUID
 */
export async function getOrder(uuid: string): Promise<Order> {
  const res = await axios.get(`${API_BASE}/${uuid}`, {
    headers: getAxiosAuthHeaders(),
  });
  return res.data;
}

/**
 * Update order details
 */
export async function updateOrder(uuid: string, data: UpdateOrderDto): Promise<Order> {
  const res = await axios.patch(`${API_BASE}/${uuid}`, data, {
    headers: getAxiosAuthHeaders(),
  });
  return res.data;
}

/**
 * Update order status
 */
export async function updateOrderStatus(uuid: string, status: 'pending' | 'processing' | 'completed' | 'cancelled'): Promise<Order> {
  const res = await axios.patch(`${API_BASE}/${uuid}/status`, { status }, {
    headers: getAxiosAuthHeaders(),
  });
  return res.data;
}

/**
 * Cancel an order
 */
export async function cancelOrder(uuid: string): Promise<Order> {
  const res = await axios.patch(`${API_BASE}/${uuid}/cancel`, {}, {
    headers: getAxiosAuthHeaders(),
  });
  return res.data;
}

/**
 * Delete an order (soft delete)
 */
export async function deleteOrder(uuid: string): Promise<void> {
  await axios.delete(`${API_BASE}/${uuid}`, {
    headers: getAxiosAuthHeaders(),
  });
}

/**
 * Get orders by warehouse (non-paginated)
 */
export async function getOrdersByWarehouse(warehouseUuid: string): Promise<Order[]> {
  const res = await axios.get(`${API_BASE}/list-by-warehouse/${warehouseUuid}`, {
    headers: getAxiosAuthHeaders(),
  });
  return res.data;
} 