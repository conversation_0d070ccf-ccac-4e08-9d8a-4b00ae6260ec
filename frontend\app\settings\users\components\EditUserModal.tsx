import React, { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Dialog } from '@headlessui/react';
import { ExclamationCircleIcon } from '@heroicons/react/20/solid';
import { User } from '../usersApi';
import { VanDto } from '@/app/logistics/vans/vansApi';

const editUserSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  newPassword: z.string().min(6, 'Password must be at least 6 characters').optional().or(z.literal('')),
});

export type EditUserForm = z.infer<typeof editUserSchema>;

interface EditUserModalProps {
  isOpen: boolean;
  onClose: () => void;
  user: User | null;
  onSave: (uuid: string, data: EditUserForm) => void;
  isLoading: boolean;
  roles: { uuid: string; name: string }[];
  vans: VanDto[];
}

const EditUserModal: React.FC<EditUserModalProps> = ({ isOpen, onClose, user, onSave, isLoading, roles, vans }) => {
  const { register, handleSubmit, reset, setFocus, watch, formState: { errors } } = useForm<EditUserForm>({
    resolver: zodResolver(editUserSchema),
  });

  useEffect(() => {
    if (user) {
      reset({ name: user.name, newPassword: '' });
      setTimeout(() => setFocus('name'), 100);
    } else {
      reset();
    }
  }, [user, isOpen, reset, setFocus]);

  const handleFormSubmit = (data: EditUserForm) => {
    if (user) {
      onSave(user.uuid, data);
    }
  };

  return (
    <Dialog open={isOpen} onClose={onClose} className="relative z-50">
      <div className="fixed inset-0 bg-black/30" aria-hidden="true" />
      <div className="fixed inset-0 flex items-center justify-center p-4">
        <Dialog.Panel className="w-full max-w-sm rounded-lg bg-white p-6 shadow-xl">
          <Dialog.Title className="text-lg font-bold text-gray-900">Edit User</Dialog.Title>
          <Dialog.Description className="mt-2 text-sm text-gray-500">
            Update the user&#39;s name or set a new password.
          </Dialog.Description>

          {user && (
            <form onSubmit={handleSubmit(handleFormSubmit)} className="mt-4 space-y-4">
              <div>
                <label htmlFor="edit-name" className="block text-sm font-medium text-gray-700">Name <span className="text-red-500">*</span></label>
                <input id="edit-name" {...register('name')} placeholder="e.g. John Doe" className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm" />
                {errors.name && <p className="mt-1 flex items-center text-xs text-red-500"><ExclamationCircleIcon className="mr-1 h-4 w-4" />{errors.name.message}</p>}
              </div>
              <div>
                <label htmlFor="edit-password" className="block text-sm font-medium text-gray-700">New Password (optional)</label>
                <input id="edit-password" type="password" {...register('newPassword')} placeholder="Leave blank to keep current" className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm" />
                {errors.newPassword && <p className="mt-1 flex items-center text-xs text-red-500"><ExclamationCircleIcon className="mr-1 h-4 w-4" />{errors.newPassword.message}</p>}
              </div>
              <div className="mt-6 flex flex-col space-y-2">
                <button type="submit" disabled={isLoading} className="inline-flex w-full justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:bg-blue-300">
                  {isLoading ? 'Saving...' : 'Save Changes'}
                </button>
                <button type="button" onClick={onClose} className="inline-flex w-full justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2">
                  Cancel
                </button>
              </div>
            </form>
          )}
        </Dialog.Panel>
      </div>
    </Dialog>
  );
};

export default EditUserModal;
