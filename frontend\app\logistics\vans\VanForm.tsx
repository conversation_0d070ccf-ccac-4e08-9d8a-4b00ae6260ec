import React, { useState, useEffect } from "react";

export interface VanFormValues {
  name: string;
  licensePlate?: string;
  model?: string;
  year?: number;
}

interface VanFormErrors {
  name?: string;
  licensePlate?: string;
  model?: string;
  year?: string;
}

interface VanFormProps {
  initialValues?: VanFormValues;
  onSubmit: (values: VanFormValues) => Promise<void>;
  onCancel: () => void;
  isLoading?: boolean;
  error?: string | null;
}

const VanForm: React.FC<VanFormProps> = ({
  initialValues = { name: "", licensePlate: "", model: "", year: undefined },
  onSubmit,
  onCancel,
  isLoading = false,
  error = null,
}) => {
  const [formValues, setFormValues] = useState<VanFormValues>(initialValues);
  const [formErrors, setFormErrors] = useState<VanFormErrors>({});

  // Reset form when initialValues change
  useEffect(() => {
    setFormValues(initialValues);
    setFormErrors({});
  }, [initialValues]);

  const validateForm = (): boolean => {
    const errors: VanFormErrors = {};

    if (!formValues.name.trim()) {
      errors.name = "Name is required";
    }

    if (formValues.year && (formValues.year < 1900 || formValues.year > new Date().getFullYear() + 1)) {
      errors.year = `Year must be between 1900 and ${new Date().getFullYear() + 1}`;
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!validateForm()) return;

    const submitData: VanFormValues = {
      name: formValues.name.trim(),
      licensePlate: formValues.licensePlate?.trim() || undefined,
      model: formValues.model?.trim() || undefined,
      year: formValues.year || undefined,
    };

    try {
      await onSubmit(submitData);
    } catch (error) {
      // Error handling is done in parent component
    }
  };

  const handleChange = (field: keyof VanFormValues, value: string | number | undefined) => {
    setFormValues(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (formErrors[field as keyof VanFormErrors]) {
      setFormErrors(prev => ({ ...prev, [field as keyof VanFormErrors]: undefined }));
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      {/* Error Summary */}
      {error && (
        <div className="p-3 bg-red-50 border border-red-200 rounded-md">
          <div className="flex items-center">
            <svg className="w-5 h-5 text-red-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
            </svg>
            <span className="text-sm text-red-700">{error}</span>
          </div>
        </div>
      )}

      {/* Van Name - Required */}
      <div>
        <label htmlFor="van-name" className="block text-sm font-medium text-gray-700 mb-1">
          Van Name <span className="text-red-500">*</span>
        </label>
        <input
          id="van-name"
          type="text"
          value={formValues.name}
          onChange={(e) => handleChange("name", e.target.value)}
          placeholder="Enter van name"
          className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
            formErrors.name ? "border-red-500" : "border-gray-300"
          }`}
          disabled={isLoading}
          autoFocus
        />
        {formErrors.name && (
          <p className="mt-1 text-sm text-red-600 flex items-center">
            <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
            {formErrors.name}
          </p>
        )}
      </div>

      {/* License Plate - Optional */}
      <div>
        <label htmlFor="license-plate" className="block text-sm font-medium text-gray-700 mb-1">
          License Plate
        </label>
        <input
          id="license-plate"
          type="text"
          value={formValues.licensePlate || ""}
          onChange={(e) => handleChange("licensePlate", e.target.value)}
          placeholder="Enter license plate"
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          disabled={isLoading}
        />
      </div>

      {/* Model - Optional */}
      <div>
        <label htmlFor="model" className="block text-sm font-medium text-gray-700 mb-1">
          Model
        </label>
        <input
          id="model"
          type="text"
          value={formValues.model || ""}
          onChange={(e) => handleChange("model", e.target.value)}
          placeholder="Enter van model"
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          disabled={isLoading}
        />
      </div>

      {/* Year - Optional */}
      <div>
        <label htmlFor="year" className="block text-sm font-medium text-gray-700 mb-1">
          Year
        </label>
        <input
          id="year"
          type="number"
          value={formValues.year || ""}
          onChange={(e) => handleChange("year", e.target.value ? parseInt(e.target.value) : undefined)}
          placeholder="Enter year"
          min="1900"
          max={new Date().getFullYear() + 1}
          className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
            formErrors.year ? "border-red-500" : "border-gray-300"
          }`}
          disabled={isLoading}
        />
        {formErrors.year && (
          <p className="mt-1 text-sm text-red-600 flex items-center">
            <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
            {formErrors.year}
          </p>
        )}
      </div>

      {/* Form Actions */}
      <div className="flex flex-col space-y-3 pt-4">
        <button
          type="submit"
          disabled={isLoading}
          className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
        >
          {isLoading ? (
            <>
              <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Processing...
            </>
          ) : (
            "Save Van"
          )}
        </button>
        <button
          type="button"
          onClick={onCancel}
          disabled={isLoading}
          className="w-full bg-gray-100 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Cancel
        </button>
      </div>
    </form>
  );
};

export default VanForm; 