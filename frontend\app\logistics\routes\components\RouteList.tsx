"use client";

import React from "react";
import { Route } from "../api";
import { Route as RouteIcon, MapPin, Clock, Calendar, Trash2, Eye } from "lucide-react";

interface RouteListProps {
  routes: Route[];
  currentRoute: Route | null;
  onRouteSelect: (route: Route) => void;
  onRouteDelete?: (route: Route) => void;
}

const RouteList: React.FC<RouteListProps> = ({
  routes,
  currentRoute,
  onRouteSelect,
  onRouteDelete
}) => {
  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const formatTime = (date: Date) => {
    return new Date(date).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (routes.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="text-center">
          <RouteIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Routes Found</h3>
          <p className="text-gray-500">
            Create your first route by selecting customers and computing an optimized path.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      <div className="px-6 py-4 border-b border-gray-200">
        <h3 className="text-lg font-medium text-gray-900">Existing Routes</h3>
        <p className="text-sm text-gray-500">{routes.length} route{routes.length !== 1 ? 's' : ''} found</p>
      </div>
      
      <div className="divide-y divide-gray-200 max-h-96 overflow-y-auto">
        {routes.map((route) => {
          const isSelected = currentRoute?.uuid === route.uuid;
          
          return (
            <div
              key={route.uuid}
              className={`p-4 hover:bg-gray-50 transition-colors cursor-pointer ${
                isSelected ? 'bg-blue-50 border-l-4 border-blue-500' : ''
              }`}
              onClick={() => onRouteSelect(route)}
            >
              <div className="flex items-start justify-between">
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2 mb-2">
                    <RouteIcon className="w-4 h-4 text-blue-600" />
                    <h4 className="text-sm font-medium text-gray-900 truncate">
                      {route.name}
                    </h4>
                    {isSelected && (
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        Active
                      </span>
                    )}
                  </div>
                  
                  {route.description && (
                    <p className="text-sm text-gray-500 mb-3 line-clamp-2">
                      {route.description}
                    </p>
                  )}
                  
                  <div className="flex items-center space-x-4 text-xs text-gray-500">
                    <div className="flex items-center space-x-1">
                      <MapPin className="w-3 h-3" />
                      <span>{route.optimizedRoute.length} stops</span>
                    </div>
                    
                    <div className="flex items-center space-x-1">
                      <span className="w-1 h-1 bg-gray-400 rounded-full"></span>
                      <span>{route.totalDistance.toFixed(1)} km</span>
                    </div>
                    
                    <div className="flex items-center space-x-1">
                      <Calendar className="w-3 h-3" />
                      <span>{formatDate(route.createdAt)}</span>
                    </div>
                    
                    <div className="flex items-center space-x-1">
                      <Clock className="w-3 h-3" />
                      <span>{formatTime(route.createdAt)}</span>
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center space-x-2 ml-4">
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      onRouteSelect(route);
                    }}
                    className="p-2 rounded-lg hover:bg-gray-100 transition-colors"
                    title="View Route"
                  >
                    <Eye className="w-4 h-4 text-gray-600" />
                  </button>
                  
                  {onRouteDelete && (
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        onRouteDelete(route);
                      }}
                      className="p-2 rounded-lg hover:bg-red-100 transition-colors"
                      title="Delete Route"
                    >
                      <Trash2 className="w-4 h-4 text-red-600" />
                    </button>
                  )}
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default RouteList; 