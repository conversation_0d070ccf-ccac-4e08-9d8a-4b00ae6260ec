.taskBar {
  --transition: all 0.2s ease-in-out;
  --radius: var(--radius-medium);

  display: flex;
  flex-direction: row;
  width: auto;
  height: 100vh;
  background: var(--color-surface);
  color: var(--color-textPrimary);
  flex-shrink: 0;
  box-shadow: var(--shadow-small);
}

/* Primary Navigation Column */
.primaryNavContainer {
  display: flex;
  flex-direction: column;
  width: 7.5rem;
  flex-shrink: 0;
  background: var(--color-surface);
  border-right: 1px solid var(--color-border);
  overflow-y: auto;
}

/* Secondary Navigation Column (Sub-items) */
.secondaryNavContainer {
  display: flex;
  flex-direction: column;
  width: 9.5rem;
  flex-shrink: 0;
  background: var(--color-background);
  border-right: 1px solid var(--color-border);
  overflow-y: auto;
}

.secondaryHeader {
  padding: 1.75rem 1rem 1.5rem;
  background: var(--color-background);
  position: sticky;
  top: 0;
  z-index: 10;
}

.activeItemName {
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--color-primary);
  margin: 0 0 0.5rem;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.headerDivider {
  height: 1px;
  background-color: var(--color-border);
  margin: 0.25rem 0 0.5rem;
}

/* Always visible elements - Removed opacity/transform/transition from base styles */
.logoText,
.label,
.chevron,
.profileInfo,
.logoutButton {
  opacity: 1;
  transform: translateX(0);
  transition: none; /* No transitions on visibility/position for these elements */
  transition-delay: 0s;
}

.logo {
  display: flex;
  align-items: center;
  padding: 1rem 0.5rem;
  gap: 0.75rem;
  border-bottom: 1px solid var(--color-border);
  background: var(--color-surface);
  z-index: 10;
}

.logoIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  background: var(--color-primary);
  color: white;
  border-radius: var(--radius);
  font-size: 1rem;
  font-weight: 700;
  flex-shrink: 0;
}

.logoText {
  font-size: 1.25rem;
  font-weight: 700;
  white-space: nowrap;
  color: var(--text);
}

/* Main Navigation (Primary Column) */
.primaryNav,
.secondaryNav { /* Both navs share similar flex and scrollbar properties */
  flex: 1;
  overflow-y: auto;
  padding: 0.75rem 0.25rem;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  scrollbar-width: thin;
  scrollbar-color: var(--color-border) transparent;
}

.secondaryNav {
  flex: 1;
  overflow-y: auto;
  padding: 0 0.25rem 0.5rem;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

/* Scrollbar styles for both primary and secondary navs */
.primaryNav::-webkit-scrollbar,
.secondaryNav::-webkit-scrollbar {
  width: 4px;
}

.primaryNav::-webkit-scrollbar-track,
.secondaryNav::-webkit-scrollbar-track {
  background: transparent;
}

.primaryNav::-webkit-scrollbar-thumb,
.secondaryNav::-webkit-scrollbar-thumb {
  background-color: var(--color-border);
  border-radius: 20px;
}

.primaryNav::-webkit-scrollbar-thumb:hover,
.secondaryNav::-webkit-scrollbar-thumb:hover {
  background: var(--color-primary);
}

.navItem {
  position: relative;
}

.navButton { /* Styles for primary items in the first column */
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: 0.75rem 0.5rem;
  border-radius: var(--radius);
  color: var(--color-textSecondary);
  transition: var(--transition);
  background: transparent;
  border: none;
  cursor: pointer;
  text-align: center;
  gap: 0.25rem;
  position: relative;
  font-size: 0.75rem;
  font-weight: 500;
  overflow: hidden;
  margin: 0.125rem 0;
  min-height: 4rem;
}

.navButton::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 3px;
  background: var(--primary);
  transform: scaleY(0);
  transform-origin: center;
  transition: var(--transition);
  border-radius: 0 4px 4px 0;
}

.navButton:hover {
  background: var(--color-hover);
  color: var(--color-textPrimary);
}

.navButton.active {
  color: var(--color-primary);
  background: var(--color-active);
  font-weight: 600;
}

.navButton.active .icon {
  color: var(--color-primary);
}

.navButton.active::before {
  transform: scaleY(1);
}

.icon {
  font-size: 1.25rem;
  width: 1.5rem;
  height: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  color: var(--text-muted);
  margin-bottom: 0.25rem;
}

.label {
  white-space: nowrap;
  text-align: center;
  line-height: 1.2;
  font-size: 0.7rem;
  padding: 0 0.25rem;
}

.chevron {
  margin-left: auto;
  transition: transform 0.2s ease; /* Keep transition for rotation */
  font-size: 0.9em;
}

.rotated {
  transform: rotate(90deg);
}

/* Secondary Navigation Items (Sub-items in the second column) */
.subNavItem { /* Wrapper for sub-item and its nested sub-menu */
  position: relative;
}

.subNavLink { /* Styles for items in the secondary column */
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: 0.75rem 0.5rem;
  min-height: 4rem;
  text-align: center;
  border-radius: var(--radius);
  color: var(--color-textSecondary);
  transition: var(--transition);
  background: transparent;
  border: none;
  cursor: pointer;
  text-align: center;
  gap: 0.75rem;
  position: relative;
  font-size: 0.9375rem;
  font-weight: 500;
  overflow: hidden;
  margin: 0.125rem 0;
}

.subNavLink::before { /* Active indicator for secondary items */
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 3px;
  background: var(--color-primary);
  transform: scaleY(0);
  transform-origin: center;
  transition: var(--transition);
  border-radius: 0 4px 4px 0;
}

.subNavLink:hover {
  background: var(--color-hover);
  color: var(--color-textPrimary);
}

.subNavLink.active {
  color: var(--color-primary);
  background: var(--color-active);
  font-weight: 600;
}

.subNavLink.active .subIcon {
  color: var(--color-primary);
}

.subNavLink.active::before {
  transform: scaleY(1);
}

.subIcon { /* Icon for secondary nav items */
  font-size: 1.1rem; /* Same as main icon for consistency */
  width: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  color: var(--color-textSecondary);
}






/* Footer for Primary Nav (Always expanded) */
.footer {
  margin-top: auto;
  padding: 0.5rem;
  border-top: 1px solid var(--color-border);
  background: var(--color-surface);
  position: sticky;
  bottom: 0;
  z-index: 20;
}

.profile {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
  text-align: center;
}

.avatar {
  width: 1.5rem;
  height: 1.5rem;
  border-radius: 50%;
  background: var(--color-hover);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  font-size: 0.8rem;
}

.profileInfo {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.1rem;
  width: 100%;
}

.userName {
  font-size: 0.6rem;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
}

.logoutButton {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.25rem;
  background: none;
  border: none;
  color: var(--color-textSecondary);
  font-size: 0.6rem;
  cursor: pointer;
  padding: 0.1rem 0.2rem;
  border-radius: 0.2rem;
  transition: var(--transition);
  width: 100%;
}

.logoutButton:hover {
  background: var(--color-hover);
  color: var(--color-primary);
}

/* Ensure the main nav content doesn't get hidden behind the footer */
.primaryNav {
  padding-bottom: 4rem; /* Make space for the footer */
  overflow-y: auto;
}

/* Animations (only for nested sub-menus within the second column) */
@keyframes fadeIn { /* Kept for reference but not used for profile/footer */
  from {
    opacity: 0;
    transform: translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}
