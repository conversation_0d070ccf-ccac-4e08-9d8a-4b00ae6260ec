import { NextRequest, NextResponse } from 'next/server';

const BACKEND_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000';

export async function GET(request: NextRequest) {
  const pathSegments = request.nextUrl.pathname.split('/').filter(Boolean);
  const apiPath = pathSegments.slice(1).join('/'); // Remove 'api' prefix
  const url = new URL(request.url);
  const backendUrl = `${BACKEND_URL}/${apiPath}${url.search}`;
  
  // Forward Authorization header for all endpoints
  const headers: HeadersInit = {
    'Content-Type': 'application/json',
  };
  
  const authHeader = request.headers.get('authorization');
  if (authHeader) {
    headers['Authorization'] = authHeader;
  }
  
  try {
    // Special handling for auth/health endpoint
    if (apiPath === 'auth/health') {
      console.log('[API Proxy] Health check request to backend:', backendUrl);
      
      const response = await fetch(backendUrl, {
        method: 'GET',
        headers,
        signal: AbortSignal.timeout(5000), // 5 second timeout for health checks
      });
      
      if (response.ok) {
        const data = await response.json();
        console.log('[API Proxy] Health check successful:', data);
        return NextResponse.json(data, { status: response.status });
      } else {
        console.error('[API Proxy] Health check failed:', response.status, response.statusText);
        return NextResponse.json(
          { error: 'Backend health check failed', status: response.status },
          { status: response.status }
        );
      }
    }
    
    const response = await fetch(backendUrl, {
      method: 'GET',
      headers,
    });
    
    const data = await response.json();
    return NextResponse.json(data, { status: response.status });
  } catch (error) {
    console.error('API proxy error:', error);
    
    // Special error handling for health checks
    if (apiPath === 'auth/health') {
      console.error('[API Proxy] Health check connection error:', error);
      return NextResponse.json(
        { 
          error: 'Backend connection failed', 
          message: error instanceof Error ? error.message : 'Unknown error',
          timestamp: new Date().toISOString()
        }, 
        { status: 503 }
      );
    }
    
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  const pathSegments = request.nextUrl.pathname.split('/').filter(Boolean);
  const apiPath = pathSegments.slice(1).join('/'); // Remove 'api' prefix
  const backendUrl = `${BACKEND_URL}/${apiPath}`;
  
  try {
    const body = await request.text();
    
    // Log sales-related requests for debugging
    if (apiPath.includes('sales')) {
      console.log(`[API Proxy] POST ${apiPath}`);
      console.log(`[API Proxy] Request body:`, body);
    }
    
    // Log customer-related requests for debugging
    if (apiPath.includes('customers')) {
      console.log(`[API Proxy] POST ${apiPath}`);
      console.log(`[API Proxy] Request body:`, body);
      console.log(`[API Proxy] Auth header:`, request.headers.get('authorization') ? 'Present' : 'Missing');
    }
    
    // Forward Authorization header for all endpoints
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    };
    
    const authHeader = request.headers.get('authorization');
    if (authHeader) {
      headers['Authorization'] = authHeader;
    }
    
    const response = await fetch(backendUrl, {
      method: 'POST',
      headers,
      body,
    });
    
    const responseData = await response.text();
    
    // Log sales-related responses for debugging
    if (apiPath.includes('sales')) {
      console.log(`[API Proxy] Response status:`, response.status);
      console.log(`[API Proxy] Response body:`, responseData);
    }
    
    // Log customer-related responses for debugging
    if (apiPath.includes('customers')) {
      console.log(`[API Proxy] Response status:`, response.status);
      console.log(`[API Proxy] Response body:`, responseData);
    }
    
    if (!response.ok) {
      // Log error details for sales endpoints
      if (apiPath.includes('sales')) {
        console.error(`[API Proxy] Error ${response.status} for ${apiPath}`);
        console.error(`[API Proxy] Error response:`, responseData);
      }
      
      // Log error details for customer endpoints
      if (apiPath.includes('customers')) {
        console.error(`[API Proxy] Error ${response.status} for ${apiPath}`);
        console.error(`[API Proxy] Error response:`, responseData);
      }
      return new Response(responseData, {
        status: response.status,
        headers: { 'Content-Type': 'application/json' },
      });
    }
    
    return new Response(responseData, {
      status: response.status,
      headers: { 'Content-Type': 'application/json' },
    });
  } catch (error) {
    console.error('[API Proxy] Error:', error);
    return new Response(JSON.stringify({ error: 'Internal Server Error' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
}

export async function PUT(request: NextRequest) {
  const pathSegments = request.nextUrl.pathname.split('/').filter(Boolean);
  const apiPath = pathSegments.slice(1).join('/'); // Remove 'api' prefix
  const backendUrl = `${BACKEND_URL}/${apiPath}`;
  
  try {
    const body = await request.text();
    
    // Log sales-related requests for debugging
    if (apiPath.includes('sales')) {
      console.log(`[API Proxy] PUT ${apiPath}`);
      console.log(`[API Proxy] Request body:`, body);
    }
    
    // Log products-related requests for debugging
    if (apiPath.includes('products')) {
      console.log(`[API Proxy] PUT ${apiPath}`);
      console.log(`[API Proxy] Request body:`, body);
      console.log(`[API Proxy] Backend URL: ${backendUrl}`);
      console.log(`[API Proxy] Request headers:`, Object.fromEntries(request.headers.entries()));
    }
    
    // Forward Authorization header for all endpoints
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    };
    
    const authHeader = request.headers.get('authorization');
    if (authHeader) {
      headers['Authorization'] = authHeader;
    }
    
    const response = await fetch(backendUrl, {
      method: 'PUT',
      headers,
      body,
    });
    
    const responseData = await response.text();
    
    // Log sales-related responses for debugging
    if (apiPath.includes('sales')) {
      console.log(`[API Proxy] Response status:`, response.status);
      console.log(`[API Proxy] Response body:`, responseData);
    }
    
    // Log products-related responses for debugging
    if (apiPath.includes('products')) {
      console.log(`[API Proxy] Response status:`, response.status);
      console.log(`[API Proxy] Response headers:`, Object.fromEntries(response.headers.entries()));
      console.log(`[API Proxy] Response body:`, responseData);
    }
    
    if (!response.ok) {
      // Log error details for sales endpoints
      if (apiPath.includes('sales')) {
        console.error(`[API Proxy] Error ${response.status} for ${apiPath}`);
        console.error(`[API Proxy] Error response:`, responseData);
      }
      
      // Log error details for products endpoints
      if (apiPath.includes('products')) {
        console.error(`[API Proxy] Error ${response.status} for ${apiPath}`);
        console.error(`[API Proxy] Backend URL that failed:`, backendUrl);
        console.error(`[API Proxy] Error response:`, responseData);
      }
      
      return new Response(responseData, {
        status: response.status,
        headers: { 'Content-Type': 'application/json' },
      });
    }
    
    return new Response(responseData, {
      status: response.status,
      headers: { 'Content-Type': 'application/json' },
    });
  } catch (error) {
    console.error('[API Proxy] Error:', error);
    return new Response(JSON.stringify({ error: 'Internal Server Error' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
}

export async function DELETE(request: NextRequest) {
  const pathSegments = request.nextUrl.pathname.split('/').filter(Boolean);
  const apiPath = pathSegments.slice(1).join('/'); // Remove 'api' prefix
  const url = new URL(request.url);
  const backendUrl = `${BACKEND_URL}/${apiPath}${url.search}`;
  
  // Forward Authorization header for all endpoints
  const headers: HeadersInit = {
    'Content-Type': 'application/json',
  };
  
  const authHeader = request.headers.get('authorization');
  if (authHeader) {
    headers['Authorization'] = authHeader;
  }
  
  try {
    const response = await fetch(backendUrl, {
      method: 'DELETE',
      headers,
    });
    
    const responseData = await response.text();
    
    if (!response.ok) {
      return new Response(responseData, {
        status: response.status,
        headers: { 'Content-Type': 'application/json' },
      });
    }
    
    return new Response(responseData, {
      status: response.status,
      headers: { 'Content-Type': 'application/json' },
    });
  } catch (error) {
    console.error('[API Proxy] Error:', error);
    return new Response(JSON.stringify({ error: 'Internal Server Error' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
}

export async function PATCH(request: NextRequest) {
  const pathSegments = request.nextUrl.pathname.split('/').filter(Boolean);
  const apiPath = pathSegments.slice(1).join('/'); // Remove 'api' prefix
  const backendUrl = `${BACKEND_URL}/${apiPath}`;
  
  try {
    const body = await request.text();
    
    // Log sales-related requests for debugging
    if (apiPath.includes('sales')) {
      console.log(`[API Proxy] PATCH ${apiPath}`);
      console.log(`[API Proxy] Backend URL: ${backendUrl}`);
      console.log(`[API Proxy] Request body:`, body);
      console.log(`[API Proxy] Request headers:`, Object.fromEntries(request.headers.entries()));
    }
    
    // Forward Authorization header for all endpoints
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    };
    
    const authHeader = request.headers.get('authorization');
    if (authHeader) {
      headers['Authorization'] = authHeader;
    }
    
    console.log(`[API Proxy] Making PATCH request to backend: ${backendUrl}`);
    console.log(`[API Proxy] Backend request headers:`, headers);
    
    const response = await fetch(backendUrl, {
      method: 'PATCH',
      headers,
      body,
    });
    
    const responseData = await response.text();
    
    // Log sales-related responses for debugging
    if (apiPath.includes('sales')) {
      console.log(`[API Proxy] Response status:`, response.status);
      console.log(`[API Proxy] Response headers:`, Object.fromEntries(response.headers.entries()));
      console.log(`[API Proxy] Response body:`, responseData);
    }
    
    if (!response.ok) {
      // Log error details for sales endpoints
      if (apiPath.includes('sales')) {
        console.error(`[API Proxy] Error ${response.status} for ${apiPath}`);
        console.error(`[API Proxy] Error response:`, responseData);
        console.error(`[API Proxy] Backend URL that failed:`, backendUrl);
      }
      return new Response(responseData, {
        status: response.status,
        headers: { 'Content-Type': 'application/json' },
      });
    }
    
    return new Response(responseData, {
      status: response.status,
      headers: { 'Content-Type': 'application/json' },
    });
  } catch (error) {
    console.error('[API Proxy] Error:', error);
    if (apiPath.includes('sales')) {
      console.error('[API Proxy] Sales endpoint error details:', {
        apiPath,
        backendUrl,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
    return new Response(JSON.stringify({ error: 'Internal Server Error' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
} 