// useCustomerPayments.ts - React Query hooks for customer payments endpoints
// Follows project convention: All data fetching uses React Query hooks
// Based on customerPaymentsApi.ts functions

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  getCustomerPayments,
  getCustomerPaymentByUuid,
  createCustomerPayment,
  updateCustomerPayment,
  deleteCustomerPayment,
  filterCustomerPayments,
  getPaymentsByCustomer,
  refundCustomerPayment,
  cancelCustomerPayment,
  getCustomerPaymentHistory,
  getCustomerCredit,
  getCustomerSales,
  getCustomerWithCredit,
  CustomerPayment,
  Customer,
  CustomerSale,
  CreateCustomerPaymentDto,
  UpdateCustomerPaymentDto,
  FilterCustomerPaymentDto,
  PaginationQueryDto,
  PaginatedResponseDto,
  RefundResponse
} from './customerPaymentsApi';

// Hook for getting paginated customer payments with filtering
export function useCustomerPayments(
  paginationQuery?: PaginationQueryDto,
  filter?: FilterCustomerPaymentDto
) {
  return useQuery({
    queryKey: ['customer-payments', paginationQuery, filter],
    queryFn: () => getCustomerPayments(paginationQuery, filter),
    staleTime: 1000 * 60 * 5, // 5 minutes
  });
}

// Hook for getting a single customer payment by UUID
export function useCustomerPayment(uuid: string, enabled: boolean = true) {
  return useQuery({
    queryKey: ['customer-payment', uuid],
    queryFn: () => getCustomerPaymentByUuid(uuid),
    enabled: enabled && !!uuid,
    staleTime: 1000 * 60 * 5, // 5 minutes
  });
}

// Hook for getting payments by customer
export function usePaymentsByCustomer(
  customerUuid: string,
  page: number = 1,
  limit: number = 10,
  enabled: boolean = true
) {
  return useQuery({
    queryKey: ['customer-payments', 'by-customer', customerUuid, page, limit],
    queryFn: () => getPaymentsByCustomer(customerUuid, page, limit),
    enabled: enabled && !!customerUuid,
    staleTime: 1000 * 60 * 5, // 5 minutes
  });
}

// Hook for advanced filtering of customer payments
export function useFilterCustomerPayments(
  filterDto: FilterCustomerPaymentDto,
  enabled: boolean = false
) {
  return useQuery({
    queryKey: ['customer-payments', 'filter', filterDto],
    queryFn: () => filterCustomerPayments(filterDto),
    enabled: enabled && !!filterDto,
    staleTime: 1000 * 60 * 5, // 5 minutes
  });
}

// Hook for creating a customer payment
export function useCreateCustomerPayment() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: createCustomerPayment,
    onSuccess: (newPayment) => {
      // Invalidate and refetch customer payments list
      queryClient.invalidateQueries({ queryKey: ['customer-payments'] });
      
      // Update specific customer payments if we have the customer UUID
      if (newPayment.customerUuid) {
        queryClient.invalidateQueries({ 
          queryKey: ['customer-payments', 'by-customer', newPayment.customerUuid] 
        });
      }
      
      // Also invalidate customers list to update credit balances
      queryClient.invalidateQueries({ queryKey: ['customers'] });
    },
    onError: (error) => {
      console.error('Failed to create customer payment:', error);
    },
  });
}

// Hook for updating a customer payment
export function useUpdateCustomerPayment() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ uuid, data }: { uuid: string; data: UpdateCustomerPaymentDto }) =>
      updateCustomerPayment(uuid, data),
    onSuccess: (updatedPayment, variables) => {
      // Update the specific payment in cache
      queryClient.setQueryData(['customer-payment', variables.uuid], updatedPayment);
      
      // Invalidate payments list
      queryClient.invalidateQueries({ queryKey: ['customer-payments'] });
      
      // Update customer payments by customer
      if (updatedPayment.customerUuid) {
        queryClient.invalidateQueries({ 
          queryKey: ['customer-payments', 'by-customer', updatedPayment.customerUuid] 
        });
      }
      
      // Also invalidate customers list to update credit balances if amount changed
      if (variables.data.amount !== undefined) {
        queryClient.invalidateQueries({ queryKey: ['customers'] });
      }
    },
    onError: (error) => {
      console.error('Failed to update customer payment:', error);
    },
  });
}

// Hook for deleting a customer payment
export function useDeleteCustomerPayment() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: deleteCustomerPayment,
    onSuccess: (_, deletedUuid) => {
      // Remove from cache
      queryClient.removeQueries({ queryKey: ['customer-payment', deletedUuid] });
      
      // Invalidate payments lists
      queryClient.invalidateQueries({ queryKey: ['customer-payments'] });
      
      // Also invalidate customers list to update credit balances
      queryClient.invalidateQueries({ queryKey: ['customers'] });
    },
    onError: (error) => {
      console.error('Failed to delete customer payment:', error);
    },
  });
}

// Hook for refunding a customer payment
export function useRefundCustomerPayment() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ uuid, userUuid, reason }: { uuid: string; userUuid: string; reason: string }) =>
      refundCustomerPayment(uuid, userUuid, reason),
    onSuccess: (refundResponse, variables) => {
      // Update the specific payment in cache
      queryClient.setQueryData(['customer-payment', variables.uuid], refundResponse.payment);
      
      // Invalidate payments lists
      queryClient.invalidateQueries({ queryKey: ['customer-payments'] });
      
      // Update customer payments by customer
      if (refundResponse.payment.customerUuid) {
        queryClient.invalidateQueries({ 
          queryKey: ['customer-payments', 'by-customer', refundResponse.payment.customerUuid] 
        });
      }
      
      // Invalidate customers list to update credit balances
      queryClient.invalidateQueries({ queryKey: ['customers'] });
    },
    onError: (error) => {
      console.error('Failed to refund customer payment:', error);
    },
  });
}

// Hook for cancelling a customer payment
export function useCancelCustomerPayment() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ uuid, userUuid }: { uuid: string; userUuid: string }) =>
      cancelCustomerPayment(uuid, userUuid),
    onSuccess: (cancelledPayment, variables) => {
      // Update the specific payment in cache
      queryClient.setQueryData(['customer-payment', variables.uuid], cancelledPayment);
      
      // Invalidate payments lists
      queryClient.invalidateQueries({ queryKey: ['customer-payments'] });
      
      // Update customer payments by customer
      if (cancelledPayment.customerUuid) {
        queryClient.invalidateQueries({ 
          queryKey: ['customer-payments', 'by-customer', cancelledPayment.customerUuid] 
        });
      }
      
      // Invalidate customers list to update credit balances
      queryClient.invalidateQueries({ queryKey: ['customers'] });
    },
    onError: (error) => {
      console.error('Failed to cancel customer payment:', error);
    },
  });
}

// Combined hook for all payment actions (similar to useCustomerActions pattern)
export function useCustomerPaymentActions() {
  const createMutation = useCreateCustomerPayment();
  const updateMutation = useUpdateCustomerPayment();
  const deleteMutation = useDeleteCustomerPayment();
  const refundMutation = useRefundCustomerPayment();
  const cancelMutation = useCancelCustomerPayment();

  return {
    // Create
    createPayment: createMutation.mutateAsync,
    isCreating: createMutation.isPending,
    createError: createMutation.error,

    // Update
    updatePayment: updateMutation.mutateAsync,
    isUpdating: updateMutation.isPending,
    updateError: updateMutation.error,

    // Delete
    deletePayment: deleteMutation.mutateAsync,
    isDeleting: deleteMutation.isPending,
    deleteError: deleteMutation.error,

    // Refund
    refundPayment: refundMutation.mutateAsync,
    isRefunding: refundMutation.isPending,
    refundError: refundMutation.error,

    // Cancel
    cancelPayment: cancelMutation.mutateAsync,
    isCancelling: cancelMutation.isPending,
    cancelError: cancelMutation.error,

    // Overall loading state
    isLoading: createMutation.isPending || updateMutation.isPending || 
               deleteMutation.isPending || refundMutation.isPending || 
               cancelMutation.isPending,
  };
}

// Customer-related hooks using latest customer controller endpoints

// Hook for getting customer payment history via customer controller
export function useCustomerPaymentHistory(
  customerUuid?: string,
  page: number = 1,
  limit: number = 10
) {
  return useQuery({
    queryKey: ['customer-payment-history', customerUuid, page, limit],
    queryFn: () => getCustomerPaymentHistory(customerUuid!, page, limit),
    enabled: !!customerUuid,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Hook for getting customer credit balance
export function useCustomerCredit(customerUuid?: string) {
  return useQuery({
    queryKey: ['customer-credit', customerUuid],
    queryFn: () => getCustomerCredit(customerUuid!),
    enabled: !!customerUuid,
    staleTime: 2 * 60 * 1000, // 2 minutes - credit balance should be relatively fresh
  });
}

// Hook for getting customer sales history
export function useCustomerSales(
  customerUuid?: string,
  page: number = 1,
  limit: number = 10
) {
  return useQuery({
    queryKey: ['customer-sales', customerUuid, page, limit],
    queryFn: () => getCustomerSales(customerUuid!, page, limit),
    enabled: !!customerUuid,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Hook for getting customer details with credit info
export function useCustomerWithCredit(customerUuid?: string) {
  return useQuery({
    queryKey: ['customer-with-credit', customerUuid],
    queryFn: () => getCustomerWithCredit(customerUuid!),
    enabled: !!customerUuid,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
} 