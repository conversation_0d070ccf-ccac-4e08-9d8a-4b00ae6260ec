{"name": "dido-distribution-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.1.1", "@mui/material": "^7.1.1", "@tanstack/react-query": "^5.80.7", "@types/leaflet": "^1.9.20", "@types/qrcode": "^1.5.5", "axios": "^1.10.0", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "leaflet": "^1.9.4", "lucide-react": "^0.525.0", "next": "^14.2.30", "qrcode": "^1.5.4", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.58.0", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-leaflet": "^4.2.1", "sonner": "^2.0.5", "zod": "^3.25.67"}, "devDependencies": {"@types/node": "^20.11.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "autoprefixer": "^10.4.17", "eslint": "^8.56.0", "eslint-config-next": "14.1.0", "postcss": "^8.4.35", "prettier": "^3.2.0", "tailwindcss": "^3.4.1", "typescript": "^5.3.3"}}