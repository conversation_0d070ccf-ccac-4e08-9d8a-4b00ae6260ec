import { SideTaskBar } from '@/components/SideTaskBar/SideTaskBar';
import TopTaskBar from '@/components/TopTaskBar/TopTaskBar';

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="flex h-screen bg-gray-50">
      <SideTaskBar />
      <div className="flex-1 flex flex-col min-h-0">
        <TopTaskBar />
        <main className="flex-1 overflow-auto">
          {children}
        </main>
      </div>
    </div>
  );
}
