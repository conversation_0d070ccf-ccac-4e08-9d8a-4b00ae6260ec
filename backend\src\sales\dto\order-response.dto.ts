import { ApiProperty } from "@nestjs/swagger";
import {
  OrderStatus,
  OrderPriority,
} from "../order.entity";

export class OrderItemSnapshotResponseDto {
  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the product",
  })
  productUuid: string;

  @ApiProperty({ example: "Product Name", description: "Product name" })
  name: string;

  @ApiProperty({ example: 10, description: "Quantity ordered" })
  quantity: number;

  @ApiProperty({ example: 25.5, description: "Unit price at time of order" })
  unitPrice: number;

  @ApiProperty({
    example: 255.0,
    description: "Line total (quantity * unitPrice)",
  })
  lineTotal: number;

  @ApiProperty({ example: 25.5, description: "Tax amount for this line item" })
  taxAmount: number;

  @ApiProperty({
    example: "Special instructions for this item",
    description: "Item-specific notes",
    required: false,
  })
  notes?: string;
}

export class OrderResponseDto {
  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the order",
  })
  uuid: string;

  @ApiProperty({ example: "ORD-1640995200000", description: "Order number" })
  orderNumber: string;

  @ApiProperty({
    example: "Customer Name",
    description: "Customer name at time of order",
    required: false,
  })
  customerName?: string;

  @ApiProperty({
    example: "12345678901",
    description: "Customer fiscal ID at time of order",
    required: false,
  })
  customerFiscalId?: string;

  @ApiProperty({
    example: "RC123456",
    description: "Customer RC at time of order",
    required: false,
  })
  customerRc?: string;

  @ApiProperty({
    example: "ART001",
    description: "Customer article number at time of order",
    required: false,
  })
  customerArticleNumber?: string;

  @ApiProperty({
    type: [OrderItemSnapshotResponseDto],
    description: "Items in the order",
  })
  itemsSnapshot: OrderItemSnapshotResponseDto[];

  @ApiProperty({ example: 150.0, description: "Subtotal amount before tax" })
  subtotal: number;

  @ApiProperty({
    example: true,
    description: "Whether tax is applied to this order",
  })
  useTax: boolean;

  @ApiProperty({
    example: 0.1,
    description: "Tax rate as decimal (e.g., 0.1 for 10%)",
  })
  taxRate: number;

  @ApiProperty({ example: 15.0, description: "Total tax amount" })
  taxAmount: number;

  @ApiProperty({ example: 165.0, description: "Total amount including tax" })
  totalAmount: number;

  @ApiProperty({
    example: "2024-01-15T10:00:00.000Z",
    description: "Order date",
  })
  orderDate: Date;

  @ApiProperty({
    example: "2024-01-20T10:00:00.000Z",
    description: "Requested delivery date",
    required: false,
  })
  requestedDeliveryDate?: Date;

  @ApiProperty({
    example: "2024-01-22T14:30:00.000Z",
    description: "Actual delivery date",
    required: false,
  })
  actualDeliveryDate?: Date;

  @ApiProperty({
    example: OrderStatus.PROCESSING,
    description: "Order status",
    enum: OrderStatus,
  })
  status: OrderStatus;

  @ApiProperty({
    example: OrderPriority.NORMAL,
    description: "Order priority level",
    enum: OrderPriority,
  })
  priority: OrderPriority;

  @ApiProperty({
    example: "Special delivery instructions",
    description: "Order notes",
    required: false,
  })
  notes?: string;

  @ApiProperty({
    example: "2024-01-15T10:00:00.000Z",
    description: "Creation timestamp",
  })
  createdAt: Date;

  @ApiProperty({
    example: "2024-01-15T11:00:00.000Z",
    description: "Last update timestamp",
  })
  updatedAt: Date;
}

export class OrdersListResponseDto {
  @ApiProperty({ type: [OrderResponseDto], description: "Array of orders" })
  orders: OrderResponseDto[];

  @ApiProperty({ example: 1, description: "Current page number" })
  page: number;

  @ApiProperty({ example: 10, description: "Items per page" })
  limit: number;

  @ApiProperty({ example: 50, description: "Total number of orders" })
  total: number;

  @ApiProperty({ example: 5, description: "Total number of pages" })
  totalPages: number;

  constructor(
    orders: OrderResponseDto[],
    total: number,
    page: number,
    limit: number,
    totalPages: number,
  ) {
    this.orders = orders;
    this.total = total;
    this.page = page;
    this.limit = limit;
    this.totalPages = totalPages;
  }
}

import { Order } from "../order.entity";
import { OrderItem } from "../order-item.entity";

/**
 * Transform Order to OrderResponseDto
 */
export function toOrderResponseDto(order: Order): OrderResponseDto {
  return {
    uuid: order.id,
    orderNumber: order.orderNumber,
    customerName: order.customerName,
    customerFiscalId: order.customerFiscalId,
    customerRc: order.customerRc,
    customerArticleNumber: order.customerArticleNumber,
    itemsSnapshot: order.orderItems?.map((item) => ({
      productUuid: item.productUuid,
      name: item.name,
      quantity: item.quantity,
      unitPrice: item.unitPrice,
      lineTotal: item.lineTotal,
      taxAmount: item.taxAmount,
      notes: item.notes,
    })) || [],
    subtotal: order.subtotal,
    useTax: order.useTax,
    taxRate: order.taxRate,
    taxAmount: order.taxAmount,
    totalAmount: order.totalAmount,
    orderDate: order.orderDate,
    requestedDeliveryDate: order.requestedDeliveryDate,
    actualDeliveryDate: order.actualDeliveryDate,
    status: order.status,
    priority: order.priority,
    notes: order.notes,
    createdAt: order.createdAt,
    updatedAt: order.updatedAt,
  };
}
