import { Test, TestingModule } from '@nestjs/testing';
import { LogsUtilityService } from '../src/logs/logs-utility.service';
import { LogsService } from '../src/logs/logs.service';

describe('LogsUtilityService Delta Functionality', () => {
  let service: LogsUtilityService;
  let mockLogsService: jest.Mocked<LogsService>;

  beforeEach(async () => {
    mockLogsService = {
      create: jest.fn(),
    } as any;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        LogsUtilityService,
        {
          provide: LogsService,
          useValue: mockLogsService,
        },
      ],
    }).compile();

    service = module.get<LogsUtilityService>(LogsUtilityService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should calculate delta for simple object changes', async () => {
    const oldData = {
      name: '<PERSON>',
      age: 30,
      status: 'active',
    };

    const newData = {
      name: '<PERSON>',
      age: 30,
      status: 'inactive',
    };

    await service.logUpdate(
      'user-uuid',
      'user_123',
      'User updated',
      oldData,
      newData,
      { updatedBy: 'admin' }
    );

    expect(mockLogsService.create).toHaveBeenCalledWith({
      userUuid: 'user-uuid',
      operation: 'updated',
      entity: 'user_123',
      description: 'User updated',
      data: {
        updatedBy: 'admin',
        changes: {
          name: { before: 'John Doe', after: 'John Smith' },
          status: { before: 'active', after: 'inactive' },
        },
        changedFields: ['name', 'status'],
        changeCount: 2,
      },
    });
  });

  it('should handle creation (null to object)', async () => {
    const newData = {
      name: 'New Product',
      price: 100,
    };

    await service.logCreation(
      'user-uuid',
      'product_456',
      'Product created',
      newData,
      { createdBy: 'admin' }
    );

    expect(mockLogsService.create).toHaveBeenCalledWith({
      userUuid: 'user-uuid',
      operation: 'created',
      entity: 'product_456',
      description: 'Product created',
      data: {
        createdBy: 'admin',
        changes: {
          name: { before: null, after: 'New Product' },
          price: { before: null, after: 100 },
        },
        changedFields: ['name', 'price'],
        changeCount: 2,
      },
    });
  });

  it('should handle deletion (object to null)', async () => {
    const deletedData = {
      name: 'Deleted Product',
      price: 50,
    };

    await service.logDeletion(
      'user-uuid',
      'product_789',
      'Product deleted',
      deletedData,
      { deletedBy: 'admin' }
    );

    expect(mockLogsService.create).toHaveBeenCalledWith({
      userUuid: 'user-uuid',
      operation: 'deleted',
      entity: 'product_789',
      description: 'Product deleted',
      data: {
        deletedBy: 'admin',
        changes: {
          name: { before: 'Deleted Product', after: null },
          price: { before: 50, after: null },
        },
        changedFields: ['name', 'price'],
        changeCount: 2,
      },
    });
  });

  it('should skip internal fields', async () => {
    const oldData = {
      id: '123',
      name: 'John Doe',
      createdAt: '2023-01-01',
      updatedAt: '2023-01-01',
      password: 'secret',
    };

    const newData = {
      id: '123',
      name: 'John Smith',
      createdAt: '2023-01-01',
      updatedAt: '2023-01-02',
      password: 'newsecret',
    };

    await service.logUpdate(
      'user-uuid',
      'user_123',
      'User updated',
      oldData,
      newData
    );

    expect(mockLogsService.create).toHaveBeenCalledWith({
      userUuid: 'user-uuid',
      operation: 'updated',
      entity: 'user_123',
      description: 'User updated',
      data: {
        changes: {
          name: { before: 'John Doe', after: 'John Smith' },
        },
        changedFields: ['name'],
        changeCount: 1,
      },
    });
  });

  it('should handle no changes', async () => {
    const data = {
      name: 'John Doe',
      age: 30,
    };

    await service.logUpdate(
      'user-uuid',
      'user_123',
      'User updated',
      data,
      data
    );

    expect(mockLogsService.create).toHaveBeenCalledWith({
      userUuid: 'user-uuid',
      operation: 'updated',
      entity: 'user_123',
      description: 'User updated',
      data: {
        changes: {},
        changedFields: [],
        changeCount: 0,
      },
    });
  });

  it('should handle complex nested objects', async () => {
    const oldData = {
      user: { name: 'John', age: 30 },
      items: [{ id: 1, name: 'Item 1' }],
    };

    const newData = {
      user: { name: 'John', age: 31 },
      items: [{ id: 1, name: 'Item 1 Updated' }],
    };

    await service.logUpdate(
      'user-uuid',
      'sale_123',
      'Sale updated',
      oldData,
      newData
    );

    expect(mockLogsService.create).toHaveBeenCalledWith({
      userUuid: 'user-uuid',
      operation: 'updated',
      entity: 'sale_123',
      description: 'Sale updated',
      data: {
        changes: {
          user: { 
            before: { name: 'John', age: 30 }, 
            after: { name: 'John', age: 31 } 
          },
          items: { 
            before: [{ id: 1, name: 'Item 1' }], 
            after: [{ id: 1, name: 'Item 1 Updated' }] 
          },
        },
        changedFields: ['user', 'items'],
        changeCount: 2,
      },
    });
  });
});
