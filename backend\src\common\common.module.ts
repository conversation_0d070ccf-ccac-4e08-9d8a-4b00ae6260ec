import { Global, Module } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { InventoryService } from "../inventory/inventory.service.typeorm";
import { StockAdjustmentService } from "../inventory/stock-adjustment.service";
import { StockAdjustment } from "../inventory/stock-adjustment.entity";
import { InventoryItem as InventoryItemEntity } from "../inventory/inventory-item.entity";
import { Storage } from "../inventory/storage.entity";
import { Warehouse } from "../warehouses/warehouse.entity";
import { Product } from "../products/product.entity";
import { User } from "../users/user.entity";

@Global()
@Module({
  imports: [
    TypeOrmModule.forFeature([StockAdjustment, InventoryItemEntity, Storage, Warehouse, Product, User]),
  ],
  providers: [InventoryService, StockAdjustmentService],
  exports: [InventoryService, StockAdjustmentService, TypeOrmModule], // Export both services and modules
})
export class CommonModule {}
