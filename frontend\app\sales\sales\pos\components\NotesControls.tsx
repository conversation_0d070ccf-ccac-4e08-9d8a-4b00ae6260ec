import React from 'react';
import { posStyles } from '../styles/posStyles';
import type { NotesControlsProps } from '../types';

export function NotesControls({ notesEnabled, notes, onNotesEnabledChange, onNotesChange, disabled = false }: NotesControlsProps) {
  return (
    <div className="mb-3 space-y-2">
      {/* Notes Toggle */}
      <div className="flex items-center justify-between">
        <label className="flex items-center space-x-2 cursor-pointer">
          <input
            type="checkbox"
            checked={notesEnabled}
            onChange={(e) => onNotesEnabledChange(e.target.checked)}
            disabled={disabled}
            className={`w-3 h-3 text-blue-600 border-gray-300 rounded focus:ring-blue-500 ${
              disabled ? 'opacity-50 cursor-not-allowed' : ''
            }`}
          />
          <span className="text-xs font-medium text-gray-700">Add Notes</span>
        </label>
      </div>

      {/* Notes Textarea - Only show when notes are enabled */}
      {notesEnabled && (
        <div className="space-y-1">
          <label className="block text-xs font-medium text-gray-700">
            Notes (optional)
          </label>
          <textarea
            value={notes}
            onChange={(e) => onNotesChange(e.target.value)}
            disabled={disabled}
            placeholder="Add any notes about this sale..."
            className={`w-full p-2 border border-gray-300 rounded-md resize-none focus:outline-none focus:ring-1 focus:ring-blue-500 text-xs ${
              disabled ? 'bg-gray-100 cursor-not-allowed opacity-50' : ''
            }`}
            rows={2}
          />
        </div>
      )}
    </div>
  );
} 