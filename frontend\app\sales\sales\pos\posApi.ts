// posApi.ts - API utilities for POS (Point of Sale) endpoints
import axios from 'axios';
import { getAxiosAuthHeaders } from '@/utils/authHeaders';
import { getAccountSettingsByUser } from '../../../settings/accountSettingsApi';
import { getCustomers as getCustomersFromCustomersApi } from '../../customers/customersApi';
import type { CustomerFilter } from '@/components/CustomerModal';

const API_BASE = '/api';

// Product interfaces
export interface Product {
  uuid: string;
  name: string;
  description?: string;
  sku?: string;
  barcode?: string;
  price?: number;
  cost?: number;
  warehouseUuid: string;
  currentStock?: number;
  stockStorageUuid?: string;
  customerPrice?: number;
  stockWarning?: boolean;
  // Customer pricing fields from backend
  retailPrice?: number;
  wholesalePrice?: number;
  midWholesalePrice?: number;
  institutionalPrice?: number;
  customerType?: 'retail' | 'wholesale' | 'mid-wholesale' | 'institutional';
}

export interface PaginatedProductsResponse {
  data: Product[];
  total: number;
  page: number;
  limit: number;
  hasNext: boolean;
  hasPrev: boolean;
}

// Response interface for categories-only requests
export interface CategoriesResponse {
  data: Product[];
  total: number;
  page: number;
  limit: number;
  hasNext: boolean;
  hasPrev: boolean;
  categories: string[];
}

// Customer interfaces
export interface Customer {
  uuid: string;
  name: string;
  email?: string;
  phone?: string;
  address?: string;
  fiscalId?: string;
  warehouseUuid: string;
  creditLimit: number;
  currentCredit: number;
}

export interface CreateCustomerDto {
  name: string;
  email?: string;
  phone?: string;
  address?: string;
  fiscalId?: string;
  warehouseUuid: string;
  creditLimit?: number;
}

export interface UpdateCustomerDto {
  name?: string;
  email?: string;
  phone?: string;
  address?: string;
  fiscalId?: string;
  creditLimit?: number;
}

// Sale interfaces
export interface SaleItem {
  productUuid: string;
  name: string; // Add name field that backend expects
  quantity: number;
  unitPrice: number;
  totalPrice: number;
}

export interface CreateSaleDto {
  customerUuid: string;
  warehouseUuid: string;
  userUuid: string;
  items: SaleItem[];
  paymentMethod: string;
  amountPaid: number;
  useTax: boolean;
  taxRate: number;
}

export interface Sale {
  uuid: string;
  customerUuidString: string;
  customerUuid?: string; // For backward compatibility
  warehouseUuid: string;
  userUuid: string;
  items: SaleItem[];
  itemsSnapshot?: any[]; // For edit mode
  subtotal: number;
  taxAmount: number;
  totalAmount: number;
  amountPaid: number;
  balanceDue: number;
  paymentMethod: string;
  status: string;
  useTax?: boolean;
  taxRate?: number;
  createdAt: string;
}

// Filter options for the unified products filter endpoint
export interface ProductFilterOptions {
  name?: string;
  productCategoryUuid?: string;
  sku?: string;
  barcode?: string;
  search?: string;
  customerType?: 'retail' | 'wholesale' | 'mid-wholesale' | 'institutional';
  minRetailPrice?: number;
  maxRetailPrice?: number;
  minWholesalePrice?: number;
  maxWholesalePrice?: number;
  minMidWholesalePrice?: number;
  maxMidWholesalePrice?: number;
  minInstitutionalPrice?: number;
  maxInstitutionalPrice?: number;
  returnCategoriesOnly?: boolean;
}

/**
 * Fetch products using the unified /products/filter endpoint
 * This replaces the old separate endpoints: /filter/advanced, /pricing, /search, /categories
 */
export async function getProducts(
  warehouseUuid: string, 
  options: ProductFilterOptions = {},
  page: number = 1, 
  limit: number = 13,
  signal?: AbortSignal
): Promise<PaginatedProductsResponse | CategoriesResponse> {
  const params = new URLSearchParams();
  params.append('warehouseUuid', warehouseUuid);
  params.append('page', page.toString());
  params.append('limit', limit.toString());
  
  // Add filter options
  if (options.name !== undefined && options.name !== null && options.name.trim() !== '') {
    params.append('name', options.name.trim());
  }
  if (options.productCategoryUuid) {
    params.append('productCategoryUuid', options.productCategoryUuid);
  }
  if (options.sku !== undefined && options.sku !== null && options.sku.trim() !== '') {
    params.append('sku', options.sku.trim());
  }
  if (options.barcode !== undefined && options.barcode !== null && options.barcode.trim() !== '') {
    params.append('barcode', options.barcode.trim());
  }
  if (options.search !== undefined && options.search !== null && options.search.trim() !== '') {
    params.append('search', options.search.trim());
  }
  if (options.customerType) {
    params.append('customerType', options.customerType);
  }
  if (options.minRetailPrice !== undefined) {
    params.append('minRetailPrice', options.minRetailPrice.toString());
  }
  if (options.maxRetailPrice !== undefined) {
    params.append('maxRetailPrice', options.maxRetailPrice.toString());
  }
  if (options.minWholesalePrice !== undefined) {
    params.append('minWholesalePrice', options.minWholesalePrice.toString());
  }
  if (options.maxWholesalePrice !== undefined) {
    params.append('maxWholesalePrice', options.maxWholesalePrice.toString());
  }
  if (options.minMidWholesalePrice !== undefined) {
    params.append('minMidWholesalePrice', options.minMidWholesalePrice.toString());
  }
  if (options.maxMidWholesalePrice !== undefined) {
    params.append('maxMidWholesalePrice', options.maxMidWholesalePrice.toString());
  }
  if (options.minInstitutionalPrice !== undefined) {
    params.append('minInstitutionalPrice', options.minInstitutionalPrice.toString());
  }
  if (options.maxInstitutionalPrice !== undefined) {
    params.append('maxInstitutionalPrice', options.maxInstitutionalPrice.toString());
  }
  if (options.returnCategoriesOnly) {
    params.append('returnCategoriesOnly', 'true');
  }

  const url = `${API_BASE}/products/filter?${params.toString()}`;
  
  try {
    const res = await axios.get(url, {
      headers: getAxiosAuthHeaders(),
      signal, // Add AbortController signal
    });
    
    // Handle categories-only response
    if (options.returnCategoriesOnly) {
      const categoriesResponse: CategoriesResponse = {
        data: [],
        total: 0,
        page: 1,
        limit: 1,
        hasNext: false,
        hasPrev: false,
        categories: res.data.categories || []
      };
      return categoriesResponse;
    }
    
    // Use the backend response directly
    const response: PaginatedProductsResponse = {
      data: res.data.data || [],
      total: res.data.total || 0,
      page: res.data.page || 1,
      limit: res.data.limit || limit,
      hasNext: res.data.hasNext || false,
      hasPrev: res.data.hasPrev || false
    };
    
    return response;
  } catch (err) {
    // Check if this is an abort error
    if (axios.isCancel(err)) {
      throw new Error('Request cancelled');
    }
    throw err;
  }
}

/**
 * Fetch products with customer-specific pricing using the unified endpoint
 */
export async function getProductsWithPricing(
  warehouseUuid: string,
  customerType: 'retail' | 'wholesale' | 'mid-wholesale' | 'institutional',
  name?: string,
  page: number = 1,
  limit: number = 13,
  signal?: AbortSignal
): Promise<PaginatedProductsResponse> {
  const response = await getProducts(warehouseUuid, {
    name,
    customerType
  }, page, limit, signal);
  
  // Since we're not using returnCategoriesOnly, this will always be PaginatedProductsResponse
  return response as PaginatedProductsResponse;
}

/**
 * Fetch products with customer-specific pricing (updated to match backend signature)
 * This function is used by the POS module and matches the backend /products/filter endpoint
 */
export async function getProductsWithCustomerPricing(
  warehouseUuid: string,
  customerType: 'retail' | 'wholesale' | 'mid-wholesale' | 'institutional',
  name?: string,
  page: number = 1,
  limit: number = 13,
  category?: string,
  signal?: AbortSignal
): Promise<PaginatedProductsResponse> {
  const options: ProductFilterOptions = {
    customerType
  };
  
  // Add name filter if provided
  if (name && name.trim() !== '') {
    options.name = name.trim();
  }
  
  // Add category filter if provided
  if (category && category.trim() !== '') {
    options.productCategoryUuid = category.trim();
  }
  
  const response = await getProducts(warehouseUuid, options, page, limit, signal);
  
  // Since we're not using returnCategoriesOnly, this will always be PaginatedProductsResponse
  return response as PaginatedProductsResponse;
}

/**
 * Search products by name, SKU, or barcode using the unified endpoint
 */
export async function searchProducts(
  warehouseUuid: string,
  searchTerm: string,
  page: number = 1,
  limit: number = 13,
  signal?: AbortSignal
): Promise<PaginatedProductsResponse> {
  const response = await getProducts(warehouseUuid, {
    search: searchTerm
  }, page, limit, signal);
  
  // Since we're not using returnCategoriesOnly, this will always be PaginatedProductsResponse
  return response as PaginatedProductsResponse;
}

/**
 * Get product categories for a warehouse using the unified endpoint
 */
export async function getProductCategories(warehouseUuid: string): Promise<{ categories: string[] }> {
  try {
    const response = await getProducts(warehouseUuid, {
      returnCategoriesOnly: true
    }, 1, 1) as CategoriesResponse;
    
    // Handle the categories-only response
    if (response.categories) {
      return { categories: response.categories || [] };
    }
    
    return { categories: [] };
  } catch (err) {
    return { categories: [] };
  }
}

/**
 * Get account settings for tax configuration
 */
export async function getAccountSettings(userUuid: string) {
  try {
    return await getAccountSettingsByUser(userUuid);
  } catch (error) {
    // Return default settings if account settings not found
    return {
      preferredTaxRate: 0,
      preferredUseTax: false,
      preferredPaymentMethod: 'cash'
    };
  }
}

/**
 * Get user account settings (alias for getAccountSettings for backward compatibility)
 */
export async function getUserAccountSettings(userUuid: string) {
  return getAccountSettings(userUuid);
}

/**
 * Fetch customers for a warehouse, optionally filtered by name
 */
export async function getCustomers(
  warehouseUuid: string,
  name?: string,
  page: number = 1,
  limit: number = 10
): Promise<{ data: Customer[]; total: number; page: number; limit: number; hasNext: boolean; hasPrev: boolean }> {
  const params = new URLSearchParams({
    warehouseUuid,
    page: page.toString(),
    limit: limit.toString()
  });
  
  if (name && name.trim()) {
    params.append('name', name.trim());
  }

  try {
    const response = await axios.get(`${API_BASE}/customers?${params.toString()}`, {
      headers: getAxiosAuthHeaders(),
    });
    return response.data;
  } catch (err) {
    throw err;
  }
}

/**
 * Create a new customer
 */
export async function createCustomer(data: CreateCustomerDto): Promise<Customer> {
  try {
    const res = await axios.post(`${API_BASE}/customers`, data, {
      headers: getAxiosAuthHeaders(),
    });
    return res.data;
  } catch (err) {
    throw err;
  }
}

/**
 * Get customer sales history
 */
export async function getCustomerSales(
  customerUuid: string,
  page: number = 1,
  limit: number = 10
): Promise<{ data: any[]; total: number; page: number; limit: number; hasNext: boolean; hasPrev: boolean }> {
  const params = new URLSearchParams({
    page: page.toString(),
    limit: limit.toString()
  });

  try {
    const res = await axios.get(`${API_BASE}/customers/${customerUuid}/sales?${params.toString()}`, {
      headers: getAxiosAuthHeaders(),
    });
    return res.data;
  } catch (err) {
    throw err;
  }
}

/**
 * Get a single customer by UUID
 */
export async function getCustomer(uuid: string): Promise<Customer> {
  try {
    const res = await axios.get(`${API_BASE}/customers/${uuid}`, {
      headers: getAxiosAuthHeaders(),
    });
    return res.data;
  } catch (err) {
    throw err;
  }
}

/**
 * Update customer details
 */
export async function updateCustomer(uuid: string, data: UpdateCustomerDto): Promise<Customer> {
  try {
    const res = await axios.patch(`${API_BASE}/customers/${uuid}`, data, {
      headers: getAxiosAuthHeaders(),
    });
    return res.data;
  } catch (err) {
    throw err;
  }
}

/**
 * Delete a customer
 */
export async function deleteCustomer(uuid: string): Promise<void> {
  try {
    await axios.delete(`${API_BASE}/customers/${uuid}`, {
      headers: getAxiosAuthHeaders(),
    });
  } catch (err) {
    throw err;
  }
}

/**
 * Get a single product by UUID
 */
export async function getProduct(uuid: string): Promise<Product> {
  try {
    const response = await axios.get(`${API_BASE}/products/${encodeURIComponent(uuid)}`, {
      headers: getAxiosAuthHeaders(),
    });
    return response.data;
  } catch (err) {
    throw err;
  }
}

/**
 * Create a new sale (debug version for testing)
 */
export async function createSaleDebug(payload: CreateSaleDto): Promise<Sale> {
  try {
    const res = await axios.post(`${API_BASE}/sales/debug`, payload, {
      headers: getAxiosAuthHeaders(),
    });
    return res.data;
  } catch (err) {
    throw err;
  }
}

/**
 * Create a new sale
 */
export async function createSale(payload: CreateSaleDto): Promise<Sale> {
  try {
    const res = await axios.post(`${API_BASE}/sales`, payload, {
      headers: getAxiosAuthHeaders(),
    });
    return res.data;
  } catch (err) {
    throw err;
  }
}

/**
 * Add products to an existing sale
 */
export async function addProductsToSale(
  saleUuid: string,
  payload: { items: SaleItem[] },
  userUuid: string
): Promise<Sale> {
  try {
    const mappedItems = payload.items.map(item => ({
      productUuid: item.productUuid,
      name: item.name || 'Unknown Product',
      quantity: item.quantity,
      unitPrice: item.unitPrice
    }));

    const res = await axios.patch(`${API_BASE}/sales/${saleUuid}`, { items: mappedItems, userUuid }, {
      headers: getAxiosAuthHeaders(),
    });
    return res.data;
  } catch (err: any) {
    throw err;
  }
}

/**
 * Update payment for a sale
 * NOTE: This function should not be used directly. Use updateSale instead to ensure items are included.
 */
export async function updateSalePayment(
  saleUuid: string,
  paymentData: { paymentMethod: string; amountPaid: number },
  userUuid: string
): Promise<Sale> {
  try {
    // First, get the current sale to include its items
    const currentSale = await getSale(saleUuid);
    
    // Use updateSale instead to include items
    return await updateSale(saleUuid, {
      paymentMethod: paymentData.paymentMethod,
      amountPaid: paymentData.amountPaid,
      items: currentSale.items || [],
      userUuid
    });
  } catch (err) {
    throw err;
  }
}

/**
 * Get sales by warehouse
 */
export async function getSalesByWarehouse(warehouseUuid: string): Promise<Sale[]> {
  try {
    const res = await axios.get(`${API_BASE}/sales/list-by-warehouse/${warehouseUuid}`, {
      headers: getAxiosAuthHeaders(),
    });
    return res.data;
  } catch (err) {
    throw err;
  }
}

/**
 * Get a single sale by UUID
 */
export async function getSale(uuid: string): Promise<Sale> {
  try {
    const res = await axios.get(`${API_BASE}/sales/${uuid}`, {
      headers: getAxiosAuthHeaders(),
    });
    return res.data;
  } catch (err) {
    throw err;
  }
}

/**
 * Update sale details
 */
// Interface for sale update data that matches backend UpdateSaleDto
export interface UpdateSaleData {
  userUuid: string;
  customerUuid?: string;
  customerName?: string;
  customerFiscalId?: string;
  customerRc?: string;
  customerArticleNumber?: string;
  orderUuid?: string;
  items?: SaleItem[]; // Add items field for sale updates
  subtotal?: number;
  useTax?: boolean;
  taxRate?: number;
  taxAmount?: number;
  totalAmount?: number;
  amountPaid?: number;
  balanceDue?: number;
  paymentMethod?: string;
  paymentDate?: string[];
  invoiceDate?: string;
  dueDate?: string;
  status?: string;
  updatedBy?: string;
}

export async function updateSale(
  saleUuid: string,
  data: UpdateSaleData
): Promise<Sale> {
  try {
    const res = await axios.patch(`${API_BASE}/sales/${saleUuid}`, data, {
      headers: getAxiosAuthHeaders(),
    });
    
    return res.data;
  } catch (err: any) {
    throw err;
  }
}

/**
 * Set sale payment (alias for updateSalePayment)
 * NOTE: This function should not be used directly. Use updateSale instead to ensure items are included.
 */
export async function setSalePayment(saleUuid: string, paymentData: { paymentMethod: string; amountPaid: number }, userUuid: string): Promise<Sale> {
  return updateSalePayment(saleUuid, paymentData, userUuid);
}

/**
 * Filter customers
 */
export async function filterCustomers(filter: CustomerFilter): Promise<{ data: Customer[]; total: number; page: number; totalPages: number }> {
  try {
    const response = await axios.post(`${API_BASE}/customers/filter`, {
      ...filter,
      page: 1,
      limit: 13
    }, {
      headers: getAxiosAuthHeaders(),
    });
    
    return {
      data: response.data.data || [],
      total: response.data.total || 0,
      page: response.data.page || 1,
      totalPages: response.data.totalPages || Math.ceil((response.data.total || 0) / 13),
    };
  } catch (err: any) {
    throw new Error(err.response?.data?.message || 'Failed to filter customers');
  }
}

/**
 * Update sale customer
 */
export async function updateSaleCustomer(saleUuid: string, customerUuid: string, userUuid: string): Promise<Sale> {
  return updateSale(saleUuid, { customerUuid, userUuid });
}

/**
 * Check stock levels for warnings
 */
export async function checkStockLevelsForWarnings(products: Product[]): Promise<Product[]> {
  return products.map(product => ({
    ...product,
    stockWarning: product.currentStock ? product.currentStock < 10 : false
  }));
}

/**
 * Debug sale creation (alias for createSaleDebug)
 */
export async function debugSaleCreation(payload: CreateSaleDto): Promise<Sale> {
  return createSaleDebug(payload);
}

/**
 * Get or create default customer
 */
export async function getOrCreateDefaultCustomer(warehouseUuid: string): Promise<Customer> {
  try {
    const customersResponse = await filterCustomers({ 
      warehouseUuid, 
      name: 'Walk-in Customer' 
    });
    
    if (customersResponse.data.length > 0) {
      return customersResponse.data[0];
    }
    
    const defaultCustomerData: CreateCustomerDto = {
      name: 'Walk-in Customer',
      warehouseUuid,
      creditLimit: 0
    };
    
    return await createCustomer(defaultCustomerData);
  } catch (error) {
    throw new Error('Failed to get or create default customer');
  }
}