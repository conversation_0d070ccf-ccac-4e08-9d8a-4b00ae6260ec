// productCategoriesApi.ts - API utilities for Product Categories endpoints
import axios from 'axios';
import { getAxiosAuthHeaders } from '@/utils/authHeaders';

const API_BASE = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000';

export interface ProductCategory {
  uuid: string;
  name: string;
  warehouseUuidString?: string;
  createdByString?: string;
  createdByName?: string;
  updatedByString?: string;
  updatedByName?: string;
  createdAt: Date;
  updatedAt: Date;
}

// Pagination interfaces matching backend DTOs
export interface PaginationMeta {
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

export interface PaginatedResponse<T> {
  data: T[];
  meta: PaginationMeta;
}

export interface PaginationQuery {
  page?: number;
  limit?: number;
}

// Get all product categories with pagination (warehouse-scoped)
export async function getAllProductCategories(
  warehouseUuid: string,
  paginationQuery?: PaginationQuery
): Promise<PaginatedResponse<ProductCategory>> {
  const params = new URLSearchParams();
  if (paginationQuery?.page) params.append('page', paginationQuery.page.toString());
  if (paginationQuery?.limit) params.append('limit', paginationQuery.limit.toString());
  if (warehouseUuid) params.append('warehouseUuid', warehouseUuid);
  
  const url = `/api/product-categories${params.toString() ? `?${params.toString()}` : ''}`;
  const res = await axios.get(url, {
    headers: getAxiosAuthHeaders(),
  });
  // Fixed: Backend returns data directly, not wrapped in meta
  return {
    data: res.data.data,
    meta: {
      total: res.data.total,
      page: res.data.page,
      limit: res.data.limit,
      totalPages: res.data.totalPages,
      hasNext: res.data.page < res.data.totalPages,
      hasPrev: res.data.page > 1
    }
  };
}

// Search product categories by name (warehouse-scoped)
export async function searchProductCategories(
  name: string,
  warehouseUuid: string,
  paginationQuery?: PaginationQuery
): Promise<PaginatedResponse<ProductCategory>> {
  const params = new URLSearchParams({ name });
  if (paginationQuery?.page) params.append('page', paginationQuery.page.toString());
  if (paginationQuery?.limit) params.append('limit', paginationQuery.limit.toString());
  if (warehouseUuid) params.append('warehouseUuid', warehouseUuid);
  
  const url = `/api/product-categories/search?${params.toString()}`;
  const res = await axios.get(url, {
    headers: getAxiosAuthHeaders(),
  });
  // Fixed: Backend returns data directly, not wrapped in meta
  return {
    data: res.data.data,
    meta: {
      total: res.data.total,
      page: res.data.page,
      limit: res.data.limit,
      totalPages: res.data.totalPages,
      hasNext: res.data.page < res.data.totalPages,
      hasPrev: res.data.page > 1
    }
  };
}

// Get all product categories (non-paginated for dropdowns)
export async function getAllProductCategoriesRaw(warehouseUuid: string): Promise<ProductCategory[]> {
  const params = new URLSearchParams({ limit: '1000' });
  if (warehouseUuid) params.append('warehouseUuid', warehouseUuid);
  const res = await axios.get(`/api/product-categories?${params.toString()}`, {
    headers: getAxiosAuthHeaders(),
  });
  return res.data.data;
}

// Create a new product category
export async function createProductCategory(
  data: { name: string },
  warehouseUuid: string,
  userUuid: string
): Promise<ProductCategory> {
  const payload: any = {
    ...data,
    warehouseUuid,
  };
  
  // Only include userUuid if it's provided and not empty
  if (userUuid && userUuid.trim()) {
    payload.userUuid = userUuid;
  }
  
  const res = await axios.post('/api/product-categories', payload, {
    headers: getAxiosAuthHeaders(),
  });
  return res.data;
}

// Update a product category
export async function updateProductCategory(
  uuid: string, 
  data: { name: string },
  userUuid: string
): Promise<ProductCategory> {
  const payload: any = {
    ...data,
  };
  
  // Only include userUuid if it's provided and not empty
  if (userUuid && userUuid.trim()) {
    payload.userUuid = userUuid;
  }
  
  const res = await axios.patch(`/api/product-categories/${uuid}`, payload, {
    headers: getAxiosAuthHeaders(),
  });
  return res.data;
}

// Delete a product category
export async function deleteProductCategory(uuid: string): Promise<void> {
  await axios.delete(`/api/product-categories/${uuid}`, {
    headers: getAxiosAuthHeaders(),
  });
}

// Get a product category by UUID
export async function getProductCategoryById(uuid: string): Promise<ProductCategory> {
  const res = await axios.get(`/api/product-categories/${uuid}`, {
    headers: getAxiosAuthHeaders(),
  });
  return res.data;
} 