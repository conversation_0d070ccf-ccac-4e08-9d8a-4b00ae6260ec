// productsApi.ts - API utilities for Products endpoints
// 
// UPDATED TO USE LATEST BACKEND ENDPOINTS:
// - Replaced multiple old endpoints (/by-warehouse, /categories, /pricing, /search, /filter/advanced) 
//   with unified /filter endpoint
// - Added support for search, customer pricing, and category listing through single endpoint
// - Maintained backward compatibility with existing function names
// - Enhanced filter options to match backend controller capabilities
//
// POTENTIAL ISSUES TO MONITOR:
// - Verify unified endpoint handles all filter combinations correctly
// - Check that customer pricing works with the new endpoint structure
// - Ensure category listing returns proper format
// - Monitor performance with complex filter combinations
//
// MISSING ENDPOINTS (if any issues arise):
// - All functionality should be available through the unified /filter endpoint
// - If specific features are missing, they should be added to the backend controller

import axios from 'axios';
import { getAxiosAuthHeaders } from '@/utils/authHeaders';

const API_BASE = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000';

export interface Product {
  uuid: string;
  warehouseUuid: string;
  warehouseUuidString?: string;
  name: string;
  description?: string;
  sku?: string;
  barcode?: string;
  // Fixed: Use productCategoryUuid to match backend, keep both for backward compatibility
  productCategoryUuid?: string;
  productCategoryUuidString?: string;
  // Simple price field for basic pricing
  price?: number;
  // Updated pricing structure to match backend
  retailPrice?: number;
  wholesalePrice?: number;
  midWholesalePrice?: number;
  institutionalPrice?: number;
  cost?: number;
  isDeleted?: boolean;
  createdAt?: string;
  updatedAt?: string;
}

// Customer pricing interface for the pricing endpoint
export interface ProductWithCustomerPricing extends Product {
  customerPrice: number;
  customerType: 'retail' | 'wholesale' | 'mid-wholesale' | 'institutional';
}

// Pagination interfaces matching backend DTOs
export interface PaginationMeta {
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

export interface PaginatedResponseDto<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  hasNext: boolean;
  hasPrev: boolean;
}

export interface PaginationQuery {
  page?: number;
  limit?: number;
}

// Advanced filter options interface matching backend
export interface AdvancedFilterOptions {
  name?: string;
  productCategoryUuid?: string;
  sku?: string;
  barcode?: string;
  search?: string;
  customerType?: 'retail' | 'wholesale' | 'mid-wholesale' | 'institutional';
  minRetailPrice?: number;
  maxRetailPrice?: number;
  minWholesalePrice?: number;
  maxWholesalePrice?: number;
  minMidWholesalePrice?: number;
  maxMidWholesalePrice?: number;
  minInstitutionalPrice?: number;
  maxInstitutionalPrice?: number;
}

// Get all products with pagination (main endpoint)
export async function getAllProducts(paginationQuery?: PaginationQuery): Promise<PaginatedResponseDto<Product>> {
  const params = new URLSearchParams();
  if (paginationQuery?.page) params.append('page', paginationQuery.page.toString());
  if (paginationQuery?.limit) params.append('limit', paginationQuery.limit.toString());
  
  const url = `/api/products${params.toString() ? `?${params.toString()}` : ''}`;
  const res = await axios.get(url, {
    headers: getAxiosAuthHeaders(),
  });
  return res.data;
}

// Get total count of products
export async function getProductsCount(): Promise<{ count: number }> {
  const res = await axios.get('/api/products/count', {
    headers: getAxiosAuthHeaders(),
  });
  return res.data;
}

// Get unique product categories for a warehouse (using new unified filter endpoint)
export async function getProductCategories(warehouseUuid: string): Promise<{ categories: string[] }> {
  const params = new URLSearchParams({
    warehouseUuid,
    returnCategoriesOnly: 'true'
  });
  
  const url = `/api/products/filter?${params.toString()}`;
  const res = await axios.get(url, {
    headers: getAxiosAuthHeaders(),
  });
  return res.data;
}

// Unified product filtering with multiple criteria (replaces all old endpoints)
export async function filterProducts(
  warehouseUuid: string,
  filters: AdvancedFilterOptions = {},
  paginationQuery?: PaginationQuery
): Promise<PaginatedResponseDto<Product | ProductWithCustomerPricing>> {
  const params = new URLSearchParams({ warehouseUuid });
  
  // Add filter parameters
  if (filters.name) params.append('name', filters.name);
  if (filters.productCategoryUuid) {
    console.log('[productsApi] Adding productCategoryUuid filter:', filters.productCategoryUuid);
    params.append('productCategoryUuid', filters.productCategoryUuid);
  }
  if (filters.sku) params.append('sku', filters.sku);
  if (filters.barcode) params.append('barcode', filters.barcode);
  if (filters.search) params.append('search', filters.search);
  if (filters.customerType) params.append('customerType', filters.customerType);
  if (filters.minRetailPrice !== undefined) params.append('minRetailPrice', filters.minRetailPrice.toString());
  if (filters.maxRetailPrice !== undefined) params.append('maxRetailPrice', filters.maxRetailPrice.toString());
  if (filters.minWholesalePrice !== undefined) params.append('minWholesalePrice', filters.minWholesalePrice.toString());
  if (filters.maxWholesalePrice !== undefined) params.append('maxWholesalePrice', filters.maxWholesalePrice.toString());
  if (filters.minMidWholesalePrice !== undefined) params.append('minMidWholesalePrice', filters.minMidWholesalePrice.toString());
  if (filters.maxMidWholesalePrice !== undefined) params.append('maxMidWholesalePrice', filters.maxMidWholesalePrice.toString());
  if (filters.minInstitutionalPrice !== undefined) params.append('minInstitutionalPrice', filters.minInstitutionalPrice.toString());
  if (filters.maxInstitutionalPrice !== undefined) params.append('maxInstitutionalPrice', filters.maxInstitutionalPrice.toString());
  
  // Add pagination parameters
  if (paginationQuery?.page) params.append('page', paginationQuery.page.toString());
  if (paginationQuery?.limit) params.append('limit', paginationQuery.limit.toString());
  
  const url = `/api/products/filter?${params.toString()}`;
  
  // DEBUG: Log the request details
  console.log('[productsApi] Unified filter request:', {
    url,
    warehouseUuid,
    filters,
    paginationQuery,
    fullUrl: url,
    categoryFilter: filters.productCategoryUuid
  });
  
  const res = await axios.get(url, {
    headers: getAxiosAuthHeaders(),
  });
  
  // DEBUG: Log the response
  console.log('[productsApi] Unified filter response:', {
    status: res.status,
    totalProducts: res.data.total,
    returnedProducts: res.data.data?.length || 0,
    hasNext: res.data.hasNext,
    hasPrev: res.data.hasPrev
  });
  
  return res.data;
}

// Backward compatibility functions that now use the unified filter endpoint

// Get products with customer-specific pricing (now uses unified filter)
export async function getProductsWithCustomerPricing(
  warehouseUuid: string,
  customerType: 'retail' | 'wholesale' | 'mid-wholesale' | 'institutional',
  paginationQuery?: PaginationQuery
): Promise<PaginatedResponseDto<ProductWithCustomerPricing>> {
  return filterProducts(
    warehouseUuid,
    { customerType },
    paginationQuery
  ) as Promise<PaginatedResponseDto<ProductWithCustomerPricing>>;
}

// Search products by name, SKU, or barcode (now uses unified filter)
export async function searchProducts(
  warehouseUuid: string, 
  query: string, 
  paginationQuery?: PaginationQuery
): Promise<PaginatedResponseDto<Product>> {
  return filterProducts(
    warehouseUuid,
    { search: query },
    paginationQuery
  ) as Promise<PaginatedResponseDto<Product>>;
}

// Advanced product filtering (now uses unified filter)
export async function advancedFilterProducts(
  warehouseUuid: string,
  filters: AdvancedFilterOptions,
  paginationQuery?: PaginationQuery
): Promise<PaginatedResponseDto<Product>> {
  return filterProducts(
    warehouseUuid,
    filters,
    paginationQuery
  ) as Promise<PaginatedResponseDto<Product>>;
}

// Basic filter by warehouse and name (now uses unified filter)
export async function filterProductsBasic(
  warehouseUuid: string, 
  name?: string,
  paginationQuery?: PaginationQuery
): Promise<PaginatedResponseDto<Product>> {
  return filterProducts(
    warehouseUuid,
    { name },
    paginationQuery
  ) as Promise<PaginatedResponseDto<Product>>;
}

// Legacy function for backward compatibility
export async function getProductsByWarehouse(warehouseUuid: string): Promise<Product[]> {
  const result = await filterProducts(warehouseUuid, {});
  return result.data;
}

// Non-paginated version for backward compatibility (uses list-raw endpoint)
export async function getAllProductsRaw(): Promise<Product[]> {
  const res = await axios.get('/api/products/list-raw', {
    headers: getAxiosAuthHeaders(),
  });
  return res.data;
}

// Create a new product
export async function createProduct(data: Omit<Product, 'uuid' | 'isDeleted' | 'createdAt' | 'updatedAt'>): Promise<Product> {
  const res = await axios.post('/api/products', data, {
    headers: getAxiosAuthHeaders(),
  });
  return res.data;
}

// Update an existing product
export async function updateProduct(uuid: string, data: Partial<Product>): Promise<Product> {
  console.log('[productsApi] updateProduct called with:', { uuid, data });
  
  // Handle category UUID normalization and remove non-updatable fields
  const { warehouseUuid, isDeleted, createdAt, updatedAt, productCategoryUuidString, ...rest } = data as any;
  const body = {
    ...rest,
    productCategoryUuid: productCategoryUuidString || data.productCategoryUuid || undefined
  };
  
  console.log('[productsApi] Final request body:', body);
  console.log('[productsApi] Price fields in request body:', {
    retailPrice: body.retailPrice,
    wholesalePrice: body.wholesalePrice,
    midWholesalePrice: body.midWholesalePrice,
    institutionalPrice: body.institutionalPrice,
    cost: body.cost
  });
  
  const res = await axios.put(`/api/products/${uuid}`, body, {
    headers: getAxiosAuthHeaders(),
  });
  
  console.log('[productsApi] Response received:', res.data);
  return res.data;
}

// Delete a product (soft delete)
export async function deleteProduct(uuid: string): Promise<void> {
  await axios.delete(`/api/products/${uuid}`, {
    headers: getAxiosAuthHeaders(),
  });
}

// Delete all products (hard delete)
export async function deleteAllProducts(): Promise<{ message: string; deletedCount: number }> {
  const res = await axios.delete('/api/products/all', {
    headers: getAxiosAuthHeaders(),
  });
  return res.data;
}

// Get a single product by UUID
export async function getProductById(uuid: string): Promise<Product> {
  const res = await axios.get(`/api/products/${uuid}`, {
    headers: getAxiosAuthHeaders(),
  });
  return res.data;
}

// Get all products (non-paginated, for backward compatibility)
export async function getProducts(): Promise<Product[]> {
  const result = await getAllProducts();
  return result.data;
}
