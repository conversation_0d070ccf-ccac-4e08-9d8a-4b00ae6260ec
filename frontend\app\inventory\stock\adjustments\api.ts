import axios from "axios";
import { getAxiosAuthHeaders } from '@/utils/authHeaders';

export interface StockLevel {
  productUuid: string;
  quantity: number;
}

export interface StockAdjustmentHistoryItem {
  uuid: string;
  userUuid: string;
  userEmail: string;
  warehouseUuid: string;
  storageUuid: string;
  productUuid: string;
  productName: string;
  productSku?: string;
  quantityAdjusted: number;
  reason: string;
  createdAt: string;
}

export interface StockAdjustmentHistoryResponse {
  data: StockAdjustmentHistoryItem[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

const STOCK_API = "/api/inventory/stock";
const STOCK_ADJUSTMENT_API = "/api/inventory/stock-adjustment";
const STORAGE_API = "/api/inventory/storage";

// Get stock levels for specific products in a storage
export async function getStockLevels(storageUuid: string, productUuids: string[]): Promise<StockLevel[]> {
  if (!storageUuid || !productUuids.length) return [];
  
  try {
    const response = await axios.get(`${STOCK_API}/levels`, {
      params: { 
        storageUuid, 
        productUuids: productUuids.join(",") 
      },
      headers: getAxiosAuthHeaders(),
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching stock levels:', error);
    throw error;
  }
}

// Get stock adjustment history for a storage with pagination
export async function getStockAdjustmentHistory(
  storageUuid: string, 
  page: number = 1, 
  limit: number = 10
): Promise<StockAdjustmentHistoryResponse> {
  if (!storageUuid) return { data: [], total: 0, page: 1, limit: 10, totalPages: 0 };
  
  try {
    const response = await axios.get(`${STOCK_ADJUSTMENT_API}/by-storage/${storageUuid}`, {
      params: { page, limit },
      headers: getAxiosAuthHeaders(),
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching stock adjustment history:', error);
    throw error;
  }
}

// Create stock adjustments
export async function createStockAdjustments(
  data: { storageUuid: string; adjustments: Array<{ productUuid: string; adjustment: number; reason: string }> },
  userUuid: string,
  warehouseUuid: string
): Promise<any> {
  try {
    // Validate adjustments
    const validAdjustments = data.adjustments.filter(
      (adj) => adj.productUuid && adj.adjustment !== 0
    );
    if (validAdjustments.length === 0) {
      throw new Error("Please fill product and adjustment fields for at least one adjustment row.");
    }
    
    const productUuids = validAdjustments.map((adj) => adj.productUuid);
    if (new Set(productUuids).size !== productUuids.length) {
      throw new Error(
        "Duplicate products found. Each product can only be adjusted once per submission."
      );
    }

    // Create individual requests for each adjustment
    const adjustmentPromises = validAdjustments.map(adj => {
      const payload = {
        storageUuid: data.storageUuid,
        productUuid: adj.productUuid,
        quantityAdjusted: adj.adjustment, // Backend expects 'quantityAdjusted'
        reason: adj.reason || '',
        userUuid,
        warehouseUuid
      };
      return axios.post(STOCK_ADJUSTMENT_API, payload, {
        headers: getAxiosAuthHeaders(),
      });
    });

    const responses = await Promise.all(adjustmentPromises);
    return responses.map(response => response.data);
  } catch (error) {
    console.error('Error creating stock adjustments:', error);
    throw error;
  }
}

// Get storage locations for a warehouse
export async function getStoragesByWarehouse(warehouseUuid: string): Promise<any[]> {
  if (!warehouseUuid) return [];
  
  try {
    const response = await axios.get(STORAGE_API, {
      params: { warehouseUuid },
      headers: getAxiosAuthHeaders(),
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching storages:', error);
    throw error;
  }
} 