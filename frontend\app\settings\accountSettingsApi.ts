// API abstraction for account settings module
import axios from 'axios';
import { getAxiosAuthHeaders } from '@/utils/authHeaders';

export interface AccountSettings {
  uuid: string;
  userUuid: string;
  theme: 'light' | 'dark' | 'auto';
  language: string;
  timezone: string;
  currency: string;
  dateFormat: string;
  timeFormat: string;
  notifications: {
    email: boolean;
    push: boolean;
    sms: boolean;
  };
  privacy: {
    profileVisibility: 'public' | 'private' | 'friends';
    dataSharing: boolean;
  };
  // POS-related settings
  preferredTaxRate?: number;
  preferredUseTax?: boolean;
  preferredPaymentMethod?: string;
  isDeleted: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface CreateAccountSettingsDto {
  theme?: 'light' | 'dark' | 'auto';
  language?: string;
  timezone?: string;
  currency?: string;
  dateFormat?: string;
  timeFormat?: string;
  notifications?: {
    email?: boolean;
    push?: boolean;
    sms?: boolean;
  };
  privacy?: {
    profileVisibility?: 'public' | 'private' | 'friends';
    dataSharing?: boolean;
  };
  // POS-related settings
  preferredTaxRate?: number;
  preferredUseTax?: boolean;
  preferredPaymentMethod?: string;
}

export interface UpdateAccountSettingsDto {
  theme?: 'light' | 'dark' | 'auto';
  language?: string;
  timezone?: string;
  currency?: string;
  dateFormat?: string;
  timeFormat?: string;
  notifications?: {
    email?: boolean;
    push?: boolean;
    sms?: boolean;
  };
  privacy?: {
    profileVisibility?: 'public' | 'private' | 'friends';
    dataSharing?: boolean;
  };
  // POS-related settings
  preferredTaxRate?: number;
  preferredUseTax?: boolean;
  preferredPaymentMethod?: string;
}

// Get account settings by user UUID
export async function getAccountSettingsByUser(userUuid: string): Promise<AccountSettings | null> {
  const response = await axios.get(`/api/account-settings/by-user/${userUuid}`, {
    headers: getAxiosAuthHeaders(),
  });
  return response.data;
}

// Get account settings by UUID
export async function getAccountSettings(uuid: string): Promise<AccountSettings> {
  const response = await axios.get(`/api/account-settings/${uuid}`, {
    headers: getAxiosAuthHeaders(),
  });
  return response.data;
}

// Create account settings
export async function createAccountSettings(data: CreateAccountSettingsDto & { userUuid: string }): Promise<AccountSettings> {
  const response = await axios.post('/api/account-settings', data, {
    headers: getAxiosAuthHeaders(),
  });
  return response.data;
}

// Update account settings
export async function updateAccountSettings(uuid: string, data: UpdateAccountSettingsDto): Promise<AccountSettings> {
  const response = await axios.put(`/api/account-settings/${uuid}`, data, {
    headers: getAxiosAuthHeaders(),
  });
  return response.data;
}

// Delete account settings (soft delete)
export async function deleteAccountSettings(uuid: string): Promise<void> {
  await axios.delete(`/api/account-settings/${uuid}`, {
    headers: getAxiosAuthHeaders(),
  });
}

// Get all account settings (admin function)
export async function getAllAccountSettings(): Promise<AccountSettings[]> {
  const response = await axios.get('/api/account-settings/list', {
    headers: getAxiosAuthHeaders(),
  });
  return response.data;
} 