---
description: The feature implementation workflow for mobile tasks
---

1. First You must first Analyse the full content of the following documentation (500 lines each): mobile/README.md, frontend/docs/UI_GUIDELINES.md
2. Analyse other files provided by user
3. Make sure to report issues and list the missing endpoints needed to accomplish the user request.
4. Next, look at the user request and fullfill it.
5. You must report all errors and potential issues you encounter