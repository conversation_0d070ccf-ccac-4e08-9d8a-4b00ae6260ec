import { <PERSON><PERSON>ty, PrimaryColumn, Column, CreateDateColumn, UpdateDateColumn, Index } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Uuid7 } from '../utils/uuid7';

@Entity('vans')
export class Van {
  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the van (primary key)",
  })
  @PrimaryColumn('uuid')
  id: string;

  @ApiProperty({ example: "Van 001", description: "Van name" })
  @Column()
  name: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the warehouse the van belongs to",
  })
  @Column('uuid')
  @Index()
  warehouseUuid: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the storage (truck) assigned to the van",
  })
  @Column('uuid')
  @Index()
  storageUuid: string;

  @ApiProperty({
    example: "ABC-123",
    description: "Van license plate",
    required: false,
  })
  @Column({ nullable: true })
  licensePlate?: string;

  @ApiProperty({
    example: "Ford Transit",
    description: "Van model",
    required: false,
  })
  @Column({ nullable: true })
  model?: string;

  @ApiProperty({ example: 2020, description: "Van year", required: false })
  @Column({ nullable: true })
  year?: number;

  @ApiProperty({ example: false, description: "Soft delete flag" })
  @Column({ default: false })
  isDeleted: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Helper method to generate UUID
  static generateId(): string {
    return new Uuid7().toString();
  }

  // Helper method to generate UUID
  static fromBinary(binary: any): string {
    if (!binary) return null;
    try {
      return new Uuid7(binary).toString();
    } catch {
      return null;
    }
  }
} 