import 'package:flutter/material.dart';
import '../../shared/services/auth_service.dart';

class LoginPage extends StatefulWidget {
  const LoginPage({super.key});

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final AuthService _authService = AuthService();
  bool _isLoading = false;
  String? _errorMessage;
  bool _showPasswordField = false;

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  /// Check if the password looks like a hash or has other issues
  String? _validatePasswordInput(String? password) {
    if (password == null || password.isEmpty) {
      return null; // Allow empty passwords for passwordless login
    }
    
    // Check for bcrypt hash patterns
    if (password.startsWith(r'$2b$') && password.length == 60) {
      return 'Please enter your actual password, not a hash. This might be from auto-fill - try typing your password manually.';
    }
    
    // Check for other common hash patterns
    if (password.startsWith(r'$2a$') || password.startsWith(r'$2y$') || password.startsWith(r'$2x$')) {
      return 'Please enter your actual password, not a hash. This might be from auto-fill - try typing your password manually.';
    }
    
    // Check for other suspicious patterns
    if (password.length > 100) {
      return 'Password seems too long. Please enter your actual password.';
    }
    
    // Check for base64-like patterns (common in some password managers)
    if (password.length > 30 && RegExp(r'^[A-Za-z0-9+/=]+$').hasMatch(password)) {
      return 'This looks like an encoded password. Please enter your actual password.';
    }
    
    return null;
  }

  Future<void> _login() async {
    if (!_formKey.currentState!.validate()) return;

    // Additional password validation
    final passwordValidationError = _validatePasswordInput(_passwordController.text);
    if (passwordValidationError != null) {
      setState(() {
        _errorMessage = passwordValidationError;
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final success = await _authService.login(
        _emailController.text.trim(),
        _passwordController.text, // Empty string is allowed for passwordless login
      );

      if (success) {
        // Navigation will be handled by AuthWrapper
      } else {
        setState(() {
          _errorMessage = 'Invalid email or password';
        });
      }
    } catch (e) {
      setState(() {
        // Check if the error is related to passwordless login
        if (e.toString().contains('passwordless login')) {
          _errorMessage = 'This account is set up for passwordless login. Please leave the password field empty.';
        } else if (e.toString().contains('requires a password')) {
          _errorMessage = 'This account requires a password. Please enter your password.';
        } else if (e.toString().contains('password format') || e.toString().contains('hash')) {
          _errorMessage = 'Password format error. Please enter your actual password, not a hash. Try typing it manually instead of using auto-fill.';
        } else {
          _errorMessage = 'Login failed. Please try again.';
        }
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _loginWithGoogle() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Debug Google Sign-In configuration before attempting login
      await _authService.debugGoogleSignInConfig();
      
      final success = await _authService.loginWithGoogle();

      if (success) {
        // Navigation will be handled by AuthWrapper
      } else {
        setState(() {
          _errorMessage = 'Google sign-in failed. Please check the debug logs for details.';
        });
      }
    } catch (e) {
      print('Login page error: $e');
      setState(() {
        _errorMessage = 'Google sign-in failed: ${e.toString()}';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) => Scaffold(
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Logo and title
              Icon(
                Icons.business,
                size: 80,
                color: Theme.of(context).primaryColor,
              ),
              const SizedBox(height: 24),
              Text(
                'Dido Distribution',
                style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              Text(
                'Mobile App',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: Colors.grey[600],
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 48),

              // Login form
              Form(
                key: _formKey,
                child: Column(
                  children: [
                    TextFormField(
                      controller: _emailController,
                      keyboardType: TextInputType.emailAddress,
                      decoration: const InputDecoration(
                        labelText: 'Email',
                        prefixIcon: Icon(Icons.email),
                        border: OutlineInputBorder(),
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter your email';
                        }
                        if (!value.contains('@')) {
                          return 'Please enter a valid email';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),
                    
                    // Password field with toggle
                    AnimatedContainer(
                      duration: const Duration(milliseconds: 300),
                      height: _showPasswordField ? null : 0,
                      child: _showPasswordField ? Column(
                        children: [
                          TextFormField(
                            controller: _passwordController,
                            obscureText: true,
                            decoration: const InputDecoration(
                              labelText: 'Password',
                              prefixIcon: Icon(Icons.lock),
                              border: OutlineInputBorder(),
                            ),
                            validator: (value) {
                              return _validatePasswordInput(value);
                            },
                          ),
                          const SizedBox(height: 16),
                        ],
                      ) : const SizedBox.shrink(),
                    ),
                    
                    // Toggle password field button
                    TextButton.icon(
                      onPressed: () {
                        setState(() {
                          _showPasswordField = !_showPasswordField;
                          if (!_showPasswordField) {
                            _passwordController.clear();
                          }
                        });
                      },
                      icon: Icon(_showPasswordField ? Icons.visibility_off : Icons.visibility),
                      label: Text(_showPasswordField ? 'Hide Password Field' : 'I have a password'),
                      style: TextButton.styleFrom(
                        foregroundColor: Theme.of(context).primaryColor,
                      ),
                    ),
                    
                    const SizedBox(height: 8),
                    
                    // Info text
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.blue[50],
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.blue[200]!),
                      ),
                      child: Row(
                        children: [
                          Icon(Icons.info, color: Colors.blue[700], size: 20),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              _showPasswordField 
                                ? 'Enter your password to login with password authentication.'
                                : 'Login without password by just entering your email address.',
                              style: TextStyle(
                                color: Colors.blue[700],
                                fontSize: 13,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    
                    const SizedBox(height: 16),

                    // Error message
                    if (_errorMessage != null)
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.red[50],
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.red[300]!),
                        ),
                        child: Row(
                          children: [
                            Icon(Icons.error, color: Colors.red[700], size: 20),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                _errorMessage!,
                                style: TextStyle(color: Colors.red[700]),
                              ),
                            ),
                          ],
                        ),
                      ),
                    if (_errorMessage != null) const SizedBox(height: 16),

                    // Login button
                    SizedBox(
                      width: double.infinity,
                      height: 48,
                      child: ElevatedButton(
                        onPressed: _isLoading ? null : _login,
                        child: _isLoading
                            ? const SizedBox(
                                height: 20,
                                width: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                ),
                              )
                            : Text(_showPasswordField ? 'Login with Password' : 'Login (Passwordless)'),
                      ),
                    ),

                    const SizedBox(height: 16),

                    // Divider
                    Row(
                      children: [
                        const Expanded(child: Divider()),
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 16),
                          child: Text(
                            'OR',
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                        const Expanded(child: Divider()),
                      ],
                    ),

                    const SizedBox(height: 16),

                    // Google Sign-In button
                    SizedBox(
                      width: double.infinity,
                      height: 48,
                      child: OutlinedButton.icon(
                        onPressed: _isLoading ? null : _loginWithGoogle,
                        icon: _isLoading
                            ? const SizedBox(
                                height: 20,
                                width: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                ),
                              )
                            : const Icon(
                                Icons.account_circle,
                                size: 20,
                              ),
                        label: const Text('Continue with Google'),
                        style: OutlinedButton.styleFrom(
                          side: BorderSide(color: Colors.grey[300]!),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
} 