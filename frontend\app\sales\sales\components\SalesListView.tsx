import React from 'react';
import { FiEye, FiShoppingCart, FiPrinter, FiX } from 'react-icons/fi';
import ItemsTable from '@/components/itemsTable/ItemsTable';
import TableActionButtons from '@/components/itemsTable/TableActionButtons';
import { Sale } from '../salesApi';
import { formatDate, formatCurrency, getStatusBadgeColor } from '../salesHelpers';

interface SalesListViewProps {
  sales: Sale[];
  totalPages: number;
  currentPage: number;
  isLoading: boolean;
  isLoadingInvoice: boolean;
  isLoadingEditSale?: boolean;
  isLoadingViewDetails?: boolean;
  isLoadingPrintInvoice?: boolean;
  isLoadingCancel?: boolean;
  onPageChange: (page: number) => void;
  onViewDetails: (sale: Sale) => void;
  onEdit: (sale: Sale) => void;
  onPrintInvoice: (sale: Sale) => void;
  onCancel: (sale: Sale) => void;
}

export const SalesListView: React.FC<SalesListViewProps> = ({
  sales,
  totalPages,
  currentPage,
  isLoading,
  isLoadingInvoice,
  isLoadingEditSale = false,
  isLoadingViewDetails = false,
  isLoadingPrintInvoice = false,
  isLoadingCancel = false,
  onPageChange,
  onViewDetails,
  onEdit,
  onPrintInvoice,
  onCancel
}) => {
  // Check if any action is currently loading
  const isAnyActionLoading = isLoadingEditSale || isLoadingViewDetails || isLoadingPrintInvoice || isLoadingCancel;

  return (
    <div className="bg-white rounded-xl shadow-lg border border-gray-200">
      <ItemsTable
        columns={[
          { 
            key: 'invoiceNumber', 
            header: 'Invoice #',
            render: (value: string, row: Sale) => (
              <div className="font-semibold text-gray-900">{value}</div>
            )
          },
          { 
            key: 'customerName', 
            header: 'Customer',
            render: (value: string, row: Sale) => (
              <div className="text-sm text-gray-900">{row.customerName || 'N/A'}</div>
            )
          },
          { 
            key: 'status', 
            header: 'Status',
            render: (value: string, row: Sale) => (
              <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusBadgeColor(value)}`}>
                {value.replace('_', ' ')}
              </span>
            )
          },
          { 
            key: 'totalAmount', 
            header: 'Total',
            render: (value: number, row: Sale) => (
              <div>
                <div className="text-sm text-gray-900">{formatCurrency(value)}</div>
                {row.balanceDue > 0 && (
                  <div className="text-xs text-red-600">
                    Due: {formatCurrency(row.balanceDue)}
                  </div>
                )}
              </div>
            )
          },
          { 
            key: 'createdAt', 
            header: 'Date',
            render: (value: string, row: Sale) => (
              <div className="text-sm text-gray-900">{formatDate(value)}</div>
            )
          },
          {
            key: 'actions',
            header: 'Actions',
            render: (_: any, row: Sale) => (
              <div className="flex items-center justify-center gap-2">
                <button
                  onClick={() => {
                    console.log('[SalesListView] View Details button clicked for sale:', {
                      uuid: row.uuid,
                      invoiceNumber: row.invoiceNumber,
                      status: row.status
                    });
                    onViewDetails(row);
                  }}
                  className="p-2 rounded-full hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-blue-400 disabled:opacity-50 disabled:cursor-not-allowed"
                  title="View Details"
                  aria-label="View Details"
                  disabled={isAnyActionLoading}
                >
                  {isLoadingViewDetails ? (
                    <div className="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin" />
                  ) : (
                  <FiEye className="w-4 h-4 text-blue-600" />
                  )}
                </button>
                <button
                  onClick={() => {
                    console.log('[SalesListView] Edit button clicked for sale:', {
                      uuid: row.uuid,
                      invoiceNumber: row.invoiceNumber,
                      status: row.status
                    });
                    onEdit(row);
                  }}
                  className="p-2 rounded-full hover:bg-purple-100 focus:outline-none focus:ring-2 focus:ring-purple-400 disabled:opacity-50 disabled:cursor-not-allowed"
                  title="Edit Sale"
                  aria-label="Edit Sale"
                  disabled={isAnyActionLoading}
                >
                  {isLoadingEditSale ? (
                    <div className="w-4 h-4 border-2 border-purple-600 border-t-transparent rounded-full animate-spin" />
                  ) : (
                  <FiShoppingCart className="w-4 h-4 text-purple-600" />
                  )}
                </button>
                <button
                  onClick={() => onPrintInvoice(row)}
                  className="p-2 rounded-full hover:bg-green-100 focus:outline-none focus:ring-2 focus:ring-green-400 disabled:opacity-50 disabled:cursor-not-allowed"
                  title="Print Invoice"
                  aria-label="Print Invoice"
                  disabled={isAnyActionLoading}
                >
                  {isLoadingPrintInvoice ? (
                    <div className="w-4 h-4 border-2 border-green-600 border-t-transparent rounded-full animate-spin" />
                  ) : (
                  <FiPrinter className="w-4 h-4 text-green-600" />
                  )}
                </button>
                <button
                  onClick={() => onCancel(row)}
                  className="p-2 rounded-full hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-red-400 disabled:opacity-50 disabled:cursor-not-allowed"
                  title="Cancel Sale"
                  aria-label="Cancel Sale"
                  disabled={isAnyActionLoading || row.status === 'cancelled'}
                >
                  {isLoadingCancel ? (
                    <div className="w-4 h-4 border-2 border-red-600 border-t-transparent rounded-full animate-spin" />
                  ) : (
                  <FiX className="w-4 h-4 text-red-600" />
                  )}
                </button>
              </div>
            ),
            headerClassName: 'text-center',
            cellClassName: 'text-center',
          },
        ]}
        data={sales}
        isLoading={isLoading}
        noDataText={<span className="text-gray-400">No sales found.</span>}
        containerClassName=""
      />

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="px-4 py-3 border-t border-gray-200">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-700">
              Showing page {currentPage} of {totalPages}
            </div>
            <div className="flex space-x-2">
              <button
                onClick={() => onPageChange(currentPage - 1)}
                disabled={currentPage === 1}
                className="px-3 py-1 text-sm border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
              >
                Previous
              </button>
              <button
                onClick={() => onPageChange(currentPage + 1)}
                disabled={currentPage === totalPages}
                className="px-3 py-1 text-sm border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
              >
                Next
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}; 