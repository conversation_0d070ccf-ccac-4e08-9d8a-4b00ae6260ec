import { MigrationInterface, QueryRunner } from "typeorm";

export class FirstMigration1753519907699 implements MigrationInterface {
    name = 'FirstMigration1753519907699'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "warehouses" ("id" uuid NOT NULL, "name" character varying, "description" character varying, "userUuid" uuid NOT NULL, "mainStorageUuid" uuid, "isDeleted" boolean NOT NULL DEFAULT false, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_56ae21ee2432b2270b48867e4be" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_652ceca4292dbe2a6c367f0a74" ON "warehouses" ("userUuid") `);
        await queryRunner.query(`CREATE INDEX "IDX_b6a3393d3310c0a072c467270f" ON "warehouses" ("mainStorageUuid") `);
        await queryRunner.query(`CREATE TABLE "roles" ("id" uuid NOT NULL, "name" character varying NOT NULL, "permissions" text array NOT NULL DEFAULT '{}', "warehouseUuid" uuid NOT NULL, "isDeleted" boolean NOT NULL DEFAULT false, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_c1433d71a4838793a49dcad46ab" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_fab9825adaa137531a363274c1" ON "roles" ("warehouseUuid") `);
        await queryRunner.query(`CREATE TABLE "vans" ("id" uuid NOT NULL, "name" character varying NOT NULL, "warehouseUuid" uuid NOT NULL, "storageUuid" uuid NOT NULL, "licensePlate" character varying, "model" character varying, "year" integer, "isDeleted" boolean NOT NULL DEFAULT false, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_e99634b8c51fff5d98a44d8bfbe" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_44199adb201eb4bc094a39d071" ON "vans" ("warehouseUuid") `);
        await queryRunner.query(`CREATE INDEX "IDX_96cb370ab57b8830da712c923b" ON "vans" ("storageUuid") `);
        await queryRunner.query(`CREATE TABLE "features" ("id" uuid NOT NULL, "identifier" character varying NOT NULL, "name" character varying NOT NULL, "isDeleted" boolean NOT NULL DEFAULT false, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "UQ_1284938679c82100115a9b68492" UNIQUE ("identifier"), CONSTRAINT "PK_5c1e336df2f4a7051e5bf08a941" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_1284938679c82100115a9b6849" ON "features" ("identifier") `);
        await queryRunner.query(`CREATE TABLE "suppliers" ("id" uuid NOT NULL, "warehouseUuid" uuid, "userUuid" uuid NOT NULL, "name" character varying NOT NULL, "fiscalId" character varying, "rc" character varying, "code" character varying, "articleNumber" character varying, "description" character varying, "email" character varying, "phone" character varying, "address" character varying, "latitude" numeric(10,8), "longitude" numeric(11,8), "notes" character varying, "isDeleted" boolean NOT NULL DEFAULT false, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_b70ac51766a9e3144f778cfe81e" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_dac342b8fab96d19bd854775bd" ON "suppliers" ("warehouseUuid") `);
        await queryRunner.query(`CREATE INDEX "IDX_0f6113d0c1bac97ffade96fab8" ON "suppliers" ("userUuid") `);
        await queryRunner.query(`CREATE TYPE "public"."users_usertype_enum" AS ENUM('super', 'user')`);
        await queryRunner.query(`CREATE TABLE "users" ("id" uuid NOT NULL, "warehouseUuid" uuid NOT NULL, "email" character varying, "name" character varying NOT NULL, "roleUuid" uuid, "userType" "public"."users_usertype_enum" NOT NULL DEFAULT 'user', "vanUuid" uuid, "password" character varying, "isDeleted" boolean NOT NULL DEFAULT false, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "UQ_97672ac88f789774dd47f7c8be3" UNIQUE ("email"), CONSTRAINT "PK_a3ffb1c0c8416b9fc6f907b7433" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_c7d8c4678cf1c2bf3a706e7c90" ON "users" ("warehouseUuid") `);
        await queryRunner.query(`CREATE INDEX "IDX_b39d66a4614c9ce63dba591b09" ON "users" ("roleUuid") `);
        await queryRunner.query(`CREATE INDEX "IDX_c188d445366e2a493172acf03c" ON "users" ("vanUuid") `);
        await queryRunner.query(`CREATE TABLE "account_plans" ("id" uuid NOT NULL, "identifier" character varying NOT NULL, "name" character varying NOT NULL, "features" text array NOT NULL, "isDeleted" boolean NOT NULL DEFAULT false, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "UQ_b1cb7ec405354fe580ee2bceb68" UNIQUE ("identifier"), CONSTRAINT "PK_a90e68f8c0c77deb8f1dec10693" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_b1cb7ec405354fe580ee2bceb6" ON "account_plans" ("identifier") `);
        await queryRunner.query(`CREATE TYPE "public"."sales_paymentmethod_enum" AS ENUM('cash', 'credit_card', 'bank_transfer', 'mobile_payment', 'cheque', 'other')`);
        await queryRunner.query(`CREATE TYPE "public"."sales_status_enum" AS ENUM('paid', 'partially_paid', 'unpaid', 'cancelled')`);
        await queryRunner.query(`CREATE TABLE "sales" ("id" uuid NOT NULL, "invoiceNumber" character varying NOT NULL, "customerUuid" uuid, "customerName" character varying, "customerFiscalId" character varying, "customerRc" character varying, "customerArticleNumber" character varying, "orderUuid" uuid, "subtotal" numeric(10,2) NOT NULL DEFAULT '0', "useTax" boolean NOT NULL DEFAULT false, "taxRate" numeric(5,4) NOT NULL DEFAULT '0.1', "taxAmount" numeric(10,2) NOT NULL DEFAULT '0', "totalAmount" numeric(10,2) NOT NULL, "amountPaid" numeric(10,2) NOT NULL DEFAULT '0', "balanceDue" numeric(10,2) NOT NULL DEFAULT '0', "paymentMethod" "public"."sales_paymentmethod_enum" NOT NULL DEFAULT 'cash', "paymentDate" jsonb, "invoiceDate" TIMESTAMP NOT NULL, "dueDate" TIMESTAMP NOT NULL, "status" "public"."sales_status_enum" NOT NULL DEFAULT 'unpaid', "createdBy" uuid NOT NULL, "updatedBy" uuid NOT NULL, "isDeleted" boolean NOT NULL DEFAULT false, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "UQ_4f4006dc362ea9cc6308b4fb23d" UNIQUE ("invoiceNumber"), CONSTRAINT "PK_4f0bc990ae81dba46da680895ea" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_697d250cee82454463dcf7cd03" ON "sales" ("customerUuid") `);
        await queryRunner.query(`CREATE INDEX "IDX_c155d66393bcfb33d2713ed983" ON "sales" ("orderUuid") `);
        await queryRunner.query(`CREATE INDEX "IDX_1a09bec123377bb0adee2e00e6" ON "sales" ("createdBy") `);
        await queryRunner.query(`CREATE INDEX "IDX_23a47ae0fbbb0b5183f156d881" ON "sales" ("updatedBy") `);
        await queryRunner.query(`CREATE TABLE "sale_items" ("id" uuid NOT NULL, "saleUuid" uuid NOT NULL, "productUuid" uuid NOT NULL, "name" character varying NOT NULL, "quantity" numeric(10,2) NOT NULL, "unitPrice" numeric(10,2) NOT NULL, "lineTotal" numeric(10,2) NOT NULL, "taxAmount" numeric(10,2) NOT NULL DEFAULT '0', "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_5a7dc5b4562a9e590528b3e08ab" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_0efca62e7f82b324a8ec9fbc6d" ON "sale_items" ("saleUuid") `);
        await queryRunner.query(`CREATE INDEX "IDX_e075999cadd7b6c2f8fd88bd0e" ON "sale_items" ("productUuid") `);
        await queryRunner.query(`CREATE TABLE "quote_items" ("id" uuid NOT NULL, "quoteUuid" uuid NOT NULL, "productUuid" uuid NOT NULL, "name" character varying NOT NULL, "quantity" numeric(10,2) NOT NULL, "unitPrice" numeric(10,2) NOT NULL, "lineTotal" numeric(10,2) NOT NULL, "notes" character varying, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_135ad3f02b5abcf65fb5cb20ad2" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_549fc572192bfb0368fea966f4" ON "quote_items" ("quoteUuid") `);
        await queryRunner.query(`CREATE INDEX "IDX_338bbc4dda25f6ad120ea89a89" ON "quote_items" ("productUuid") `);
        await queryRunner.query(`CREATE TYPE "public"."quotes_status_enum" AS ENUM('draft', 'sent', 'accepted', 'rejected', 'expired', 'converted')`);
        await queryRunner.query(`CREATE TABLE "quotes" ("id" uuid NOT NULL, "quoteNumber" character varying NOT NULL, "customerUuid" uuid NOT NULL, "warehouseUuid" uuid NOT NULL, "totalAmount" numeric(10,2) NOT NULL, "status" "public"."quotes_status_enum" NOT NULL DEFAULT 'draft', "quoteDate" TIMESTAMP NOT NULL, "expiryDate" TIMESTAMP NOT NULL, "notes" character varying, "convertedToOrderUuid" uuid, "createdBy" uuid NOT NULL, "updatedBy" uuid NOT NULL, "isDeleted" boolean NOT NULL DEFAULT false, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "UQ_a3cfb26a07c0ac65bd019e9bc50" UNIQUE ("quoteNumber"), CONSTRAINT "PK_99a0e8bcbcd8719d3a41f23c263" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_cb7a370eb2780e8f57790046a6" ON "quotes" ("customerUuid") `);
        await queryRunner.query(`CREATE INDEX "IDX_90e6a859d9b93209baf96c0695" ON "quotes" ("warehouseUuid") `);
        await queryRunner.query(`CREATE INDEX "IDX_b125c437bdb29a8fae2159df26" ON "quotes" ("convertedToOrderUuid") `);
        await queryRunner.query(`CREATE INDEX "IDX_437fcbe61c403c74f95167094f" ON "quotes" ("createdBy") `);
        await queryRunner.query(`CREATE INDEX "IDX_00b46d17fb57fdc223dfa9421b" ON "quotes" ("updatedBy") `);
        await queryRunner.query(`CREATE TABLE "order_items" ("id" uuid NOT NULL, "orderUuid" uuid NOT NULL, "productUuid" uuid NOT NULL, "name" character varying NOT NULL, "quantity" numeric(10,2) NOT NULL, "unitPrice" numeric(10,2) NOT NULL, "lineTotal" numeric(10,2) NOT NULL, "taxAmount" numeric(10,2) NOT NULL DEFAULT '0', "notes" character varying, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_005269d8574e6fac0493715c308" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_7ba6254fb98518a78489f289aa" ON "order_items" ("orderUuid") `);
        await queryRunner.query(`CREATE INDEX "IDX_cfb14ae07f338a26a7f7f78383" ON "order_items" ("productUuid") `);
        await queryRunner.query(`CREATE TYPE "public"."orders_status_enum" AS ENUM('draft', 'pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled', 'returned')`);
        await queryRunner.query(`CREATE TYPE "public"."orders_priority_enum" AS ENUM('low', 'normal', 'high', 'urgent')`);
        await queryRunner.query(`CREATE TABLE "orders" ("id" uuid NOT NULL, "orderNumber" character varying NOT NULL, "customerUuid" uuid NOT NULL, "customerName" character varying, "customerFiscalId" character varying, "customerRc" character varying, "customerArticleNumber" character varying, "warehouseUuid" uuid NOT NULL, "quoteUuid" uuid, "subtotal" numeric(10,2) NOT NULL DEFAULT '0', "useTax" boolean NOT NULL DEFAULT false, "taxRate" numeric(5,4) NOT NULL DEFAULT '0.1', "taxAmount" numeric(10,2) NOT NULL DEFAULT '0', "totalAmount" numeric(10,2) NOT NULL, "orderDate" TIMESTAMP NOT NULL, "requestedDeliveryDate" TIMESTAMP, "actualDeliveryDate" TIMESTAMP, "status" "public"."orders_status_enum" NOT NULL DEFAULT 'draft', "priority" "public"."orders_priority_enum" NOT NULL DEFAULT 'normal', "notes" character varying, "createdBy" uuid NOT NULL, "updatedBy" uuid NOT NULL, "isDeleted" boolean NOT NULL DEFAULT false, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "UQ_59b0c3b34ea0fa5562342f24143" UNIQUE ("orderNumber"), CONSTRAINT "PK_710e2d4957aa5878dfe94e4ac2f" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_6254c38686cb8fa526e460e2f3" ON "orders" ("customerUuid") `);
        await queryRunner.query(`CREATE INDEX "IDX_112a0b06b6ba052cd38b24e760" ON "orders" ("warehouseUuid") `);
        await queryRunner.query(`CREATE INDEX "IDX_71759a2e943d5ac56ac6581ea7" ON "orders" ("quoteUuid") `);
        await queryRunner.query(`CREATE INDEX "IDX_8d17fd47a7bbf512e58209fbb3" ON "orders" ("createdBy") `);
        await queryRunner.query(`CREATE INDEX "IDX_6b1a67abb0da83b51c7a2fbac4" ON "orders" ("updatedBy") `);
        await queryRunner.query(`CREATE TABLE "routes" ("id" uuid NOT NULL, "name" character varying NOT NULL, "description" text, "customerLocations" jsonb NOT NULL, "optimizedRoute" jsonb NOT NULL, "totalDistance" numeric(10,2) NOT NULL, "warehouseId" uuid NOT NULL, "isDeleted" boolean NOT NULL DEFAULT false, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_76100511cdfa1d013c859f01d8b" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "regions" ("id" uuid NOT NULL, "name" character varying NOT NULL, "description" character varying, "latitude" numeric(10,8), "longitude" numeric(11,8), "warehouseUuid" uuid NOT NULL, "isDeleted" boolean NOT NULL DEFAULT false, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_4fcd12ed6a046276e2deb08801c" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_2cd9e0fc23ee71e5290abf7b74" ON "regions" ("warehouseUuid") `);
        await queryRunner.query(`CREATE TYPE "public"."purchases_paymentmethod_enum" AS ENUM('cash', 'credit_card', 'bank_transfer', 'mobile_payment', 'cheque', 'other')`);
        await queryRunner.query(`CREATE TYPE "public"."purchases_status_enum" AS ENUM('paid', 'partially_paid', 'unpaid', 'cancelled')`);
        await queryRunner.query(`CREATE TABLE "purchases" ("id" uuid NOT NULL, "userUuid" uuid NOT NULL, "warehouseUuid" uuid NOT NULL, "supplierUuid" uuid, "itemsSnapshot" jsonb NOT NULL DEFAULT '[]', "totalAmount" numeric(10,2) NOT NULL DEFAULT '0', "amountPaid" numeric(10,2) NOT NULL DEFAULT '0', "balanceDue" numeric(10,2) NOT NULL DEFAULT '0', "paymentMethod" "public"."purchases_paymentmethod_enum" NOT NULL DEFAULT 'cash', "paymentDate" TIMESTAMP NOT NULL DEFAULT now(), "invoiceDate" TIMESTAMP NOT NULL DEFAULT now(), "dueDate" TIMESTAMP NOT NULL DEFAULT now(), "status" "public"."purchases_status_enum" NOT NULL DEFAULT 'unpaid', "isDeleted" boolean NOT NULL DEFAULT false, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_1d55032f37a34c6eceacbbca6b8" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_c465641423669ca997574fc976" ON "purchases" ("userUuid") `);
        await queryRunner.query(`CREATE INDEX "IDX_e298b4ae6fa4e222aa419a3b7a" ON "purchases" ("warehouseUuid") `);
        await queryRunner.query(`CREATE INDEX "IDX_b93490e30940d67011cbcadaba" ON "purchases" ("supplierUuid") `);
        await queryRunner.query(`CREATE TABLE "products" ("id" uuid NOT NULL, "warehouseUuid" uuid NOT NULL, "name" character varying NOT NULL, "description" character varying, "sku" character varying, "barcode" character varying, "productCategoryUuid" uuid, "retailPrice" numeric(10,2), "wholesalePrice" numeric(10,2), "midWholesalePrice" numeric(10,2), "institutionalPrice" numeric(10,2), "cost" numeric(10,2), "isDeleted" boolean NOT NULL DEFAULT false, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_0806c755e0aca124e67c0cf6d7d" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_ea85f9d9a2263b2208291654ce" ON "products" ("warehouseUuid") `);
        await queryRunner.query(`CREATE INDEX "IDX_5ebd2dd1cf8dcac295cd4348df" ON "products" ("productCategoryUuid") `);
        await queryRunner.query(`CREATE TABLE "product_categories" ("id" uuid NOT NULL, "name" character varying NOT NULL, "warehouseUuid" uuid NOT NULL, "createdBy" uuid NOT NULL, "updatedBy" uuid NOT NULL, "isDeleted" boolean NOT NULL DEFAULT false, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_7069dac60d88408eca56fdc9e0c" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_10a72b47d7fc7a582eeb5d32db" ON "product_categories" ("warehouseUuid") `);
        await queryRunner.query(`CREATE INDEX "IDX_21ae5e0232ee071f22d3b255a1" ON "product_categories" ("createdBy") `);
        await queryRunner.query(`CREATE INDEX "IDX_85584faefbab21ea138ce54276" ON "product_categories" ("updatedBy") `);
        await queryRunner.query(`CREATE TABLE "logs" ("id" uuid NOT NULL, "userUuid" uuid NOT NULL, "operation" character varying NOT NULL, "entity" character varying NOT NULL, "description" text, "data" json, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_fb1b805f2f7795de79fa69340ba" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_62424ef4784630c9a1d7b455b6" ON "logs" ("userUuid") `);
        await queryRunner.query(`CREATE INDEX "IDX_6fe51a247e223fde9196a64b85" ON "logs" ("operation") `);
        await queryRunner.query(`CREATE INDEX "IDX_874897ac4b6729a705ff40f126" ON "logs" ("entity") `);
        await queryRunner.query(`CREATE TYPE "public"."storages_type_enum" AS ENUM('warehouse', 'truck')`);
        await queryRunner.query(`CREATE TABLE "storages" ("id" uuid NOT NULL, "type" "public"."storages_type_enum" NOT NULL DEFAULT 'warehouse', "name" character varying NOT NULL, "warehouseUuid" uuid NOT NULL, "userUuid" uuid NOT NULL, "isDeleted" boolean NOT NULL DEFAULT false, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_2f2d2fae6dc214f7f3ec52189ce" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_caae1d5cff8f170ac653970f5c" ON "storages" ("warehouseUuid") `);
        await queryRunner.query(`CREATE INDEX "IDX_d09a9916c3a45e57cf78c6b762" ON "storages" ("userUuid") `);
        await queryRunner.query(`CREATE TABLE "stock_adjustments" ("id" uuid NOT NULL, "userUuid" uuid NOT NULL, "warehouseUuid" uuid NOT NULL, "storageUuid" uuid NOT NULL, "productUuid" uuid NOT NULL, "quantityAdjusted" integer NOT NULL, "reason" character varying, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_7dc03d92f242dd489d33b80d063" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_cde015e5952ac7f22017a1b171" ON "stock_adjustments" ("userUuid") `);
        await queryRunner.query(`CREATE INDEX "IDX_a5f9a231f6416b7ff53aa1fbe6" ON "stock_adjustments" ("warehouseUuid") `);
        await queryRunner.query(`CREATE INDEX "IDX_6d279dfbfc8cb882656e2014c7" ON "stock_adjustments" ("storageUuid") `);
        await queryRunner.query(`CREATE INDEX "IDX_7cdeeb4ae5a355b5d74cf6f8bb" ON "stock_adjustments" ("productUuid") `);
        await queryRunner.query(`CREATE TABLE "inventory_items" ("id" uuid NOT NULL, "productUuid" uuid NOT NULL, "storageUuid" uuid NOT NULL, "quantity" integer NOT NULL DEFAULT '0', "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_cf2f451407242e132547ac19169" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_61c901c2641b051293e45a16e7" ON "inventory_items" ("productUuid") `);
        await queryRunner.query(`CREATE INDEX "IDX_4761c1ee9d89dd922d493d2e64" ON "inventory_items" ("storageUuid") `);
        await queryRunner.query(`CREATE TYPE "public"."customer_payments_paymentmethod_enum" AS ENUM('cash', 'credit_card', 'debit_card', 'bank_transfer', 'check', 'mobile_payment')`);
        await queryRunner.query(`CREATE TYPE "public"."customer_payments_status_enum" AS ENUM('pending', 'completed', 'failed', 'cancelled', 'refunded')`);
        await queryRunner.query(`CREATE TABLE "customer_payments" ("id" uuid NOT NULL, "customerUuid" uuid NOT NULL, "userUuid" uuid NOT NULL, "warehouseUuid" uuid NOT NULL, "saleUuid" uuid, "paymentMethod" "public"."customer_payments_paymentmethod_enum" NOT NULL, "amount" numeric(10,2) NOT NULL, "status" "public"."customer_payments_status_enum" NOT NULL DEFAULT 'pending', "description" character varying NOT NULL, "referenceNumber" character varying, "processedAt" TIMESTAMP, "previousCreditBalance" numeric(10,2) NOT NULL, "newCreditBalance" numeric(10,2) NOT NULL, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_49f9fc4bd44d957db20148928d1" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_c4f626bf4728516ce5ca2e5b90" ON "customer_payments" ("customerUuid") `);
        await queryRunner.query(`CREATE INDEX "IDX_4acea20b8da6aa09168913f4d1" ON "customer_payments" ("userUuid") `);
        await queryRunner.query(`CREATE INDEX "IDX_3ed2430e5cd85882586b4cb028" ON "customer_payments" ("warehouseUuid") `);
        await queryRunner.query(`CREATE INDEX "IDX_7087bb99d5aa5c2c1d5a922960" ON "customer_payments" ("saleUuid") `);
        await queryRunner.query(`CREATE TYPE "public"."customers_customertype_enum" AS ENUM('retail', 'wholesale', 'mid-wholesale', 'institutional')`);
        await queryRunner.query(`CREATE TABLE "customers" ("id" uuid NOT NULL, "name" character varying NOT NULL, "fiscalId" character varying, "email" character varying, "phone" character varying, "address" character varying, "rc" character varying, "articleNumber" character varying, "customerType" "public"."customers_customertype_enum" NOT NULL, "latitude" numeric(10,8), "longitude" numeric(11,8), "warehouseUuid" uuid, "regionUuid" uuid, "currentCredit" numeric(10,2) NOT NULL DEFAULT '0', "isDeleted" boolean NOT NULL DEFAULT false, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_133ec679a801fab5e070f73d3ea" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_27cc54f946c9f9f6323a099ab4" ON "customers" ("warehouseUuid") `);
        await queryRunner.query(`CREATE INDEX "IDX_00f58093dbb2791f182a1d5855" ON "customers" ("regionUuid") `);
        await queryRunner.query(`CREATE INDEX "IDX_5a8b204a7d1df8097bbf1d2483" ON "customers" ("currentCredit") `);
        await queryRunner.query(`CREATE TYPE "public"."credit_adjustments_adjustmenttype_enum" AS ENUM('sale', 'payment', 'refund', 'manual', 'correction')`);
        await queryRunner.query(`CREATE TABLE "credit_adjustments" ("id" uuid NOT NULL, "customerUuid" uuid NOT NULL, "userUuid" uuid NOT NULL, "warehouseUuid" uuid NOT NULL, "saleUuid" uuid, "adjustmentType" "public"."credit_adjustments_adjustmenttype_enum" NOT NULL, "amountAdjusted" numeric(10,2) NOT NULL, "previousBalance" numeric(10,2) NOT NULL, "newBalance" numeric(10,2) NOT NULL, "reason" character varying NOT NULL, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_577bdb2762a2adccf5c01bf2d49" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_2f5da58436bb645f6b835c0660" ON "credit_adjustments" ("customerUuid") `);
        await queryRunner.query(`CREATE INDEX "IDX_b6f1f3836bffb78cce3831f169" ON "credit_adjustments" ("userUuid") `);
        await queryRunner.query(`CREATE INDEX "IDX_834d9606960019170b4d3b771c" ON "credit_adjustments" ("warehouseUuid") `);
        await queryRunner.query(`CREATE INDEX "IDX_a6546e3c66d1855bd7e4a98037" ON "credit_adjustments" ("saleUuid") `);
        await queryRunner.query(`CREATE TABLE "companies" ("id" uuid NOT NULL, "userUuid" uuid NOT NULL, "name" character varying NOT NULL, "nif" character varying NOT NULL, "rc" character varying NOT NULL, "articleNumber" character varying NOT NULL, "address" character varying NOT NULL, "website" character varying, "phoneNumber" character varying NOT NULL, "isDeleted" boolean NOT NULL DEFAULT false, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_d4bc3e82a314fa9e29f652c2c22" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_bfe2d11c5455d5014fb0830862" ON "companies" ("userUuid") `);
        await queryRunner.query(`CREATE TABLE "refresh_tokens" ("tokenId" uuid NOT NULL, "userId" uuid NOT NULL, "userEmail" character varying NOT NULL, "token" character varying NOT NULL, "expiresAt" TIMESTAMP NOT NULL, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "lastUsedAt" TIMESTAMP, "isRevoked" boolean NOT NULL DEFAULT false, "ipAddress" character varying, "userAgent" character varying, "familyId" uuid, CONSTRAINT "PK_48064cd66bef5bbbcc3eb196229" PRIMARY KEY ("tokenId"))`);
        await queryRunner.query(`CREATE INDEX "IDX_610102b60fea1455310ccd299d" ON "refresh_tokens" ("userId") `);
        await queryRunner.query(`CREATE TABLE "rate_limits" ("identifier" character varying NOT NULL, "attempts" integer NOT NULL DEFAULT '0', "firstAttempt" TIMESTAMP NOT NULL DEFAULT now(), "lastAttempt" TIMESTAMP NOT NULL DEFAULT now(), "lockedUntil" TIMESTAMP, "lockoutReason" character varying, CONSTRAINT "PK_12a88d702be8e369a412d0a1f3b" PRIMARY KEY ("identifier"))`);
        await queryRunner.query(`CREATE INDEX "IDX_12a88d702be8e369a412d0a1f3" ON "rate_limits" ("identifier") `);
        await queryRunner.query(`CREATE TABLE "auth_audits" ("id" SERIAL NOT NULL, "eventType" character varying NOT NULL, "userId" uuid, "userEmail" character varying, "ipAddress" character varying, "userAgent" character varying, "timestamp" TIMESTAMP NOT NULL DEFAULT now(), "details" jsonb, "success" boolean NOT NULL, "errorMessage" character varying, CONSTRAINT "PK_2fb5dc34a39902eb1d7f163bb0d" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_4437faa0e58d1482d9ef88f3cc" ON "auth_audits" ("userId") `);
        await queryRunner.query(`CREATE TABLE "account_settings" ("id" uuid NOT NULL, "userId" uuid NOT NULL, "preferredLanguage" character varying(10) NOT NULL DEFAULT 'en', "preferredTheme" character varying(10) NOT NULL DEFAULT 'light', "preferredPaymentMethod" character varying(50) NOT NULL DEFAULT 'cash', "invoiceFormat" character varying(50) NOT NULL DEFAULT 'standard', "preferredTaxRate" numeric(5,2) NOT NULL DEFAULT '20', "preferredUseTax" boolean NOT NULL DEFAULT true, "userAccountPlan" character varying(50) NOT NULL DEFAULT 'basic', "isDeleted" boolean NOT NULL DEFAULT false, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_cede89a31d2392a1064087af67a" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "sale_items" ADD CONSTRAINT "FK_0efca62e7f82b324a8ec9fbc6dd" FOREIGN KEY ("saleUuid") REFERENCES "sales"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "quote_items" ADD CONSTRAINT "FK_549fc572192bfb0368fea966f41" FOREIGN KEY ("quoteUuid") REFERENCES "quotes"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "order_items" ADD CONSTRAINT "FK_7ba6254fb98518a78489f289aaa" FOREIGN KEY ("orderUuid") REFERENCES "orders"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "routes" ADD CONSTRAINT "FK_f44b11d541063eca5345de21355" FOREIGN KEY ("warehouseId") REFERENCES "warehouses"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "account_settings" ADD CONSTRAINT "FK_f3c07482086b855b78187190407" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "account_settings" DROP CONSTRAINT "FK_f3c07482086b855b78187190407"`);
        await queryRunner.query(`ALTER TABLE "routes" DROP CONSTRAINT "FK_f44b11d541063eca5345de21355"`);
        await queryRunner.query(`ALTER TABLE "order_items" DROP CONSTRAINT "FK_7ba6254fb98518a78489f289aaa"`);
        await queryRunner.query(`ALTER TABLE "quote_items" DROP CONSTRAINT "FK_549fc572192bfb0368fea966f41"`);
        await queryRunner.query(`ALTER TABLE "sale_items" DROP CONSTRAINT "FK_0efca62e7f82b324a8ec9fbc6dd"`);
        await queryRunner.query(`DROP TABLE "account_settings"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_4437faa0e58d1482d9ef88f3cc"`);
        await queryRunner.query(`DROP TABLE "auth_audits"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_12a88d702be8e369a412d0a1f3"`);
        await queryRunner.query(`DROP TABLE "rate_limits"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_610102b60fea1455310ccd299d"`);
        await queryRunner.query(`DROP TABLE "refresh_tokens"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_bfe2d11c5455d5014fb0830862"`);
        await queryRunner.query(`DROP TABLE "companies"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_a6546e3c66d1855bd7e4a98037"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_834d9606960019170b4d3b771c"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_b6f1f3836bffb78cce3831f169"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_2f5da58436bb645f6b835c0660"`);
        await queryRunner.query(`DROP TABLE "credit_adjustments"`);
        await queryRunner.query(`DROP TYPE "public"."credit_adjustments_adjustmenttype_enum"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_5a8b204a7d1df8097bbf1d2483"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_00f58093dbb2791f182a1d5855"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_27cc54f946c9f9f6323a099ab4"`);
        await queryRunner.query(`DROP TABLE "customers"`);
        await queryRunner.query(`DROP TYPE "public"."customers_customertype_enum"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_7087bb99d5aa5c2c1d5a922960"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_3ed2430e5cd85882586b4cb028"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_4acea20b8da6aa09168913f4d1"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_c4f626bf4728516ce5ca2e5b90"`);
        await queryRunner.query(`DROP TABLE "customer_payments"`);
        await queryRunner.query(`DROP TYPE "public"."customer_payments_status_enum"`);
        await queryRunner.query(`DROP TYPE "public"."customer_payments_paymentmethod_enum"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_4761c1ee9d89dd922d493d2e64"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_61c901c2641b051293e45a16e7"`);
        await queryRunner.query(`DROP TABLE "inventory_items"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_7cdeeb4ae5a355b5d74cf6f8bb"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_6d279dfbfc8cb882656e2014c7"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_a5f9a231f6416b7ff53aa1fbe6"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_cde015e5952ac7f22017a1b171"`);
        await queryRunner.query(`DROP TABLE "stock_adjustments"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_d09a9916c3a45e57cf78c6b762"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_caae1d5cff8f170ac653970f5c"`);
        await queryRunner.query(`DROP TABLE "storages"`);
        await queryRunner.query(`DROP TYPE "public"."storages_type_enum"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_874897ac4b6729a705ff40f126"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_6fe51a247e223fde9196a64b85"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_62424ef4784630c9a1d7b455b6"`);
        await queryRunner.query(`DROP TABLE "logs"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_85584faefbab21ea138ce54276"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_21ae5e0232ee071f22d3b255a1"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_10a72b47d7fc7a582eeb5d32db"`);
        await queryRunner.query(`DROP TABLE "product_categories"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_5ebd2dd1cf8dcac295cd4348df"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_ea85f9d9a2263b2208291654ce"`);
        await queryRunner.query(`DROP TABLE "products"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_b93490e30940d67011cbcadaba"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_e298b4ae6fa4e222aa419a3b7a"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_c465641423669ca997574fc976"`);
        await queryRunner.query(`DROP TABLE "purchases"`);
        await queryRunner.query(`DROP TYPE "public"."purchases_status_enum"`);
        await queryRunner.query(`DROP TYPE "public"."purchases_paymentmethod_enum"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_2cd9e0fc23ee71e5290abf7b74"`);
        await queryRunner.query(`DROP TABLE "regions"`);
        await queryRunner.query(`DROP TABLE "routes"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_6b1a67abb0da83b51c7a2fbac4"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_8d17fd47a7bbf512e58209fbb3"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_71759a2e943d5ac56ac6581ea7"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_112a0b06b6ba052cd38b24e760"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_6254c38686cb8fa526e460e2f3"`);
        await queryRunner.query(`DROP TABLE "orders"`);
        await queryRunner.query(`DROP TYPE "public"."orders_priority_enum"`);
        await queryRunner.query(`DROP TYPE "public"."orders_status_enum"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_cfb14ae07f338a26a7f7f78383"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_7ba6254fb98518a78489f289aa"`);
        await queryRunner.query(`DROP TABLE "order_items"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_00b46d17fb57fdc223dfa9421b"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_437fcbe61c403c74f95167094f"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_b125c437bdb29a8fae2159df26"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_90e6a859d9b93209baf96c0695"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_cb7a370eb2780e8f57790046a6"`);
        await queryRunner.query(`DROP TABLE "quotes"`);
        await queryRunner.query(`DROP TYPE "public"."quotes_status_enum"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_338bbc4dda25f6ad120ea89a89"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_549fc572192bfb0368fea966f4"`);
        await queryRunner.query(`DROP TABLE "quote_items"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_e075999cadd7b6c2f8fd88bd0e"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_0efca62e7f82b324a8ec9fbc6d"`);
        await queryRunner.query(`DROP TABLE "sale_items"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_23a47ae0fbbb0b5183f156d881"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_1a09bec123377bb0adee2e00e6"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_c155d66393bcfb33d2713ed983"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_697d250cee82454463dcf7cd03"`);
        await queryRunner.query(`DROP TABLE "sales"`);
        await queryRunner.query(`DROP TYPE "public"."sales_status_enum"`);
        await queryRunner.query(`DROP TYPE "public"."sales_paymentmethod_enum"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_b1cb7ec405354fe580ee2bceb6"`);
        await queryRunner.query(`DROP TABLE "account_plans"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_c188d445366e2a493172acf03c"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_b39d66a4614c9ce63dba591b09"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_c7d8c4678cf1c2bf3a706e7c90"`);
        await queryRunner.query(`DROP TABLE "users"`);
        await queryRunner.query(`DROP TYPE "public"."users_usertype_enum"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_0f6113d0c1bac97ffade96fab8"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_dac342b8fab96d19bd854775bd"`);
        await queryRunner.query(`DROP TABLE "suppliers"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_1284938679c82100115a9b6849"`);
        await queryRunner.query(`DROP TABLE "features"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_96cb370ab57b8830da712c923b"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_44199adb201eb4bc094a39d071"`);
        await queryRunner.query(`DROP TABLE "vans"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_fab9825adaa137531a363274c1"`);
        await queryRunner.query(`DROP TABLE "roles"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_b6a3393d3310c0a072c467270f"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_652ceca4292dbe2a6c367f0a74"`);
        await queryRunner.query(`DROP TABLE "warehouses"`);
    }

}
