---
description: The feature implementation workflow for backend tasks
---

1. First You must first Analyse the full content of the following documentation (500 lines each): backend/README.md, backend/docs/CODE_GENERATION.md, backend/docs/UUID_USAGE_GUIDELINES.md, backend/docs/API_LAYER.md, backend/docs/ENTITY.md.
2. Analyse other files provided by user
3. Make sure to report issues and list the missing list of function or features and prompt the user.
4. Next, look at the user request and fullfill it.
5. Make sure to keep TODO list and update the TODO list often.
6. You must report all errors and potential issues you encounter
7. You are prohibited from starting the backend server for testing, only build is allowed