import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, IsArray, Validate<PERSON>ested, <PERSON><PERSON><PERSON>ber, IsOptional } from "class-validator";
import { Type } from "class-transformer";
import { ApiProperty } from "@nestjs/swagger";

export class SaleItemDto {
  @ApiProperty({
    description: "UUID of the product",
    example: "01890b6e-7b8c-7e6b-8e2e-7b8c7e6b8e2e",
  })
  @IsString()
  @IsUUID('all', { message: "productUuid must be a valid UUID" })
  productUuid: string;

  @ApiProperty({
    description: "Name of the product",
    example: "Product Name",
  })
  @IsString()
  name: string;

  @ApiProperty({
    description: "Quantity of the product",
    example: 5,
  })
  @IsNumber()
  quantity: number;

  @ApiProperty({
    description: "Unit price of the product",
    example: 10.99,
  })
  @IsNumber()
  unitPrice: number;

  @ApiProperty({
    description: "Line total (calculated if not provided)",
    example: 54.95,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  lineTotal?: number;

  @ApiProperty({
    description: "Tax amount for this item",
    example: 5.50,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  taxAmount?: number;
}

export class AddProductsToSaleDto {
  @ApiProperty({
    description: "UUID of the user adding the products",
    example: "01890b6e-7b8c-7e6b-8e2e-7b8c7e6b8e2e",
  })
  @IsString()
  @IsUUID('all', { message: "userUuid must be a valid UUID" })
  userUuid: string;

  @ApiProperty({
    description: "Array of items to add to the sale",
    type: [SaleItemDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => SaleItemDto)
  items: SaleItemDto[];
} 