'use client';

import { useAuth } from '@/contexts/AuthContext';
import ProtectedRoute from '@/components/ProtectedRoute';
import { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { 
  FiPackage, 
  FiPlus, 
  FiFilter, 
  FiEye, 
  FiEdit, 
  FiTrash2, 
  FiX,
  FiSearch,
  FiCalendar,
  FiDollarSign,
  FiUser,
  FiShoppingCart
} from 'react-icons/fi';
import { getSales, getSale, cancelSale, updateSaleStatus, Sale, SaleStatus, PaymentMethods } from '../sales/salesApi';

// Project convention: inject warehouseUuid and userUuid from AuthContext, never from user input

interface SalesFilter {
  status?: SaleStatus;
  paymentMethod?: PaymentMethods;
  customerUuid?: string;
  invoiceNumber?: string;
  createdFrom?: string;
  createdTo?: string;
  minAmount?: number;
  maxAmount?: number;
}

export default function SalesOrdersPage() {
  const { user } = useAuth();
  const warehouseUuid = user?.warehouseUuid;
  const userUuid = user?.uuid;

  const [sales, setSales] = useState<Sale[]>([]);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalSales, setTotalSales] = useState(0);
  const [showFilters, setShowFilters] = useState(false);
  const [filter, setFilter] = useState<SalesFilter>({});
  const [selectedSale, setSelectedSale] = useState<Sale | null>(null);
  const [showSaleDetails, setShowSaleDetails] = useState(false);
  const [cancellingSale, setCancellingSale] = useState<string | null>(null);
  const [updatingStatus, setUpdatingStatus] = useState<string | null>(null);

  // Load sales data
  const loadSales = async () => {
    if (!warehouseUuid) return;
    
    setLoading(true);
    try {
      const response = await getSales(
        { page: currentPage, limit: 10 },
        { ...filter, warehouseUuid }
      );
      setSales(response.data);
      setTotalPages(response.totalPages);
      setTotalSales(response.total);
    } catch (error: any) {
      console.error('Error loading sales:', error);
      toast.error('Failed to load sales');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadSales();
  }, [warehouseUuid, currentPage, filter]);

  // Handle sale cancellation
  const handleCancelSale = async (saleUuid: string) => {
    if (!userUuid) {
      toast.error('User information not available');
      return;
    }

    setCancellingSale(saleUuid);
    try {
      await cancelSale(saleUuid, userUuid);
      toast.success('Sale cancelled successfully');
      loadSales(); // Refresh the list
    } catch (error: any) {
      console.error('Error cancelling sale:', error);
      toast.error('Failed to cancel sale');
    } finally {
      setCancellingSale(null);
    }
  };

  // Handle view sale details
  const handleViewDetails = async (saleUuid: string) => {
    try {
      const sale = await getSale(saleUuid);
      setSelectedSale(sale);
      setShowSaleDetails(true);
    } catch (error: any) {
      console.error('Error loading sale details:', error);
      toast.error('Failed to load sale details');
    }
  };

  // Handle filter changes
  const handleFilterChange = (key: keyof SalesFilter, value: any) => {
    setFilter(prev => ({ ...prev, [key]: value }));
    setCurrentPage(1); // Reset to first page when filtering
  };

  // Clear all filters
  const clearFilters = () => {
    setFilter({});
    setCurrentPage(1);
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Get status badge color
  const getStatusColor = (status: SaleStatus) => {
    switch (status) {
      case SaleStatus.PAID:
        return 'bg-green-100 text-green-800';
      case SaleStatus.PARTIALLY_PAID:
        return 'bg-yellow-100 text-yellow-800';
      case SaleStatus.UNPAID:
        return 'bg-red-100 text-red-800';
      case SaleStatus.CANCELLED:
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Handle status update
  const handleStatusUpdate = async (saleUuid: string, newStatus: SaleStatus) => {
    if (!userUuid) {
      toast.error('User information not available');
      return;
    }

    setUpdatingStatus(saleUuid);
    try {
      await updateSaleStatus(saleUuid, newStatus, userUuid);
      toast.success('Sale status updated successfully');
      loadSales(); // Refresh the list
      if (selectedSale?.uuid === saleUuid) {
        // Update the selected sale in the modal
        const updatedSale = await getSale(saleUuid);
        setSelectedSale(updatedSale);
      }
    } catch (error: any) {
      console.error('Error updating sale status:', error);
      toast.error('Failed to update sale status');
    } finally {
      setUpdatingStatus(null);
    }
  };

  if (!warehouseUuid) {
    return (
      <ProtectedRoute>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <FiUser className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No warehouse assigned</h3>
            <p className="mt-1 text-sm text-gray-500">Please contact your administrator.</p>
          </div>
        </div>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-gray-50 p-6">
        <div className="max-w-7xl mx-auto">
          {/* Header */}
          <div className="mb-8">
            <div className="flex justify-between items-center">
              <div>
                <h1 className="text-3xl font-bold text-gray-900 mb-2">Sales Orders</h1>
                <p className="text-gray-600">Manage customer sales and track order status</p>
              </div>
              <div className="flex items-center space-x-3">
                <button
                  onClick={() => setShowFilters(!showFilters)}
                  className="flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                >
                  <FiFilter className="mr-2" />
                  Filters
                </button>
                <button
                  onClick={() => window.location.href = '/sales/sales?view=pos'}
                  className="flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700"
                >
                  <FiPlus className="mr-2" />
                  New Sale
                </button>
              </div>
            </div>
          </div>

          {/* Filters */}
          {showFilters && (
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {/* Status Filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
                  <select
                    value={filter.status || ''}
                    onChange={(e) => handleFilterChange('status', e.target.value || undefined)}
                    className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">All Statuses</option>
                    {Object.values(SaleStatus).map(status => (
                      <option key={status} value={status}>
                        {status.replace('_', ' ').toUpperCase()}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Payment Method Filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Payment Method</label>
                  <select
                    value={filter.paymentMethod || ''}
                    onChange={(e) => handleFilterChange('paymentMethod', e.target.value || undefined)}
                    className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">All Methods</option>
                    {Object.values(PaymentMethods).map(method => (
                      <option key={method} value={method}>
                        {method.replace('_', ' ').toUpperCase()}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Invoice Number Filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Invoice Number</label>
                  <input
                    type="text"
                    value={filter.invoiceNumber || ''}
                    onChange={(e) => handleFilterChange('invoiceNumber', e.target.value || undefined)}
                    placeholder="Search invoice..."
                    className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>

                {/* Amount Range */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Min Amount</label>
                  <input
                    type="number"
                    value={filter.minAmount || ''}
                    onChange={(e) => handleFilterChange('minAmount', e.target.value ? parseFloat(e.target.value) : undefined)}
                    placeholder="Min amount"
                    className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>

              <div className="mt-4 flex justify-between items-center">
                <button
                  onClick={clearFilters}
                  className="text-sm text-gray-600 hover:text-gray-800"
                >
                  Clear all filters
                </button>
                <div className="text-sm text-gray-500">
                  {totalSales} sales found
                </div>
              </div>
            </div>
          )}

          {/* Sales List */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            {loading ? (
              <div className="p-8 text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <p className="mt-2 text-gray-600">Loading sales...</p>
              </div>
            ) : sales.length === 0 ? (
              <div className="p-8 text-center">
                <FiShoppingCart className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">No sales found</h3>
                <p className="mt-1 text-sm text-gray-500">
                  {Object.keys(filter).length > 0 
                    ? 'Try adjusting your filters or create a new sale.'
                    : 'Get started by creating your first sale.'
                  }
                </p>
                {Object.keys(filter).length === 0 && (
                  <button
                    onClick={() => window.location.href = '/sales/sales?view=pos'}
                    className="mt-4 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700"
                  >
                    <FiPlus className="mr-2" />
                    Create Sale
                  </button>
                )}
              </div>
            ) : (
              <>
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Invoice
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Customer
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Amount
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Status
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Payment
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Date
                        </th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {sales.map((sale) => (
                        <tr key={sale.uuid} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm font-medium text-gray-900">
                              {sale.invoiceNumber}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-900">
                              {sale.customerName || 'Unknown Customer'}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm font-medium text-gray-900">
                              {formatCurrency(sale.totalAmount)}
                            </div>
                            <div className="text-xs text-gray-500">
                              Paid: {formatCurrency(sale.amountPaid)}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(sale.status)}`}>
                              {sale.status.replace('_', ' ').toUpperCase()}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-900">
                              {sale.paymentMethod?.replace('_', ' ').toUpperCase() || 'N/A'}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-900">
                              {formatDate(sale.createdAt)}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <div className="flex items-center justify-end space-x-2">
                              <button
                                onClick={() => handleViewDetails(sale.uuid)}
                                className="text-blue-600 hover:text-blue-900"
                                title="View Details"
                              >
                                <FiEye className="h-4 w-4" />
                              </button>
                              <button
                                onClick={() => window.location.href = `/sales/sales?view=pos&edit=${sale.uuid}`}
                                className="text-green-600 hover:text-green-900"
                                title="Edit Sale"
                              >
                                <FiEdit className="h-4 w-4" />
                              </button>
                              {sale.status !== SaleStatus.CANCELLED && (
                                <button
                                  onClick={() => handleCancelSale(sale.uuid)}
                                  disabled={cancellingSale === sale.uuid}
                                  className="text-red-600 hover:text-red-900 disabled:opacity-50"
                                  title="Cancel Sale"
                                >
                                  {cancellingSale === sale.uuid ? (
                                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-red-600"></div>
                                  ) : (
                                    <FiTrash2 className="h-4 w-4" />
                                  )}
                                </button>
                              )}
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>

                {/* Pagination */}
                {totalPages > 1 && (
                  <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                    <div className="flex-1 flex justify-between sm:hidden">
                      <button
                        onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                        disabled={currentPage === 1}
                        className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
                      >
                        Previous
                      </button>
                      <button
                        onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                        disabled={currentPage === totalPages}
                        className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
                      >
                        Next
                      </button>
                    </div>
                    <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                      <div>
                        <p className="text-sm text-gray-700">
                          Showing page <span className="font-medium">{currentPage}</span> of{' '}
                          <span className="font-medium">{totalPages}</span>
                        </p>
                      </div>
                      <div>
                        <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                          <button
                            onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                            disabled={currentPage === 1}
                            className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                          >
                            Previous
                          </button>
                          <button
                            onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                            disabled={currentPage === totalPages}
                            className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                          >
                            Next
                          </button>
                        </nav>
                      </div>
                    </div>
                  </div>
                )}
              </>
            )}
          </div>
        </div>

        {/* Sale Details Modal */}
        {showSaleDetails && selectedSale && (
          <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div className="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
              <div className="mt-3">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-medium text-gray-900">Sale Details</h3>
                  <button
                    onClick={() => setShowSaleDetails(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <FiX className="h-6 w-6" />
                  </button>
                </div>
                
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Invoice Number</label>
                      <p className="mt-1 text-sm text-gray-900">{selectedSale.invoiceNumber}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Status</label>
                      <div className="mt-1 flex items-center space-x-2">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(selectedSale.status)}`}>
                          {selectedSale.status.replace('_', ' ').toUpperCase()}
                        </span>
                        <select
                          value={selectedSale.status}
                          onChange={(e) => handleStatusUpdate(selectedSale.uuid, e.target.value as SaleStatus)}
                          disabled={updatingStatus === selectedSale.uuid}
                          className="ml-2 text-sm border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
                        >
                          {Object.values(SaleStatus).map(status => (
                            <option key={status} value={status}>
                              {status.replace('_', ' ').toUpperCase()}
                            </option>
                          ))}
                        </select>
                        {updatingStatus === selectedSale.uuid && (
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                        )}
                      </div>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Customer</label>
                      <p className="mt-1 text-sm text-gray-900">{selectedSale.customerName || 'Unknown Customer'}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Payment Method</label>
                      <p className="mt-1 text-sm text-gray-900">{selectedSale.paymentMethod?.replace('_', ' ').toUpperCase() || 'N/A'}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Total Amount</label>
                      <p className="mt-1 text-sm font-medium text-gray-900">{formatCurrency(selectedSale.totalAmount)}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Amount Paid</label>
                      <p className="mt-1 text-sm text-gray-900">{formatCurrency(selectedSale.amountPaid)}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Balance Due</label>
                      <p className="mt-1 text-sm text-gray-900">{formatCurrency(selectedSale.balanceDue)}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Created Date</label>
                      <p className="mt-1 text-sm text-gray-900">{formatDate(selectedSale.createdAt)}</p>
                    </div>
                  </div>

                  {/* Items */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Items</label>
                    <div className="border rounded-md">
                      {selectedSale.itemsSnapshot.map((item, index) => (
                        <div key={index} className="flex justify-between items-center p-3 border-b last:border-b-0">
                          <div>
                            <p className="text-sm font-medium text-gray-900">{item.productName}</p>
                            <p className="text-xs text-gray-500">Qty: {item.quantity} × {formatCurrency(item.unitPrice)}</p>
                          </div>
                          <p className="text-sm font-medium text-gray-900">{formatCurrency(item.lineTotal)}</p>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>

                <div className="mt-6 flex justify-end space-x-3">
                  <button
                    onClick={() => setShowSaleDetails(false)}
                    className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
                  >
                    Close
                  </button>
                  <button
                    onClick={() => {
                      setShowSaleDetails(false);
                      window.location.href = `/sales/sales?view=pos&edit=${selectedSale.uuid}`;
                    }}
                    className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700"
                  >
                    Edit Sale
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </ProtectedRoute>
  );
}
