// API abstraction for companies module
import axios from 'axios';
import { getAxiosAuthHeaders } from '@/utils/authHeaders';

export interface Company {
  uuid: string;
  name: string;
  description?: string;
  address?: string;
  phone?: string;
  email?: string;
  website?: string;
  userUuid: string;
  isDeleted: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface CreateCompanyDto {
  name: string;
  description?: string;
  address?: string;
  phone?: string;
  email?: string;
  website?: string;
}

export interface UpdateCompanyDto {
  name?: string;
  description?: string;
  address?: string;
  phone?: string;
  email?: string;
  website?: string;
}

// Get companies by user UUID
export async function getCompaniesByUser(userUuid: string): Promise<Company[]> {
  const response = await axios.get(`/api/companies/by-user/${userUuid}`, {
    headers: getAxiosAuthHeaders(),
  });
  return response.data;
}

// Get company by UUID
export async function getCompany(uuid: string): Promise<Company> {
  const response = await axios.get(`/api/companies/${uuid}`, {
    headers: getAxiosAuthHeaders(),
  });
  return response.data;
}

// Create company
export async function createCompany(data: CreateCompanyDto & { userUuid: string }): Promise<Company> {
  const response = await axios.post('/api/companies', data, {
    headers: getAxiosAuthHeaders(),
  });
  return response.data;
}

// Update company
export async function updateCompany(uuid: string, data: UpdateCompanyDto): Promise<Company> {
  const response = await axios.put(`/api/companies/${uuid}`, data, {
    headers: getAxiosAuthHeaders(),
  });
  return response.data;
}

// Delete company (soft delete)
export async function deleteCompany(uuid: string): Promise<void> {
  await axios.delete(`/api/companies/${uuid}`, {
    headers: getAxiosAuthHeaders(),
  });
}

// Get all companies (admin function)
export async function getAllCompanies(): Promise<Company[]> {
  const response = await axios.get('/api/companies/list', {
    headers: getAxiosAuthHeaders(),
  });
  return response.data;
} 