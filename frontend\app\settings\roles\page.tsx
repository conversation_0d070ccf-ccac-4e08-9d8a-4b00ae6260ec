'use client';

import React, { useState } from 'react';
import ItemsTable from '../../../components/itemsTable/ItemsTable';
import TableActionButtons from '../../../components/itemsTable/TableActionButtons';
import { useForm } from 'react-hook-form';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  fetchRoles,
  fetchPermissions,
  createRole,
  editRole,
  deleteRole,
  Role,
  Permission
} from './rolesApi';

import { useAuth } from '../../../contexts/AuthContext'; // adjust the path if needed


const RolesPermissionsPage: React.FC = () => {
  const queryClient = useQueryClient();
  const [modalOpen, setModalOpen] = useState(false);
  const [editRoleUuid, setEditRoleUuid] = useState<string | null>(null);
  const [selectedPermissions, setSelectedPermissions] = useState<string[]>([]);

  const auth = useAuth();
  const warehouseUuid = auth.user?.warehouseUuid || '';

  // Fetch roles
  const { data: roles, isLoading: loadingRoles, error: rolesError } = useQuery({
    queryKey: ['roles', warehouseUuid],
    queryFn: () => fetchRoles(warehouseUuid),
    enabled: !!warehouseUuid,
  });
  // Fetch permissions
  const { data: permissions, isLoading: loadingPermissions } = useQuery({
    queryKey: ['permissions'],
    queryFn: fetchPermissions,
  });

  // Mutations
  const addRoleMutation = useMutation({
    mutationFn: createRole,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['roles', warehouseUuid] });
      setModalOpen(false);
    }
  });
  const editRoleMutation = useMutation({
    mutationFn: ({ uuid, permissions }: { uuid: string; permissions: string[] }) => editRole(uuid, permissions),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['roles', warehouseUuid] });
      setModalOpen(false);
    }
  });
  const deleteRoleMutation = useMutation({
    mutationFn: deleteRole,
    onSuccess: () => queryClient.invalidateQueries({ queryKey: ['roles', warehouseUuid] })
  });

  // Form
  const { register, handleSubmit, reset, setValue, watch } = useForm<{ name: string; permissions: string[] }>({
    defaultValues: { name: '', permissions: [] }
  });

  // Open modal for add/edit
  const openModal = (role?: Role) => {
    if (role) {
      setEditRoleUuid(role.uuid);
      reset({ name: role.name, permissions: role.permissions });
      setSelectedPermissions(role.permissions);
    } else {
      setEditRoleUuid(null);
      reset({ name: '', permissions: [] });
      setSelectedPermissions([]);
    }
    setModalOpen(true);
  };

  // Handle permission checkbox change
  const handlePermissionChange = (perm: string) => {
    const current = watch('permissions') || [];
    if (current.includes(perm)) {
      setValue('permissions', current.filter((p: string) => p !== perm));
      setSelectedPermissions(current.filter((p: string) => p !== perm));
    } else {
      setValue('permissions', [...current, perm]);
      setSelectedPermissions([...current, perm]);
    }
  };

  // Handle select/deselect all
  const handleSelectAll = () => {
    const allPerms = permissions?.map(p => p) || [];
    setValue('permissions', allPerms);
    setSelectedPermissions(allPerms);
  };

  const handleDeselectAll = () => {
    setValue('permissions', []);
    setSelectedPermissions([]);
  };

  // Submit handler
  const onSubmit = (data: { name: string; permissions: string[] }) => {
    if (editRoleUuid) {
      editRoleMutation.mutate({ uuid: editRoleUuid, permissions: data.permissions });
    } else {
      addRoleMutation.mutate({ ...data, warehouseUuid });
    }
  };

  // UI
  return (
    <div className="p-4 sm:p-6 w-full">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-bold">Roles & Permissions</h1>
        <button
          className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
          onClick={() => openModal()}
        >
          Add Role
        </button>
      </div>

      {/* Roles Table */}
      <>
        {loadingRoles ? (
          <div className="p-4 text-center">Loading...</div>
        ) : rolesError ? (
          <div className="p-4 text-center text-red-500">Error loading roles</div>
        ) : (
          <ItemsTable
            columns={[
              {
                key: 'name',
                header: 'Role Name',
                cellClassName: 'font-semibold',
              },
              {
                key: 'permissions',
                header: 'Permissions',
                render: (value: string[], row: Role) =>
                  value && value.length > 0 ? value.join(', ') : <span className="text-gray-400">None</span>,
                cellClassName: 'text-sm',
              },
              {
                key: 'actions',
                header: 'Actions',
                render: (_: any, row: Role) => (
                  <TableActionButtons
                    onEdit={() => openModal(row)}
                    onDelete={() => {
                      if (window.confirm(`Delete role \"${row.name}\"?`)) deleteRoleMutation.mutate(row.uuid);
                    }}
                  />
                ),
                headerClassName: 'text-center',
                cellClassName: 'text-center align-middle',
              },
            ]}
            data={Array.isArray(roles) ? roles : []}
            noDataText={<span className="text-gray-400">No roles found.</span>}
            containerClassName="bg-white rounded-xl shadow-lg border border-gray-200 w-full"
          />
        )}
      </>

      {/* Add/Edit Modal */}
      {modalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-lg p-6 w-full max-w-md relative">
            <button
              className="absolute top-2 right-2 text-gray-400 hover:text-gray-600"
              onClick={() => setModalOpen(false)}
              aria-label="Close"
            >&times;</button>
            <h2 className="text-lg font-bold mb-4">{editRoleUuid ? 'Edit Role' : 'Add Role'}</h2>
            <form onSubmit={handleSubmit(onSubmit)}>
              {!editRoleUuid && (
                <div className="mb-4">
                  <label className="block text-sm font-medium mb-1">Role Name</label>
                  <input
                    className="w-full border rounded px-3 py-2 focus:outline-none focus:ring focus:border-blue-300"
                    {...register('name', { required: true })}
                    disabled={!!editRoleUuid}
                  />
                </div>
              )}
              <div className="mb-4">
                <div className="flex justify-between items-center mb-1">
                  <label className="block text-sm font-medium">Permissions</label>
                  <div>
                    <button type="button" onClick={handleSelectAll} className="text-xs text-blue-600 hover:underline mr-2">Select All</button>
                    <button type="button" onClick={handleDeselectAll} className="text-xs text-blue-600 hover:underline">Deselect All</button>
                  </div>
                </div>
                <div className="flex flex-col gap-1 max-h-48 overflow-y-auto border rounded p-2">
                  {loadingPermissions ? (
                    <span>Loading...</span>
                  ) : Array.isArray(permissions) && permissions.length > 0 ? (
                    permissions.map((perm: Permission) => (
                      <label key={perm} className="inline-flex items-center gap-2">
                        <input
                          type="checkbox"
                          value={perm}
                          checked={selectedPermissions.includes(perm)}
                          onChange={() => handlePermissionChange(perm)}
                        />
                        <span>{perm}</span>
                      </label>
                    ))
                  ) : (
                    <span className="text-gray-400">No permissions available.</span>
                  )}
                </div>
              </div>
              <div className="flex justify-end gap-2 mt-6">
                <button
                  type="button"
                  className="px-4 py-2 rounded bg-gray-200 hover:bg-gray-300"
                  onClick={() => setModalOpen(false)}
                >Cancel</button>
                <button
                  type="submit"
                  className="px-4 py-2 rounded bg-blue-600 text-white hover:bg-blue-700"
                  disabled={addRoleMutation.isPending || editRoleMutation.isPending}
                >{editRoleUuid ? 'Save Changes' : 'Create Role'}</button>
              </div>
              {(addRoleMutation.isError || editRoleMutation.isError) && (
                <div className="text-red-500 mt-2 text-sm">
                  {addRoleMutation.error instanceof Error ? addRoleMutation.error.message : ''}
                  {editRoleMutation.error instanceof Error ? editRoleMutation.error.message : ''}
                </div>
              )}
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default RolesPermissionsPage;
