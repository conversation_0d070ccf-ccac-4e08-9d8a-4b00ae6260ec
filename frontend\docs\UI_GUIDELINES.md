# User Interface General Guidelines

- For all entity management pages (e.g., products, customers, suppliers, etc.), provide a clear and prominent button to add a new entity.
- The standard keyboard shortcut for opening the 'Add' modal is **F2**. This allows users to quickly add a new item using the keyboard.
- The button text should indicate the shortcut, e.g., "Add [Entity] (F2)".
- Ensure that keyboard shortcuts are accessible and do not interfere with form field navigation or browser defaults.
- Apply this convention consistently across all modules for a unified user experience.

---

## Typography & Font Sizes

Use consistent font sizes throughout the application for better readability and visual hierarchy:

### **Headings**
- **Page Titles:** `text-3xl font-bold` (e.g., "Stock Adjustments", "Products")
- **Section Headers:** `text-xl font-semibold` (e.g., "Recent Adjustments", "Product Categories")
- **Subsection Headers:** `text-lg font-medium` (e.g., form sections, card titles)
- **Small Headers:** `text-base font-semibold` (e.g., table column headers, field groups)

### **Body Text**
- **Default Text:** `text-base` (16px) - Use for most content, descriptions, and general text
- **Small Text:** `text-sm` (14px) - Use for secondary information, captions, and helper text
- **Extra Small:** `text-xs` (12px) - Use sparingly for metadata, timestamps, and very small labels

### **Form Elements**
- **Input Labels:** `text-sm font-medium` - Clear, readable labels for form fields
- **Input Text:** `text-base` - Standard size for input fields and text areas
- **Button Text:** `text-sm font-medium` - Consistent button text sizing
- **Error Messages:** `text-sm text-red-600` - Error text should be smaller but clearly visible

### **Tables & Lists**
- **Table Headers:** `text-sm font-semibold` - Compact but readable headers
- **Table Content:** `text-sm` - Standard size for table data
- **List Items:** `text-base` - Readable list content

### **Navigation & UI Elements**
- **Navigation Links:** `text-sm font-medium` - Consistent navigation text
- **Breadcrumbs:** `text-sm` - Smaller, secondary navigation
- **Status Indicators:** `text-xs font-medium` - Small status badges and indicators

### **Responsive Considerations**
- **Mobile:** Maintain minimum 16px for touch targets and readability
- **Desktop:** Can use smaller sizes (14px, 12px) for secondary information
- **Accessibility:** Ensure sufficient contrast ratios for all text sizes

---

## Pagination Standards

- **Default Page Size:** All paginated lists should display **10 items per page** by default
- **Page Size Options:** Provide options for users to change page size (e.g., 10, 25, 50, 100 items per page)
- **Pagination Controls:** Include clear navigation controls (Previous, Next, page numbers) with proper accessibility attributes
- **Page Information:** Display current page number and total items (e.g., "Showing 1-10 of 45 items")
- **Consistent Implementation:** Apply this pagination standard across all entity management pages and data tables

---

## Standard Modal Entity Form Pattern

Use the following conventions for all modal-based add/edit forms for entities (e.g., product, storage, category, etc.):

**Key Features:**
- **Centered Modal:** Modal appears centered with a white, rounded, shadowed panel (`max-w-sm` for good responsiveness).
- **Title & Description:** Clear title (e.g., "Add Entity"/"Edit Entity") and a brief description or instruction at the top.
- **Form Layout:**
  - Vertical layout with consistent spacing (`space-y-4` or `mb-4` between fields).
  - Each field has a visible label and appropriate placeholder.
  - The first field is auto-focused when modal opens.
- **Validation & Errors:**
  - Required fields are clearly marked (e.g., with a red asterisk).
  - Validation errors are shown directly below the relevant field, with an icon and accessible error text.
- **Buttons:**
  - Primary submit button is full-width, blue, rounded, and shows a loading spinner when submitting.
  - Secondary Cancel button is also full-width, styled as a neutral/gray button, and always closes the modal.
- **Accessibility:**
  - Modal uses `role="dialog"`, `aria-modal`, and `aria-labelledby` attributes.
  - Focus is trapped within the modal; Escape key closes the modal.
- **No IDs in Form:**
  - Do not include warhouse/user/entity IDs as form fields; inject them from context in the parent/page.
- **Responsiveness:**
  - Modal and form adapt to smaller screens (use `w-full max-w-sm`).

> **Apply this pattern for all future entity modals to ensure a consistent, accessible, and user-friendly experience.**

---

## Table Layout Standards

- **Fixed Height Tables:** Use fixed height for data tables to prevent layout shifts during loading states
- **Consistent Dimensions:** Tables should maintain the same height whether displaying data, loading, or showing empty states
- **Scrollable Content:** When table content exceeds the fixed height, implement vertical scrolling within the table body
- **Loading States:** Loading spinners and empty state messages should be centered within the fixed table dimensions
- **Height Configuration:** Provide optional height configuration for different table use cases while maintaining consistent behavior
