import { ApiProperty } from "@nestjs/swagger";

export class RouteLocationDto {
  @ApiProperty({ example: 40.7128, description: "Latitude coordinate" })
  latitude: number;

  @ApiProperty({ example: -74.006, description: "Longitude coordinate" })
  longitude: number;

  @ApiProperty({
    example: "Customer A",
    description: "Name of the customer",
    required: false,
  })
  customerName?: string;
}

export class OptimizedRouteLocationDto extends RouteLocationDto {
  @ApiProperty({
    example: 1,
    description: "Visit order in the optimized route",
  })
  order: number;
}

export class RouteResponseDto {
  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the route",
  })
  uuid: string;

  @ApiProperty({ example: "Morning Delivery Route", description: "Route name" })
  name: string;

  @ApiProperty({
    example: "Optimized route for morning deliveries in downtown area",
    description: "Route description",
    required: false,
  })
  description?: string;

  @ApiProperty({
    example: [
      { latitude: 40.7128, longitude: -74.006, customerName: "Customer A" },
      { latitude: 40.7589, longitude: -73.9851, customerName: "Customer B" },
    ],
    description: "Original customer locations before optimization",
    type: [RouteLocationDto],
  })
  customerLocations: RouteLocationDto[];

  @ApiProperty({
    example: [
      {
        latitude: 40.7128,
        longitude: -74.006,
        customerName: "Customer A",
        order: 1,
      },
      {
        latitude: 40.7589,
        longitude: -73.9851,
        customerName: "Customer B",
        order: 2,
      },
    ],
    description: "Optimized route with visit order",
    type: [OptimizedRouteLocationDto],
  })
  optimizedRoute: OptimizedRouteLocationDto[];

  @ApiProperty({
    example: 15.5,
    description: "Total distance of the optimized route in kilometers",
  })
  totalDistance: number;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the warehouse this route belongs to",
  })
  warehouseUuidString: string;

  @ApiProperty({
    example: "2024-01-15T10:30:00.000Z",
    description: "Route creation timestamp",
  })
  createdAt: Date;

  @ApiProperty({
    example: "2024-01-15T10:30:00.000Z",
    description: "Route last update timestamp",
  })
  updatedAt: Date;
}

export function toRouteResponseDto(route: any): RouteResponseDto {
  return {
    uuid: route.uuid,
    name: route.name,
    description: route.description,
    customerLocations: route.customerLocations,
    optimizedRoute: route.optimizedRoute,
    totalDistance: route.totalDistance,
    warehouseUuidString: route.warehouseUuidString,
    createdAt: route.createdAt,
    updatedAt: route.updatedAt,
  };
}
