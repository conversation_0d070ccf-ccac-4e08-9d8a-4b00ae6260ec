import 'package:flutter/foundation.dart';
import 'auth_service.dart';

/// Permission constants matching backend permissions
class Permissions {
  static const String dashboardView = 'dashboard.view';
  static const String salesView = 'sales.view';
  static const String salesEdit = 'sales.edit';
  static const String salesApprove = 'sales.approve';
  static const String purchasingView = 'purchasing.view';
  static const String purchasingEdit = 'purchasing.edit';
  static const String inventoryView = 'inventory.view';
  static const String inventoryManage = 'inventory.manage';
  static const String logisticsView = 'logistics.view';
  static const String logisticsManage = 'logistics.manage';
  static const String reportsView = 'reports.view';
  static const String userManage = 'user.manage';
  static const String settingsManage = 'settings.manage';
}

/// Role constants matching backend roles
class Roles {
  static const String admin = 'admin';
  static const String manager = 'manager';
  static const String sales = 'sales';
  static const String purchasing = 'purchasing';
  static const String warehouse = 'warehouse';
  static const String staff = 'staff';
  static const String mobileSaleAgent = 'mobile sale agent';
}

/// Service to manage permissions and role-based access
class PermissionService {
  factory PermissionService() => _instance;
  PermissionService._internal();
  static final PermissionService _instance = PermissionService._internal();

  final AuthService _authService = AuthService();

  /// Check if current user has a specific permission
  bool hasPermission(String permission) => _authService.hasPermission(permission);

  /// Check if current user has a specific role
  bool hasRole(String role) => _authService.hasRole(role);

  /// Get available navigation items based on user permissions
  List<NavigationItem> getAvailableNavigationItems() {
    final items = <NavigationItem>[];
    
    debugPrint('🔵 PERMISSION SERVICE: Checking navigation permissions');
    debugPrint('🔵 PERMISSION SERVICE: User roles: ${_authService.currentUser?.roles.map((r) => r.name).toList() ?? []}');
    debugPrint('🔵 PERMISSION SERVICE: User permissions: ${_authService.currentUser?.allPermissions ?? []}');

    // Dashboard - available for admin and users with dashboard permission
    final canViewDashboard = hasPermission(Permissions.dashboardView) || hasRole(Roles.admin);
    debugPrint('🔵 PERMISSION SERVICE: Dashboard access: $canViewDashboard');
    if (canViewDashboard) {
      items.add(NavigationItem.dashboard);
    }

    // Sales - available for mobile sale agents, sales roles, and admin
    final canViewSales = hasPermission(Permissions.salesView) || 
        hasRole(Roles.mobileSaleAgent) || 
        hasRole(Roles.sales) ||
        hasRole(Roles.admin);
    debugPrint('🔵 PERMISSION SERVICE: Sales access: $canViewSales');
    if (canViewSales) {
      items.add(NavigationItem.sales);
    }

    // Inventory - available for users with inventory permissions
    final canViewInventory = hasPermission(Permissions.inventoryView) || hasRole(Roles.admin);
    debugPrint('🔵 PERMISSION SERVICE: Inventory access: $canViewInventory');
    if (canViewInventory) {
      items.add(NavigationItem.inventory);
    }

    // Logistics - available for users with logistics permissions
    final canViewLogistics = hasPermission(Permissions.logisticsView) || hasRole(Roles.admin);
    debugPrint('🔵 PERMISSION SERVICE: Logistics access: $canViewLogistics');
    if (canViewLogistics) {
      items.add(NavigationItem.logistics);
    }

    // Purchasing - available for users with purchasing permissions
    final canViewPurchasing = hasPermission(Permissions.purchasingView) || hasRole(Roles.admin);
    debugPrint('🔵 PERMISSION SERVICE: Purchasing access: $canViewPurchasing');
    if (canViewPurchasing) {
      items.add(NavigationItem.purchasing);
    }

    // Reports - available for most roles
    final canViewReports = hasPermission(Permissions.reportsView) || hasRole(Roles.admin);
    debugPrint('🔵 PERMISSION SERVICE: Reports access: $canViewReports');
    if (canViewReports) {
      items.add(NavigationItem.reports);
    }

    // Settings - available for admin and users with settings permissions
    final canViewSettings = hasPermission(Permissions.settingsManage) || hasRole(Roles.admin);
    debugPrint('🔵 PERMISSION SERVICE: Settings access: $canViewSettings');
    if (canViewSettings) {
      items.add(NavigationItem.settings);
    }

    debugPrint('🟢 PERMISSION SERVICE: Available navigation items: ${items.map((i) => i.name).toList()}');
    return items;
  }

  /// Get features available for mobile sale agents
  List<MobileSaleAgentFeature> getMobileSaleAgentFeatures() {
    if (!hasRole(Roles.mobileSaleAgent)) return [];

    return [
      MobileSaleAgentFeature.clients,
      MobileSaleAgentFeature.sales,
      MobileSaleAgentFeature.stats,
      MobileSaleAgentFeature.maps,
      MobileSaleAgentFeature.missions,
      MobileSaleAgentFeature.messages,
    ];
  }

  /// Get features available for admin users
  List<AdminFeature> getAdminFeatures() {
    if (!hasRole(Roles.admin)) return [];

    return [
      AdminFeature.vans,
      AdminFeature.inventory,
      AdminFeature.sales,
      AdminFeature.purchases,
      AdminFeature.warehouses,
      AdminFeature.users,
      AdminFeature.reports,
      AdminFeature.settings,
    ];
  }

  /// Check if user can access a specific feature
  bool canAccessFeature(String featureName) {
    switch (featureName) {
      case 'dashboard':
        return hasPermission(Permissions.dashboardView);
      case 'sales':
        return hasPermission(Permissions.salesView);
      case 'inventory':
        return hasPermission(Permissions.inventoryView);
      case 'logistics':
        return hasPermission(Permissions.logisticsView);
      case 'purchasing':
        return hasPermission(Permissions.purchasingView);
      case 'reports':
        return hasPermission(Permissions.reportsView);
      case 'settings':
        return hasPermission(Permissions.settingsManage);
      default:
        return false;
    }
  }

  /// Get user's primary role for UI customization
  String? getPrimaryRole() {
    if (hasRole(Roles.admin)) return Roles.admin;
    if (hasRole(Roles.mobileSaleAgent)) return Roles.mobileSaleAgent;
    if (hasRole(Roles.manager)) return Roles.manager;
    if (hasRole(Roles.sales)) return Roles.sales;
    if (hasRole(Roles.purchasing)) return Roles.purchasing;
    if (hasRole(Roles.warehouse)) return Roles.warehouse;
    if (hasRole(Roles.staff)) return Roles.staff;
    return null;
  }
}

/// Navigation items available in the app
enum NavigationItem {
  dashboard,
  sales,
  inventory,
  logistics,
  purchasing,
  reports,
  settings,
}

/// Features available for mobile sale agents
enum MobileSaleAgentFeature {
  clients,
  sales,
  stats,
  maps,
  missions,
  messages,
}

/// Features available for admin users
enum AdminFeature {
  vans,
  inventory,
  sales,
  purchases,
  warehouses,
  users,
  reports,
  settings,
} 