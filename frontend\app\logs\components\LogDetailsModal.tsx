import React, { useEffect } from 'react';
import { FiX, FiUser, FiCalendar, FiTag, FiFileText, FiDatabase } from 'react-icons/fi';
import type { Log } from '../logsApi';

export interface LogDetailsModalProps {
  isOpen: boolean;
  log: Log | null;
  onClose: () => void;
  userName?: string;
}

export function LogDetailsModal({ isOpen, log, onClose, userName }: LogDetailsModalProps) {
  // Handle escape key to close modal
  useEffect(() => {
    if (!isOpen) return;
    
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        e.preventDefault();
        onClose();
      }
    };
    
    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, onClose]);

  // Handle clicking outside modal to close
  const handleOverlayClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  // Format date for display
  const formatDate = (date: Date | string) => {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return dateObj.toLocaleString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    });
  };

  // Get operation color based on operation type
  const getOperationColor = (operation: string) => {
    const op = operation.toLowerCase();
    if (op.includes('create') || op.includes('add')) {
      return 'bg-green-100 text-green-800 border-green-200';
    } else if (op.includes('update') || op.includes('edit') || op.includes('modify')) {
      return 'bg-blue-100 text-blue-800 border-blue-200';
    } else if (op.includes('delete') || op.includes('remove')) {
      return 'bg-red-100 text-red-800 border-red-200';
    } else if (op.includes('cancel') || op.includes('reject')) {
      return 'bg-orange-100 text-orange-800 border-orange-200';
    } else if (op.includes('approve') || op.includes('complete')) {
      return 'bg-purple-100 text-purple-800 border-purple-200';
    } else if (op.includes('transfer') || op.includes('move')) {
      return 'bg-indigo-100 text-indigo-800 border-indigo-200';
    } else if (op.includes('adjust') || op.includes('modify')) {
      return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    } else {
      return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  // Check if the log data contains delta changes
  const hasDeltaChanges = (data: Record<string, any>) => {
    return data && data.changes && typeof data.changes === 'object';
  };

  // Check if an array contains business items (has id, name, price, quantity, etc.)
  const isBusinessItemsArray = (arr: any[]) => {
    if (!arr || arr.length === 0) return false;
    const firstItem = arr[0];
    return typeof firstItem === 'object' &&
           firstItem !== null &&
           ('id' in firstItem || 'name' in firstItem || 'price' in firstItem || 'quantity' in firstItem || 'productUuid' in firstItem);
  };

  // Render business item with key details highlighted
  const renderBusinessItem = (item: any) => {
    if (!item || typeof item !== 'object') {
      return (
        <div className="bg-gray-100 p-2 rounded text-xs">
          <span className="text-gray-500">Invalid item: {String(item)}</span>
        </div>
      );
    }

    const { id, productUuid, name, price, unitPrice, quantity, lineTotal, ...otherProps } = item;
    const displayPrice = price || unitPrice;

    // If no recognizable properties, show the raw object
    if (!name && !id && !productUuid && quantity === undefined && displayPrice === undefined && !lineTotal) {
      return (
        <div className="bg-gray-100 p-2 rounded text-xs font-mono">
          <pre>{JSON.stringify(item, null, 2)}</pre>
        </div>
      );
    }

    return (
      <div className="space-y-2">
        <div className="flex flex-wrap gap-3">
          {name && (
            <div className="flex items-center gap-1">
              <span className="text-xs text-gray-500">Name:</span>
              <span className="font-medium text-gray-900">{name}</span>
            </div>
          )}
          {quantity !== undefined && (
            <div className="flex items-center gap-1">
              <span className="text-xs text-gray-500">Qty:</span>
              <span className="font-medium text-purple-600">{quantity}</span>
            </div>
          )}
          {displayPrice !== undefined && (
            <div className="flex items-center gap-1">
              <span className="text-xs text-gray-500">Price:</span>
              <span className="font-medium text-green-600">${displayPrice}</span>
            </div>
          )}
          {lineTotal !== undefined && (
            <div className="flex items-center gap-1">
              <span className="text-xs text-gray-500">Total:</span>
              <span className="font-medium text-blue-600">${lineTotal}</span>
            </div>
          )}
          {(id !== undefined || productUuid !== undefined) && (
            <div className="flex items-center gap-1">
              <span className="text-xs text-gray-500">ID:</span>
              <span className="font-mono text-xs text-gray-600">{id || productUuid}</span>
            </div>
          )}
        </div>
        {Object.keys(otherProps).length > 0 && (
          <div className="text-xs text-gray-500">
            +{Object.keys(otherProps).length} other properties
          </div>
        )}
      </div>
    );
  };

  // Render items snapshot side by side
  const renderItemsSnapshot = (beforeItems: any[], afterItems: any[]) => {
    // Ensure we have arrays to work with
    const before = Array.isArray(beforeItems) ? beforeItems : [];
    const after = Array.isArray(afterItems) ? afterItems : [];

    // Check if either array contains business items, or if this looks like an items field
    const hasBusinessItems = isBusinessItemsArray(before) || isBusinessItemsArray(after);

    // Always show the snapshot if we have arrays (even if they don't look like business items)
    // This handles cases where the detection might fail but we still want to show the items
    if (!hasBusinessItems && before.length === 0 && after.length === 0) {
      return null;
    }

    return (
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Old Items */}
        <div>
          <div className="text-sm font-medium text-red-700 mb-3 flex items-center gap-2">
            <span className="w-2 h-2 bg-red-500 rounded-full"></span>
            Old Items ({before.length} items)
          </div>
          <div className="space-y-3 max-h-96 overflow-y-auto">
            {before.length > 0 ? (
              before.map((item, index) => (
                <div key={index} className="bg-red-50 border border-red-200 rounded p-3">
                  {renderBusinessItem(item)}
                </div>
              ))
            ) : (
              <div className="text-gray-500 italic text-center py-4">No items</div>
            )}
          </div>
        </div>

        {/* New Items */}
        <div>
          <div className="text-sm font-medium text-green-700 mb-3 flex items-center gap-2">
            <span className="w-2 h-2 bg-green-500 rounded-full"></span>
            New Items ({after.length} items)
          </div>
          <div className="space-y-3 max-h-96 overflow-y-auto">
            {after.length > 0 ? (
              after.map((item, index) => (
                <div key={index} className="bg-green-50 border border-green-200 rounded p-3">
                  {renderBusinessItem(item)}
                </div>
              ))
            ) : (
              <div className="text-gray-500 italic text-center py-4">No items</div>
            )}
          </div>
        </div>
      </div>
    );
  };

  // Check if a value looks like a date
  const isDateValue = (value: any) => {
    if (typeof value === 'string') {
      // Check for ISO date format or common date patterns
      return /^\d{4}-\d{2}-\d{2}/.test(value) || /\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/.test(value);
    }
    return false;
  };

  // Format a date value nicely
  const formatDateValue = (value: string) => {
    try {
      const date = new Date(value);
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch {
      return value;
    }
  };

  // Simple value renderer
  const renderSimpleValue = (value: any) => {
    if (value === null) return <span className="text-gray-500 italic">null</span>;
    if (value === undefined) return <span className="text-gray-500 italic">undefined</span>;
    if (typeof value === 'string') {
      // Check if it's a date string
      if (isDateValue(value)) {
        return <span className="text-blue-600">{formatDateValue(value)}</span>;
      }
      return <span className="text-blue-600">"{value}"</span>;
    }
    if (typeof value === 'number') return <span className="text-purple-600">{value}</span>;
    if (typeof value === 'boolean') return <span className="text-orange-600">{String(value)}</span>;
    if (Array.isArray(value)) {
      return <span className="text-gray-600">[{value.length} items]</span>;
    }
    if (typeof value === 'object') {
      return (
        <div className="bg-gray-100 p-2 rounded text-xs font-mono max-w-md overflow-x-auto">
          <pre>{JSON.stringify(value, null, 2)}</pre>
        </div>
      );
    }
    return <span className="text-gray-600">{String(value)}</span>;
  };

  // Render simple field changes
  const renderFieldChange = (field: string, change: { before: any; after: any }) => {
    // Special handling for items arrays - check both field name and if arrays contain business items
    const isItemsField = field.toLowerCase().includes('items') || field.toLowerCase().includes('products');
    const hasBusinessItems = (Array.isArray(change.before) && isBusinessItemsArray(change.before)) ||
                            (Array.isArray(change.after) && isBusinessItemsArray(change.after));

    if (isItemsField && (Array.isArray(change.before) || Array.isArray(change.after))) {
      return renderItemsSnapshot(change.before || [], change.after || []);
    }

    // Also handle any array that contains business items, regardless of field name
    if (hasBusinessItems) {
      return renderItemsSnapshot(change.before || [], change.after || []);
    }

    // Simple before/after display for other fields
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
        <div>
          <div className="text-xs font-medium text-red-700 mb-1">Before</div>
          <div className="bg-red-50 border border-red-200 rounded p-3">
            {renderSimpleValue(change.before)}
          </div>
        </div>
        <div>
          <div className="text-xs font-medium text-green-700 mb-1">After</div>
          <div className="bg-green-50 border border-green-200 rounded p-3">
            {renderSimpleValue(change.after)}
          </div>
        </div>
      </div>
    );
  };

  // Render delta changes in a clean format
  const renderDeltaChanges = (changes: Record<string, { before: any; after: any }>) => {
    const changeEntries = Object.entries(changes);

    if (changeEntries.length === 0) {
      return (
        <div className="text-center py-4">
          <p className="text-gray-500 text-sm">No changes detected</p>
        </div>
      );
    }

    return (
      <div className="space-y-6">
        {changeEntries.map(([field, change]) => {
          return (
            <div key={field} className="border border-gray-200 rounded-lg p-4">
              <h5 className="font-medium text-gray-900 mb-4 capitalize">
                {field.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
              </h5>
              {renderFieldChange(field, change)}
            </div>
          );
        })}
      </div>
    );
  };

  if (!isOpen || !log) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" onClick={handleOverlayClick}>
      <div className="bg-white rounded-lg shadow-lg max-w-6xl w-full mx-4 max-h-[90vh] overflow-hidden flex flex-col">
        {/* Header */}
        <div className="p-6 border-b border-gray-200 flex-shrink-0 flex items-center justify-between">
          <div>
            <h3 className="text-xl font-bold text-gray-900">
              Log Details
            </h3>
            <p className="text-sm text-gray-600 mt-1">
              View detailed information about this log entry
            </p>
          </div>
          <button
            onClick={onClose}
            className="p-2 rounded-full hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors"
            title="Close"
            aria-label="Close modal"
          >
            <FiX className="h-5 w-5 text-gray-500" />
          </button>
        </div>
        
        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6">
          {/* Basic Information - Compact Layout */}
          <div className="mb-6">
            <h4 className="text-lg font-semibold text-gray-900 mb-4">Basic Information</h4>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {/* Operation */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center gap-2">
                  <FiTag className="w-4 h-4" />
                  Operation
                </label>
                <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${getOperationColor(log.operation)}`}>
                  {log.operation}
                </span>
              </div>

              {/* Entity */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center gap-2">
                  <FiDatabase className="w-4 h-4" />
                  Entity
                </label>
                <span className="text-gray-900 font-medium">{log.entity}</span>
              </div>

              {/* User */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center gap-2">
                  <FiUser className="w-4 h-4" />
                  User
                </label>
                <span className="text-gray-900 font-medium">
                  {userName || 'Unknown User'}
                </span>
                <p className="text-xs text-gray-500 mt-1">{log.userUuid}</p>
              </div>

              {/* Timestamp */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center gap-2">
                  <FiCalendar className="w-4 h-4" />
                  Timestamp
                </label>
                <span className="text-gray-900">{formatDate(log.createdAt)}</span>
              </div>
            </div>

            {/* Description - Full Width */}
            {log.description && (
              <div className="mt-4">
                <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center gap-2">
                  <FiFileText className="w-4 h-4" />
                  Description
                </label>
                <p className="text-gray-900 bg-gray-50 p-3 rounded-lg border">
                  {log.description}
                </p>
              </div>
            )}
          </div>

          {/* Data Section - Full Width */}
          <div className="mb-6">
            <h4 className="text-lg font-semibold text-gray-900 mb-4">
              {log.data && hasDeltaChanges(log.data) ? 'Changes' : 'Additional Data'}
            </h4>

            {log.data ? (
              <div>
                {hasDeltaChanges(log.data) ? (
                  <div>
                    {/* Delta Changes */}
                    <div className="mb-6">
                      <label className="block text-sm font-medium text-gray-700 mb-3">
                        Field Changes ({log.data.changeCount || 0} changes)
                      </label>
                      {renderDeltaChanges(log.data.changes)}
                    </div>
                  </div>
                ) : (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      JSON Data
                    </label>
                    <div className="bg-gray-900 text-green-400 p-4 rounded-lg border overflow-x-auto">
                      <pre className="text-sm font-mono whitespace-pre-wrap">
                        {JSON.stringify(log.data, null, 2)}
                      </pre>
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div className="text-center py-8">
                <FiDatabase className="w-12 h-12 text-gray-300 mx-auto mb-3" />
                <p className="text-gray-500 text-sm">No additional data available</p>
              </div>
            )}
          </div>

          {/* Log ID */}
          <div className="pt-6 border-t border-gray-200">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Log ID
            </label>
            <code className="text-xs text-gray-600 bg-gray-100 px-2 py-1 rounded">
              {log.id}
            </code>
          </div>
        </div>

        {/* Footer */}
        <div className="p-6 border-t border-gray-200 flex-shrink-0 flex justify-end">
          <button
            onClick={onClose}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-400 transition-colors"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
}
