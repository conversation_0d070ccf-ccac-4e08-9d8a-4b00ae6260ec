import React, { useEffect, useState } from 'react';
import { FiX, FiUser, FiCalendar, FiTag, FiFileText, FiDatabase, FiChevronDown, FiChevronRight } from 'react-icons/fi';
import type { Log } from '../logsApi';

export interface LogDetailsModalProps {
  isOpen: boolean;
  log: Log | null;
  onClose: () => void;
  userName?: string;
}

export function LogDetailsModal({ isOpen, log, onClose, userName }: LogDetailsModalProps) {
  // State for expandable tabs
  const [showOldDataTab, setShowOldDataTab] = useState(false);
  const [showNewDataTab, setShowNewDataTab] = useState(false);

  // Handle escape key to close modal
  useEffect(() => {
    if (!isOpen) return;
    
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        e.preventDefault();
        onClose();
      }
    };
    
    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, onClose]);

  // Handle clicking outside modal to close
  const handleOverlayClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  // Format date for display
  const formatDate = (date: Date | string) => {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return dateObj.toLocaleString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    });
  };

  // Get operation color based on operation type
  const getOperationColor = (operation: string) => {
    const op = operation.toLowerCase();
    if (op.includes('create') || op.includes('add')) {
      return 'bg-green-100 text-green-800 border-green-200';
    } else if (op.includes('update') || op.includes('edit') || op.includes('modify')) {
      return 'bg-blue-100 text-blue-800 border-blue-200';
    } else if (op.includes('delete') || op.includes('remove')) {
      return 'bg-red-100 text-red-800 border-red-200';
    } else if (op.includes('cancel') || op.includes('reject')) {
      return 'bg-orange-100 text-orange-800 border-orange-200';
    } else if (op.includes('approve') || op.includes('complete')) {
      return 'bg-purple-100 text-purple-800 border-purple-200';
    } else if (op.includes('transfer') || op.includes('move')) {
      return 'bg-indigo-100 text-indigo-800 border-indigo-200';
    } else if (op.includes('adjust') || op.includes('modify')) {
      return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    } else {
      return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  // Format JSON data for display
  const formatJsonData = (data: Record<string, any>) => {
    try {
      return JSON.stringify(data, null, 2);
    } catch (error) {
      return 'Invalid JSON data';
    }
  };

  // Check if the log data contains delta changes
  const hasDeltaChanges = (data: Record<string, any>) => {
    return data && data.changes && typeof data.changes === 'object';
  };

  // Extract raw old data from delta changes
  const extractOldData = (data: Record<string, any>) => {
    if (!hasDeltaChanges(data)) return null;

    const oldData: Record<string, any> = {};
    Object.entries(data.changes).forEach(([field, change]: [string, any]) => {
      if (change && typeof change === 'object' && 'before' in change) {
        oldData[field] = change.before;
      }
    });
    return Object.keys(oldData).length > 0 ? oldData : null;
  };

  // Extract raw new data from delta changes
  const extractNewData = (data: Record<string, any>) => {
    if (!hasDeltaChanges(data)) return null;

    const newData: Record<string, any> = {};
    Object.entries(data.changes).forEach(([field, change]: [string, any]) => {
      if (change && typeof change === 'object' && 'after' in change) {
        newData[field] = change.after;
      }
    });
    return Object.keys(newData).length > 0 ? newData : null;
  };



  // Check if a field should be treated as a business date
  const isBusinessDate = (fieldName: string, value: any) => {
    const dateFields = ['paymentDate', 'dueDate', 'createdAt', 'updatedAt', 'date', 'timestamp'];
    return dateFields.some(field => fieldName.toLowerCase().includes(field.toLowerCase())) ||
           (typeof value === 'string' && /^\d{4}-\d{2}-\d{2}/.test(value));
  };

  // Format business dates nicely
  const formatBusinessDate = (value: any) => {
    if (!value) return 'Not set';
    try {
      const date = new Date(value);
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch {
      return String(value);
    }
  };

  // Sophisticated change viewer that analyzes data types and shows meaningful differences
  const renderSophisticatedChanges = (before: any, after: any, fieldName?: string) => {
    // Handle null/undefined cases
    if (before === null && after === null) {
      return <div className="text-gray-500 italic">No change (both null)</div>;
    }

    if (before === null) {
      return (
        <div className="space-y-2">
          <div className="flex items-center gap-2 text-sm font-medium text-green-700">
            <span className="w-2 h-2 bg-green-500 rounded-full"></span>
            Added
          </div>
          <div className="bg-green-50 border border-green-200 rounded p-3">
            {fieldName && isBusinessDate(fieldName, after) ?
              <span className="text-blue-600">{formatBusinessDate(after)}</span> :
              renderValue(after)
            }
          </div>
        </div>
      );
    }

    if (after === null) {
      return (
        <div className="space-y-2">
          <div className="flex items-center gap-2 text-sm font-medium text-red-700">
            <span className="w-2 h-2 bg-red-500 rounded-full"></span>
            Deleted
          </div>
          <div className="bg-red-50 border border-red-200 rounded p-3">
            {fieldName && isBusinessDate(fieldName, before) ?
              <span className="text-blue-600">{formatBusinessDate(before)}</span> :
              renderValue(before)
            }
          </div>
        </div>
      );
    }

    // Both values exist - check for business dates first
    if (fieldName && isBusinessDate(fieldName, before) && isBusinessDate(fieldName, after)) {
      if (before === after) {
        return <div className="text-gray-500 italic">No change</div>;
      }
      return (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          <div>
            <div className="text-xs font-medium text-red-700 mb-1">Before</div>
            <div className="bg-red-50 border border-red-200 rounded p-3">
              <span className="text-blue-600">{formatBusinessDate(before)}</span>
            </div>
          </div>
          <div>
            <div className="text-xs font-medium text-green-700 mb-1">After</div>
            <div className="bg-green-50 border border-green-200 rounded p-3">
              <span className="text-blue-600">{formatBusinessDate(after)}</span>
            </div>
          </div>
        </div>
      );
    }

    // Analyze types
    const beforeType = Array.isArray(before) ? 'array' : typeof before;
    const afterType = Array.isArray(after) ? 'array' : typeof after;

    // Type changed
    if (beforeType !== afterType) {
      return (
        <div className="space-y-3">
          <div className="text-sm font-medium text-orange-700">Type Changed: {beforeType} → {afterType}</div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            <div>
              <div className="text-xs font-medium text-red-700 mb-1">Before ({beforeType})</div>
              <div className="bg-red-50 border border-red-200 rounded p-3">
                {renderValue(before)}
              </div>
            </div>
            <div>
              <div className="text-xs font-medium text-green-700 mb-1">After ({afterType})</div>
              <div className="bg-green-50 border border-green-200 rounded p-3">
                {renderValue(after)}
              </div>
            </div>
          </div>
        </div>
      );
    }

    // Same type - handle specific comparisons
    if (beforeType === 'array') {
      return renderArrayChanges(before, after);
    } else if (beforeType === 'object') {
      return renderObjectChanges(before, after);
    } else {
      // Primitive values
      if (before === after) {
        return <div className="text-gray-500 italic">No change</div>;
      }
      return renderPrimitiveChange(before, after);
    }
  };

  // Helper function to render a value with appropriate formatting
  const renderValue = (value: any) => {
    if (value === null) return <span className="text-gray-500 italic">null</span>;
    if (value === undefined) return <span className="text-gray-500 italic">undefined</span>;
    if (typeof value === 'string') return <span className="text-blue-600">"{value}"</span>;
    if (typeof value === 'number') return <span className="text-purple-600">{value}</span>;
    if (typeof value === 'boolean') return <span className="text-orange-600">{String(value)}</span>;
    if (Array.isArray(value)) {
      return (
        <div className="font-mono text-sm">
          <span className="text-gray-500">[</span>
          {value.length === 0 ? (
            <span className="text-gray-400 italic">empty</span>
          ) : (
            <span className="text-gray-600">{value.length} items</span>
          )}
          <span className="text-gray-500">]</span>
        </div>
      );
    }
    if (typeof value === 'object') {
      const keys = Object.keys(value);
      return (
        <div className="font-mono text-sm">
          <span className="text-gray-500">{'{'}</span>
          {keys.length === 0 ? (
            <span className="text-gray-400 italic">empty</span>
          ) : (
            <span className="text-gray-600">{keys.length} properties</span>
          )}
          <span className="text-gray-500">{'}'}</span>
        </div>
      );
    }
    return <span className="text-gray-600">{String(value)}</span>;
  };

  // Render changes for primitive values
  const renderPrimitiveChange = (before: any, after: any) => {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
        <div>
          <div className="text-xs font-medium text-red-700 mb-1">Before</div>
          <div className="bg-red-50 border border-red-200 rounded p-3">
            {renderValue(before)}
          </div>
        </div>
        <div>
          <div className="text-xs font-medium text-green-700 mb-1">After</div>
          <div className="bg-green-50 border border-green-200 rounded p-3">
            {renderValue(after)}
          </div>
        </div>
      </div>
    );
  };

  // Check if an array contains business items (has id, name, price, quantity, etc.)
  const isBusinessItemsArray = (arr: any[]) => {
    if (!arr || arr.length === 0) return false;
    const firstItem = arr[0];
    return typeof firstItem === 'object' &&
           firstItem !== null &&
           ('id' in firstItem || 'name' in firstItem || 'price' in firstItem || 'quantity' in firstItem);
  };

  // Render business item with key details highlighted
  const renderBusinessItem = (item: any) => {
    if (!item || typeof item !== 'object') {
      return renderValue(item);
    }

    const { id, name, price, quantity, ...otherProps } = item;

    return (
      <div className="space-y-2">
        <div className="flex flex-wrap gap-3">
          {name && (
            <div className="flex items-center gap-1">
              <span className="text-xs text-gray-500">Name:</span>
              <span className="font-medium text-gray-900">{name}</span>
            </div>
          )}
          {quantity !== undefined && (
            <div className="flex items-center gap-1">
              <span className="text-xs text-gray-500">Qty:</span>
              <span className="font-medium text-purple-600">{quantity}</span>
            </div>
          )}
          {price !== undefined && (
            <div className="flex items-center gap-1">
              <span className="text-xs text-gray-500">Price:</span>
              <span className="font-medium text-green-600">${price}</span>
            </div>
          )}
          {id !== undefined && (
            <div className="flex items-center gap-1">
              <span className="text-xs text-gray-500">ID:</span>
              <span className="font-mono text-xs text-gray-600">{id}</span>
            </div>
          )}
        </div>
        {Object.keys(otherProps).length > 0 && (
          <div className="text-xs text-gray-500">
            +{Object.keys(otherProps).length} other properties
          </div>
        )}
      </div>
    );
  };

  // Compare business items by ID or by content
  const findMatchingItem = (item: any, array: any[]) => {
    if (!item || !array) return null;

    // Try to match by ID first
    if (item.id !== undefined) {
      return array.find(arrItem => arrItem && arrItem.id === item.id);
    }

    // Fallback to exact match
    return array.find(arrItem => JSON.stringify(arrItem) === JSON.stringify(item));
  };

  // Render changes for arrays - enhanced for business items
  const renderArrayChanges = (before: any[], after: any[]) => {
    const isBusinessItems = isBusinessItemsArray(before) || isBusinessItemsArray(after);

    if (isBusinessItems) {
      return renderBusinessItemsChanges(before, after);
    }

    // For non-business arrays, use simple comparison
    const maxLength = Math.max(before.length, after.length);
    const changes = [];

    for (let i = 0; i < maxLength; i++) {
      const beforeItem = i < before.length ? before[i] : undefined;
      const afterItem = i < after.length ? after[i] : undefined;

      if (beforeItem === undefined) {
        changes.push({ type: 'added', index: i, value: afterItem });
      } else if (afterItem === undefined) {
        changes.push({ type: 'removed', index: i, value: beforeItem });
      } else if (JSON.stringify(beforeItem) !== JSON.stringify(afterItem)) {
        changes.push({ type: 'modified', index: i, before: beforeItem, after: afterItem });
      }
    }

    if (changes.length === 0) {
      return <div className="text-gray-500 italic">No changes in array</div>;
    }

    return (
      <div className="space-y-3">
        <div className="text-sm font-medium text-gray-700">
          Array Changes ({before.length} → {after.length} items)
        </div>
        {changes.map((change, idx) => (
          <div key={idx} className="border border-gray-200 rounded p-3">
            {change.type === 'added' && (
              <div className="space-y-2">
                <div className="flex items-center gap-2 text-sm font-medium text-green-700">
                  <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                  Added at index {change.index}
                </div>
                <div className="bg-green-50 border border-green-200 rounded p-2">
                  {renderValue(change.value)}
                </div>
              </div>
            )}
            {change.type === 'removed' && (
              <div className="space-y-2">
                <div className="flex items-center gap-2 text-sm font-medium text-red-700">
                  <span className="w-2 h-2 bg-red-500 rounded-full"></span>
                  Removed from index {change.index}
                </div>
                <div className="bg-red-50 border border-red-200 rounded p-2">
                  {renderValue(change.value)}
                </div>
              </div>
            )}
            {change.type === 'modified' && (
              <div className="space-y-2">
                <div className="flex items-center gap-2 text-sm font-medium text-blue-700">
                  <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                  Modified at index {change.index}
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                  <div>
                    <div className="text-xs font-medium text-red-700 mb-1">Before</div>
                    <div className="bg-red-50 border border-red-200 rounded p-2">
                      {renderValue(change.before)}
                    </div>
                  </div>
                  <div>
                    <div className="text-xs font-medium text-green-700 mb-1">After</div>
                    <div className="bg-green-50 border border-green-200 rounded p-2">
                      {renderValue(change.after)}
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        ))}
      </div>
    );
  };

  // Specialized renderer for business items (products, line items, etc.)
  const renderBusinessItemsChanges = (before: any[], after: any[]) => {
    const changes = [];
    const processedAfterItems = new Set();

    // Find removed and modified items
    for (const beforeItem of before) {
      const matchingAfterItem = findMatchingItem(beforeItem, after);

      if (!matchingAfterItem) {
        changes.push({ type: 'removed', item: beforeItem });
      } else {
        processedAfterItems.add(after.indexOf(matchingAfterItem));
        if (JSON.stringify(beforeItem) !== JSON.stringify(matchingAfterItem)) {
          changes.push({ type: 'modified', before: beforeItem, after: matchingAfterItem });
        }
      }
    }

    // Find added items
    for (let i = 0; i < after.length; i++) {
      if (!processedAfterItems.has(i)) {
        const afterItem = after[i];
        const matchingBeforeItem = findMatchingItem(afterItem, before);
        if (!matchingBeforeItem) {
          changes.push({ type: 'added', item: afterItem });
        }
      }
    }

    if (changes.length === 0) {
      return <div className="text-gray-500 italic">No changes in items</div>;
    }

    return (
      <div className="space-y-3">
        <div className="text-sm font-medium text-gray-700">
          Items Changes ({before.length} → {after.length} items)
        </div>
        {changes.map((change, idx) => (
          <div key={idx} className="border border-gray-200 rounded p-3">
            {change.type === 'added' && (
              <div className="space-y-2">
                <div className="flex items-center gap-2 text-sm font-medium text-green-700">
                  <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                  Added Item
                </div>
                <div className="bg-green-50 border border-green-200 rounded p-3">
                  {renderBusinessItem(change.item)}
                </div>
              </div>
            )}
            {change.type === 'removed' && (
              <div className="space-y-2">
                <div className="flex items-center gap-2 text-sm font-medium text-red-700">
                  <span className="w-2 h-2 bg-red-500 rounded-full"></span>
                  Removed Item
                </div>
                <div className="bg-red-50 border border-red-200 rounded p-3">
                  {renderBusinessItem(change.item)}
                </div>
              </div>
            )}
            {change.type === 'modified' && (
              <div className="space-y-2">
                <div className="flex items-center gap-2 text-sm font-medium text-blue-700">
                  <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                  Modified Item {change.before.name && `"${change.before.name}"`}
                </div>
                {renderBusinessItemChanges(change.before, change.after)}
              </div>
            )}
          </div>
        ))}
      </div>
    );
  };

  // Render detailed changes within a business item
  const renderBusinessItemChanges = (before: any, after: any) => {
    const beforeKeys = Object.keys(before);
    const afterKeys = Object.keys(after);
    const allKeys = Array.from(new Set([...beforeKeys, ...afterKeys]));

    const changes = [];

    for (const key of allKeys) {
      const beforeValue = before[key];
      const afterValue = after[key];

      if (!beforeKeys.includes(key)) {
        changes.push({ type: 'added', key, value: afterValue });
      } else if (!afterKeys.includes(key)) {
        changes.push({ type: 'removed', key, value: beforeValue });
      } else if (JSON.stringify(beforeValue) !== JSON.stringify(afterValue)) {
        changes.push({ type: 'modified', key, before: beforeValue, after: afterValue });
      }
    }

    if (changes.length === 0) {
      return <div className="text-gray-500 italic">No property changes</div>;
    }

    return (
      <div className="bg-blue-50 border border-blue-200 rounded p-3 space-y-2">
        {changes.map((change, idx) => (
          <div key={idx} className="flex items-center justify-between text-sm">
            {change.type === 'modified' && (
              <>
                <span className="font-medium text-gray-700 capitalize">{change.key}:</span>
                <div className="flex items-center gap-2">
                  <span className="text-red-600 line-through">{renderInlineValue(change.before)}</span>
                  <span className="text-gray-400">→</span>
                  <span className="text-green-600 font-medium">{renderInlineValue(change.after)}</span>
                </div>
              </>
            )}
            {change.type === 'added' && (
              <>
                <span className="font-medium text-gray-700 capitalize">{change.key}:</span>
                <span className="text-green-600 font-medium">+{renderInlineValue(change.value)}</span>
              </>
            )}
            {change.type === 'removed' && (
              <>
                <span className="font-medium text-gray-700 capitalize">{change.key}:</span>
                <span className="text-red-600 line-through">-{renderInlineValue(change.value)}</span>
              </>
            )}
          </div>
        ))}
      </div>
    );
  };

  // Render a value inline (for compact display)
  const renderInlineValue = (value: any) => {
    if (value === null) return 'null';
    if (value === undefined) return 'undefined';
    if (typeof value === 'string') return `"${value}"`;
    if (typeof value === 'number') return value.toString();
    if (typeof value === 'boolean') return value.toString();
    if (Array.isArray(value)) return `[${value.length} items]`;
    if (typeof value === 'object') return `{${Object.keys(value).length} props}`;
    return String(value);
  };

  // Render changes for objects - show added, removed, and modified properties
  const renderObjectChanges = (before: Record<string, any>, after: Record<string, any>) => {
    const beforeKeys = Object.keys(before);
    const afterKeys = Object.keys(after);
    const allKeys = Array.from(new Set([...beforeKeys, ...afterKeys]));

    const changes = [];

    for (const key of allKeys) {
      const beforeValue = before[key];
      const afterValue = after[key];

      if (!beforeKeys.includes(key)) {
        changes.push({ type: 'added', key, value: afterValue });
      } else if (!afterKeys.includes(key)) {
        changes.push({ type: 'removed', key, value: beforeValue });
      } else if (JSON.stringify(beforeValue) !== JSON.stringify(afterValue)) {
        changes.push({ type: 'modified', key, before: beforeValue, after: afterValue });
      }
    }

    if (changes.length === 0) {
      return <div className="text-gray-500 italic">No changes in object</div>;
    }

    return (
      <div className="space-y-3">
        <div className="text-sm font-medium text-gray-700">
          Object Changes ({beforeKeys.length} → {afterKeys.length} properties)
        </div>
        {changes.map((change, idx) => (
          <div key={idx} className="border border-gray-200 rounded p-3">
            {change.type === 'added' && (
              <div className="space-y-2">
                <div className="flex items-center gap-2 text-sm font-medium text-green-700">
                  <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                  Added property: <code className="bg-gray-100 px-1 rounded">{change.key}</code>
                </div>
                <div className="bg-green-50 border border-green-200 rounded p-2">
                  {renderValue(change.value)}
                </div>
              </div>
            )}
            {change.type === 'removed' && (
              <div className="space-y-2">
                <div className="flex items-center gap-2 text-sm font-medium text-red-700">
                  <span className="w-2 h-2 bg-red-500 rounded-full"></span>
                  Removed property: <code className="bg-gray-100 px-1 rounded">{change.key}</code>
                </div>
                <div className="bg-red-50 border border-red-200 rounded p-2">
                  {renderValue(change.value)}
                </div>
              </div>
            )}
            {change.type === 'modified' && (
              <div className="space-y-2">
                <div className="flex items-center gap-2 text-sm font-medium text-blue-700">
                  <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                  Modified property: <code className="bg-gray-100 px-1 rounded">{change.key}</code>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                  <div>
                    <div className="text-xs font-medium text-red-700 mb-1">Before</div>
                    <div className="bg-red-50 border border-red-200 rounded p-2">
                      {renderValue(change.before)}
                    </div>
                  </div>
                  <div>
                    <div className="text-xs font-medium text-green-700 mb-1">After</div>
                    <div className="bg-green-50 border border-green-200 rounded p-2">
                      {renderValue(change.after)}
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        ))}
      </div>
    );
  };

  // Render delta changes in a user-friendly format
  const renderDeltaChanges = (changes: Record<string, { before: any; after: any }>) => {
    const changeEntries = Object.entries(changes);

    if (changeEntries.length === 0) {
      return (
        <div className="text-center py-4">
          <p className="text-gray-500 text-sm">No changes detected</p>
        </div>
      );
    }

    return (
      <div className="space-y-6">
        {changeEntries.map(([field, change]) => {
          return (
            <div key={field} className="border border-gray-200 rounded-lg p-4">
              <h5 className="font-medium text-gray-900 mb-4 capitalize">
                {field.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
              </h5>

              {/* Sophisticated Change Analysis */}
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700">
                  � Change Analysis
                </label>
                <p className="text-xs text-gray-500 mb-3">
                  Intelligent analysis showing what was added, removed, or modified
                </p>
                {renderSophisticatedChanges(change.before, change.after, field)}
              </div>
            </div>
          );
        })}
      </div>
    );
  };

  if (!isOpen || !log) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" onClick={handleOverlayClick}>
      <div className="bg-white rounded-lg shadow-lg max-w-6xl w-full mx-4 max-h-[90vh] overflow-hidden flex flex-col">
        {/* Header */}
        <div className="p-6 border-b border-gray-200 flex-shrink-0 flex items-center justify-between">
          <div>
            <h3 className="text-xl font-bold text-gray-900">
              Log Details
            </h3>
            <p className="text-sm text-gray-600 mt-1">
              View detailed information about this log entry
            </p>
          </div>
          <button
            onClick={onClose}
            className="p-2 rounded-full hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors"
            title="Close"
            aria-label="Close modal"
          >
            <FiX className="h-5 w-5 text-gray-500" />
          </button>
        </div>
        
        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6">
          {/* Basic Information - Compact Layout */}
          <div className="mb-6">
            <h4 className="text-lg font-semibold text-gray-900 mb-4">Basic Information</h4>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {/* Operation */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center gap-2">
                  <FiTag className="w-4 h-4" />
                  Operation
                </label>
                <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${getOperationColor(log.operation)}`}>
                  {log.operation}
                </span>
              </div>

              {/* Entity */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center gap-2">
                  <FiDatabase className="w-4 h-4" />
                  Entity
                </label>
                <span className="text-gray-900 font-medium">{log.entity}</span>
              </div>

              {/* User */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center gap-2">
                  <FiUser className="w-4 h-4" />
                  User
                </label>
                <span className="text-gray-900 font-medium">
                  {userName || 'Unknown User'}
                </span>
                <p className="text-xs text-gray-500 mt-1">{log.userUuid}</p>
              </div>

              {/* Timestamp */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center gap-2">
                  <FiCalendar className="w-4 h-4" />
                  Timestamp
                </label>
                <span className="text-gray-900">{formatDate(log.createdAt)}</span>
              </div>
            </div>

            {/* Description - Full Width */}
            {log.description && (
              <div className="mt-4">
                <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center gap-2">
                  <FiFileText className="w-4 h-4" />
                  Description
                </label>
                <p className="text-gray-900 bg-gray-50 p-3 rounded-lg border">
                  {log.description}
                </p>
              </div>
            )}
          </div>

          {/* Data Section - Full Width */}
          <div className="mb-6">
            <h4 className="text-lg font-semibold text-gray-900 mb-4">
              {log.data && hasDeltaChanges(log.data) ? 'Changes' : 'Additional Data'}
            </h4>

            {log.data ? (
              <div>
                {hasDeltaChanges(log.data) ? (
                  <div>
                    {/* Delta Changes */}
                    <div className="mb-6">
                      <label className="block text-sm font-medium text-gray-700 mb-3">
                        Field Changes ({log.data.changeCount || 0} changes)
                      </label>
                      {renderDeltaChanges(log.data.changes)}
                    </div>

                    {/* Additional metadata */}
                    {Object.keys(log.data).some(key => !['changes', 'changedFields', 'changeCount'].includes(key)) && (
                      <div className="mt-6">
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Additional Information
                        </label>
                        <div className="bg-gray-900 text-green-400 p-4 rounded-lg border overflow-x-auto">
                          <pre className="text-sm font-mono whitespace-pre-wrap">
                            {formatJsonData(
                              Object.fromEntries(
                                Object.entries(log.data).filter(([key]) =>
                                  !['changes', 'changedFields', 'changeCount'].includes(key)
                                )
                              )
                            )}
                          </pre>
                        </div>
                      </div>
                    )}
                  </div>
                ) : (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      JSON Data
                    </label>
                    <div className="bg-gray-900 text-green-400 p-4 rounded-lg border overflow-x-auto">
                      <pre className="text-sm font-mono whitespace-pre-wrap">
                        {formatJsonData(log.data)}
                      </pre>
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div className="text-center py-8">
                <FiDatabase className="w-12 h-12 text-gray-300 mx-auto mb-3" />
                <p className="text-gray-500 text-sm">No additional data available</p>
              </div>
            )}
          </div>

          {/* Raw Data Tabs - Only show if delta changes exist */}
          {log.data && hasDeltaChanges(log.data) && (
            <div className="mb-6">
              <h4 className="text-lg font-semibold text-gray-900 mb-4">Raw Data</h4>

              {/* Old Data Tab */}
              {extractOldData(log.data) && (
                <div className="mb-4 border border-gray-200 rounded-lg overflow-hidden">
                  <button
                    onClick={() => setShowOldDataTab(!showOldDataTab)}
                    className="w-full px-4 py-3 bg-gray-50 hover:bg-gray-100 flex items-center justify-between text-left transition-colors"
                  >
                    <div className="flex items-center gap-2">
                      {showOldDataTab ? (
                        <FiChevronDown className="w-4 h-4 text-gray-600" />
                      ) : (
                        <FiChevronRight className="w-4 h-4 text-gray-600" />
                      )}
                      <span className="font-medium text-gray-900">Raw Old Data (Before Changes)</span>
                    </div>
                    <span className="text-xs text-gray-500 bg-red-100 px-2 py-1 rounded">
                      Original Values
                    </span>
                  </button>
                  {showOldDataTab && (
                    <div className="p-4 bg-gray-900 text-green-400 overflow-x-auto">
                      <pre className="text-sm font-mono whitespace-pre-wrap">
                        {formatJsonData(extractOldData(log.data) || {})}
                      </pre>
                    </div>
                  )}
                </div>
              )}

              {/* New Data Tab */}
              {extractNewData(log.data) && (
                <div className="mb-4 border border-gray-200 rounded-lg overflow-hidden">
                  <button
                    onClick={() => setShowNewDataTab(!showNewDataTab)}
                    className="w-full px-4 py-3 bg-gray-50 hover:bg-gray-100 flex items-center justify-between text-left transition-colors"
                  >
                    <div className="flex items-center gap-2">
                      {showNewDataTab ? (
                        <FiChevronDown className="w-4 h-4 text-gray-600" />
                      ) : (
                        <FiChevronRight className="w-4 h-4 text-gray-600" />
                      )}
                      <span className="font-medium text-gray-900">Raw New Data (After Changes)</span>
                    </div>
                    <span className="text-xs text-gray-500 bg-green-100 px-2 py-1 rounded">
                      Updated Values
                    </span>
                  </button>
                  {showNewDataTab && (
                    <div className="p-4 bg-gray-900 text-green-400 overflow-x-auto">
                      <pre className="text-sm font-mono whitespace-pre-wrap">
                        {formatJsonData(extractNewData(log.data) || {})}
                      </pre>
                    </div>
                  )}
                </div>
              )}
            </div>
          )}

          {/* Log ID */}
          <div className="pt-6 border-t border-gray-200">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Log ID
            </label>
            <code className="text-xs text-gray-600 bg-gray-100 px-2 py-1 rounded">
              {log.id}
            </code>
          </div>
        </div>

        {/* Footer */}
        <div className="p-6 border-t border-gray-200 flex-shrink-0 flex justify-end">
          <button
            onClick={onClose}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-400 transition-colors"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
} 