import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, MoreThanOrEqual } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { AuthAudit } from './auth-audit.entity';

export enum AuthEventType {
  LOGIN_SUCCESS = 'LOGIN_SUCCESS',
  LOGIN_FAILURE = 'LOGIN_FAILURE',
  LOGOUT = 'LOGOUT',
  ACCOUNT_LOCKED = 'ACCOUNT_LOCKED',
  ACCOUNT_UNLOCKED = 'ACCOUNT_UNLOCKED',
  PASSWORD_CHANGE = 'PASSWORD_CHANGE',
  GOOGLE_LOGIN_SUCCESS = 'GOOGLE_LOGIN_SUCCESS',
  GOOGLE_LOGIN_FAILURE = 'GOOGLE_LOGIN_FAILURE',
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED',
  SUSPICIOUS_ACTIVITY = 'SUSPICIOUS_ACTIVITY',
}

export interface AuthAuditEvent {
  eventType: AuthEventType;
  userId?: string;
  userEmail?: string;
  ipAddress?: string;
  userAgent?: string;
  timestamp: Date;
  details?: Record<string, any>;
  success: boolean;
  errorMessage?: string;
}

@Injectable()
export class AuthAuditService {
  private readonly logger = new Logger(AuthAuditService.name);

  constructor(
    @InjectRepository(AuthAudit) private authAuditRepository: Repository<AuthAudit>,
  ) {}

  async logEvent(event: AuthAuditEvent): Promise<void> {
    try {
      const auditRecord = new AuthAudit();
      auditRecord.eventType = event.eventType;
      auditRecord.userId = event.userId;
      auditRecord.userEmail = event.userEmail;
      auditRecord.ipAddress = event.ipAddress;
      auditRecord.userAgent = event.userAgent;
      auditRecord.timestamp = event.timestamp || new Date();
      auditRecord.details = event.details;
      auditRecord.success = event.success;
      auditRecord.errorMessage = event.errorMessage;
      
      await this.authAuditRepository.save(auditRecord);
      
      // Log to console for debugging
      this.logger.log(`Auth Event: ${event.eventType} - ${event.userEmail || 'Unknown'} - ${event.success ? 'SUCCESS' : 'FAILURE'}`);
      
      if (event.errorMessage) {
        this.logger.warn(`Auth Error: ${event.errorMessage}`);
      }
    } catch (error) {
      this.logger.error('Failed to log auth audit event:', error);
    }
  }

  async logLoginSuccess(userId: string, userEmail: string, ipAddress?: string, userAgent?: string): Promise<void> {
    await this.logEvent({
      eventType: AuthEventType.LOGIN_SUCCESS,
      userId,
      userEmail,
      ipAddress,
      userAgent,
      timestamp: new Date(),
      success: true,
    });
  }

  async logLoginFailure(userEmail: string, errorMessage: string, ipAddress?: string, userAgent?: string): Promise<void> {
    await this.logEvent({
      eventType: AuthEventType.LOGIN_FAILURE,
      userEmail,
      ipAddress,
      userAgent,
      timestamp: new Date(),
      success: false,
      errorMessage,
    });
  }

  async logGoogleLoginSuccess(userId: string, userEmail: string, ipAddress?: string, userAgent?: string): Promise<void> {
    await this.logEvent({
      eventType: AuthEventType.GOOGLE_LOGIN_SUCCESS,
      userId,
      userEmail,
      ipAddress,
      userAgent,
      timestamp: new Date(),
      success: true,
    });
  }

  async logGoogleLoginFailure(userEmail: string, errorMessage: string, ipAddress?: string, userAgent?: string): Promise<void> {
    await this.logEvent({
      eventType: AuthEventType.GOOGLE_LOGIN_FAILURE,
      userEmail,
      ipAddress,
      userAgent,
      timestamp: new Date(),
      success: false,
      errorMessage,
    });
  }

  async logLogout(userId: string, userEmail: string, ipAddress?: string, userAgent?: string): Promise<void> {
    await this.logEvent({
      eventType: AuthEventType.LOGOUT,
      userId,
      userEmail,
      ipAddress,
      userAgent,
      timestamp: new Date(),
      success: true,
    });
  }

  async logAccountLocked(userEmail: string, reason: string, ipAddress?: string): Promise<void> {
    await this.logEvent({
      eventType: AuthEventType.ACCOUNT_LOCKED,
      userEmail,
      ipAddress,
      timestamp: new Date(),
      success: false,
      errorMessage: reason,
      details: { reason },
    });
  }

  async logRateLimitExceeded(userEmail: string, ipAddress?: string): Promise<void> {
    await this.logEvent({
      eventType: AuthEventType.RATE_LIMIT_EXCEEDED,
      userEmail,
      ipAddress,
      timestamp: new Date(),
      success: false,
      errorMessage: 'Rate limit exceeded',
    });
  }

  async getRecentEvents(userEmail: string, hours: number = 24): Promise<AuthAuditEvent[]> {
    const cutoffTime = new Date(Date.now() - hours * 60 * 60 * 1000);
    
    const events = await this.authAuditRepository.find({
      where: {
        userEmail,
        timestamp: MoreThanOrEqual(cutoffTime),
      },
      order: {
        timestamp: 'DESC',
      },
    });
    
    return events as unknown as AuthAuditEvent[];
  }

  async getFailedLoginAttempts(userEmail: string, hours: number = 1): Promise<AuthAuditEvent[]> {
    const cutoffTime = new Date(Date.now() - hours * 60 * 60 * 1000);
    
    const events = await this.authAuditRepository.find({
      where: {
        userEmail,
        eventType: AuthEventType.LOGIN_FAILURE,
        timestamp: MoreThanOrEqual(cutoffTime),
      },
      order: {
        timestamp: 'DESC',
      },
    });
    
    return events as unknown as AuthAuditEvent[];
  }
} 