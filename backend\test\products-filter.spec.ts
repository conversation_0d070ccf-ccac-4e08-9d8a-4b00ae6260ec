import { Test, TestingModule } from "@nestjs/testing";
import { ProductsService } from "../src/products/products.service.typeorm";
import { getRepositoryToken } from "@nestjs/typeorm";
import { Product } from "../src/products/product.entity";
import { Uuid7 } from "../src/utils/uuid7";

describe("ProductsService - Advanced Filter", () => {
  let service: ProductsService;
  let mockProductRepository: any;

  beforeEach(async () => {
    // Create mock for Product repository
    mockProductRepository = {
      find: jest.fn(),
      count: jest.fn(),
      createQueryBuilder: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ProductsService,
        {
          provide: getRepositoryToken(Product),
          useValue: mockProductRepository,
        },
      ],
    }).compile();

    service = module.get<ProductsService>(ProductsService);
  });

  describe("filterAdvanced", () => {
    it("should throw error for invalid warehouseUuid", async () => {
      // Arrange
      const invalidWarehouseUuid = "invalid-uuid";
      const productCategoryUuid = new Uuid7().toString();

      // Mock the repository methods for invalid UUID
      mockProductRepository.find.mockResolvedValue([]);
      mockProductRepository.count.mockResolvedValue(0);

      // Act & Assert
      const result = await service.filterAdvanced(
        invalidWarehouseUuid,
        { productCategoryUuid },
        1,
        10,
      );
      
      expect(result.data).toHaveLength(0);
      expect(result.total).toBe(0);
    });

    it("should throw error for invalid productCategoryUuid", async () => {
      // Arrange
      const warehouseUuid = new Uuid7().toString();
      const invalidProductCategoryUuid = "invalid-uuid";

      // Mock the repository methods for invalid UUID
      mockProductRepository.find.mockResolvedValue([]);
      mockProductRepository.count.mockResolvedValue(0);

      // Act & Assert
      const result = await service.filterAdvanced(
        warehouseUuid,
        { productCategoryUuid: invalidProductCategoryUuid },
        1,
        10,
      );
      
      expect(result.data).toHaveLength(0);
      expect(result.total).toBe(0);
    });

    it("should handle valid UUIDs without throwing errors", async () => {
      // Arrange
      const warehouseUuid = new Uuid7().toString();
      const productCategoryUuid = new Uuid7().toString();
      const mockProducts = [
        {
          id: new Uuid7().toString(),
          name: "Test Product",
          productCategoryUuid: productCategoryUuid,
        },
      ];

      // Mock the repository methods
      mockProductRepository.find.mockResolvedValue(mockProducts);
      mockProductRepository.count.mockResolvedValue(1);

      // Act
      const result = await service.filterAdvanced(
        warehouseUuid,
        { productCategoryUuid },
        1,
        10,
      );

      // Assert
      expect(mockProductRepository.find).toHaveBeenCalled();
      expect(result.data).toHaveLength(1);
    });

    it("should handle filtering when products have no productCategoryUuid", async () => {
      // Arrange
      const warehouseUuid = new Uuid7().toString();
      const productCategoryUuid = new Uuid7().toString();

      // Mock the repository methods
      mockProductRepository.find.mockResolvedValue([]); // No products match the category filter
      mockProductRepository.count.mockResolvedValue(0);

      // Act
      const result = await service.filterAdvanced(
        warehouseUuid,
        { productCategoryUuid },
        1,
        10,
      );

      // Assert
      expect(mockProductRepository.find).toHaveBeenCalled();
      expect(result.data).toHaveLength(0);
      expect(result.total).toBe(0);
    });

    it("should handle filtering without productCategoryUuid filter", async () => {
      // Arrange
      const warehouseUuid = new Uuid7().toString();
      const mockProducts = [
        {
          id: new Uuid7().toString(),
          name: "Test Product",
          productCategoryUuid: undefined, // Product without category
        },
      ];

      // Mock the repository methods
      mockProductRepository.find.mockResolvedValue(mockProducts);
      mockProductRepository.count.mockResolvedValue(1);

      // Act - filter without productCategoryUuid
      const result = await service.filterAdvanced(
        warehouseUuid,
        { name: "Test" }, // Only filter by name
        1,
        10,
      );

      // Assert
      expect(mockProductRepository.find).toHaveBeenCalled();
      expect(result.data).toHaveLength(1);
    });
  });
});
