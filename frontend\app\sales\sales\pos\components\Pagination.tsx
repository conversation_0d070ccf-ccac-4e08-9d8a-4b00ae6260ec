import React from 'react';
import { ChevronLeftIcon, ChevronRightIcon } from '@heroicons/react/24/outline';
import type { PaginationInfo } from '../types';

interface PaginationProps {
  pagination: PaginationInfo;
  onPageChange: (page: number) => void;
  onNextPage: () => void;
  onPrevPage: () => void;
  isLoading?: boolean;
  disabled?: boolean;
}

export function Pagination({
  pagination,
  onPageChange,
  onNextPage,
  onPrevPage,
  isLoading = false,
  disabled = false
}: PaginationProps) {
  const { page, limit, total, hasNext, hasPrev } = pagination;
  const totalPages = Math.ceil(total / limit);
  const startItem = (page - 1) * limit + 1;
  const endItem = Math.min(page * limit, total);

  // Check if buttons should be disabled
  const isDisabled = isLoading || disabled;

  // Generate page numbers to display
  const getPageNumbers = () => {
    const pages: number[] = [];
    const maxPages = 3; // Show max 3 page numbers for cleaner look
    
    if (totalPages <= maxPages) {
      // Show all pages if total is small
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      // Show pages around current page
      const start = Math.max(1, page - 1);
      const end = Math.min(totalPages, page + 1);
      
      if (start > 1) {
        pages.push(1);
        if (start > 2) pages.push(-1); // -1 represents ellipsis
      }
      
      for (let i = start; i <= end; i++) {
        pages.push(i);
      }
      
      if (end < totalPages) {
        if (end < totalPages - 1) pages.push(-1); // ellipsis
        pages.push(totalPages);
      }
    }
    
    return pages;
  };

  const pageNumbers = getPageNumbers();

  if (total === 0) {
    return null;
  }

  // Always show pagination info even for single page to help users understand the limit
  const showNavigation = true; // Always show navigation

  return (
    <div className="bg-white border-t border-gray-200 px-4 py-3 flex-shrink-0 relative z-10">
      <div className="flex items-center justify-between">
        {/* Info */}
        <div className="flex items-center">
          <span className="text-sm text-gray-700">
            <span className="font-medium">{startItem}-{endItem}</span> of{' '}
            <span className="font-medium">{total}</span> products
          </span>
        </div>

        {/* Navigation */}
        {showNavigation && (
          <div className="flex items-center space-x-1">
            {/* Previous button */}
            <button
              onClick={onPrevPage}
              disabled={!hasPrev || isDisabled}
              className="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-600 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 hover:text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-white transition-colors"
              title="Previous page"
            >
              <ChevronLeftIcon className="h-4 w-4 mr-1" />
              Previous
            </button>

            {/* Page numbers */}
            <div className="flex items-center space-x-1 mx-4">
              {pageNumbers.map((pageNum, index) => {
                if (pageNum === -1) {
                  return (
                    <span key={`ellipsis-${index}`} className="px-2 py-1 text-gray-400">
                      ...
                    </span>
                  );
                }

                return (
                  <button
                    key={pageNum}
                    onClick={() => onPageChange(pageNum)}
                    disabled={isDisabled}
                    className={`inline-flex items-center justify-center w-10 h-10 text-sm font-medium rounded-lg transition-colors ${
                      pageNum === page
                        ? 'bg-blue-600 text-white shadow-sm'
                        : 'text-gray-700 bg-white border border-gray-300 hover:bg-gray-50'
                    } disabled:opacity-50 disabled:cursor-not-allowed`}
                  >
                    {pageNum}
                  </button>
                );
              })}
            </div>

            {/* Next button */}
            <button
              onClick={onNextPage}
              disabled={!hasNext || isDisabled}
              className="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-600 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 hover:text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-white transition-colors"
              title="Next page"
            >
              Next
              <ChevronRightIcon className="h-4 w-4 ml-1" />
            </button>
          </div>
        )}
      </div>
      
      {/* Page info */}
      <div className="flex items-center justify-center mt-2">
        <span className="text-xs text-gray-500">
          Page {page} of {totalPages}
        </span>
      </div>
    </div>
  );
} 