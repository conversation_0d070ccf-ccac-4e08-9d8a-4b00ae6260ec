"use client";

// Customer Payments Page
// Convention: Always inject warehouseUuid and userUuid from AuthContext in parent/page component
// Uses ItemsTable component for consistent UI as per project guidelines
// Follows UI guidelines for entity management pages (F2 shortcut, clear action buttons)

import React, { useState, useEffect, useMemo, useCallback } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { useCustomerPayments, useCustomerPaymentActions } from "../useCustomerPayments";
import { 
  CustomerPayment, 
  CreateCustomerPaymentDto, 
  UpdateCustomerPaymentDto, 
  FilterCustomerPaymentDto,
  formatCurrency,
  formatPaymentMethod,
  formatPaymentStatus,
  getStatusColor
} from "../customerPaymentsApi";
import { useCustomers } from "../useCustomers";
import ItemsTable, { ItemsTableColumn } from "@/components/itemsTable/ItemsTable";
import TableActionButtons from "@/components/itemsTable/TableActionButtons";
import ProtectedRoute from "@/components/ProtectedRoute";
import { toast } from "sonner";
import { Search, Filter, Plus, Eye, Edit, X, DollarSign, RefreshCw, Ban } from "lucide-react";

// Import modals
import CustomerPaymentModal from "./CustomerPaymentModal";
import CustomerPaymentDetailsModal from "./CustomerPaymentDetailsModal";

// Custom hooks for better organization
const useDebounce = (value: string, delay: number) => {
  const [debouncedValue, setDebouncedValue] = useState(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => clearTimeout(handler);
  }, [value, delay]);

  return debouncedValue;
};

const usePaymentFilters = (warehouseUuid?: string) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [customerFilter, setCustomerFilter] = useState("");
  const [statusFilter, setStatusFilter] = useState("");
  const [paymentMethodFilter, setPaymentMethodFilter] = useState("");
  const [dateFromFilter, setDateFromFilter] = useState("");
  const [dateToFilter, setDateToFilter] = useState("");
  const [amountMinFilter, setAmountMinFilter] = useState("");
  const [amountMaxFilter, setAmountMaxFilter] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [sortBy, setSortBy] = useState("createdAt");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");

  const debouncedSearchTerm = useDebounce(searchTerm, 300);

  const filter: FilterCustomerPaymentDto = useMemo(() => {
    const filterObj: FilterCustomerPaymentDto = {};
    
    if (warehouseUuid) {
      filterObj.warehouseUuid = warehouseUuid;
    }
    
    if (customerFilter.trim()) {
      filterObj.customerUuid = customerFilter.trim();
    }
    
    if (debouncedSearchTerm.trim()) {
      filterObj.description = debouncedSearchTerm.trim();
    }
    
    if (statusFilter) {
      filterObj.status = statusFilter as any;
    }
    
    if (paymentMethodFilter) {
      filterObj.paymentMethod = paymentMethodFilter as any;
    }
    
    if (dateFromFilter) {
      filterObj.fromDate = new Date(dateFromFilter).toISOString();
    }
    
    if (dateToFilter) {
      filterObj.toDate = new Date(dateToFilter).toISOString();
    }
    
    if (amountMinFilter) {
      filterObj.minAmount = parseFloat(amountMinFilter);
    }
    
    if (amountMaxFilter) {
      filterObj.maxAmount = parseFloat(amountMaxFilter);
    }
    
    return filterObj;
  }, [warehouseUuid, debouncedSearchTerm, customerFilter, statusFilter, paymentMethodFilter, dateFromFilter, dateToFilter, amountMinFilter, amountMaxFilter]);

  // Reset page when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [debouncedSearchTerm, customerFilter, statusFilter, paymentMethodFilter, dateFromFilter, dateToFilter, amountMinFilter, amountMaxFilter]);

  return {
    searchTerm,
    setSearchTerm,
    customerFilter,
    setCustomerFilter,
    statusFilter,
    setStatusFilter,
    paymentMethodFilter,
    setPaymentMethodFilter,
    dateFromFilter,
    setDateFromFilter,
    dateToFilter,
    setDateToFilter,
    amountMinFilter,
    setAmountMinFilter,
    amountMaxFilter,
    setAmountMaxFilter,
    currentPage,
    setCurrentPage,
    pageSize,
    setPageSize,
    sortBy,
    setSortBy,
    sortOrder,
    setSortOrder,
    filter,
  };
};

// Components
const SearchAndFilters: React.FC<{
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  customerFilter: string;
  setCustomerFilter: (customer: string) => void;
  statusFilter: string;
  setStatusFilter: (status: string) => void;
  paymentMethodFilter: string;
  setPaymentMethodFilter: (method: string) => void;
  dateFromFilter: string;
  setDateFromFilter: (date: string) => void;
  dateToFilter: string;
  setDateToFilter: (date: string) => void;
  amountMinFilter: string;
  setAmountMinFilter: (amount: string) => void;
  amountMaxFilter: string;
  setAmountMaxFilter: (amount: string) => void;
  pageSize: number;
  setPageSize: (size: number) => void;
  onAddPayment: () => void;
  customers: any[];
  showAdvancedFilters: boolean;
  setShowAdvancedFilters: (show: boolean) => void;
}> = ({
  searchTerm,
  setSearchTerm,
  customerFilter,
  setCustomerFilter,
  statusFilter,
  setStatusFilter,
  paymentMethodFilter,
  setPaymentMethodFilter,
  dateFromFilter,
  setDateFromFilter,
  dateToFilter,
  setDateToFilter,
  amountMinFilter,
  setAmountMinFilter,
  amountMaxFilter,
  setAmountMaxFilter,
  pageSize,
  setPageSize,
  onAddPayment,
  customers,
  showAdvancedFilters,
  setShowAdvancedFilters,
}) => (
  <div className="mb-6 space-y-4">
    <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
        {/* Search */}
        <div className="relative">
          <input
            type="text"
            placeholder="Search payments..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 w-64"
          />
          <Search className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" />
        </div>

        {/* Quick filters */}
        <div className="flex gap-2">
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">All Statuses</option>
            <option value="pending">Pending</option>
            <option value="completed">Completed</option>
            <option value="failed">Failed</option>
            <option value="cancelled">Cancelled</option>
            <option value="refunded">Refunded</option>
          </select>

          <select
            value={paymentMethodFilter}
            onChange={(e) => setPaymentMethodFilter(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">All Methods</option>
            <option value="cash">Cash</option>
            <option value="credit_card">Credit Card</option>
            <option value="debit_card">Debit Card</option>
            <option value="bank_transfer">Bank Transfer</option>
            <option value="check">Check</option>
            <option value="mobile_payment">Mobile Payment</option>
          </select>

          <button
            onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
            className="flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
          >
            <Filter className="mr-2 h-4 w-4" />
            Filters
          </button>
        </div>
      </div>

      <div className="flex items-center gap-4">
        {/* Page Size */}
        <select
          value={pageSize}
          onChange={(e) => setPageSize(Number(e.target.value))}
          className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value={10}>10 per page</option>
          <option value={25}>25 per page</option>
          <option value={50}>50 per page</option>
        </select>

        {/* Add Payment Button */}
        <button
          onClick={onAddPayment}
          className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <Plus className="mr-2 h-4 w-4" />
          Add Payment (F2)
        </button>
      </div>
    </div>

    {/* Advanced Filters */}
    {showAdvancedFilters && (
      <div className="bg-gray-50 p-4 rounded-lg border">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* Customer Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Customer
            </label>
            <select
              value={customerFilter}
              onChange={(e) => setCustomerFilter(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">All Customers</option>
              {customers.map((customer) => (
                <option key={customer.uuid} value={customer.uuid}>
                  {customer.name}
                </option>
              ))}
            </select>
          </div>

          {/* Date From */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              From Date
            </label>
            <input
              type="date"
              value={dateFromFilter}
              onChange={(e) => setDateFromFilter(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Date To */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              To Date
            </label>
            <input
              type="date"
              value={dateToFilter}
              onChange={(e) => setDateToFilter(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Amount Min */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Min Amount
            </label>
            <input
              type="number"
              step="0.01"
              placeholder="0.00"
              value={amountMinFilter}
              onChange={(e) => setAmountMinFilter(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Amount Max */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Max Amount
            </label>
            <input
              type="number"
              step="0.01"
              placeholder="999999.99"
              value={amountMaxFilter}
              onChange={(e) => setAmountMaxFilter(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>
      </div>
    )}
  </div>
);



// Main component
export default function CustomerPaymentsPage() {
  const { user } = useAuth();
  const warehouseUuid = user?.warehouseUuid;
  const userUuid = user?.uuid;

  // Modal states
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);
  const [editingPayment, setEditingPayment] = useState<CustomerPayment | null>(null);
  const [selectedPayment, setSelectedPayment] = useState<CustomerPayment | null>(null);
  const [modalError, setModalError] = useState<string | null>(null);
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);

  // Custom hooks
  const filters = usePaymentFilters(warehouseUuid || undefined);
  const actions = useCustomerPaymentActions();

  // Fetch customer payments
  const { 
    data: paymentsResponse, 
    isLoading, 
    error: fetchError 
  } = useCustomerPayments(
    { 
      page: filters.currentPage, 
      limit: filters.pageSize,
      sortBy: filters.sortBy,
      sortOrder: filters.sortOrder
    },
    filters.filter
  );

  // Fetch customers for filter dropdown
  const { data: customersResponse } = useCustomers(
    { page: 1, limit: 1000 },
    { warehouseUuid: warehouseUuid || undefined }
  );

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'F2') {
        e.preventDefault();
        handleAddPayment();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, []);

  // Event handlers
  const handleAddPayment = useCallback(() => {
    setEditingPayment(null);
    setModalError(null);
    setIsModalOpen(true);
  }, []);

  const handleEditPayment = useCallback((payment: CustomerPayment) => {
    setEditingPayment(payment);
    setModalError(null);
    setIsModalOpen(true);
  }, []);

  const handleViewPaymentDetails = useCallback((payment: CustomerPayment) => {
    setSelectedPayment(payment);
    setIsDetailsModalOpen(true);
  }, []);

  const handleRefundPayment = useCallback(async (payment: CustomerPayment) => {
    if (!userUuid) {
      toast.error('User information not available');
      return;
    }

    const reason = prompt('Enter refund reason:');
    if (!reason?.trim()) {
      toast.error('Refund reason is required');
      return;
    }

    if (!window.confirm(`Are you sure you want to refund ${formatCurrency(payment.amount)} for this payment?`)) {
      return;
    }

    try {
      await actions.refundPayment({
        uuid: payment.uuid,
        userUuid,
        reason: reason.trim()
      });
      toast.success('Payment refunded successfully');
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || error.message || 'Failed to refund payment';
      toast.error(errorMessage);
    }
  }, [actions.refundPayment, userUuid]);

  const handleCancelPayment = useCallback(async (payment: CustomerPayment) => {
    if (!userUuid) {
      toast.error('User information not available');
      return;
    }

    if (!window.confirm(`Are you sure you want to cancel this ${formatCurrency(payment.amount)} payment?`)) {
      return;
    }

    try {
      await actions.cancelPayment({
        uuid: payment.uuid,
        userUuid
      });
      toast.success('Payment cancelled successfully');
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || error.message || 'Failed to cancel payment';
      toast.error(errorMessage);
    }
  }, [actions.cancelPayment, userUuid]);

  const handleDeletePayment = useCallback(async (payment: CustomerPayment) => {
    if (!window.confirm(`Are you sure you want to delete this ${formatCurrency(payment.amount)} payment?`)) {
      return;
    }

    try {
      await actions.deletePayment(payment.uuid);
      toast.success('Payment deleted successfully');
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || error.message || 'Failed to delete payment';
      toast.error(errorMessage);
    }
  }, [actions.deletePayment]);

  // Table columns
  const columns: ItemsTableColumn<CustomerPayment>[] = useMemo(() => [
    {
      key: 'customerName',
      header: 'Customer',
      cellClassName: 'text-left font-medium',
      headerClassName: 'text-left',
      render: (value: string, payment: CustomerPayment) => (
        <div>
          <div className="font-medium">{value || 'Unknown Customer'}</div>
          {payment.saleInvoiceNumber && (
            <div className="text-sm text-gray-500">Invoice: {payment.saleInvoiceNumber}</div>
          )}
        </div>
      ),
    },
    {
      key: 'amount',
      header: 'Amount',
      cellClassName: 'text-right font-semibold',
      headerClassName: 'text-right',
      render: (value: number) => (
        <span className="text-green-600 font-semibold">
          {formatCurrency(value)}
        </span>
      ),
    },
    {
      key: 'paymentMethod',
      header: 'Method',
      cellClassName: 'text-center',
      headerClassName: 'text-center',
      render: (value: string) => (
        <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded text-sm">
          {formatPaymentMethod(value as any)}
        </span>
      ),
    },
    {
      key: 'status',
      header: 'Status',
      cellClassName: 'text-center',
      headerClassName: 'text-center',
      render: (value: string) => {
        const colorClass = getStatusColor(value as any);
        return (
          <span className={`px-2 py-1 rounded text-sm ${colorClass}`}>
            {formatPaymentStatus(value as any)}
          </span>
        );
      },
    },
    {
      key: 'description',
      header: 'Description',
      cellClassName: 'text-left',
      headerClassName: 'text-left',
      render: (value: string) => (
        <div className="max-w-xs truncate" title={value}>
          {value || '—'}
        </div>
      ),
    },
    {
      key: 'createdAt',
      header: 'Date',
      cellClassName: 'text-center',
      headerClassName: 'text-center',
      render: (value: Date) => (
        <div className="text-sm">
          {new Date(value).toLocaleDateString()}
        </div>
      ),
    },
    {
      key: 'actions',
      header: 'Actions',
      headerClassName: 'text-center',
      cellClassName: 'text-center align-middle',
      render: (_: any, payment: CustomerPayment) => (
        <div className="flex items-center justify-center gap-2">
          <button
            onClick={() => handleViewPaymentDetails(payment)}
            className="p-2 rounded-full hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-blue-400"
            title="View Details"
          >
            <Eye className="h-4 w-4 text-blue-600" />
          </button>
          
          {payment.status === 'pending' && (
            <>
              <button
                onClick={() => handleEditPayment(payment)}
                className="p-2 rounded-full hover:bg-yellow-100 focus:outline-none focus:ring-2 focus:ring-yellow-400"
                title="Edit"
                disabled={actions.isLoading}
              >
                <Edit className="h-4 w-4 text-yellow-600" />
              </button>
              <button
                onClick={() => handleCancelPayment(payment)}
                className="p-2 rounded-full hover:bg-orange-100 focus:outline-none focus:ring-2 focus:ring-orange-400"
                title="Cancel"
                disabled={actions.isLoading}
              >
                <Ban className="h-4 w-4 text-orange-600" />
              </button>
              <button
                onClick={() => handleDeletePayment(payment)}
                className="p-2 rounded-full hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-red-400"
                title="Delete"
                disabled={actions.isLoading}
              >
                <X className="h-4 w-4 text-red-600" />
              </button>
            </>
          )}
          
          {payment.status === 'completed' && (
            <button
              onClick={() => handleRefundPayment(payment)}
              className="p-2 rounded-full hover:bg-purple-100 focus:outline-none focus:ring-2 focus:ring-purple-400"
              title="Refund"
              disabled={actions.isLoading}
            >
              <RefreshCw className="h-4 w-4 text-purple-600" />
            </button>
          )}
        </div>
      ),
    },
  ], [handleViewPaymentDetails, handleEditPayment, handleCancelPayment, handleDeletePayment, handleRefundPayment, actions.isLoading]);

  // Data - extract from nested meta structure
  const payments = paymentsResponse?.data || [];
  const totalPages = paymentsResponse?.meta?.totalPages || 0;
  const totalPayments = paymentsResponse?.meta?.total || 0;
  const customers = customersResponse?.data || [];

  if (fetchError) {
    return (
      <ProtectedRoute>
        <div className="p-6">
          <div className="text-center py-12">
            <div className="text-red-600 text-lg font-medium mb-2">
              Error loading customer payments
            </div>
            <div className="text-gray-600">
              {fetchError instanceof Error ? fetchError.message : 'An unexpected error occurred'}
            </div>
          </div>
        </div>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute>
      <div className="p-6">
        {/* Header */}
        <div className="mb-6">
          <div className="flex items-center space-x-3 mb-2">
            <DollarSign className="h-8 w-8 text-green-600" />
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Customer Payments</h1>
              <p className="mt-1 text-sm text-gray-600">
                Manage customer payments and process refunds
              </p>
            </div>
          </div>
        </div>
        
        {/* Search and Filters */}
        <SearchAndFilters
          searchTerm={filters.searchTerm}
          setSearchTerm={filters.setSearchTerm}
          customerFilter={filters.customerFilter}
          setCustomerFilter={filters.setCustomerFilter}
          statusFilter={filters.statusFilter}
          setStatusFilter={filters.setStatusFilter}
          paymentMethodFilter={filters.paymentMethodFilter}
          setPaymentMethodFilter={filters.setPaymentMethodFilter}
          dateFromFilter={filters.dateFromFilter}
          setDateFromFilter={filters.setDateFromFilter}
          dateToFilter={filters.dateToFilter}
          setDateToFilter={filters.setDateToFilter}
          amountMinFilter={filters.amountMinFilter}
          setAmountMinFilter={filters.setAmountMinFilter}
          amountMaxFilter={filters.amountMaxFilter}
          setAmountMaxFilter={filters.setAmountMaxFilter}
          pageSize={filters.pageSize}
          setPageSize={filters.setPageSize}
          onAddPayment={handleAddPayment}
          customers={customers}
          showAdvancedFilters={showAdvancedFilters}
          setShowAdvancedFilters={setShowAdvancedFilters}
        />

        {/* Results Info */}
        <div className="mb-4 text-sm text-gray-600">
          Showing {payments.length} of {totalPayments} payments
        </div>

        {/* Table */}
        {isLoading ? (
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span className="ml-2 text-gray-600">Loading payments...</span>
          </div>
        ) : (
          <ItemsTable
            columns={columns}
            data={payments}
            noDataText={<span className="text-gray-400">No payments found.</span>}
            containerClassName="bg-white rounded-xl shadow-lg border border-gray-200 w-full"
            pagination={{
              currentPage: filters.currentPage,
              totalPages: totalPages,
              onPageChange: filters.setCurrentPage,
              totalItems: totalPayments,
              itemsPerPage: filters.pageSize,
            }}
          />
        )}

        {/* Modals */}
        <CustomerPaymentModal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          initialData={editingPayment}
          error={modalError}
          isLoading={actions.isLoading}
        />

        <CustomerPaymentDetailsModal
          isOpen={isDetailsModalOpen}
          onClose={() => setIsDetailsModalOpen(false)}
          payment={selectedPayment}
        />
      </div>
    </ProtectedRoute>
  );
} 