// invoiceApi.ts - API utilities for invoice-related endpoints
import axios from 'axios';
import { getAxiosAuthHeaders } from '@/utils/authHeaders';

const API_BASE = '/api';

// Company interface matching backend DTO
export interface CompanyInfo {
  uuid: string;
  name: string;
  nif: string;
  rc: string;
  articleNumber: string;
  address: string;
  phoneNumber: string;
  website?: string;
}

// Customer interface matching backend DTO
export interface CustomerInfo {
  uuid: string;
  name: string;
  fiscalId: string;
  email?: string;
  phone?: string;
  address?: string;
  rc?: string;
  articleNumber?: string;
  customerType: 'retail' | 'wholesale' | 'mid-wholesale' | 'institutional';
}

// Get company information for the current user
export async function getCompanyByUser(userUuid: string): Promise<CompanyInfo[]> {
  const res = await axios.get(`${API_BASE}/companies/by-user/${userUuid}`, {
    headers: getAxiosAuthHeaders(),
  });
  return res.data;
}

// Get customer information by UUID
export async function getCustomerByUuid(customerUuid: string): Promise<CustomerInfo> {
  const res = await axios.get(`${API_BASE}/customers/${customerUuid}`, {
    headers: getAxiosAuthHeaders(),
  });
  return res.data;
} 