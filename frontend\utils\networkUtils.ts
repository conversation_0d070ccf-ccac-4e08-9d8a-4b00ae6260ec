// Network utility functions for handling connection issues

export interface NetworkInfo {
  online: boolean;
  connectionType?: string;
  effectiveType?: string;
  downlink?: number;
  rtt?: number;
}

export function getNetworkInfo(): NetworkInfo {
  const info: NetworkInfo = {
    online: navigator.onLine,
  };

  // Check for Network Information API
  if ('connection' in navigator) {
    const conn = (navigator as any).connection;
    if (conn) {
      info.connectionType = conn.type;
      info.effectiveType = conn.effectiveType;
      info.downlink = conn.downlink;
      info.rtt = conn.rtt;
    }
  }

  return info;
}

export function isConnectionSlow(): boolean {
  const info = getNetworkInfo();
  
  // Check if connection is slow based on effective type
  if (info.effectiveType) {
    return ['slow-2g', '2g', '3g'].includes(info.effectiveType);
  }
  
  // Check if downlink is very slow (less than 1 Mbps)
  if (info.downlink && info.downlink < 1) {
    return true;
  }
  
  // Check if RTT is very high (more than 200ms)
  if (info.rtt && info.rtt > 200) {
    return true;
  }
  
  return false;
}

export function isConnectionUnstable(): boolean {
  return !navigator.onLine || isConnectionSlow();
}

export function getConnectionAdvice(): string | null {
  if (!navigator.onLine) {
    return "No internet connection detected. Please check your network settings.";
  }
  
  if (isConnectionSlow()) {
    return "Your connection is slow. Authentication may take longer than usual.";
  }
  
  return null;
}

// Exponential backoff retry function
export async function retryWithBackoff<T>(
  fn: () => Promise<T>,
  maxRetries: number = 3,
  baseDelay: number = 1000
): Promise<T> {
  let lastError: Error;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error as Error;
      
      if (attempt === maxRetries) {
        throw lastError;
      }
      
      // Exponential backoff: 1s, 2s, 4s, etc.
      const delay = baseDelay * Math.pow(2, attempt - 1);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
  
  throw lastError!;
}

// Test connection speed by making a simple request
export async function testConnectionSpeed(): Promise<{
  responseTime: number;
  status: 'good' | 'slow' | 'unstable';
}> {
  const startTime = Date.now();
  
  try {
    const response = await fetch('https://www.google.com/favicon.ico', {
      method: 'HEAD',
      mode: 'no-cors',
      cache: 'no-cache'
    });
    
    const endTime = Date.now();
    const responseTime = endTime - startTime;
    
    if (responseTime < 1000) {
      return { responseTime, status: 'good' };
    } else if (responseTime < 3000) {
      return { responseTime, status: 'slow' };
    } else {
      return { responseTime, status: 'unstable' };
    }
  } catch (error) {
    return { responseTime: Infinity, status: 'unstable' };
  }
}

// Listen for online/offline events
export function addNetworkListeners(
  onOnline: () => void,
  onOffline: () => void
): () => void {
  window.addEventListener('online', onOnline);
  window.addEventListener('offline', onOffline);
  
  // Return cleanup function
  return () => {
    window.removeEventListener('online', onOnline);
    window.removeEventListener('offline', onOffline);
  };
} 