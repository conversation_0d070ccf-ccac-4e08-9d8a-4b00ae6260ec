import { <PERSON><PERSON>ty, PrimaryColumn, Column, CreateDateColumn, UpdateDateColumn, Index } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Uuid7 } from '../utils/uuid7';

@Entity('products')
export class Product {
  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the product (primary key)",
  })
  @PrimaryColumn('uuid')
  id: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the warehouse the product belongs to",
  })
  @Column('uuid')
  @Index()
  warehouseUuid: string;

  @ApiProperty({ example: "Product Name", description: "Product name" })
  @Column()
  name: string;

  @ApiProperty({ 
    example: "Product description", 
    description: "Product description",
    required: false 
  })
  @Column({ nullable: true })
  description?: string;

  @ApiProperty({ 
    example: "SKU123", 
    description: "Stock Keeping Unit",
    required: false 
  })
  @Column({ nullable: true })
  sku?: string;

  @ApiProperty({ 
    example: "1234567890123", 
    description: "Product barcode",
    required: false 
  })
  @Column({ nullable: true })
  barcode?: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the product category",
    required: false,
  })
  @Column('uuid', { nullable: true })
  @Index()
  productCategoryUuid?: string;

  @ApiProperty({ 
    example: 25.99, 
    description: "Price for retail customers",
    required: false 
  })
  @Column('decimal', { precision: 10, scale: 2, nullable: true })
  retailPrice?: number;

  @ApiProperty({ 
    example: 22.99, 
    description: "Price for wholesale customers",
    required: false 
  })
  @Column('decimal', { precision: 10, scale: 2, nullable: true })
  wholesalePrice?: number;

  @ApiProperty({
    example: 20.99,
    description: "Price for mid-wholesale customers",
    required: false,
  })
  @Column('decimal', { precision: 10, scale: 2, nullable: true })
  midWholesalePrice?: number;

  @ApiProperty({
    example: 18.99,
    description: "Price for institutional customers",
    required: false,
  })
  @Column('decimal', { precision: 10, scale: 2, nullable: true })
  institutionalPrice?: number;

  @ApiProperty({ 
    example: 15.5, 
    description: "Cost price of the product",
    required: false 
  })
  @Column('decimal', { precision: 10, scale: 2, nullable: true })
  cost?: number;

  @ApiProperty({ example: false, description: "Soft delete flag" })
  @Column({ default: false })
  isDeleted: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Helper method to generate UUID
  static generateId(): string {
    return new Uuid7().toString();
  }

  // Helper method to generate UUID
  static fromBinary(binary: any): string {
    if (!binary) return null;
    try {
      return new Uuid7(binary).toString();
    } catch {
      return null;
    }
  }
} 