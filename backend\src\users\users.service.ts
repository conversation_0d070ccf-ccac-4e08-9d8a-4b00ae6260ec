import {
  Injectable,
  NotFoundException,
  BadRequestException,
  Inject,
  forwardRef,
} from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository, DataSource, In } from "typeorm";
import { User } from "./user.entity";
import { Role } from "./role.entity";
import { UserDto, toUserDto, toUserDtoArray } from "./dto/user.dto";
import { Uuid7 } from "../utils/uuid7";
import { WarehousesService } from "../warehouses/warehouses.service";
import { InventoryService } from "../inventory/inventory.service.typeorm";
import { VansService } from "../vans/vans.service.typeorm";
import { Warehouse } from "../warehouses/warehouse.entity";
import { Storage } from "../inventory/storage.entity";
import { StorageType } from "../inventory/storage_type.enum";

@Injectable()
export class UsersService {
  constructor(
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(Role)
    private roleRepository: Repository<Role>,
    private dataSource: DataSource,
    private warehousesService: WarehousesService,
    @Inject(forwardRef(() => InventoryService))
    private inventoryService: InventoryService,
    private vansService: VansService,
    @Inject(forwardRef(() => require("./user_roles.service").UserRolesService))
    private userRolesService: any,
  ) {}

  async assignVanToUser(
    userUuid: string,
    vanUuid: string,
  ): Promise<{ success: boolean }> {
    // Find user
    const user = await this.userRepository.findOne({
      where: { id: userUuid }
    });
    if (!user) throw new NotFoundException("User not found");

    // Check if user is a mobile sale agent
    if (user.roleUuid) {
      const role = await this.userRolesService.getRoleByUuid(user.roleUuid);
      if (!role || role.name !== "mobile sale agent") {
        throw new BadRequestException(
          "Only mobile sale agents can be assigned vans",
        );
      }
    } else {
      throw new BadRequestException(
        "User must have a role assigned to receive a van",
      );
    }

    // Super users should not get van assignments
    if (user.userType === "super") {
      throw new BadRequestException("Super users cannot be assigned vans");
    }

    // Find van
    const van = await this.vansService.getVanByUuid(vanUuid);
    if (!van) throw new NotFoundException("Van not found");

    // Check if van is already assigned to another user
    const assignedUser = await this.userRepository.findOne({
      where: { vanUuid: vanUuid }
    });
    if (assignedUser && assignedUser.id !== user.id) {
      throw new BadRequestException("Van is already assigned to another user");
    }

    // Assign van to user
    user.vanUuid = vanUuid;
    await this.userRepository.save(user);
    return { success: true };
  }

  async changePassword(
    uuid: string,
    _oldPassword: string,
    newPassword: string,
  ): Promise<{ success: boolean }> {
    const bcrypt = require("bcrypt");
    const user = await this.userRepository.findOne({
      where: { id: uuid }
    });
    if (!user) throw new NotFoundException("User not found");
    user.password = await bcrypt.hash(newPassword, 10);
    await this.userRepository.save(user);
    return { success: true };
  }

  async findAll(): Promise<UserDto[]> {
    const users = await this.userRepository.find({
      where: { isDeleted: false }
    });
    return toUserDtoArray(users);
  }

  async findByWarehouseUuid(warehouseUuid: string): Promise<UserDto[]> {
    const users = await this.userRepository.find({
      where: { warehouseUuid, isDeleted: false }
    });
    return toUserDtoArray(users);
  }

  async changeName(uuid: string, name: string): Promise<{ success: boolean }> {
    const user = await this.userRepository.findOne({
      where: { id: uuid }
    });
    if (!user) throw new NotFoundException("User not found");
    user.name = name;
    await this.userRepository.save(user);
    return { success: true };
  }

  async updateProfile(uuid: string, body: any): Promise<{ success: boolean }> {
    const user = await this.userRepository.findOne({
      where: { id: uuid }
    });
    if (!user) throw new NotFoundException("User not found");

    // Update allowed fields
    if (body.name !== undefined) user.name = body.name;
    if (body.email !== undefined) user.email = body.email;
    if (body.roleUuid !== undefined) user.roleUuid = body.roleUuid;
    if (body.userType !== undefined) user.userType = body.userType;

    await this.userRepository.save(user);
    return { success: true };
  }

  async create(
    warehouseUuid: string | undefined,
    email: string,
    name: string,
    password?: string,
    userType?: "super" | "user",
    roleUuid?: string,
  ): Promise<UserDto> {
    const bcrypt = require("bcrypt");
    let hashedPassword: string | undefined = password
      ? await bcrypt.hash(password, 10)
      : undefined;

    let warehouseUuidToUse = warehouseUuid;
    let createdDefaultWarehouse = false;

    console.log("[UsersService] Starting user creation process");
    console.log("[UsersService] Input warehouseUuid:", warehouseUuid);
    console.log("[UsersService] Input userType:", userType);

    // Start transaction for user creation
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // If userType is 'super' and warehouseUuid not provided, we'll create the user first, then create the warehouse
      if (userType === "super" && !warehouseUuidToUse) {
        warehouseUuidToUse = new Uuid7().toString();
        createdDefaultWarehouse = true;
        console.log("[UsersService] Generated temporary warehouse UUID for super user:", warehouseUuidToUse);
      }

      if (!warehouseUuidToUse) {
        throw new BadRequestException(
          "warehouseUuid is required to create a user. This may indicate a logic error in warehouse creation or role seeding.",
        );
      }

      console.log("[UsersService] Using warehouse UUID:", warehouseUuidToUse);
      console.log("[UsersService] Will create default warehouse:", createdDefaultWarehouse);

      // Check if this is the first user for the warehouse
      const existingUsers = await this.userRepository.find({
        where: { warehouseUuid: warehouseUuidToUse, isDeleted: false }
      });

      let finalUserType: "super" | "user";
      if (existingUsers.length === 0) {
        // First user for this warehouse can be super
        finalUserType = userType === "super" ? "super" : "super";
      } else {
        // Only allow creation of normal users
        if (userType === "super") {
          throw new BadRequestException(
            "Cannot create additional super users for this warehouse.",
          );
        }
        finalUserType = "user";
      }

      console.log("[UsersService] Final user type:", finalUserType);

      // Determine roleUuid: use provided roleUuid if present, otherwise default to admin for super user
      let finalRoleUuid: string | undefined = undefined;
      if (roleUuid) {
        // Validate that the role exists
        const roleExists = await this.userRolesService.getRoleByUuid(roleUuid);
        if (!roleExists) {
          throw new BadRequestException(
            `Role with UUID ${roleUuid} does not exist`,
          );
        }

        // Validate that the role belongs to the same warehouse
        if (roleExists.warehouseUuid !== warehouseUuidToUse) {
          throw new BadRequestException(
            `Role with UUID ${roleUuid} does not belong to the specified warehouse`,
          );
        }
        finalRoleUuid = roleUuid;
      } else if (finalUserType === "super" && !createdDefaultWarehouse) {
        // Find the admin role for this warehouse
        try {
          const adminRole = await this.userRolesService.findRoleByNameAndWarehouseUuid(
            "admin",
            warehouseUuidToUse,
          );
          if (adminRole) {
            finalRoleUuid = adminRole.id;
          }
        } catch (error) {
          // If admin role doesn't exist, we'll create it later
          console.log("Admin role not found, will be created later");
        }
      }

      // Create the user
      const user = new User();
      user.id = User.generateId();
      user.warehouseUuid = warehouseUuidToUse;
      user.email = email;
      user.name = name;
      user.password = hashedPassword;
      user.userType = finalUserType;
      user.roleUuid = finalRoleUuid;
      user.isDeleted = false;

      console.log("[UsersService] Creating user with warehouse UUID:", user.warehouseUuid);
      const savedUser = await this.userRepository.save(user);
      console.log("[UsersService] User created with ID:", savedUser.id);

      // If this is a super user and we created a default warehouse, create the warehouse and roles
      if (createdDefaultWarehouse && finalUserType === "super") {
        console.log("[UsersService] Creating default warehouse for super user within transaction");
        console.log("[UsersService] User's current warehouse UUID:", savedUser.warehouseUuid);
        
        // Create warehouse within the same transaction
        const warehouse = new Warehouse();
        warehouse.id = Warehouse.generateId();
        warehouse.name = `${name}'s Warehouse`;
        warehouse.userUuid = savedUser.id;
        warehouse.isDeleted = false;
        
        console.log("[UsersService] Creating warehouse with UUID:", warehouse.id);
        const savedWarehouse = await queryRunner.manager.save(warehouse);
        console.log("[UsersService] Warehouse saved with UUID:", savedWarehouse.id);

        // Create main storage within the same transaction
        const mainStorage = new Storage();
        mainStorage.id = Storage.generateId();
        mainStorage.name = "Main Storage";
        mainStorage.warehouseUuid = warehouse.id;
        mainStorage.userUuid = savedUser.id;
        mainStorage.type = StorageType.WAREHOUSE;
        mainStorage.isDeleted = false;
        
        const savedMainStorage = await queryRunner.manager.save(mainStorage);
        console.log("[UsersService] Created main storage:", savedMainStorage.id);

        // Update warehouse with mainStorageUuid within the same transaction
        savedWarehouse.mainStorageUuid = savedMainStorage.id;
        const updatedWarehouse = await queryRunner.manager.save(savedWarehouse);
        console.log("[UsersService] Updated warehouse with main storage UUID:", savedMainStorage.id);

        console.log("[UsersService] Warehouse created with UUID:", updatedWarehouse.id);
        console.log("[UsersService] User's warehouse UUID before update:", savedUser.warehouseUuid);
        
        // CRITICAL FIX: Update user's warehouseUuid to match the actual warehouse UUID
        savedUser.warehouseUuid = updatedWarehouse.id;
        await queryRunner.manager.save(savedUser);
        
        console.log("[UsersService] Updated user's warehouse UUID to:", savedUser.warehouseUuid);

        // Create default roles with the correct warehouse UUID within the same transaction
        const { DEFAULT_ROLES } = require('./default_roles');
        const roles = DEFAULT_ROLES.map((role) => {
          const newRole = new Role();
          newRole.id = Role.generateId();
          newRole.name = role.name;
          newRole.permissions = role.permissions;
          newRole.warehouseUuid = updatedWarehouse.id; // Use actual warehouse UUID
          newRole.isDeleted = false;
          return newRole;
        });
        
        console.log("[UsersService] Creating roles with warehouse UUID:", updatedWarehouse.id);
        const savedRoles = await queryRunner.manager.save(roles);
        console.log("[UsersService] Roles created successfully:", savedRoles.map(r => ({ name: r.name, uuid: r.id })));

        // Update user with admin role within the same transaction
        const adminRole = savedRoles.find(r => r.name === "admin");
        if (adminRole) {
          savedUser.roleUuid = adminRole.id;
          await queryRunner.manager.save(savedUser);
          console.log("[UsersService] Assigned admin role to user:", adminRole.id);
        } else {
          console.error("[UsersService] ERROR: Admin role was not created");
          throw new Error("Admin role was not created");
        }
      }

      await queryRunner.commitTransaction();
      console.log("[UsersService] User creation transaction committed successfully");
      console.log("[UsersService] Final user warehouse UUID:", savedUser.warehouseUuid);
      
      return toUserDto(savedUser);

    } catch (error) {
      await queryRunner.rollbackTransaction();
      console.error("[UsersService] Error during user creation, transaction rolled back:", error);
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async findAllRaw(): Promise<UserDto[]> {
    const users = await this.userRepository.find();
    return toUserDtoArray(users);
  }

  async findOne(uuid: string): Promise<UserDto> {
    const user = await this.userRepository.findOne({
      where: { id: uuid, isDeleted: false }
    });
    if (!user) throw new NotFoundException("User not found");
    return toUserDto(user);
  }

  async findByEmail(email: string): Promise<UserDto | null> {
    const user = await this.userRepository.findOne({
      where: { email, isDeleted: false }
    });
    return user ? toUserDto(user) : null;
  }

  async findByUuid(uuid: string): Promise<UserDto | null> {
    const user = await this.userRepository.findOne({
      where: { id: uuid, isDeleted: false }
    });
    return user ? toUserDto(user) : null;
  }

  async findByName(name: string): Promise<UserDto | null> {
    const user = await this.userRepository.findOne({
      where: { name, isDeleted: false }
    });
    return user ? toUserDto(user) : null;
  }

  async findMany(uuids: string[]): Promise<UserDto[]> {
    const users = await this.userRepository.find({
      where: { id: In(uuids), isDeleted: false }
    });
    return toUserDtoArray(users);
  }

  async deleteAllUsers(): Promise<{ success: boolean; deletedCount: number }> {
    // Delete ALL users including soft-deleted ones
    // Using query builder to bypass TypeORM's empty criteria restriction
    const result = await this.userRepository
      .createQueryBuilder()
      .delete()
      .from(User)
      .execute();
    return { success: true, deletedCount: result.affected || 0 };
  }

  async changeWarhouse(
    uuid: string,
    warehouseUuid: string,
  ): Promise<{ success: boolean }> {
    const user = await this.userRepository.findOne({
      where: { id: uuid }
    });
    if (!user) throw new NotFoundException("User not found");

    // Validate warehouse exists
    const warehouse = await this.warehousesService.findOne(warehouseUuid);
    if (!warehouse) throw new NotFoundException("Warehouse not found");

    // Update user's warehouse
    user.warehouseUuid = warehouseUuid;
    
    // Clear role assignment as it belongs to the old warehouse
    user.roleUuid = undefined;
    
    await this.userRepository.save(user);
    return { success: true };
  }

  async deleteUser(uuid: string) {
    const user = await this.userRepository.findOne({
      where: { id: uuid }
    });
    if (!user) throw new NotFoundException("User not found");

    // Soft delete
    user.isDeleted = true;
    await this.userRepository.save(user);
    return { success: true };
  }

  async assignAdminRoleToUser(userUuid: string): Promise<{ success: boolean }> {
    const user = await this.userRepository.findOne({
      where: { id: userUuid }
    });
    if (!user) throw new NotFoundException("User not found");

    // Find admin role for the user's warehouse
    const adminRole = await this.userRolesService.findRoleByNameAndWarehouseUuid(
      "admin",
      user.warehouseUuid,
    );
    if (!adminRole) throw new NotFoundException("Admin role not found");

    user.roleUuid = adminRole.id;
    await this.userRepository.save(user);
    return { success: true };
  }

  async findUsersWithoutRoles(): Promise<UserDto[]> {
    const users = await this.userRepository.find({
      where: { 
        roleUuid: null,
        isDeleted: false 
      }
    });
    return toUserDtoArray(users);
  }

  // Debug method to check warehouse UUID consistency
  async debugWarehouseUuidConsistency(): Promise<{
    users: Array<{ id: string; name: string; warehouseUuid: string; userType: string }>;
    warehouses: Array<{ id: string; name: string; userUuid: string }>;
    mismatches: Array<{ userId: string; userName: string; userWarehouseUuid: string; actualWarehouseUuid: string }>;
  }> {
    console.log("[UsersService] Starting warehouse UUID consistency check");
    
    // Get all users
    const users = await this.userRepository.find({
      where: { isDeleted: false },
      select: ['id', 'name', 'warehouseUuid', 'userType']
    });
    
    console.log("[UsersService] Found users:", users.length);
    
    // Get all warehouses
    const warehouses = await this.warehousesService.findAllRaw();
    
    console.log("[UsersService] Found warehouses:", warehouses.length);
    
    // Check for mismatches
    const mismatches = [];
    
    for (const user of users) {
      if (user.warehouseUuid) {
        const warehouse = warehouses.find(w => w.uuid === user.warehouseUuid);
        if (!warehouse) {
          console.log(`[UsersService] WARNING: User ${user.name} (${user.id}) has warehouseUuid ${user.warehouseUuid} but no warehouse exists with that UUID`);
          mismatches.push({
            userId: user.id,
            userName: user.name,
            userWarehouseUuid: user.warehouseUuid,
            actualWarehouseUuid: 'NOT_FOUND'
          });
        } else {
          console.log(`[UsersService] User ${user.name} warehouse UUID matches: ${user.warehouseUuid}`);
        }
      } else {
        console.log(`[UsersService] User ${user.name} has no warehouseUuid assigned`);
      }
    }
    
    // Check for warehouses without users
    for (const warehouse of warehouses) {
      const usersForWarehouse = users.filter(u => u.warehouseUuid === warehouse.uuid);
      if (usersForWarehouse.length === 0) {
        console.log(`[UsersService] WARNING: Warehouse ${warehouse.name} (${warehouse.uuid}) has no users assigned`);
      } else {
        console.log(`[UsersService] Warehouse ${warehouse.name} has ${usersForWarehouse.length} users`);
      }
    }
    
    return {
      users: users.map(u => ({
        id: u.id,
        name: u.name,
        warehouseUuid: u.warehouseUuid,
        userType: u.userType
      })),
      warehouses: warehouses.map(w => ({
        id: w.uuid,
        name: w.name,
        userUuid: w.userUuidString
      })),
      mismatches
    };
  }
} 