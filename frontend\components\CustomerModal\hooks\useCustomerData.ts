import { useState, useEffect, useCallback, useRef } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { filterCustomers, type CustomerFilter } from '../api/customerApi';
import type { Customer } from '../types';

interface UseCustomerDataReturn {
  customers: Customer[];
  isLoading: boolean;
  isSearching: boolean;
  error: string | null;
  currentPage: number;
  totalPages: number;
  totalCustomers: number;
  loadCustomers: (page?: number, search?: string) => Promise<void>;
  clearError: () => void;
}

export function useCustomerData(): UseCustomerDataReturn {
  const { user } = useAuth();
  const warehouseUuid = user?.warehouseUuid;
  
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isSearching, setIsSearching] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalCustomers, setTotalCustomers] = useState(0);
  
  // Cache for storing customer data
  const customerCache = useRef<Map<string, { data: Customer[]; timestamp: number; total: number; totalPages: number }>>(new Map());
  const requestTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isRequestInProgressRef = useRef(false);
  const currentRequestRef = useRef<string | null>(null);
  
  // Cache expiration time (5 minutes)
  const CACHE_EXPIRY = 5 * 60 * 1000;
  
  // Generate cache key for the current request
  const getCacheKey = useCallback((page: number, search: string) => {
    return `${warehouseUuid}-${page}-${search}`;
  }, [warehouseUuid]);
  
  // Check if cached data is still valid
  const isCacheValid = useCallback((cacheKey: string) => {
    const cached = customerCache.current.get(cacheKey);
    if (!cached) return false;
    
    const now = Date.now();
    return (now - cached.timestamp) < CACHE_EXPIRY;
  }, []);
  
  // Load customers with caching and request deduplication
  const loadCustomers = useCallback(async (page: number = 1, search: string = '') => {
    if (!warehouseUuid) {
      console.error('[useCustomerData] No warehouseUuid available');
      setError('Warehouse information not available');
      return;
    }
    
    const cacheKey = getCacheKey(page, search);
    
    // Check if we have valid cached data
    if (isCacheValid(cacheKey)) {
      const cached = customerCache.current.get(cacheKey)!;
      console.log('[useCustomerData] Using cached data for:', cacheKey);
      setCustomers(cached.data);
      setCurrentPage(page);
      setTotalPages(cached.totalPages);
      setTotalCustomers(cached.total);
      setError(null);
      return;
    }
    
    // Prevent concurrent requests with the same parameters
    if (isRequestInProgressRef.current && currentRequestRef.current === cacheKey) {
      console.log('[useCustomerData] Request already in progress for:', cacheKey);
      return;
    }
    
    // Cancel any pending request
    if (requestTimeoutRef.current) {
      clearTimeout(requestTimeoutRef.current);
    }
    
    isRequestInProgressRef.current = true;
    currentRequestRef.current = cacheKey;
    setIsLoading(page === 1);
    setIsSearching(page > 1 || !!search);
    setError(null);
    
    console.log('[useCustomerData] Loading customers:', { page, search, warehouseUuid, cacheKey });
    
    try {
      const filter: CustomerFilter = { 
        warehouseUuid,
      };
      
      // Add search filter if provided
      if (search && search.trim()) {
        filter.name = search.trim();
        console.log('[useCustomerData] Adding name filter:', filter.name);
      }
      
      console.log('[useCustomerData] API call with filter:', filter);
      const response = await filterCustomers(filter);
      console.log('[useCustomerData] API response:', response);
      
      // Cache the response
      customerCache.current.set(cacheKey, {
        data: response.data || [],
        timestamp: Date.now(),
        total: response.total || 0,
        totalPages: response.totalPages || Math.ceil((response.total || 0) / 20)
      });
      
      setCustomers(response.data || []);
      setCurrentPage(response.page || page);
      setTotalPages(response.totalPages || Math.ceil((response.total || 0) / 20));
      setTotalCustomers(response.total || 0);
      
      console.log('[useCustomerData] State updated:', {
        customersCount: response.data?.length || 0,
        currentPage: response.page || page,
        totalPages: response.totalPages || Math.ceil((response.total || 0) / 20),
        totalCustomers: response.total || 0
      });
      
    } catch (err: any) {
      console.error('[useCustomerData] Error loading customers:', err);
      setError(err.message || 'Failed to load customers');
    } finally {
      isRequestInProgressRef.current = false;
      currentRequestRef.current = null;
      setIsLoading(false);
      setIsSearching(false);
    }
  }, [warehouseUuid, getCacheKey, isCacheValid]);
  
  // Clear error function
  const clearError = useCallback(() => {
    setError(null);
  }, []);
  
  // Cleanup effect for component unmount
  useEffect(() => {
    return () => {
      // Clear any pending timeouts
      if (requestTimeoutRef.current) {
        clearTimeout(requestTimeoutRef.current);
      }
      // Reset request state
      isRequestInProgressRef.current = false;
      currentRequestRef.current = null;
    };
  }, []);
  
  return {
    customers,
    isLoading,
    isSearching,
    error,
    currentPage,
    totalPages,
    totalCustomers,
    loadCustomers,
    clearError,
  };
} 