"use client";

// Customer Locations Map Page
// Convention: Always inject warehouseUuid and userUuid from AuthContext in parent/page component
// Uses existing customer API from customersApi.ts
// Implements comprehensive filtering for customers on map
// Follows UI guidelines from UI_GUIDELINES.md

import React, { useState, useEffect, useMemo } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { useCustomers } from "../useCustomers";
import { Customer, FilterCustomerDto } from "../customersApi";
import ProtectedRoute from "@/components/ProtectedRoute";
import { MapPin, Filter, Search, X, Maximize2, Minimize2 } from "lucide-react";
import dynamic from "next/dynamic";

// Dynamically import the map component to avoid SSR issues
const CustomerMap = dynamic(() => import("./CustomerMap"), {
  ssr: false,
  loading: () => (
    <div className="flex items-center justify-center h-96 bg-gray-100 rounded-lg">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
        <p className="text-gray-600">Loading map...</p>
      </div>
    </div>
  )
});

export default function CustomerLocationsPage() {
  const { user } = useAuth();
  const warehouseUuid = user?.warehouseUuid;

  // State for filtering
  const [searchTerm, setSearchTerm] = useState("");
  const [customerTypeFilters, setCustomerTypeFilters] = useState<string[]>([]);
  const [showFilters, setShowFilters] = useState(true);
  const [isMapExpanded, setIsMapExpanded] = useState(false);

  // Build filter object
  const filter: FilterCustomerDto = useMemo(() => {
    const filterObj: FilterCustomerDto = {};
    
    if (warehouseUuid) {
      filterObj.warehouseUuid = warehouseUuid;
    }
    
    if (searchTerm.trim()) {
      filterObj.name = searchTerm.trim();
    }
    
    // Note: Backend only supports single customerType filter, so we'll filter on frontend
    // if multiple types are selected
    if (customerTypeFilters.length === 1) {
      filterObj.customerType = customerTypeFilters[0] as any;
    }
    
    return filterObj;
  }, [warehouseUuid, searchTerm, customerTypeFilters]);

  // Fetch customers with filtering (get all customers, no pagination for map)
  const { 
    data: customersResponse, 
    isLoading, 
    error: fetchError 
  } = useCustomers(
    { page: 1, limit: 1000 }, // Large limit to get all customers for map
    filter
  );

  // Filter customers that have location data and apply frontend filters
  const customersWithLocation = useMemo(() => {
    if (!customersResponse?.data) return [];
    
    let filtered = customersResponse.data.filter(customer => 
      customer.latitude != null && customer.longitude != null
    );
    
    // Apply frontend customer type filter when multiple types are selected
    if (customerTypeFilters.length > 1) {
      filtered = filtered.filter(customer => 
        customerTypeFilters.includes(customer.customerType)
      );
    }
    
    return filtered;
  }, [customersResponse, customerTypeFilters]);

  // Clear all filters
  const clearAllFilters = () => {
    setSearchTerm("");
    setCustomerTypeFilters([]);
  };

  // Check if any filters are active
  const hasActiveFilters = searchTerm || customerTypeFilters.length > 0;

  // Handle customer type checkbox changes
  const handleCustomerTypeChange = (type: string, checked: boolean) => {
    if (checked) {
      setCustomerTypeFilters(prev => [...prev, type]);
    } else {
      setCustomerTypeFilters(prev => prev.filter(t => t !== type));
    }
  };

  return (
    <ProtectedRoute>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <MapPin className="h-8 w-8 text-blue-600" />
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Customer Locations</h1>
              <p className="text-gray-600">View all customers on the world map</p>
            </div>
          </div>
          
          <div className="flex items-center space-x-3">
            <button
              onClick={() => setShowFilters(!showFilters)}
              className={`flex items-center space-x-2 px-4 py-2 rounded-lg border transition-colors ${
                showFilters 
                  ? 'bg-blue-600 text-white border-blue-600' 
                  : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
              }`}
            >
              <Filter className="h-4 w-4" />
              <span>Filters</span>
              {hasActiveFilters && (
                <span className="bg-red-500 text-white text-xs rounded-full px-2 py-1">
                  {(searchTerm ? 1 : 0) + customerTypeFilters.length}
                </span>
              )}
            </button>
          </div>
        </div>

        {/* Filters Panel */}
        {showFilters && (
          <div className="bg-white rounded-lg shadow-md border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Filter Customers</h3>
              <div className="flex items-center space-x-2">
                {hasActiveFilters && (
                  <button
                    onClick={clearAllFilters}
                    className="text-sm text-red-600 hover:text-red-700 flex items-center space-x-1"
                  >
                    <X className="h-4 w-4" />
                    <span>Clear All</span>
                  </button>
                )}
                <button
                  onClick={() => setShowFilters(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X className="h-5 w-5" />
                </button>
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Name Search */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Customer Name
                </label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <input
                    type="text"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    placeholder="Search by name..."
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>

              {/* Customer Type */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Customer Type
                </label>
                <div className="space-y-2">
                  {[
                    { value: 'retail', label: 'Retail', icon: '👤' },
                    { value: 'wholesale', label: 'Wholesale', icon: '🏪' },
                    { value: 'mid-wholesale', label: 'Mid-Wholesale', icon: '🛒' },
                    { value: 'institutional', label: 'Institutional', icon: '🏢' }
                  ].map(({ value, label, icon }) => (
                    <label key={value} className="flex items-center space-x-2 cursor-pointer">
                      <input
                        type="checkbox"
                        checked={customerTypeFilters.includes(value)}
                        onChange={(e) => handleCustomerTypeChange(value, e.target.checked)}
                        className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
                      />
                      <span className="text-sm">{icon}</span>
                      <span className="text-sm text-gray-700">{label}</span>
                    </label>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Stats */}
        {!isMapExpanded && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-white rounded-lg shadow-md border border-gray-200 p-6">
              <div className="flex items-center">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <MapPin className="h-6 w-6 text-blue-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total Customers</p>
                  <p className="text-xl font-bold text-gray-900">
                    {customersResponse?.data?.length || 0}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-md border border-gray-200 p-6">
              <div className="flex items-center">
                <div className="p-2 bg-green-100 rounded-lg">
                  <MapPin className="h-6 w-6 text-green-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">With Locations</p>
                  <p className="text-xl font-bold text-gray-900">
                    {customersWithLocation.length}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-md border border-gray-200 p-6">
              <div className="flex items-center">
                <div className="p-2 bg-orange-100 rounded-lg">
                  <MapPin className="h-6 w-6 text-orange-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Without Locations</p>
                  <p className="text-xl font-bold text-gray-900">
                    {(customersResponse?.data?.length || 0) - customersWithLocation.length}
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Map */}
        <div className={`bg-white rounded-lg shadow-md border border-gray-200 p-6 ${
          isMapExpanded ? 'fixed inset-4 z-50 flex flex-col' : ''
        }`} style={isMapExpanded ? { height: 'calc(100vh - 2rem)' } : {}}>
          <div className="mb-4 flex items-center justify-between">
            <div>
              <h2 className="text-lg font-semibold text-gray-900">Customer Locations Map</h2>
              <p className="text-sm text-gray-600">
                Showing {customersWithLocation.length} customers with location data
              </p>
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setIsMapExpanded(!isMapExpanded)}
                className="flex items-center space-x-2 px-3 py-2 rounded-lg border bg-white text-gray-700 border-gray-300 hover:bg-gray-50 transition-colors"
                title={isMapExpanded ? 'Minimize map' : 'Expand map to full screen'}
              >
                {isMapExpanded ? <Minimize2 className="h-4 w-4" /> : <Maximize2 className="h-4 w-4" />}
                <span>{isMapExpanded ? 'Minimize' : 'Expand'}</span>
              </button>
            </div>
          </div>
          
          {isLoading ? (
            <div className={`flex items-center justify-center bg-gray-100 rounded-lg ${
              isMapExpanded ? 'flex-1' : 'h-96'
            }`}>
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
                <p className="text-gray-600">Loading customers...</p>
              </div>
            </div>
          ) : fetchError ? (
            <div className={`flex items-center justify-center bg-red-50 rounded-lg ${
              isMapExpanded ? 'flex-1' : 'h-96'
            }`}>
              <div className="text-center">
                <p className="text-red-600 font-medium">Error loading customers</p>
                <p className="text-red-500 text-sm mt-1">
                  {fetchError instanceof Error ? fetchError.message : 'Unknown error'}
                </p>
              </div>
            </div>
          ) : (
            <div className={`rounded-lg overflow-hidden ${
              isMapExpanded ? 'flex-1 min-h-0' : 'h-96'
            }`}>
              <CustomerMap customers={customersWithLocation} isExpanded={isMapExpanded} />
            </div>
          )}
        </div>
      </div>
    </ProtectedRoute>
  );
} 