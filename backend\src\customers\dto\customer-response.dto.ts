import { ApiProperty } from "@nestjs/swagger";
import { Customer } from "../customer.entity";

export class CustomerResponseDto {
  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the customer",
  })
  uuid: string;

  @ApiProperty({ example: "<PERSON>", description: "Customer name" })
  name: string;

  @ApiProperty({
    example: "12345678901",
    description: "Fiscal identification number (tax ID)",
  })
  fiscalId?: string;

  @ApiProperty({
    example: "<EMAIL>",
    description: "Customer email",
  })
  email?: string;

  @ApiProperty({ example: "+1234567890", description: "Customer phone" })
  phone?: string;

  @ApiProperty({
    example: "123 Main St, City, Country",
    description: "Customer address",
  })
  address?: string;

  @ApiProperty({
    example: "RC123456",
    description: "Commercial Register number",
  })
  rc?: string;

  @ApiProperty({ example: "ART001", description: "Article number" })
  articleNumber?: string;

  @ApiProperty({
    example: "retail",
    description: "Customer type",
    enum: ["retail", "wholesale", "mid-wholesale", "institutional"],
  })
  customerType: "retail" | "wholesale" | "mid-wholesale" | "institutional";

  @ApiProperty({
    example: 40.7128,
    description: "Latitude coordinate for customer location",
  })
  latitude?: number;

  @ApiProperty({
    example: -74.006,
    description: "Longitude coordinate for customer location",
  })
  longitude?: number;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the warehouse",
  })
  warehouseUuidString?: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the region",
  })
  regionUuidString?: string;

  @ApiProperty({
    example: 0.0,
    description: "Current credit balance for the customer",
  })
  currentCredit: number;

  @ApiProperty({ example: false, description: "Soft delete flag" })
  isDeleted: boolean;

  @ApiProperty({
    example: "2024-01-01T00:00:00.000Z",
    description: "Creation timestamp",
  })
  createdAt: Date;

  @ApiProperty({
    example: "2024-01-01T00:00:00.000Z",
    description: "Last update timestamp",
  })
  updatedAt: Date;
}

export function toCustomerResponseDto(
  customer: Customer,
): CustomerResponseDto {
  return {
    uuid: customer.id,
    name: customer.name,
    fiscalId: customer.fiscalId,
    email: customer.email,
    phone: customer.phone,
    address: customer.address,
    rc: customer.rc,
    articleNumber: customer.articleNumber,
    customerType: customer.customerType,
    latitude: customer.latitude,
    longitude: customer.longitude,
    warehouseUuidString: customer.warehouseUuid,
    regionUuidString: customer.regionUuid,
    currentCredit: customer.currentCredit,
    isDeleted: customer.isDeleted,
    createdAt: customer.createdAt,
    updatedAt: customer.updatedAt,
  };
}
