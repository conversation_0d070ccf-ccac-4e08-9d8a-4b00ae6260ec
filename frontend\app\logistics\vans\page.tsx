"use client";
// Convention: warehouseUuid is injected from AuthContext, never from user input.
// See docs/CODE_GENERATION.md for details.
// All API calls use the abstraction in ./vansApi.ts.
// All errors must be shown as both toast and inline summary.
// Modal is accessible and keyboard-friendly.

import React, { useEffect, useState, useCallback } from "react";
import { toast } from "react-hot-toast";
import ItemsTable from "@/components/itemsTable/ItemsTable";
import TableActionButtons from "@/components/itemsTable/TableActionButtons";
import VanModal from "./VanModal";
import { VanFormValues } from "./VanForm";
import { 
  listVans, 
  createVan, 
  updateVan, 
  softDeleteVan, 
  VanDto, 
  CreateVanDto, 
  UpdateVanDto 
} from "./vansApi";
import { useAuth } from "@/contexts/AuthContext";

type Van = VanDto;

const VansPage: React.FC = () => {
  // State hooks
  const [vansList, setVansList] = useState<Van[]>([]);
  const [loading, setLoading] = useState(false);
  const [editing, setEditing] = useState<Van | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [modalOpen, setModalOpen] = useState(false);

  const { user } = useAuth();
  const userReady = !!(user && user.warehouseUuid);

  // --- Callbacks for performance ---

  // Unified modal close handler
  const handleCloseModal = useCallback(() => {
    setModalOpen(false);
    setEditing(null);
    setError(null);
  }, []);

  // Fetch vans list
  const fetchVans = useCallback(async () => {
    if (!user?.warehouseUuid) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const data = await listVans({ warehouseUuid: user.warehouseUuid });
      setVansList(data);
    } catch (e: any) {
      const errorMessage = e.response?.data?.message || "Failed to fetch vans list";
      setError(errorMessage);
      toast.error(errorMessage);
      console.error("Fetch vans error:", e);
    }
    
    setLoading(false);
  }, [user?.warehouseUuid]);

  // Add or update van (handler for VanForm)
  const handleVanSubmit = useCallback(async (data: VanFormValues) => {
    if (!user?.warehouseUuid) {
      toast.error("User session invalid. Please refresh.");
      return;
    }

    setLoading(true);
    setError(null);

    try {
      if (editing) {
        // Update existing van
        const updateData: UpdateVanDto = {
          name: data.name,
          licensePlate: data.licensePlate || undefined,
          model: data.model || undefined,
          year: data.year || undefined,
        };
        await updateVan(editing.uuid, updateData);
        toast.success("Van updated successfully!");
      } else {
        // Create new van
        const createData: CreateVanDto = {
          name: data.name,
          warehouseUuid: user.warehouseUuid,
          licensePlate: data.licensePlate || undefined,
          model: data.model || undefined,
          year: data.year || undefined,
        };
        await createVan(createData);
        toast.success("Van added successfully!");
      }

      handleCloseModal();
      await fetchVans();
    } catch (e: any) {
      const errorMessage = e.response?.data?.message || `Failed to ${editing ? 'update' : 'create'} van`;
      setError(errorMessage);
      toast.error(errorMessage);
      console.error("Van save error:", e);
    }

    setLoading(false);
  }, [editing, user?.warehouseUuid, fetchVans, handleCloseModal]);

  // Open modal for add
  const handleAdd = useCallback(() => {
    setEditing(null);
    setError(null);
    setModalOpen(true);
  }, []);

  // Start editing
  const handleEdit = useCallback((van: Van) => {
    setEditing(van);
    setError(null);
    setModalOpen(true);
  }, []);

  // Soft delete van
  const handleDelete = useCallback(async (van: Van) => {
    if (!confirm(`Are you sure you want to delete "${van.name}"? This action cannot be undone.`)) {
      return;
    }

    setLoading(true);
    setError(null);

    try {
      await softDeleteVan(van.uuid);
      await fetchVans();
      toast.success("Van deleted successfully.");
    } catch (e: any) {
      const errorMessage = e.response?.data?.message || "Failed to delete van";
      setError(errorMessage);
      toast.error(errorMessage);
      console.error("Delete van error:", e);
    }

    setLoading(false);
  }, [fetchVans]);

  // --- Effects ---

  // F2 keyboard shortcut to open Add modal
  useEffect(() => {
    const handleF2 = (e: KeyboardEvent) => {
      if (e.key === "F2" && !modalOpen && userReady) {
        e.preventDefault();
        handleAdd();
      }
    };

    window.addEventListener("keydown", handleF2);
    return () => window.removeEventListener("keydown", handleF2);
  }, [modalOpen, userReady, handleAdd]);

  // Initial fetch
  useEffect(() => {
    if (user?.warehouseUuid) {
      fetchVans();
    }
  }, [user?.warehouseUuid, fetchVans]);

  // --- Memoized table columns ---

  const columns = React.useMemo(() => [
    {
      key: "name",
      header: "Van Name",
      headerClassName: "py-3 px-4 text-left text-gray-700 font-semibold",
      cellClassName: "py-3 px-4 align-middle text-gray-900 font-semibold",
    },
    {
      key: "licensePlate",
      header: "License Plate",
      headerClassName: "py-3 px-4 text-left text-gray-700 font-semibold",
      cellClassName: "py-3 px-4 align-middle text-gray-700",
      render: (value: string) => value || "—",
    },
    {
      key: "model",
      header: "Model",
      headerClassName: "py-3 px-4 text-left text-gray-700 font-semibold",
      cellClassName: "py-3 px-4 align-middle text-gray-700",
      render: (value: string) => value || "—",
    },
    {
      key: "year",
      header: "Year",
      headerClassName: "py-3 px-4 text-left text-gray-700 font-semibold",
      cellClassName: "py-3 px-4 align-middle text-gray-700",
      render: (value: number) => value ? value.toString() : "—",
    },
    {
      key: "createdAt",
      header: "Created",
      headerClassName: "py-3 px-4 text-left text-gray-700 font-semibold",
      cellClassName: "py-3 px-4 align-middle text-gray-500 text-sm",
      render: (value: string) => new Date(value).toLocaleDateString(),
    },
    {
      key: "actions",
      header: "Actions",
      headerClassName: "py-3 px-4 text-center text-gray-700 font-semibold",
      cellClassName: "py-3 px-4 text-center align-middle",
      render: (_: any, van: Van) => (
        <TableActionButtons
          onEdit={() => handleEdit(van)}
          onDelete={() => handleDelete(van)}
          editDisabled={loading}
          deleteDisabled={loading}
        />
      ),
    },
  ], [handleEdit, handleDelete, loading]);

  // --- Render ---

  return (
    <div className="p-6">
      {/* Page Header */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Vans</h1>
          <p className="text-gray-600 mt-1">Manage your delivery vehicles and fleet</p>
        </div>
        <button
          onClick={handleAdd}
          disabled={loading || !userReady}
          className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
        >
          <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
          </svg>
          Add Van (F2)
        </button>
      </div>

      {/* Error Display */}
      {error && (
        <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
          <div className="flex items-center">
            <svg className="w-5 h-5 text-red-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
            </svg>
            <span className="text-sm text-red-700">{error}</span>
          </div>
        </div>
      )}

      {/* Loading State */}
      {loading && vansList.length === 0 && (
        <div className="flex justify-center items-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-2 text-gray-600">Loading vans...</span>
        </div>
      )}

      {/* User Not Ready State */}
      {!userReady && (
        <div className="text-center py-8">
          <p className="text-gray-500">Please log in to view vans.</p>
        </div>
      )}

      {/* Vans Table */}
      {userReady && (
        <ItemsTable
          columns={columns}
          data={vansList}
          noDataText={
            <div className="text-center py-8">
              <svg className="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M8 7v8a2 2 0 002 2h6M8 7V5a2 2 0 012-2h4.586a1 1 0 01.707.293l4.414 4.414a1 1 0 01.293.707V15a2 2 0 01-2 2v0M8 7H6a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2v-2" />
              </svg>
              <p className="text-gray-500 text-lg">No vans found</p>
              <p className="text-gray-400 text-sm mt-1">Click &quot;Add Van&quot; to create your first van</p>
            </div>
          }
          containerClassName="bg-white rounded-xl shadow-lg border border-gray-200 w-full"
        />
      )}

      {/* Van Modal */}
      <VanModal
        isOpen={modalOpen}
        onClose={handleCloseModal}
        onSubmit={handleVanSubmit}
        van={editing}
        isLoading={loading}
        error={error}
      />
    </div>
  );
};

export default VansPage;
