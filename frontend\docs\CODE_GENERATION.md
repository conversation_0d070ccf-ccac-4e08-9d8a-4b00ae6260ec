## Project Convention: Handling `warhouseUuid` and `userUuid` for Scoped Resources

## Form Error Handling Convention
- All form submissions (e.g., stock adjustments) must surface errors both as toast notifications and as an inline error summary at the top of the form.
- API logic for form submissions must be placed in a dedicated API abstraction file (e.g., `adjustmentsApi.ts`), not directly in the page component.
- Example: Stock adjustments creation now uses `adjustmentsApi.ts` and displays errors using both toast and inline error summary.

For all warhouse- and user-scoped resources (e.g., products, storage, etc):


- **Do NOT include `warhouseUuid` or `userUuid` as inputs in forms or modals.**
- **Always inject `warhouseUuid` and `userUuid` from AuthContext in the parent/page component using `useAuth()`.**
- When calling create/update API endpoints, pass `warhouseUuid` and `userUuid` from context, not from user input.
- This keeps the UX clean, prevents accidental warhouse/user switching, and ensures security.
- **Do not filter out deleted records in the frontend**: Never filter by fields like `isDeleted` on the frontend. The backend must always return only records that are meant to be shown to the user. The frontend should always display the data as provided by the backend, without applying such filters.
- Document this pattern with a code comment at the top of each relevant page/component for maintainability.

**Example:**
```tsx
import { useAuth } from "../../contexts/AuthContext";
...
const { user } = useAuth();
const warhouseUuid = user?.warhouseUuid;
const userUuid = user?.uuid;
...
// When creating/updating:
createProduct({ ...values, warhouseUuid, userUuid });
```

> **This convention is required for all future pages and forms involving warhouse- or user-scoped data.**
>
> Debug logs are present in `app/inventory/stock-levels/page.tsx` to verify the correct usage of `warhouseUuid` and `userUuid` from `AuthContext`.

---

## Frontend Authentication & Session Validation Conventions

- On initial app load, `AuthProvider` restores the user and token from localStorage.
- All protected pages/components must be wrapped in the `ProtectedRoute` component from `components/ProtectedRoute.tsx`.
- `ProtectedRoute` checks authentication and redirects to `/auth` if not logged in.
- Session validity is checked every 30 minutes in the background; if the token is invalid, the user is logged out and redirected to `/auth`.
- Example usage:
  ```tsx
  import ProtectedRoute from '@/components/ProtectedRoute';
  // ...
  <ProtectedRoute>
    {/* protected content */}
  </ProtectedRoute>
  ```
- All authentication/session logic is centralized in `contexts/AuthContext.tsx`.

## Default List Page Theme & UI Conventions

> **Always use the ItemsTable component for all list/table pages unless there is a strong technical reason not to.**

> **Default Table Component:** The `ItemsTable` component must be used for listing items in any page. This ensures UI consistency and reduces duplicate code. Only use a custom table if ItemsTable cannot fulfill specific requirements and this is documented in the code.

> **TopTaskBar Component:** The TopTaskBar component displays the current warhouse name using the authenticated user's `warehouseName` property (from AuthContext). On reload, the name updates automatically if present in the user object; otherwise, a placeholder is shown.

For all list (table) pages, use:

> **IMPORTANT:**
> 
> All documentation, conventions, and best practices for the default ItemsTable component (including table layout, styling, reusable TableActionButtons, and code samples) have been moved to [ITEMS_TABLE.md](./ITEMS_TABLE.md).
>
> **It is MANDATORY to read and follow [ITEMS_TABLE.md](./ITEMS_TABLE.md) when creating or updating any default items list/table page.**
>
> This ensures UI consistency, code reuse, and best practices across all modules.

**Example using ItemsTable:**
```tsx
<ItemsTable
  columns={[
    { key: 'name', header: 'Name' },
    { key: 'description', header: 'Description' },
    {
      key: 'actions',
      header: 'Actions',
      render: (_: any, row: ItemType) => (
        <div className="flex items-center justify-center gap-3">
          <button className="p-2 rounded-full hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-blue-400" title="Edit" aria-label="Edit">{/* pencil icon */}</button>
          <button className="p-2 rounded-full hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-red-400" title="Delete" aria-label="Delete">{/* X icon */}</button>
        </div>
      ),
      headerClassName: 'text-center',
      cellClassName: 'text-center align-middle',
    },
  ]}
  data={items}
  noDataText={<span className="text-gray-400">No items found.</span>}
  containerClassName="bg-white rounded-xl shadow-lg border border-gray-200 w-full"
/>
```
> Use the ItemsTable component for all new list/table pages and follow these styles for maximum consistency.

---

## Crucial Step: Update Navigation

_SideTaskBar location: `frontend/components/SideTaskBar/SideTaskBar.tsx`_

> **Important:** Whenever you add a new page, you MUST update the navigation (SideTaskBar) to include a route to it. Skipping this step will make the page unreachable for users. Always verify that your new page is accessible from the app's navigation!

## Modal UX Conventions (All Resource Modals)

- **Escape key must always close the modal.**
- **When the modal opens, the first field (usually Name) must be automatically focused.**
- **Mandatory fields for product/resource modals:** Name, Price, and Cost (all required and marked with *).
- Document these conventions at the top of each modal and form component.

**Example:**
```tsx
// In your modal component:
useEffect(() => {
  if (!open) return;
  const input = modalRef.current?.querySelector('input, textarea, select');
  if (input) input.focus();
  const handleKeyDown = (e) => { if (e.key === "Escape") onClose(); };
  window.addEventListener("keydown", handleKeyDown);
  return () => window.removeEventListener("keydown", handleKeyDown);
}, [open, onClose]);
```

> **These modal conventions are required for all future resource modals and forms.**

---