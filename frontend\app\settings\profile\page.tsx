"use client";
import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import ErrorToast from '@/components/ErrorToast';
import { updateUserProfile } from '../users/usersApi';
import { getCompaniesByUser, updateCompany } from '../companiesApi';
import { getAccountSettingsByUser, createAccountSettings, updateAccountSettings } from '../accountSettingsApi';
import ProtectedRoute from '@/components/ProtectedRoute';
import { FiUser, FiGlobe, FiClock, FiSave, FiHome, FiEdit2, FiSettings } from 'react-icons/fi';

interface ProfileFormData {
  name: string;
  preferredLanguage: string;
  timezone: string;
  theme: 'light' | 'dark' | 'auto';
}

const ProfilePage = () => {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  // Fetch company information for the user (only for super users)
  const [companies, setCompanies] = useState<any[]>([]);
  const [companiesLoading, setCompaniesLoading] = useState(false);
  const [companiesError, setCompaniesError] = useState<Error | null>(null);

  // Fetch account settings for the user
  const [accountSettings, setAccountSettings] = useState<any[]>([]);
  const [accountSettingsLoading, setAccountSettingsLoading] = useState(false);
  const [accountSettingsError, setAccountSettingsError] = useState<Error | null>(null);

  useEffect(() => {
    const fetchCompanies = async () => {
      if (!user?.uuid || user.userType !== 'super') return;
      
      setCompaniesLoading(true);
      setCompaniesError(null);
      
      try {
        const data = await getCompaniesByUser(user.uuid);
        setCompanies(data);
      } catch (error) {
        setCompaniesError(error as Error);
      } finally {
        setCompaniesLoading(false);
      }
    };

    fetchCompanies();
  }, [user?.uuid, user?.userType]);

  // Fetch account settings for the user
  useEffect(() => {
    const fetchAccountSettings = async () => {
      if (!user?.uuid) return;
      
      setAccountSettingsLoading(true);
      setAccountSettingsError(null);
      
      try {
        const data = await getAccountSettingsByUser(user.uuid);
        setAccountSettings(data);
      } catch (error) {
        setAccountSettingsError(error as Error);
      } finally {
        setAccountSettingsLoading(false);
      }
    };

    fetchAccountSettings();
  }, [user?.uuid]);

  const company = companies?.[0]; // Get the first company (users typically have one company)
  const accountSetting = accountSettings?.[0]; // Get the first account setting (users typically have one)
  
  // Company editing state
  const [isEditingCompany, setIsEditingCompany] = useState(false);
  const [companyFormData, setCompanyFormData] = useState({
    name: '',
    nif: '',
    rc: '',
    articleNumber: '',
    address: '',
    phoneNumber: '',
    website: '',
  });

  // Account settings editing state
  const [isEditingAccountSettings, setIsEditingAccountSettings] = useState(false);
  const [accountSettingsFormData, setAccountSettingsFormData] = useState({
    preferredLanguage: 'en',
    preferredTheme: 'light',
    preferredPaymentMethod: 'cash',
    invoiceFormat: 'standard',
    preferredTaxRate: 20.0,
    preferredUseTax: true,
    userAccountPlan: 'basic',
  });

  // Update company form data when company changes
  useEffect(() => {
    if (company) {
      setCompanyFormData({
        name: company.name || '',
        nif: company.nif || '',
        rc: company.rc || '',
        articleNumber: company.articleNumber || '',
        address: company.address || '',
        phoneNumber: company.phoneNumber || '',
        website: company.website || '',
      });
    }
  }, [company]);

  // Update account settings form data when account settings change
  useEffect(() => {
    if (accountSetting) {
      setAccountSettingsFormData({
        preferredLanguage: accountSetting.preferredLanguage || 'en',
        preferredTheme: accountSetting.preferredTheme || 'light',
        preferredPaymentMethod: accountSetting.preferredPaymentMethod || 'cash',
        invoiceFormat: accountSetting.invoiceFormat || 'standard',
        preferredTaxRate: accountSetting.preferredTaxRate || 20.0,
        preferredUseTax: accountSetting.preferredUseTax !== undefined ? accountSetting.preferredUseTax : true,
        userAccountPlan: accountSetting.userAccountPlan || 'basic',
      });
    }
  }, [accountSetting]);

  // Company update mutation
  const updateCompanyMutation = useMutation({
    mutationFn: async (data: any) => {
      if (!company?.uuid) throw new Error('Company not found');
      return await updateCompany(company.uuid, data);
    },
    onSuccess: () => {
      // Refresh companies data
      const fetchCompanies = async () => {
        if (!user?.uuid || user.userType !== 'super') return;
        
        setCompaniesLoading(true);
        setCompaniesError(null);
        
        try {
          const data = await getCompaniesByUser(user.uuid);
          setCompanies(data);
        } catch (error) {
          setCompaniesError(error as Error);
        } finally {
          setCompaniesLoading(false);
        }
      };

      fetchCompanies();
      setIsEditingCompany(false);
      toast.success('Company information updated successfully!');
    },
    onError: (error) => {
      toast.error(<ErrorToast message={error.message} />);
    },
  });

  const handleCompanySubmit = (e: React.FormEvent) => {
    e.preventDefault();
    updateCompanyMutation.mutate(companyFormData);
  };

  const handleCompanyInputChange = (field: string, value: string) => {
    setCompanyFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  // Account settings update mutation
  const updateAccountSettingsMutation = useMutation({
    mutationFn: async (data: any) => {
      if (!user?.uuid) throw new Error('User not authenticated');
      
      if (accountSetting?.uuid) {
        // Update existing account settings
        return await updateAccountSettings(accountSetting.uuid, data);
      } else {
        // Create new account settings
        return await createAccountSettings({ ...data, userUuid: user.uuid });
      }
    },
    onSuccess: () => {
      // Refresh account settings data
      const fetchAccountSettings = async () => {
        if (!user?.uuid) return;
        
        setAccountSettingsLoading(true);
        setAccountSettingsError(null);
        
        try {
          const data = await getAccountSettingsByUser(user.uuid);
          setAccountSettings(data);
        } catch (error) {
          setAccountSettingsError(error as Error);
        } finally {
          setAccountSettingsLoading(false);
        }
      };

      fetchAccountSettings();
      setIsEditingAccountSettings(false);
      toast.success('Account settings updated successfully!');
    },
    onError: (error) => {
      toast.error(<ErrorToast message={error.message} />);
    },
  });

  const handleAccountSettingsSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    updateAccountSettingsMutation.mutate(accountSettingsFormData);
  };

  const handleAccountSettingsInputChange = (field: string, value: any) => {
    setAccountSettingsFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };
  
  // Debug logging
  console.log('User:', user);
  console.log('Companies:', companies);
  console.log('Company:', company);
  console.log('Account Settings:', accountSettings);
  console.log('Account Setting:', accountSetting);

  const [formData, setFormData] = useState<ProfileFormData>({
    name: user?.name || '',
    preferredLanguage: 'en',
    timezone: 'UTC',
    theme: 'auto',
  });

  // Update form data when user changes
  useEffect(() => {
    if (user) {
      setFormData(prev => ({
        ...prev,
        name: user.name || '',
      }));
    }
  }, [user]);

  const updateProfileMutation = useMutation({
    mutationFn: async (data: { name: string; newPassword?: string }) => {
      if (!user?.uuid) throw new Error('User not authenticated');
      return await updateUserProfile(user.uuid, data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] });
      toast.success('Profile updated successfully!');
    },
    onError: (error) => {
      toast.error(<ErrorToast message={error.message} />);
    },
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    updateProfileMutation.mutate({
      name: formData.name,
    });
  };

  const handleInputChange = (field: keyof ProfileFormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };



  if (!user) {
    return (
      <ProtectedRoute>
        <div className="p-4 sm:p-6 w-full">
          <p>Loading user profile...</p>
        </div>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute>
      <div className="p-4 sm:p-6 w-full max-w-4xl mx-auto">
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-2xl font-bold flex items-center gap-2">
            <FiUser className="text-blue-600" />
            User Profile
          </h1>
        </div>

        <div className="space-y-6 max-w-2xl">
          {/* Personal Information */}
          <div className="bg-white rounded-xl shadow-lg border border-gray-200 p-6">
            <h2 className="text-xl font-semibold mb-4 flex items-center gap-2">
              <FiUser className="text-gray-600" />
              Personal Information
            </h2>
            
            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Full Name *
                </label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                />
              </div>

              <button
                type="submit"
                disabled={updateProfileMutation.isPending}
                className="w-full bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 disabled:opacity-50 flex items-center justify-center gap-2"
              >
                {updateProfileMutation.isPending ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    Saving...
                  </>
                ) : (
                  <>
                    <FiSave />
                    Save Changes
                  </>
                )}
              </button>
            </form>
          </div>

          {/* Preferences */}
          <div className="bg-white rounded-xl shadow-lg border border-gray-200 p-6">
            <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
              <FiGlobe className="text-gray-600" />
              Preferences
            </h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Preferred Language
                </label>
                <select
                  value={formData.preferredLanguage}
                  onChange={(e) => handleInputChange('preferredLanguage', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="en">English</option>
                  <option value="es">Español</option>
                  <option value="fr">Français</option>
                  <option value="de">Deutsch</option>
                  <option value="it">Italiano</option>
                  <option value="pt">Português</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1 flex items-center gap-1">
                  <FiClock className="text-gray-500" />
                  Timezone
                </label>
                <select
                  value={formData.timezone}
                  onChange={(e) => handleInputChange('timezone', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="UTC">UTC</option>
                  <option value="America/New_York">Eastern Time</option>
                  <option value="America/Chicago">Central Time</option>
                  <option value="America/Denver">Mountain Time</option>
                  <option value="America/Los_Angeles">Pacific Time</option>
                  <option value="Europe/London">London</option>
                  <option value="Europe/Paris">Paris</option>
                  <option value="Asia/Tokyo">Tokyo</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Theme
                </label>
                <select
                  value={formData.theme}
                  onChange={(e) => handleInputChange('theme', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="light">Light</option>
                  <option value="dark">Dark</option>
                  <option value="auto">Auto (System)</option>
                </select>
              </div>
            </div>
          </div>

          {/* Account Settings */}
          <div className="bg-white rounded-xl shadow-lg border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold flex items-center gap-2">
                <FiSettings className="text-gray-600" />
                Account Settings
              </h3>
              {accountSetting && !isEditingAccountSettings && (
                <button
                  onClick={() => setIsEditingAccountSettings(true)}
                  className="flex items-center gap-2 text-blue-600 hover:text-blue-800 text-sm"
                >
                  <FiEdit2 className="w-4 h-4" />
                  Edit
                </button>
              )}
            </div>
            
            {accountSettingsLoading && (
              <div className="flex items-center justify-center py-4">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                <span className="ml-2 text-gray-600">Loading account settings...</span>
              </div>
            )}
            
            {accountSettingsError && (
              <div className="text-red-600 text-sm">
                Failed to load account settings. Please try again later.
              </div>
            )}
            
            {accountSetting && !isEditingAccountSettings && (
              <div className="space-y-3 text-sm">
                <div>
                  <span className="text-gray-600">Preferred Language:</span>
                  <p className="text-gray-800 font-medium">{accountSetting.preferredLanguage}</p>
                </div>
                
                <div>
                  <span className="text-gray-600">Theme:</span>
                  <p className="text-gray-800">{accountSetting.preferredTheme}</p>
                </div>
                
                <div>
                  <span className="text-gray-600">Payment Method:</span>
                  <p className="text-gray-800">{accountSetting.preferredPaymentMethod}</p>
                </div>
                
                <div>
                  <span className="text-gray-600">Invoice Format:</span>
                  <p className="text-gray-800">{accountSetting.invoiceFormat}</p>
                </div>
                
                <div>
                  <span className="text-gray-600">Tax Rate:</span>
                  <p className="text-gray-800">{accountSetting.preferredTaxRate}%</p>
                </div>
                
                <div>
                  <span className="text-gray-600">Use Tax by Default:</span>
                  <p className="text-gray-800">{accountSetting.preferredUseTax ? 'Yes' : 'No'}</p>
                </div>
                
                <div>
                  <span className="text-gray-600">Account Plan:</span>
                  <p className="text-gray-800">{accountSetting.userAccountPlan}</p>
                </div>
              </div>
            )}

            {(accountSetting || isEditingAccountSettings) && isEditingAccountSettings && (
              <form onSubmit={handleAccountSettingsSubmit} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Preferred Language *
                  </label>
                  <select
                    value={accountSettingsFormData.preferredLanguage}
                    onChange={(e) => handleAccountSettingsInputChange('preferredLanguage', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  >
                    <option value="en">English</option>
                    <option value="es">Español</option>
                    <option value="fr">Français</option>
                    <option value="de">Deutsch</option>
                    <option value="it">Italiano</option>
                    <option value="pt">Português</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Theme *
                  </label>
                  <select
                    value={accountSettingsFormData.preferredTheme}
                    onChange={(e) => handleAccountSettingsInputChange('preferredTheme', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  >
                    <option value="light">Light</option>
                    <option value="dark">Dark</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Payment Method *
                  </label>
                  <select
                    value={accountSettingsFormData.preferredPaymentMethod}
                    onChange={(e) => handleAccountSettingsInputChange('preferredPaymentMethod', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  >
                    <option value="cash">Cash</option>
                    <option value="card">Card</option>
                    <option value="bank_transfer">Bank Transfer</option>
                    <option value="check">Check</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Invoice Format *
                  </label>
                  <select
                    value={accountSettingsFormData.invoiceFormat}
                    onChange={(e) => handleAccountSettingsInputChange('invoiceFormat', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  >
                    <option value="standard">Standard</option>
                    <option value="detailed">Detailed</option>
                    <option value="minimal">Minimal</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Tax Rate (%) *
                  </label>
                  <input
                    type="number"
                    min="0"
                    max="100"
                    step="0.1"
                    value={accountSettingsFormData.preferredTaxRate}
                    onChange={(e) => handleAccountSettingsInputChange('preferredTaxRate', parseFloat(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  />
                </div>

                <div>
                  <label className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      checked={accountSettingsFormData.preferredUseTax}
                      onChange={(e) => handleAccountSettingsInputChange('preferredUseTax', e.target.checked)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="text-sm font-medium text-gray-700">Use tax by default</span>
                  </label>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Account Plan *
                  </label>
                  <select
                    value={accountSettingsFormData.userAccountPlan}
                    onChange={(e) => handleAccountSettingsInputChange('userAccountPlan', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  >
                    <option value="basic">Basic</option>
                    <option value="premium">Premium</option>
                    <option value="enterprise">Enterprise</option>
                  </select>
                </div>

                <div className="flex gap-3">
                  <button
                    type="submit"
                    disabled={updateAccountSettingsMutation.isPending}
                    className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50 flex items-center justify-center gap-2"
                  >
                    {updateAccountSettingsMutation.isPending ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                        Saving...
                      </>
                    ) : (
                      <>
                        <FiSave />
                        Save Changes
                      </>
                    )}
                  </button>
                  <button
                    type="button"
                    onClick={() => setIsEditingAccountSettings(false)}
                    className="flex-1 bg-gray-500 text-white py-2 px-4 rounded-md hover:bg-gray-600"
                  >
                    Cancel
                  </button>
                </div>
              </form>
            )}
            
            {!accountSettingsLoading && !accountSettingsError && !accountSetting && (
              <div className="text-center py-4">
                <p className="text-gray-500 text-sm mb-3">No account settings found.</p>
                <button
                  onClick={() => setIsEditingAccountSettings(true)}
                  className="bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 flex items-center gap-2 mx-auto"
                >
                  <FiSettings className="w-4 h-4" />
                  Create Account Settings
                </button>
              </div>
            )}
          </div>



          {/* Company Information - Only show for super users */}
          {user.userType === 'super' && (
            <div className="bg-white rounded-xl shadow-lg border border-gray-200 p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold flex items-center gap-2">
                  <FiHome className="text-gray-600" />
                  Company Information
                </h3>
                {company && !isEditingCompany && (
                  <button
                    onClick={() => setIsEditingCompany(true)}
                    className="flex items-center gap-2 text-blue-600 hover:text-blue-800 text-sm"
                  >
                    <FiEdit2 className="w-4 h-4" />
                    Edit
                  </button>
                )}
              </div>
              
              {companiesLoading && (
                <div className="flex items-center justify-center py-4">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                  <span className="ml-2 text-gray-600">Loading company information...</span>
                </div>
              )}
              
              {companiesError && (
                <div className="text-red-600 text-sm">
                  Failed to load company information. Please try again later.
                </div>
              )}
              
              {company && !isEditingCompany && (
                <div className="space-y-3 text-sm">
                  <div>
                    <span className="text-gray-600">Company Name:</span>
                    <p className="text-gray-800 font-medium">{company.name}</p>
                  </div>
                  
                  <div>
                    <span className="text-gray-600">NIF (Tax ID):</span>
                    <p className="text-gray-800">{company.nif}</p>
                  </div>
                  
                  <div>
                    <span className="text-gray-600">RC (Register Number):</span>
                    <p className="text-gray-800">{company.rc}</p>
                  </div>
                  
                  <div>
                    <span className="text-gray-600">Article Number:</span>
                    <p className="text-gray-800">{company.articleNumber}</p>
                  </div>
                  
                  <div>
                    <span className="text-gray-600">Address:</span>
                    <p className="text-gray-800">{company.address}</p>
                  </div>
                  
                  {company.phoneNumber && (
                    <div>
                      <span className="text-gray-600">Phone Number:</span>
                      <p className="text-gray-800">{company.phoneNumber}</p>
                    </div>
                  )}
                  
                  {company.website && (
                    <div>
                      <span className="text-gray-600">Website:</span>
                      <a 
                        href={company.website} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="text-blue-600 hover:text-blue-800 underline"
                      >
                        {company.website}
                      </a>
                    </div>
                  )}
                </div>
              )}

              {company && isEditingCompany && (
                <form onSubmit={handleCompanySubmit} className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Company Name *
                    </label>
                    <input
                      type="text"
                      value={companyFormData.name}
                      onChange={(e) => handleCompanyInputChange('name', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      NIF (Tax ID) *
                    </label>
                    <input
                      type="text"
                      value={companyFormData.nif}
                      onChange={(e) => handleCompanyInputChange('nif', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      RC (Register Number) *
                    </label>
                    <input
                      type="text"
                      value={companyFormData.rc}
                      onChange={(e) => handleCompanyInputChange('rc', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Article Number *
                    </label>
                    <input
                      type="text"
                      value={companyFormData.articleNumber}
                      onChange={(e) => handleCompanyInputChange('articleNumber', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Address *
                    </label>
                    <textarea
                      value={companyFormData.address}
                      onChange={(e) => handleCompanyInputChange('address', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      rows={3}
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Phone Number *
                    </label>
                    <input
                      type="tel"
                      value={companyFormData.phoneNumber}
                      onChange={(e) => handleCompanyInputChange('phoneNumber', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Website
                    </label>
                    <input
                      type="url"
                      value={companyFormData.website}
                      onChange={(e) => handleCompanyInputChange('website', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="https://www.example.com"
                    />
                  </div>

                  <div className="flex gap-3">
                    <button
                      type="submit"
                      disabled={updateCompanyMutation.isPending}
                      className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50 flex items-center justify-center gap-2"
                    >
                      {updateCompanyMutation.isPending ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                          Saving...
                        </>
                      ) : (
                        <>
                          <FiSave />
                          Save Changes
                        </>
                      )}
                    </button>
                    <button
                      type="button"
                      onClick={() => setIsEditingCompany(false)}
                      className="flex-1 bg-gray-500 text-white py-2 px-4 rounded-md hover:bg-gray-600"
                    >
                      Cancel
                    </button>
                  </div>
                </form>
              )}
              
              {!companiesLoading && !companiesError && !company && (
                <div className="text-gray-500 text-sm">
                  No company information available.
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </ProtectedRoute>
  );
};

export default ProfilePage; 