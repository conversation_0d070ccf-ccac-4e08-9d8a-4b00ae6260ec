import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";
import { InventoryItem } from "../inventory/inventory-item.entity";
import { StockAdjustment } from "../inventory/stock-adjustment.entity";
import { Quote, QuoteStatus } from "../sales/quote.entity";
import { QuoteItem } from "../sales/quote-item.entity";

@Injectable()
export class StockComputationService {
  constructor(
    @InjectRepository(InventoryItem)
    private inventoryItemRepository: Repository<InventoryItem>,
    @InjectRepository(StockAdjustment)
    private stockAdjustmentRepository: Repository<StockAdjustment>,
    @InjectRepository(Quote)
    private quoteRepository: Repository<Quote>,
    @InjectRepository(QuoteItem)
    private quoteItemRepository: Repository<QuoteItem>,
  ) {}

  async computeProductStock(productUuid: string): Promise<number> {
    // Query all stock adjustments for this product
    const adjustments = await this.stockAdjustmentRepository.find({
      where: { productUuid },
    });
    const adjustmentSum = adjustments.reduce(
      (sum, adj) => sum + (adj.quantityAdjusted || 0),
      0,
    );

    // Subtract all sold quantities from finalized quotes
    const quotes = await this.quoteRepository.find({
      where: [
        { status: QuoteStatus.ACCEPTED },
        { status: QuoteStatus.CONVERTED },
      ],
      relations: ['quoteItems'],
    });

    let soldSum = 0;
    for (const quote of quotes) {
      for (const item of quote.quoteItems) {
        if (item.productUuid === productUuid) {
          soldSum += item.quantity;
        }
      }
    }

    // Start from 0, add adjustments, subtract sold
    const result = adjustmentSum - soldSum;
    return result;
  }

  async computeAllStorageStocks(
    storageUuid: string,
  ): Promise<Record<string, number>> {
    // Get all products in this storage
    const adjustments = await this.stockAdjustmentRepository.find({
      where: { storageUuid },
    });
    const productUuids = [
      ...new Set(adjustments.map((adj) => adj.productUuid)),
    ].filter(Boolean);
    const result: Record<string, number> = {};
    for (const productUuid of productUuids) {
      result[productUuid] = await this.computeProductStock(productUuid);
    }
    return result;
  }

  async computeAllWarehouseStocks(
    warehouseUuid: string,
  ): Promise<Record<string, number>> {
    // Get all products in this warehouse
    const adjustments = await this.stockAdjustmentRepository.find({
      where: { warehouseUuid },
    });
    const productUuids = [
      ...new Set(adjustments.map((adj) => adj.productUuid)),
    ].filter(Boolean);
    const result: Record<string, number> = {};
    for (const productUuid of productUuids) {
      result[productUuid] = await this.computeProductStock(productUuid);
    }
    return result;
  }
}
