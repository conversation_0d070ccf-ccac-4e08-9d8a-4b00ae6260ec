import {
  Controller,
  Get,
  Post,
  Body,
  Req,
  Res,
  UseGuards,
  Request,
  UseFilters,
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  HttpException,
  CanActivate,
  ExecutionContext,
  Injectable,
  UnauthorizedException,
  ForbiddenException,
} from "@nestjs/common";
import { JwtService } from "@nestjs/jwt";
import { AuthGuard } from "@nestjs/passport";
import { ApiTags, ApiOperation, ApiOkResponse, ApiBody, ApiResponse } from "@nestjs/swagger";
import { UsersService } from "../users/users.service";
import { CreateUserDto } from "../users/dto/create-user.dto";
import { RolesService } from "../roles/roles.service";
import { UserRolesService } from "../users/user_roles.service";
import { Response } from "express";
import { LocalStrategy } from "./local.strategy";
import {
  UnifiedLoginDto,
  UnifiedLoginResponseDto,
} from "./dto/unified-login.dto";
import {
  EnhancedLoginDto,
  EnhancedLoginResponseDto,
  RefreshTokenDto,
  RefreshTokenResponseDto,
  LogoutDto,
  SecurityStatusDto,
} from "./dto/enhanced-login.dto";
import { UnifiedGuard } from "./unified.guard";
import { UnifiedStrategy } from "./unified.strategy";
import { OAuth2Client } from "google-auth-library";
import { AuthAuditService } from "./auth-audit.service.typeorm";
import { RateLimitingService } from "./rate-limiting.service.typeorm";
import { RefreshTokenService } from "./refresh-token.service.typeorm";


@Catch(HttpException)
export class AuthExceptionFilter implements ExceptionFilter {
  catch(exception: HttpException, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const status = exception.getStatus();
    const message = exception.message;

    console.log("Auth exception:", message);

    response.status(status).json({
      statusCode: status,
      message: message,
      error: status === 401 ? "Unauthorized" : "Error",
    });
  }
}

@Injectable()
export class CustomLocalGuard implements CanActivate {
  constructor(private readonly usersService: UsersService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const { email, password } = request.body;

    try {
      const localStrategy = new LocalStrategy(this.usersService);
      const user = await localStrategy.validate(email, password || "");

      request.user = user;
      return true;
    } catch (error) {
      console.log("Authentication failed:", error.message);
      throw error; // This will be caught by our exception filter
    }
  }
}

@ApiTags("auth")
@Controller("auth")
@UseFilters(AuthExceptionFilter)
export class AuthController {
  constructor(
    private readonly jwtService: JwtService,
    private readonly usersService: UsersService,
    private readonly rolesService: RolesService,
    private readonly userRolesService: UserRolesService,
    private readonly unifiedStrategy: UnifiedStrategy,
    private readonly authAuditService: AuthAuditService,
    private readonly rateLimitingService: RateLimitingService,
    private readonly refreshTokenService: RefreshTokenService,
  ) {}

  /**
   * Enhanced login with security features
   */
  @Post("login")
  @ApiOperation({
    summary: "Enhanced login with security features including rate limiting and audit logging",
  })
  @ApiBody({ type: EnhancedLoginDto })
  @ApiOkResponse({
    description: "Returns JWT tokens and user information with security status.",
    type: EnhancedLoginResponseDto,
  })
  @ApiResponse({ status: 429, description: "Too many login attempts" })
  @ApiResponse({ status: 423, description: "Account locked" })
  async enhancedLogin(@Body() loginDto: EnhancedLoginDto, @Request() req): Promise<EnhancedLoginResponseDto> {
    const ipAddress = loginDto.clientIp || req.ip || req.connection.remoteAddress;
    const userAgent = loginDto.userAgent || req.headers['user-agent'];

    try {
      // Check rate limiting
      const rateLimitCheck = await this.rateLimitingService.checkRateLimit(loginDto.identifier);
      if (!rateLimitCheck.allowed) {
        await this.authAuditService.logRateLimitExceeded(loginDto.identifier, ipAddress);
        throw new ForbiddenException(
          `Too many login attempts. Account locked until ${rateLimitCheck.lockedUntil?.toISOString()}`
        );
      }

      // Check IP-based rate limiting
      const ipRateLimitCheck = await this.rateLimitingService.checkIpRateLimit(ipAddress);
      if (!ipRateLimitCheck.allowed) {
        await this.authAuditService.logRateLimitExceeded(loginDto.identifier, ipAddress);
        throw new ForbiddenException('Too many login attempts from this IP address');
      }

      // Validate credentials using unified strategy
      const user = await this.unifiedStrategy.validate(loginDto.identifier, loginDto.password || "");

      // Check for suspicious activity
      const suspiciousActivity = await this.refreshTokenService.detectSuspiciousActivity(user.uuid, ipAddress);
      if (suspiciousActivity) {
        await this.authAuditService.logEvent({
          eventType: 'SUSPICIOUS_ACTIVITY' as any,
          userId: user.uuid,
          userEmail: user.email,
          ipAddress,
          userAgent,
          timestamp: new Date(),
          success: false,
          errorMessage: 'Suspicious activity detected',
        });
      }

      // Generate tokens
      const payload = { email: user.email, sub: user.uuid };
      const accessToken = this.jwtService.sign(payload);
      const { refreshToken, expiresAt: refreshExpiresAt } = await this.refreshTokenService.generateRefreshToken(
        user.uuid,
        user.email,
        ipAddress,
        userAgent,
      );

      // Get user roles
      let userRoles = [];
      try {
        if (user.roleUuidString) {
          const role = await this.userRolesService.getRoleByUuid(user.roleUuidString);
          if (role) {
            userRoles = [{
              uuid: role.uuid,
              name: role.name,
              permissions: role.permissions || [],
            }];
          }
        }
      } catch (roleError) {
        console.log("Warning - could not fetch user roles:", roleError);
      }

      // Get session statistics
      const sessionStats = await this.refreshTokenService.getTokenStats(user.uuid);

      // Record successful login
      await this.rateLimitingService.recordSuccessfulAttempt(loginDto.identifier);
      await this.authAuditService.logLoginSuccess(user.uuid, user.email, ipAddress, userAgent);

      const response: EnhancedLoginResponseDto = {
        accessToken,
        refreshToken,
        accessTokenExpiresIn: 3600, // 1 hour
        refreshTokenExpiresIn: 7 * 24 * 60 * 60, // 7 days
        user: {
          uuid: user.uuid,
          email: user.email,
          firstName: user.name?.split(" ")[0] || null,
          lastName: user.name?.split(" ").slice(1).join(" ") || null,
          phone: user.phone || null,
          isActive: !user.isDeleted,
          roles: userRoles,
          warehouseUuid: user.warehouseUuidString || null,
          vanUuid: user.vanUuidString || null,
          createdAt: user.createdAt?.toISOString() || new Date().toISOString(),
          updatedAt: user.updatedAt?.toISOString() || new Date().toISOString(),
        },
        sessionStats: {
          activeTokens: sessionStats.activeTokens,
          totalTokens: sessionStats.totalTokens,
          lastUsed: sessionStats.lastUsed?.toISOString() || null,
        },
      };

      console.log("Enhanced login successful for user:", user.email);
      return response;
    } catch (error) {
      // Record failed attempt
      await this.rateLimitingService.recordFailedAttempt(loginDto.identifier);
      await this.authAuditService.logLoginFailure(loginDto.identifier, error.message, ipAddress, userAgent);

      // Check if account should be locked
      const rateLimitStatus = await this.rateLimitingService.getRateLimitStatus(loginDto.identifier);
      if (rateLimitStatus?.lockedUntil) {
        await this.authAuditService.logAccountLocked(loginDto.identifier, 'Too many failed attempts', ipAddress);
        throw new ForbiddenException(
          `Account locked due to too many failed attempts. Unlocks at ${rateLimitStatus.lockedUntil.toISOString()}`
        );
      }

      throw error;
    }
  }

  /**
   * Legacy login endpoint (maintained for backward compatibility)
   */
  @Post("login/legacy")
  @ApiOperation({
    summary: "Legacy login endpoint for backward compatibility",
  })
  @ApiBody({ type: UnifiedLoginDto })
  @ApiOkResponse({
    description: "Returns a JWT token if credentials are valid.",
    type: UnifiedLoginResponseDto,
  })
  @UseGuards(UnifiedGuard)
  async legacyLogin(@Request() req): Promise<UnifiedLoginResponseDto> {
    try {
      const user = req.user;
      const payload = { email: user.email, sub: user.uuid };
      const accessToken = this.jwtService.sign(payload);

      let userRoles = [];
      try {
        if (user.roleUuidString) {
          const role = await this.userRolesService.getRoleByUuid(user.roleUuidString);
          if (role) {
            userRoles = [{
              uuid: role.uuid,
              name: role.name,
              permissions: role.permissions || [],
            }];
          }
        }
      } catch (roleError) {
        console.log("Warning - could not fetch user roles:", roleError);
      }

      const response: UnifiedLoginResponseDto = {
        accessToken: accessToken,
        refreshToken: null,
        user: {
          uuid: user.uuid,
          email: user.email,
          firstName: user.name?.split(" ")[0] || null,
          lastName: user.name?.split(" ").slice(1).join(" ") || null,
          phone: null,
          isActive: !user.isDeleted,
          roles: userRoles,
          warehouseUuid: user.warehouseUuidString || null,
          vanUuid: user.vanUuidString || null,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        expiresIn: 3600,
      };

      console.log("Legacy login successful for user:", user.email);
      return response;
    } catch (error) {
      console.log("Legacy login error:", error);
      throw error;
    }
  }

  /**
   * Refresh access token using refresh token
   */
  @Post("refresh")
  @ApiOperation({ summary: "Refresh access token using refresh token" })
  @ApiBody({ type: RefreshTokenDto })
  @ApiOkResponse({
    description: "Returns new access and refresh tokens.",
    type: RefreshTokenResponseDto,
  })
  async refreshToken(@Body() refreshDto: RefreshTokenDto, @Request() req): Promise<RefreshTokenResponseDto> {
    const ipAddress = refreshDto.clientIp || req.ip || req.connection.remoteAddress;
    const userAgent = refreshDto.userAgent || req.headers['user-agent'];

    try {
      // Validate and rotate refresh token
      const { refreshToken, expiresAt } = await this.refreshTokenService.rotateRefreshToken(
        refreshDto.refreshToken,
        ipAddress,
        userAgent,
      );

      // Get user from refresh token
      const tokenRecord = await this.refreshTokenService.validateRefreshToken(refreshDto.refreshToken);
      
      // Generate new access token
      const payload = { email: tokenRecord.userEmail, sub: tokenRecord.userId };
      const accessToken = this.jwtService.sign(payload);

      // Log refresh event
      await this.authAuditService.logEvent({
        eventType: 'LOGIN_SUCCESS' as any,
        userId: tokenRecord.userId,
        userEmail: tokenRecord.userEmail,
        ipAddress,
        userAgent,
        timestamp: new Date(),
        success: true,
        details: { action: 'token_refresh' },
      });

      return {
        accessToken,
        refreshToken,
        accessTokenExpiresIn: 3600, // 1 hour
        refreshTokenExpiresIn: 7 * 24 * 60 * 60, // 7 days
      };
    } catch (error) {
      await this.authAuditService.logEvent({
        eventType: 'LOGIN_FAILURE' as any,
        userEmail: 'unknown',
        ipAddress,
        userAgent,
        timestamp: new Date(),
        success: false,
        errorMessage: `Token refresh failed: ${error.message}`,
      });
      throw error;
    }
  }

  /**
   * Logout user and optionally revoke tokens
   */
  @Post("logout")
  @ApiOperation({ summary: "Logout user and optionally revoke tokens" })
  @ApiBody({ type: LogoutDto })
  @ApiOkResponse({ description: "User logged out successfully." })
  async logout(@Body() logoutDto: LogoutDto, @Request() req): Promise<{ message: string }> {
    const ipAddress = req.ip || req.connection.remoteAddress;
    const userAgent = req.headers['user-agent'];

    try {
      // Extract user info from JWT if available
      const authHeader = req.headers.authorization;
      let userId = 'unknown';
      let userEmail = 'unknown';

      if (authHeader && authHeader.startsWith('Bearer ')) {
        try {
          const token = authHeader.substring(7);
          const payload = this.jwtService.verify(token);
          userId = payload.sub;
          userEmail = payload.email;
        } catch (error) {
          // Token is invalid, continue with logout
        }
      }

      // Revoke specific refresh token if provided
      if (logoutDto.refreshToken) {
        try {
          const tokenRecord = await this.refreshTokenService.validateRefreshToken(logoutDto.refreshToken);
          await this.refreshTokenService.revokeToken(tokenRecord.tokenId);
        } catch (error) {
          // Token is already invalid, continue
        }
      }

      // Revoke all user tokens if requested
      if (logoutDto.revokeAll && userId !== 'unknown') {
        await this.refreshTokenService.revokeAllUserTokens(userId);
      }

      // Log logout event
      await this.authAuditService.logLogout(userId, userEmail, ipAddress, userAgent);

      return { message: 'Logged out successfully' };
    } catch (error) {
      console.log('Logout error:', error);
      return { message: 'Logged out successfully' };
    }
  }

  /**
   * Get security status for user
   */
  @Get("security-status")
  @ApiOperation({ summary: "Get security status for user" })
  @ApiOkResponse({
    description: "Returns security status including rate limiting and recent events.",
    type: SecurityStatusDto,
  })
  async getSecurityStatus(@Request() req): Promise<SecurityStatusDto> {
    const ipAddress = req.ip || req.connection.remoteAddress;
    const userAgent = req.headers['user-agent'];

    try {
      // Extract user info from JWT
      const authHeader = req.headers.authorization;
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        throw new UnauthorizedException('No valid token provided');
      }

      const token = authHeader.substring(7);
      const payload = this.jwtService.verify(token);
      const userEmail = payload.email;

      // Get rate limit status
      const rateLimitStatus = await this.rateLimitingService.getRateLimitStatus(userEmail);
      
      // Get recent events
      const recentEvents = await this.authAuditService.getRecentEvents(userEmail, 24);

      // Check for suspicious activity
      const suspiciousActivity = await this.refreshTokenService.detectSuspiciousActivity(payload.sub, ipAddress);

      return {
        isLocked: rateLimitStatus?.lockedUntil ? rateLimitStatus.lockedUntil > new Date() : false,
        lockedUntil: rateLimitStatus?.lockedUntil?.toISOString() || null,
        remainingAttempts: rateLimitStatus ? Math.max(0, 5 - rateLimitStatus.attempts) : 5,
        suspiciousActivityDetected: suspiciousActivity,
        recentEvents: recentEvents.map(event => ({
          eventType: event.eventType,
          timestamp: event.timestamp.toISOString(),
          ipAddress: event.ipAddress,
          success: event.success,
        })),
      };
    } catch (error) {
      throw new UnauthorizedException('Invalid token');
    }
  }

  /**
   * Health check endpoint for backend connectivity testing
   */
  @Get("health")
  @ApiOperation({ summary: "Health check endpoint" })
  @ApiOkResponse({ description: "Backend is healthy and accessible." })
  async healthCheck(): Promise<{ status: string; timestamp: string; uptime: number }> {
    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
    };
  }





  /**
   * Register a new user (with optional warehouseUuid)
   */
  @Post("register")
  @ApiOperation({ summary: "Register a new user" })
  @ApiBody({ type: CreateUserDto })
  @ApiOkResponse({ description: "User created." })
  async register(@Body() body: CreateUserDto) {
    // Create user with proper logic - let the service handle warehouse creation and role assignment
    return this.usersService.create(
      body.warehouseUuid,
      body.email,
      body.name,
      body.password,
      "super",
    );
  }

  // Redirect to Google for authentication
  /**
   * Redirects to Google for authentication
   */
  @Get("google")
  @ApiOperation({ summary: "Redirect to Google for authentication" })
  @ApiOkResponse({ description: "Redirects to Google OAuth2 login." })
  @UseGuards(AuthGuard("google"))
  async googleAuth() {
    // Guard handles redirect
  }

  /**
   * Handles Google OAuth2 callback
   */
  @Get("google/callback")
  @ApiOperation({ summary: "Google OAuth2 callback" })
  @ApiOkResponse({
    description: "Returns authenticated user info from Google.",
  })
  @UseGuards(AuthGuard("google"))
  async googleAuthRedirect(@Req() req, @Res() res) {
    // req.user will contain user info from Google
    const user = req.user;
    // Use only safe info for the token payload
    const payload = { email: user.email, sub: user.uuid };
    const token = this.jwtService.sign(payload);
    
    // Check if the request wants JSON response (for debugging or fallback)
    const acceptHeader = req.headers.accept || '';
    if (acceptHeader.includes('application/json') || req.query.format === 'json') {
      return res.json({
        user: user,
        accessToken: token,
        refreshToken: null, // Google OAuth doesn't provide refresh tokens in this flow
        expiresIn: 3600,
        message: "Google OAuth successful"
      });
    }
    
    // Redirect to frontend with token and user as query params
    const frontendUrl = `http://localhost:3000/auth/callback?token=${encodeURIComponent(token)}&user=${encodeURIComponent(JSON.stringify(user))}`;
    return res.redirect(frontendUrl);
  }

  /**
   * Fallback endpoint for Google OAuth callback that always returns JSON
   * This can be used if the redirect fails or for debugging
   */
  @Get("google/callback/json")
  @ApiOperation({ summary: "Google OAuth2 callback (JSON response)" })
  @ApiOkResponse({
    description: "Returns authenticated user info from Google as JSON.",
  })
  @UseGuards(AuthGuard("google"))
  async googleAuthCallbackJson(@Req() req, @Res() res) {
    // req.user will contain user info from Google
    const user = req.user;
    // Use only safe info for the token payload
    const payload = { email: user.email, sub: user.uuid };
    const token = this.jwtService.sign(payload);
    
    return res.json({
      user: user,
      accessToken: token,
      refreshToken: null, // Google OAuth doesn't provide refresh tokens in this flow
      expiresIn: 3600,
      message: "Google OAuth successful"
    });
  }

  /**
   * Handles Google authentication from mobile apps
   */
  @Post("google/token")
  @ApiOperation({ summary: "Authenticate with Google token from mobile" })
  @ApiBody({
    schema: {
      example: {
        accessToken: "google_access_token",
        idToken: "google_id_token",
        email: "<EMAIL>",
        name: "John Doe",
        photoUrl: "https://example.com/photo.jpg",
      },
    },
  })
  @ApiOkResponse({ description: "Returns JWT token and user data." })
  async googleTokenAuth(
    @Body()
    body: {
      accessToken: string;
      idToken: string;
      email: string;
      name: string;
      photoUrl?: string;
    },
  ) {
    try {
      const { accessToken, idToken, email, name } = body;

      console.log("Google token auth attempt for email:", email);

      if (!accessToken || !idToken) {
        console.error(
          "Missing tokens - accessToken:",
          !!accessToken,
          "idToken:",
          !!idToken,
        );
        throw new Error("Google access token and ID token are required");
      }

      if (!email || !name) {
        console.error("Missing user data - email:", !!email, "name:", !!name);
        throw new Error("Email and name are required");
      }

      // Validate Google ID token
      const googleUserInfo = await this.validateGoogleIdToken(idToken);

      if (!googleUserInfo) {
        console.error("Google ID token validation failed");
        throw new Error("Invalid Google ID token");
      }

      if (googleUserInfo.email !== email) {
        console.error(
          "Email mismatch - token email:",
          googleUserInfo.email,
          "provided email:",
          email,
        );
        throw new Error("Email mismatch between token and provided data");
      }

      console.log(
        "Google token validation successful for email:",
        googleUserInfo.email,
      );

      // Find or create user
      let user = await this.usersService.findByEmail(email);

      if (!user) {
        console.log("Creating new user for email:", email);

        try {
          // Create user with proper logic - let the service handle warehouse creation and role assignment
          user = await this.usersService.create(
            undefined,
            email,
            name,
            undefined,
            "super",
          );
          console.log("New user created:", user.email);
          console.log("User roleUuidString:", user.roleUuidString);

          // Verify that the user has a role assigned
          if (!user.roleUuidString) {
            console.error("ERROR: User created but no role assigned");
            console.error("User data:", JSON.stringify(user, null, 2));

            // Try to find and assign admin role manually
            try {
              console.log("Attempting to assign admin role to user...");
              await this.usersService.assignAdminRoleToUser(user.uuid);

              // Refetch user to get updated role
              user = await this.usersService.findByEmail(email);
              console.log("User after role assignment:", user.roleUuidString);

              if (user.roleUuidString) {
                console.log("✅ Successfully assigned admin role to user");
              } else {
                console.error("❌ Failed to assign admin role to user");
              }
            } catch (roleError) {
              console.error("Error assigning admin role:", roleError);
            }
          }
        } catch (createError) {
          console.error("Error creating user:", createError);
          throw new Error(`Failed to create user: ${createError.message}`);
        }
      } else {
        console.log("Existing user found:", user.email);
        console.log("User roleUuidString:", user.roleUuidString);

        // SECURITY FIX: Check if existing user has a role assigned
        if (!user.roleUuidString) {
          console.error("SECURITY WARNING: User has no role assigned - access denied");
          console.error("User data:", JSON.stringify(user, null, 2));
          
          throw new Error(
            `User ${email} exists but has no role assigned. Please contact an administrator to assign appropriate permissions.`
          );
        }
      }

      // Generate JWT token
      const payload = { email: user.email, sub: user.uuid };
      const access_token = this.jwtService.sign(payload);

      const response = {
        access_token,
        refresh_token: null, // Mobile apps typically don't use refresh tokens
        user: {
          uuid: user.uuid,
          email: user.email,
          name: user.name,
          roleUuidString: user.roleUuidString,
          userType: user.userType,
          warehouseUuidString: user.warehouseUuidString,
        },
        expiresIn: 3600, // 1 hour
      };

      console.log("Google token auth successful for user:", user.email);
      console.log(
        "Final response user data:",
        JSON.stringify(response.user, null, 2),
      );
      return response;
    } catch (error) {
      console.error("Google token authentication failed:", error.message);
      console.error("Error stack:", error.stack);
      throw new Error(`Google token authentication failed: ${error.message}`);
    }
  }

  /**
   * Test endpoint to directly call LocalStrategy validate method
   */
  @Post("test-login")
  @ApiOperation({ summary: "Test login endpoint that bypasses Passport" })
  @ApiBody({
    schema: {
      example: { email: "<EMAIL>", password: "password123" },
      properties: {
        email: { type: "string", description: "User email address" },
        password: { type: "string", description: "User password (optional)" },
      },
      required: ["email"],
    },
  })
  async testLogin(@Body() body: { email: string; password?: string }) {
    try {
      // Import LocalStrategy and call validate directly
      const { LocalStrategy } = await import("./local.strategy");
      const localStrategy = new LocalStrategy(this.usersService);

      const user = await localStrategy.validate(
        body.email,
        body.password || "",
      );

      const payload = { email: user.email, sub: user.uuid };
      return {
        success: true,
        access_token: this.jwtService.sign(payload),
        user: { uuid: user.uuid, email: user.email, name: user.name },
      };
    } catch (error) {
      console.log("Test login failed:", error.message);
      return {
        success: false,
        error: error.message,
        statusCode: 401,
      };
    }
  }

  /**
   * Validate Google ID token using Google Auth Library
   */
  private async validateGoogleIdToken(idToken: string): Promise<any> {
    try {
      const expectedClientId = process.env.GOOGLE_CLIENT_ID;

      if (!expectedClientId) {
        console.error(
          "GOOGLE_CLIENT_ID environment variable is not configured",
        );
        throw new Error(
          "Google client ID not configured in backend environment",
        );
      }

      console.log(
        "Validating Google ID token with client ID:",
        expectedClientId,
      );

      // Create OAuth2 client with clock skew tolerance
      const client = new OAuth2Client(expectedClientId);

      // Verify the ID token
      const ticket = await client.verifyIdToken({
        idToken: idToken,
        audience: expectedClientId,
      });

      const payload = ticket.getPayload();

      if (!payload) {
        console.error("No payload found in verified token");
        throw new Error("Invalid token payload");
      }

      // Additional validation logging
      const currentTime = Math.floor(Date.now() / 1000);
      const tokenExp = payload.exp;
      const tokenIat = payload.iat;

      console.log("Token validation details:");
      console.log("  - Current time:", currentTime);
      console.log("  - Token issued at (iat):", tokenIat);
      console.log("  - Token expires at (exp):", tokenExp);
      console.log("  - Time until expiry:", tokenExp - currentTime, "seconds");
      console.log("  - Token age:", currentTime - tokenIat, "seconds");

      if (tokenExp < currentTime) {
        const expiredBy = currentTime - tokenExp;
        console.warn("Token is expired by", expiredBy, "seconds");
        throw new Error(`Google ID token has expired ${expiredBy} seconds ago`);
      }

      console.log("Token validation successful for email:", payload.email);

      return {
        email: payload.email,
        name: payload.name,
        picture: payload.picture,
        sub: payload.sub,
        aud: payload.aud,
        exp: payload.exp,
        iat: payload.iat,
      };
    } catch (error) {
      console.error("Error validating Google ID token:", error.message);
      console.error("Error details:", error);

      // Provide more specific error messages
      if (error.message.includes("Token used too early")) {
        throw new Error("Google ID token is not yet valid (used too early)");
      } else if (
        error.message.includes("Token used too late") ||
        error.message.includes("expired")
      ) {
        // Extract timing information for better error reporting
        const match = error.message.match(
          /Token used too late, ([\d.]+) > ([\d.]+)/,
        );
        if (match) {
          const currentTime = parseFloat(match[1]);
          const expTime = parseFloat(match[2]);
          const expiredBy = Math.floor(currentTime - expTime);
          throw new Error(
            `Google ID token has expired ${expiredBy} seconds ago. Please refresh and try again.`,
          );
        }
        throw new Error(
          "Google ID token has expired. Please refresh and try again.",
        );
      } else if (error.message.includes("Invalid audience")) {
        throw new Error(
          "Google ID token audience mismatch. Check client ID configuration.",
        );
      } else if (error.message.includes("Invalid issuer")) {
        throw new Error("Google ID token has invalid issuer");
      } else if (error.message.includes("Invalid token signature")) {
        throw new Error("Google ID token signature is invalid");
      } else {
        throw new Error(`Google ID token validation failed: ${error.message}`);
      }
    }
  }
}
