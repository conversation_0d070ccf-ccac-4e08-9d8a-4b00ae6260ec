import { ApiProperty } from "@nestjs/swagger";
import { IsString, IsOptional, IsNotEmpty, IsUUID } from "class-validator";

export class CreateWarehouseDto {
  @ApiProperty({
    example: "Acme Warehouse",
    description: "Warehouse name",
    required: false,
  })
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  name?: string;

  @ApiProperty({
    example: "Main distribution center for Acme Corporation",
    description: "Warehouse description",
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the user who owns this warehouse",
    required: true,
  })
  @IsUUID("all")
  @IsNotEmpty()
  userUuid: string;
}
