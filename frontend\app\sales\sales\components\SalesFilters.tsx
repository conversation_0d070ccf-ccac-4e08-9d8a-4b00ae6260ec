import React, { useState } from 'react';
import { FiFilter, FiSearch, FiCalendar, FiDollarSign, FiUser, FiX, FiChevronDown } from 'react-icons/fi';
import { FilterSaleDto, SaleStatus, PaymentMethods } from '../salesApi';
import { CustomerModal, type Customer, type CreateCustomerDto } from '@/components/CustomerModal';

interface SalesFiltersProps {
  showFilters: boolean;
  filter: FilterSaleDto;
  onFilterChange: (newFilter: Partial<FilterSaleDto>) => void;
  onClearFilters: () => void;
  onToggleFilters: () => void;
}

export const SalesFilters: React.FC<SalesFiltersProps> = ({
  showFilters,
  filter,
  onFilterChange,
  onClearFilters,
  onToggleFilters
}) => {
  const [localFilters, setLocalFilters] = useState<FilterSaleDto>(filter);
  const [isCustomerModalOpen, setIsCustomerModalOpen] = useState(false);
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);

  const handleLocalFilterChange = (key: keyof FilterSaleDto, value: any) => {
    let sanitizedValue = value;
    
    // Special handling for numeric values
    if (key === 'minAmount' || key === 'maxAmount') {
      if (value === '' || value === null || value === undefined) {
        sanitizedValue = undefined;
      } else {
        const numValue = parseFloat(value);
        if (isNaN(numValue) || numValue < 0) {
          sanitizedValue = undefined;
        } else {
          sanitizedValue = numValue;
        }
      }
    }
    
    const newFilters = { ...localFilters, [key]: sanitizedValue };
    setLocalFilters(newFilters);
    onFilterChange({ [key]: sanitizedValue });
  };

  const handleClearFilters = () => {
    setLocalFilters({});
    setSelectedCustomer(null);
    onClearFilters();
  };

  const handleCustomerSelect = (customer: Customer | null) => {
    setSelectedCustomer(customer);
    if (customer) {
      handleLocalFilterChange('customerUuid', customer.uuid);
    } else {
      handleLocalFilterChange('customerUuid', undefined);
    }
  };

  const handleCreateCustomer = async (customerData: CreateCustomerDto) => {
    try {
      // Import the createCustomer function from CustomerModal
      const { createCustomer } = await import('@/components/CustomerModal');
      const newCustomer = await createCustomer(customerData);
      
      // Select the newly created customer
      handleCustomerSelect(newCustomer);
    } catch (error) {
      console.error('Error creating customer:', error);
      // You might want to show a toast notification here
    }
  };

  const clearCustomerFilter = () => {
    setSelectedCustomer(null);
    handleLocalFilterChange('customerUuid', undefined);
  };

  return (
    <>
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
        {/* Filter Toggle Header */}
        <div className="px-4 py-3 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <button
              onClick={onToggleFilters}
              className="flex items-center text-sm font-medium text-gray-700 hover:text-gray-900"
            >
              <FiFilter className="w-4 h-4 mr-2" />
              Filters
              <span className="ml-2 text-xs text-gray-500">
                {showFilters ? 'Hide' : 'Show'}
              </span>
            </button>
            
            {Object.values(localFilters).some(value => value !== undefined && value !== '') && (
              <button
                onClick={handleClearFilters}
                className="flex items-center text-sm text-red-600 hover:text-red-700"
              >
                <FiX className="w-4 h-4 mr-1" />
                Clear All
              </button>
            )}
          </div>
        </div>

        {/* Filter Content */}
        {showFilters && (
          <div className="p-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {/* Invoice Number */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  <FiSearch className="w-4 h-4 inline mr-1" />
                  Invoice Number
                </label>
                <input
                  type="text"
                  value={localFilters.invoiceNumber || ''}
                  onChange={(e) => handleLocalFilterChange('invoiceNumber', e.target.value)}
                  placeholder="Search by invoice..."
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              {/* Customer Picker */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  <FiUser className="w-4 h-4 inline mr-1" />
                  Customer
                </label>
                <div className="relative">
                  <button
                    type="button"
                    onClick={() => setIsCustomerModalOpen(true)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white text-left flex items-center justify-between"
                  >
                    <span className={selectedCustomer ? 'text-gray-900' : 'text-gray-500'}>
                      {selectedCustomer ? selectedCustomer.name : 'Select customer...'}
                    </span>
                    <FiChevronDown className="h-4 w-4 text-gray-400" />
                  </button>
                  
                  {selectedCustomer && (
                    <button
                      type="button"
                      onClick={clearCustomerFilter}
                      className="absolute right-8 top-1/2 transform -translate-y-1/2 p-1 text-gray-400 hover:text-gray-600"
                      title="Clear customer filter"
                    >
                      <FiX className="h-3 w-3" />
                    </button>
                  )}
                </div>
                
                {/* Selected Customer Info */}
                {selectedCustomer && (
                  <div className="mt-1 text-xs text-gray-600">
                    {selectedCustomer.fiscalId && `ID: ${selectedCustomer.fiscalId} • `}
                    {selectedCustomer.customerType && `${selectedCustomer.customerType} • `}
                    {selectedCustomer.phone && `${selectedCustomer.phone} • `}
                    {selectedCustomer.email}
                  </div>
                )}
              </div>

              {/* Status */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Status
                </label>
                <select
                  value={localFilters.status || ''}
                  onChange={(e) => handleLocalFilterChange('status', e.target.value || undefined)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">All Statuses</option>
                  <option value={SaleStatus.PAID}>Paid</option>
                  <option value={SaleStatus.PARTIALLY_PAID}>Partially Paid</option>
                  <option value={SaleStatus.UNPAID}>Unpaid</option>
                  <option value={SaleStatus.CANCELLED}>Cancelled</option>
                </select>
              </div>

              {/* Payment Method */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Payment Method
                </label>
                <select
                  value={localFilters.paymentMethod || ''}
                  onChange={(e) => handleLocalFilterChange('paymentMethod', e.target.value || undefined)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">All Methods</option>
                  <option value={PaymentMethods.CASH}>Cash</option>
                  <option value={PaymentMethods.CREDIT_CARD}>Credit Card</option>
                  <option value={PaymentMethods.BANK_TRANSFER}>Bank Transfer</option>
                  <option value={PaymentMethods.MOBILE_PAYMENT}>Mobile Payment</option>
                  <option value={PaymentMethods.CHEQUE}>Cheque</option>
                  <option value={PaymentMethods.OTHER}>Other</option>
                </select>
              </div>

              {/* Creation Date Range */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  <FiCalendar className="w-4 h-4 inline mr-1" />
                  Created From Date
                </label>
                <input
                  type="date"
                  value={localFilters.createdFrom || ''}
                  onChange={(e) => handleLocalFilterChange('createdFrom', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  <FiCalendar className="w-4 h-4 inline mr-1" />
                  Created To Date
                </label>
                <input
                  type="date"
                  value={localFilters.createdTo || ''}
                  onChange={(e) => handleLocalFilterChange('createdTo', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              {/* Amount Range */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  <FiDollarSign className="w-4 h-4 inline mr-1" />
                  Min Amount
                </label>
                <input
                  type="number"
                  value={localFilters.minAmount || ''}
                  onChange={(e) => {
                    const value = e.target.value;
                    if (value === '' || parseFloat(value) >= 0) {
                      handleLocalFilterChange('minAmount', value);
                    }
                  }}
                  onBlur={(e) => {
                    const value = e.target.value;
                    if (value === '' || parseFloat(value) >= 0) {
                      handleLocalFilterChange('minAmount', value);
                    }
                  }}
                  placeholder="0.00"
                  step="0.01"
                  min="0"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  <FiDollarSign className="w-4 h-4 inline mr-1" />
                  Max Amount
                </label>
                <input
                  type="number"
                  value={localFilters.maxAmount || ''}
                  onChange={(e) => {
                    const value = e.target.value;
                    if (value === '' || parseFloat(value) >= 0) {
                      handleLocalFilterChange('maxAmount', value);
                    }
                  }}
                  onBlur={(e) => {
                    const value = e.target.value;
                    if (value === '' || parseFloat(value) >= 0) {
                      handleLocalFilterChange('maxAmount', value);
                    }
                  }}
                  placeholder="0.00"
                  step="0.01"
                  min="0"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Customer Modal */}
      <CustomerModal
        isOpen={isCustomerModalOpen}
        onSelect={handleCustomerSelect}
        onClose={() => setIsCustomerModalOpen(false)}
        onCreateNew={handleCreateCustomer}
      />
    </>
  );
}; 