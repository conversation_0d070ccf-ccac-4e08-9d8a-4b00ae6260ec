'use client';

import React, { createContext, useContext, ReactNode, useState, useEffect } from 'react';
import { Theme, defaultTheme, getThemeByName, allThemes } from '@/styles/themes';

type ThemeContextType = {
  theme: Theme;
  themeName: string;
  setTheme: (themeName: string) => void;
  availableThemes: { name: string; label: string }[];
};

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

type ThemeProviderProps = {
  children: ReactNode;
  initialTheme?: string;
};

export const ThemeProvider: React.FC<ThemeProviderProps> = ({
  children,
  initialTheme = 'white',
}) => {
  const [themeName, setThemeName] = useState<string>(initialTheme);
  const [theme, setTheme] = useState<Theme>(getThemeByName(initialTheme));

  // Load saved theme from localStorage on initial render (client-side only)
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const savedTheme = localStorage.getItem('theme');
      if (savedTheme) {
        const savedThemeObj = getThemeByName(savedTheme);
        setThemeName(savedThemeObj.name);
        setTheme(savedThemeObj);
      }
    }
  }, []);

  // Update CSS variables when theme changes
  useEffect(() => {
    // Only run on client side
    if (typeof window === 'undefined') return;
    
    const root = document.documentElement;
    
    // Set color variables
    Object.entries(theme.colors).forEach(([key, value]) => {
      root.style.setProperty(`--color-${key}`, value);
    });

    // Set shadow variables
    Object.entries(theme.shadows).forEach(([key, value]) => {
      root.style.setProperty(`--shadow-${key}`, value);
    });

    // Set border radius variables
    Object.entries(theme.borderRadius).forEach(([key, value]) => {
      root.style.setProperty(`--radius-${key}`, value);
    });
    
    // Update the data-theme attribute for potential CSS selectors
    root.setAttribute('data-theme', theme.name);
  }, [theme]);

  const changeTheme = (newThemeName: string) => {
    const newTheme = getThemeByName(newThemeName);
    setThemeName(newTheme.name);
    setTheme(newTheme);
    // Save to localStorage
    localStorage.setItem('theme', newTheme.name);
  };

  const availableThemes = allThemes.map((t) => ({
    name: t.name,
    label: t.name.charAt(0).toUpperCase() + t.name.slice(1),
  }));

  return (
    <ThemeContext.Provider
      value={{
        theme,
        themeName,
        setTheme: changeTheme,
        availableThemes,
      }}
    >
      {children}
    </ThemeContext.Provider>
  );
};

export const useTheme = (): ThemeContextType => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};
