import { ApiProperty } from "@nestjs/swagger";

export class InventoryItemDto {
  @ApiProperty({ example: "product-uuid-string" })
  productUuid: string;

  @ApiProperty({ example: "storage-uuid-string" })
  storageUuid: string;

  @ApiProperty({ example: 100 })
  quantity: number;

  @ApiProperty({ example: "2025-06-17T19:36:22.000Z" })
  createdAt: Date;

  @ApiProperty({ example: "2025-06-17T19:36:22.000Z" })
  updatedAt: Date;

  @ApiProperty({
    example: {
      uuid: "product-uuid-string",
      warehouseUuid: "warehouse-uuid-string",
      name: "Widget",
      description: "A sample product",
      sku: "SKU123",
      barcode: "1234567890123",
      category: "Category",
      price: 10.99,
      cost: 7.5,
      isDeleted: false,
    },
    description:
      "Snapshot of the product details at the time of inventory record.",
  })
  productSnapshot: {
    uuid: string;
    warehouseUuid: string;
    name: string;
    description?: string;
    sku?: string;
    barcode?: string;
    category?: string;
    price: number;
    cost?: number;
    isDeleted: boolean;
  };
}
