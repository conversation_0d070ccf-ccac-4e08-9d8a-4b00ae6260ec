import { ApiProperty } from "@nestjs/swagger";
import {
  IsString,
  <PERSON>NotEmpty,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ength,
  IsUUID,
} from "class-validator";

export class CreateProductCategoryDto {
  @ApiProperty({
    example: "Electronics",
    description: "Name of the product category",
    minLength: 1,
    maxLength: 100,
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(1)
  @MaxLength(100)
  name: string;

  @ApiProperty({
    example: "018ea2bb-6b8d-7e9a-8a2a-7e9a8a2a7e9a",
    description: "UUID of the warehouse this category belongs to",
  })
  @IsUUID()
  @IsNotEmpty()
  warehouseUuid: string;

  @ApiProperty({
    example: "018ea2bb-6b8d-7e9a-8a2a-7e9a8a2a7e9a",
    description: "UUID of the user creating this category",
    required: true,
  })
  @IsUUID()
  @IsNotEmpty()
  userUuid: string;
}
