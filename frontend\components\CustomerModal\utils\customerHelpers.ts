// Customer helper functions for the global Customer Modal

import type { CreateCustomerDto, FormValidationResult } from '../types';

// Validate customer data
export function validateCustomer(customerData: CreateCustomerDto): FormValidationResult {
  const errors: string[] = [];

  // Validate required fields
  if (!customerData.name || customerData.name.trim() === '') {
    errors.push('Customer name is required');
  }

  // Validate email format if provided
  if (customerData.email && customerData.email.trim() !== '') {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(customerData.email)) {
      errors.push('Invalid email format');
    }
  }

  // Validate phone format if provided
  if (customerData.phone && customerData.phone.trim() !== '') {
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
    if (!phoneRegex.test(customerData.phone.replace(/\s/g, ''))) {
      errors.push('Invalid phone number format');
    }
  }

  // Validate fiscal ID format if provided
  if (customerData.fiscalId && customerData.fiscalId.trim() !== '') {
    if (customerData.fiscalId.length < 3) {
      errors.push('Fiscal ID must be at least 3 characters long');
    }
  }

  // Validate customer type
  const validCustomerTypes = ['retail', 'wholesale', 'mid-wholesale', 'institutional'];
  if (!validCustomerTypes.includes(customerData.customerType)) {
    errors.push('Invalid customer type');
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

// Format customer display name
export function formatCustomerName(customer: { name: string; fiscalId?: string }): string {
  if (customer.fiscalId) {
    return `${customer.name} (${customer.fiscalId})`;
  }
  return customer.name;
}

// Format customer contact info
export function formatCustomerContact(customer: { phone?: string; email?: string }): string {
  const parts: string[] = [];
  
  if (customer.phone) {
    parts.push(customer.phone);
  }
  
  if (customer.email) {
    parts.push(customer.email);
  }
  
  return parts.join(' • ');
}

// Get customer type display name
export function getCustomerTypeDisplayName(customerType: string): string {
  const typeMap: Record<string, string> = {
    'retail': 'Retail',
    'wholesale': 'Wholesale',
    'mid-wholesale': 'Mid-Wholesale',
    'institutional': 'Institutional',
  };
  
  return typeMap[customerType] || customerType;
} 