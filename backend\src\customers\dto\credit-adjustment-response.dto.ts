import { ApiProperty } from "@nestjs/swagger";
import {
  CreditAdjustmentType,
  CreditAdjustment,
} from "../credit-adjustment.entity";

export class CreditAdjustmentResponseDto {
  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the credit adjustment",
  })
  uuid: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the customer",
  })
  customerUuidString: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the user who made the adjustment",
  })
  userUuidString: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the warehouse",
  })
  warehouseUuidString: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the related sale",
    required: false,
  })
  saleUuidString?: string;

  @ApiProperty({
    example: "sale",
    description: "Type of credit adjustment",
    enum: Object.values(CreditAdjustmentType),
  })
  adjustmentType: CreditAdjustmentType;

  @ApiProperty({ example: 100.0, description: "Amount adjusted" })
  amountAdjusted: number;

  @ApiProperty({
    example: 50.0,
    description: "Customer credit balance before this adjustment",
  })
  previousBalance: number;

  @ApiProperty({
    example: 150.0,
    description: "Customer credit balance after this adjustment",
  })
  newBalance: number;

  @ApiProperty({
    example: "Sale payment received",
    description: "Reason for the credit adjustment",
  })
  reason: string;

  @ApiProperty({
    example: "2024-01-01T00:00:00.000Z",
    description: "Creation timestamp",
  })
  createdAt: Date;
}

export function toCreditAdjustmentResponseDto(
  adjustment: CreditAdjustment,
): CreditAdjustmentResponseDto {
  return {
    uuid: adjustment.id,
    customerUuidString: adjustment.customerUuid,
    userUuidString: adjustment.userUuid,
    warehouseUuidString: adjustment.warehouseUuid,
    saleUuidString: adjustment.saleUuid,
    adjustmentType: adjustment.adjustmentType,
    amountAdjusted: adjustment.amountAdjusted,
    previousBalance: adjustment.previousBalance,
    newBalance: adjustment.newBalance,
    reason: adjustment.reason,
    createdAt: adjustment.createdAt,
  };
}
