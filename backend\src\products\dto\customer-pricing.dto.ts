import { ApiProperty } from "@nestjs/swagger";
import { ProductDto } from "./product.dto";

export class CustomerPricingDto implements ProductDto {
  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID string representation of the product",
  })
  uuid: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID string representation of the warehouse",
  })
  warehouseUuidString?: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID string representation of the product category",
  })
  productCategoryUuidString?: string;

  @ApiProperty({ example: "Product Name" })
  name: string;

  @ApiProperty({ example: "Product description" })
  description?: string;

  @ApiProperty({ example: "SKU123" })
  sku?: string;

  @ApiProperty({ example: "1234567890" })
  barcode?: string;

  @ApiProperty({ example: 25.99, description: "Retail price" })
  retailPrice: number;

  @ApiProperty({ example: 22.99, description: "Wholesale price" })
  wholesalePrice: number;

  @ApiProperty({ example: 20.99, description: "Mid-wholesale price" })
  midWholesalePrice: number;

  @ApiProperty({ example: 18.99, description: "Institutional price" })
  institutionalPrice: number;

  @ApiProperty({ example: 15.5, description: "Cost price" })
  cost?: number;

  @ApiProperty({ example: false, description: "Soft delete flag" })
  isDeleted: boolean;

  @ApiProperty({
    example: "2023-01-01T00:00:00.000Z",
    description: "Creation date",
  })
  createdAt?: Date | string;

  @ApiProperty({
    example: "2023-01-01T00:00:00.000Z",
    description: "Last update date",
  })
  updatedAt?: Date | string;

  @ApiProperty({
    example: 25.99,
    description: "Price for the specific customer type",
  })
  customerPrice: number;

  @ApiProperty({
    example: "retail",
    description: "Customer type for this pricing",
    enum: ["retail", "wholesale", "mid-wholesale", "institutional"],
  })
  customerType: "retail" | "wholesale" | "mid-wholesale" | "institutional";
}

export class CustomerPricingResponseDto {
  @ApiProperty({
    type: [CustomerPricingDto],
    description: "Array of products with customer-specific pricing",
  })
  data: CustomerPricingDto[];

  @ApiProperty({ example: 100, description: "Total number of products" })
  total: number;

  @ApiProperty({ example: 1, description: "Current page number" })
  page: number;

  @ApiProperty({ example: 20, description: "Number of items per page" })
  limit: number;

  @ApiProperty({ example: true, description: "Whether there is a next page" })
  hasNext: boolean;

  @ApiProperty({
    example: false,
    description: "Whether there is a previous page",
  })
  hasPrev: boolean;
}
