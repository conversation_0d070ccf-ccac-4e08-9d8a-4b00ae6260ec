// DTO utility for Role output
import { Role } from "../role.entity";

export interface RoleDto {
  uuid: string; // from id field
  warehouseUuid: string; // from warehouseUuid field
  name: string;
  permissions: string[];
  isDeleted: boolean;
}

export function toRoleDto(role: Role): RoleDto {
  // Accepts a TypeORM entity
  return {
    uuid: role.id,
    warehouseUuid: role.warehouseUuid,
    name: role.name,
    permissions: role.permissions,
    isDeleted: role.isDeleted,
  };
}

export function toRoleDtoArray(roles: Role[]): RoleDto[] {
  return roles.map(toRoleDto);
}
