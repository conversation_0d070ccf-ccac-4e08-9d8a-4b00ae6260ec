import React from 'react';
import { ExclamationCircleIcon } from '@heroicons/react/20/solid';
import ItemsTable, { ItemsTableColumn } from '@/components/itemsTable/ItemsTable';
import TableActionButtons from '@/components/itemsTable/TableActionButtons';
import { User, Role } from '../usersApi';
import { VanDto } from '@/app/logistics/vans/vansApi';

interface UserTableProps {
  users: User[];
  roles: Role[];
  vans: VanDto[];
  onEdit: (user: User) => void;
  onDelete: (uuid: string) => void;
  onViewDetails: (uuid: string) => void;
  onAssignRole: (userUuid: string, roleUuid: string) => void;
  onAssignVan: (userUuid: string, vanUuid: string) => void;
  loadingRoles: boolean;
  loadingVans: boolean;
  rolesError: Error | null;
  vansError: Error | null;
  roleChanging: string | null;
  vanChanging: string | null;
  optimisticRoleMap: Record<string, string>;
  optimisticVanMap: Record<string, string>;
}

const UserTable: React.FC<UserTableProps> = ({
  users,
  roles,
  vans,
  onEdit,
  onDelete,
  onViewDetails,
  onAssignRole,
  onAssignVan,
  loadingRoles,
  loadingVans,
  rolesError,
  vansError,
  roleChanging,
  vanChanging,
  optimisticRoleMap,
  optimisticVanMap,
}) => {
  // Debug logging to understand the role matching
  React.useEffect(() => {
    if (users.length > 0 && roles.length > 0) {
      console.log('UserTable Debug - Users:', users);
      console.log('UserTable Debug - Roles:', roles);
      users.forEach((user, index) => {
        const roleMatch = roles.find(role => role.uuid === user.roleUuid);
        console.log(`User ${index} (${user.name}): roleUuid=${user.roleUuid}, roleMatch=${roleMatch?.name || 'NOT FOUND'}`);
      });
    }
  }, [users, roles]);

  // All vans are available for assignment (no filtering needed like with truck storages)

  const columns: ItemsTableColumn<User>[] = [
    {
      key: 'name',
      header: 'Name',
      render: (value) => <span className="font-semibold">{value}</span>,
    },
    {
      key: 'vanUuidString',
      header: 'Van',
      render: (_value, row) => {
        const isLoading = vanChanging === row.uuid;
        const currentVanUuid = optimisticVanMap[row.uuid] ?? row.vanUuidString ?? '';
        
        // Find the user's current role to determine if they can have van assigned
        const userRole = roles.find(role => role.uuid === row.roleUuid);
        const isMobileSaleAgent = userRole && userRole.name.toLowerCase() === 'mobile sale agent';
        const isSuper = row.userType === 'super';
        
        // Find the van for display purposes
        const vanMatch = vans.find(v => v.uuid === currentVanUuid);

        if (loadingVans) return <span aria-busy="true">Loading...</span>;
        if (vansError) return <span className="text-red-500" title={vansError.message || 'Failed to load vans'}>Error</span>;

        // If user is not mobile sale agent, show static display
        if (!isMobileSaleAgent) {
          if (!currentVanUuid) {
            return <span className="text-gray-400">N/A</span>;
          }
          return vanMatch ? (
            <span>{vanMatch.name}</span>
          ) : (
            <span className="text-gray-400">N/A</span>
          );
        }

        // For mobile sale agents, show dropdown
        return (
          <div className="flex flex-col items-center justify-center">
            <div className="flex items-center justify-center gap-2">
              <select
                className={`block w-48 rounded-lg border-2 border-gray-200 py-1.5 pl-4 pr-10 text-gray-700 bg-white shadow-sm hover:border-blue-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 focus:outline-none transition-all duration-200 sm:text-sm text-left ${isLoading ? 'opacity-50 cursor-wait' : 'cursor-pointer'}`}
                value={currentVanUuid}
                disabled={isLoading}
                onChange={e => onAssignVan(row.uuid, e.target.value)}
              >
                <option value="">No van</option>
                {vans.map(van => (
                  <option key={van.uuid} value={van.uuid}>{van.name}</option>
                ))}
              </select>
              {isLoading && <span className="animate-spin h-4 w-4 border-2 border-gray-300 border-t-blue-500 rounded-full" />}
            </div>
            {!vanMatch && currentVanUuid && !loadingVans && !isLoading && (
              <p className="mt-1 flex items-center text-xs text-yellow-600">
                <ExclamationCircleIcon className="mr-1 h-4 w-4" />Van not found
              </p>
            )}
          </div>
        );
      },
    },
    {
      key: 'roleUuid',
      header: 'Role',
      render: (_value, row) => {
        const isLoading = roleChanging === row.uuid;
        const currentRoleUuid = optimisticRoleMap[row.uuid] ?? row.roleUuid ?? '';
        const roleMatch = roles.find(role => role.uuid === currentRoleUuid);

        if (loadingRoles) return <span aria-busy="true">Loading...</span>;
        if (rolesError) return <p className="flex items-center text-sm text-red-500"><ExclamationCircleIcon className="mr-1 h-4 w-4" />Error</p>;

        return (
          <div className="flex flex-col items-center justify-center">
            <div className="flex items-center justify-center gap-2">
              <select
                className={`block w-52 rounded-lg border-2 border-gray-200 py-1.5 pl-4 pr-10 text-gray-700 bg-white shadow-sm hover:border-blue-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 focus:outline-none transition-all duration-200 sm:text-sm text-left ${isLoading ? 'opacity-50 cursor-wait' : 'cursor-pointer'} ${(roleMatch && roleMatch.name.toLowerCase() === 'mobile sale agent') ? 'bg-gray-50 border-gray-300 text-gray-500 cursor-not-allowed hover:border-gray-300' : ''}`}
                value={currentRoleUuid}
                disabled={isLoading || (roleMatch && roleMatch.name.toLowerCase() === 'mobile sale agent')}
                onChange={e => onAssignRole(row.uuid, e.target.value)}
                title={roleMatch && roleMatch.name.toLowerCase() === 'mobile sale agent' ? 'Role cannot be changed for Mobile Sale Agent' : ''}
              >
                <option value="">Select role</option>
                {roles.map(role => (
                  <option key={role.uuid} value={role.uuid}>{role.name}</option>
                ))}
              </select>
              {isLoading && <span className="animate-spin h-4 w-4 border-2 border-gray-300 border-t-blue-500 rounded-full" />}
            </div>
            {!roleMatch && !loadingRoles && !isLoading && <p className="mt-1 flex items-center text-xs text-yellow-600"><ExclamationCircleIcon className="mr-1 h-4 w-4" />No valid role</p>}
          </div>
        );
      },
    },
    {
      key: 'actions',
      header: 'Actions',
      render: (_value, row) => (
        <TableActionButtons
          onView={() => onViewDetails(row.uuid)}
          onEdit={() => onEdit(row)}
          onDelete={() => onDelete(row.uuid)}
        />
      ),
      headerClassName: 'text-center',
      cellClassName: 'text-center',
    },
  ];

  return <ItemsTable data={users} columns={columns} />;
};

export default UserTable;
// No-op edit for context update
