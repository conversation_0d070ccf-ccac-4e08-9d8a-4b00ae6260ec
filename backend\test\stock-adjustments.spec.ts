import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../src/app.module';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule, ConfigService } from '@nestjs/config';

describe('Stock Adjustments (e2e)', () => {
  let app: INestApplication;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({
          isGlobal: true,
          cache: false,
        }),
        TypeOrmModule.forRootAsync({
          imports: [ConfigModule],
          useFactory: async (configService: ConfigService) => ({
            type: 'postgres',
            host: configService.get<string>('YUGABYTE_HOST'),
            port: configService.get<number>('YUGABYTE_PORT'),
            database: configService.get<string>('YUGABYTE_DATABASE'),
            username: configService.get<string>('YUGABYTE_USER'),
            password: configService.get<string>('YUGABYTE_PASSWORD'),
            entities: [],
            synchronize: false,
            logging: false,
            ssl: false,
          }),
          inject: [ConfigService],
        }),
        AppModule,
      ],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  it('/inventory/stock-adjustment (GET) should return stock adjustments with product and user info', () => {
    return request(app.getHttpServer())
      .get('/inventory/stock-adjustment')
      .expect(200)
      .expect((res) => {
        expect(Array.isArray(res.body)).toBe(true);
        if (res.body.length > 0) {
          const adjustment = res.body[0];
          expect(adjustment).toHaveProperty('uuid');
          expect(adjustment).toHaveProperty('userUuid');
          expect(adjustment).toHaveProperty('productUuid');
          expect(adjustment).toHaveProperty('quantityAdjusted');
          expect(adjustment).toHaveProperty('createdAt');
        }
      });
  });

  it('/inventory/stock-adjustment/by-storage/:storageUuid (GET) should return adjustments with enriched data', () => {
    // This test requires a valid storage UUID, so we'll just test the endpoint structure
    const mockStorageUuid = '018ea2bb-6b8d-7e9a-8a2a-7e9a8a2a7e9a';
    
    return request(app.getHttpServer())
      .get(`/inventory/stock-adjustment/by-storage/${mockStorageUuid}`)
      .expect((res) => {
        // Should either return 200 with data or 404 if storage doesn't exist
        expect([200, 404]).toContain(res.status);
        if (res.status === 200) {
          expect(Array.isArray(res.body)).toBe(true);
          if (res.body.length > 0) {
            const adjustment = res.body[0];
            expect(adjustment).toHaveProperty('uuid');
            expect(adjustment).toHaveProperty('userUuid');
            expect(adjustment).toHaveProperty('userEmail');
            expect(adjustment).toHaveProperty('productUuid');
            expect(adjustment).toHaveProperty('productName');
            expect(adjustment).toHaveProperty('productSku');
            expect(adjustment).toHaveProperty('quantityAdjusted');
            expect(adjustment).toHaveProperty('createdAt');
          }
        }
      });
  });
}); 