import 'package:equatable/equatable.dart';

abstract class AppError extends Equatable {

  const AppError({
    required this.message,
    this.code,
    this.data,
  });
  final String message;
  final String? code;
  final dynamic data;

  @override
  List<Object?> get props => [message, code, data];
}

class NetworkError extends AppError {
  const NetworkError({
    required super.message,
    super.code,
    super.data,
  });
}

class ServerError extends AppError {
  const ServerError({
    required super.message,
    super.code,
    super.data,
  });
}

class AuthenticationError extends AppError {
  const AuthenticationError({
    required super.message,
    super.code,
    super.data,
  });
}

class ValidationError extends AppError {
  const ValidationError({
    required super.message,
    super.code,
    super.data,
  });
}

class PermissionError extends AppError {
  const PermissionError({
    required super.message,
    super.code,
    super.data,
  });
}

class DataError extends AppError {
  const DataError({
    required super.message,
    super.code,
    super.data,
  });
}

class CacheError extends AppError {
  const CacheError({
    required super.message,
    super.code,
    super.data,
  });
}

class DeviceError extends AppError {
  const DeviceError({
    required super.message,
    super.code,
    super.data,
  });
}

class UnknownError extends AppError {
  const UnknownError({
    required super.message,
    super.code,
    super.data,
  });
}

class ErrorHandler {
  static AppError handleError(error) {
    if (error is AppError) {
      return error;
    }

    // Handle different types of errors
    if (error.toString().contains('SocketException') ||
        error.toString().contains('NetworkException')) {
      return const NetworkError(
        message: 'Network connection error. Please check your internet connection.',
        code: 'NETWORK_ERROR',
      );
    }

    if (error.toString().contains('TimeoutException')) {
      return const NetworkError(
        message: 'Request timeout. Please try again.',
        code: 'TIMEOUT_ERROR',
      );
    }

    if (error.toString().contains('FormatException')) {
      return const DataError(
        message: 'Invalid data format received.',
        code: 'FORMAT_ERROR',
      );
    }

    if (error.toString().contains('Unauthorized') ||
        error.toString().contains('401')) {
      return const AuthenticationError(
        message: 'Authentication failed. Please login again.',
        code: 'AUTH_ERROR',
      );
    }

    if (error.toString().contains('Forbidden') ||
        error.toString().contains('403')) {
      return const PermissionError(
        message: 'Permission denied. You don\'t have access to this resource.',
        code: 'PERMISSION_ERROR',
      );
    }

    if (error.toString().contains('Not Found') ||
        error.toString().contains('404')) {
      return const DataError(
        message: 'Requested data not found.',
        code: 'NOT_FOUND_ERROR',
      );
    }

    if (error.toString().contains('500')) {
      return const ServerError(
        message: 'Server error occurred. Please try again later.',
        code: 'SERVER_ERROR',
      );
    }

    // Default error
    return UnknownError(
      message: 'An unexpected error occurred: ${error.toString()}',
      code: 'UNKNOWN_ERROR',
      data: error,
    );
  }

  static String getErrorMessage(AppError error) {
    switch (error.runtimeType) {
      case NetworkError:
        return 'Network connection error. Please check your internet connection.';
      case ServerError:
        return 'Server error occurred. Please try again later.';
      case AuthenticationError:
        return 'Authentication failed. Please login again.';
      case ValidationError:
        return 'Please check your input and try again.';
      case PermissionError:
        return 'Permission denied. You don\'t have access to this resource.';
      case DataError:
        return 'Data error occurred. Please try again.';
      case CacheError:
        return 'Cache error occurred. Please clear cache and try again.';
      case DeviceError:
        return 'Device error occurred. Please check device permissions.';
      default:
        return error.message;
    }
  }

  static bool isRetryable(AppError error) => error is NetworkError || error is ServerError;

  static bool requiresLogin(AppError error) => error is AuthenticationError;
} 