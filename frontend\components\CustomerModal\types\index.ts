// Global Customer Modal type definitions

export interface Customer {
  uuid: string;
  name: string;
  fiscalId: string;
  email?: string;
  phone?: string;
  address?: string;
  rc?: string;
  articleNumber?: string;
  customerType: 'retail' | 'wholesale' | 'mid-wholesale' | 'institutional';
  latitude?: number;
  longitude?: number;
  warehouseUuidString?: string;
  regionUuidString?: string;
  isDeleted: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateCustomerDto {
  name: string;
  fiscalId?: string;
  email?: string;
  phone?: string;
  address?: string;
  rc?: string;
  articleNumber?: string;
  customerType: 'retail' | 'wholesale' | 'mid-wholesale' | 'institutional';
  latitude?: number;
  longitude?: number;
  warehouseUuid?: string;
  regionUuid?: string;
}

export interface CustomerFilter {
  name?: string;
  fiscalId?: string;
  email?: string;
  phone?: string;
  address?: string;
  rc?: string;
  articleNumber?: string;
  customerType?: 'retail' | 'wholesale' | 'mid-wholesale' | 'institutional';
  warehouseUuid?: string;
  regionUuid?: string;
  isDeleted?: boolean;
}

export interface CustomerModalProps {
  isOpen: boolean;
  customers?: Customer[]; // Keep for backward compatibility but won't use
  onSelect: (customer: Customer | null) => void;
  onClose: () => void;
  onCreateNew: (customer: CreateCustomerDto) => void;
  disabled?: boolean;
}

export interface ValidationError {
  field: string;
  message: string;
}

export interface FormValidationResult {
  isValid: boolean;
  errors: string[];
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  hasNext: boolean;
  hasPrev: boolean;
  totalPages: number;
} 