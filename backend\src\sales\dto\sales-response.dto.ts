import { ApiProperty } from "@nestjs/swagger";
import {
  SaleStatus,
  PaymentMethods,
} from "../sale.entity";
import { Uuid7 } from "../../utils/uuid7";

export class SaleItemSnapshotResponseDto {
  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the product",
  })
  productUuidString: string;

  @ApiProperty({
    example: "Product Name",
    description: "The name of the product",
  })
  productName: string;

  @ApiProperty({ example: 5, description: "Quantity of the product sold" })
  quantity: number;

  @ApiProperty({ example: 10.5, description: "Unit price of the product" })
  unitPrice: number;

  @ApiProperty({ example: 52.5, description: "Total line amount" })
  lineTotal: number;

  @ApiProperty({ example: 5.25, description: "Tax amount for this line item" })
  taxAmount: number;
}

export class SaleResponseDto {
  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the sale",
  })
  uuid: string;

  @ApiProperty({ example: "INV-1234567890", description: "Invoice number" })
  invoiceNumber: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the customer",
  })
  customerUuidString: string;

  @ApiProperty({
    example: "Customer Name",
    description: "The name of the customer",
  })
  customerName: string;

  @ApiProperty({
    example: "12345678901",
    description: "Customer fiscal ID at time of sale",
  })
  customerFiscalId?: string;

  @ApiProperty({
    example: "RC123456",
    description: "Customer RC at time of sale",
  })
  customerRc?: string;

  @ApiProperty({
    example: "ART001",
    description: "Customer article number at time of sale",
  })
  customerArticleNumber?: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the user who created the sale",
  })
  createdByString: string;

  @ApiProperty({
    example: "John Doe",
    description: "The name of the user who created the sale",
  })
  createdByName: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the user who last updated the sale",
  })
  updatedByString: string;

  @ApiProperty({
    example: "Jane Smith",
    description: "The name of the user who last updated the sale",
  })
  updatedByName: string;

  @ApiProperty({
    type: [SaleItemSnapshotResponseDto],
    description: "Items in the sale",
  })
  itemsSnapshot: SaleItemSnapshotResponseDto[];

  @ApiProperty({ example: 150.0, description: "Subtotal amount before tax" })
  subtotal: number;

  @ApiProperty({
    example: true,
    description: "Whether tax is applied to this sale",
  })
  useTax: boolean;

  @ApiProperty({
    example: 0.1,
    description: "Tax rate as decimal (e.g., 0.1 for 10%)",
  })
  taxRate: number;

  @ApiProperty({ example: 15.0, description: "Total tax amount" })
  taxAmount: number;

  @ApiProperty({
    example: 165.0,
    description: "Total amount of the sale (including tax)",
  })
  totalAmount: number;

  @ApiProperty({ example: 100.0, description: "Amount paid" })
  amountPaid: number;

  @ApiProperty({ example: 65.0, description: "Balance due" })
  balanceDue: number;

  @ApiProperty({
    enum: Object.values(PaymentMethods),
    example: PaymentMethods.CASH,
    description: "Payment method used",
  })
  paymentMethod: PaymentMethods;

  @ApiProperty({
    type: [Date],
    example: ["2024-01-15T10:30:00Z"],
    description: "Payment dates",
  })
  paymentDate?: Date[];

  @ApiProperty({ example: "2024-01-15T10:30:00Z", description: "Invoice date" })
  invoiceDate: Date;

  @ApiProperty({ example: "2024-02-14T10:30:00Z", description: "Due date" })
  dueDate: Date;

  @ApiProperty({
    enum: Object.values(SaleStatus),
    example: SaleStatus.PAID,
    description: "Status of the sale",
  })
  status: SaleStatus;

  @ApiProperty({
    example: "2024-01-15T10:30:00Z",
    description: "Creation timestamp",
  })
  createdAt: Date;

  @ApiProperty({
    example: "2024-01-15T11:30:00Z",
    description: "Last update timestamp",
  })
  updatedAt: Date;
}

export class SalesListResponseDto {
  @ApiProperty({ type: [SaleResponseDto], description: "List of sales" })
  data: SaleResponseDto[];

  @ApiProperty({ example: 100, description: "Total number of sales" })
  total: number;

  @ApiProperty({ example: 1, description: "Current page number" })
  page: number;

  @ApiProperty({ example: 10, description: "Number of items per page" })
  limit: number;

  @ApiProperty({ example: 10, description: "Total number of pages" })
  totalPages: number;

  constructor(
    data: SaleResponseDto[],
    total: number,
    page: number,
    limit: number,
    totalPages: number,
  ) {
    this.data = data;
    this.total = total;
    this.page = page;
    this.limit = limit;
    this.totalPages = totalPages;
  }
}

import { Sale } from "../sale.entity";
import { SaleItem } from "../sale-item.entity";

// Utility function to convert SaleItem to enriched DTO
export function toSaleItemSnapshotResponseDto(
  item: SaleItem,
  productName: string = "Unknown Product",
): SaleItemSnapshotResponseDto {
  return {
    productUuidString: item.productUuid,
    productName: productName,
    quantity: Number(item.quantity),
    unitPrice: Number(item.unitPrice),
    lineTotal: Number(item.lineTotal),
    taxAmount: Number(item.taxAmount || 0),
  };
}

// Utility function to convert Sale to enriched DTO
export function toSaleResponseDto(
  sale: Sale,
  customerName: string = "Unknown Customer",
  createdByName: string = "Unknown User",
  updatedByName: string = "Unknown User",
  productNames: Map<string, string> = new Map(),
): SaleResponseDto {
  return {
    uuid: sale.id,
    invoiceNumber: sale.invoiceNumber,
    customerUuidString: sale.customerUuid,
    customerName: customerName,
    customerFiscalId: sale.customerFiscalId,
    customerRc: sale.customerRc,
    customerArticleNumber: sale.customerArticleNumber,
    createdByString: sale.createdBy,
    createdByName: createdByName,
    updatedByString: sale.updatedBy,
    updatedByName: updatedByName,
    itemsSnapshot: sale.saleItems?.map((item) =>
      toSaleItemSnapshotResponseDto(
        item,
        productNames.get(item.productUuid) || "Unknown Product",
      ),
    ) || [],
    subtotal: Number(sale.subtotal || 0),
    useTax: sale.useTax || false,
    taxRate: Number(sale.taxRate || 0),
    taxAmount: Number(sale.taxAmount || 0),
    totalAmount: Number(sale.totalAmount),
    amountPaid: Number(sale.amountPaid),
    balanceDue: Number(sale.balanceDue),
    paymentMethod: sale.paymentMethod,
    paymentDate: sale.paymentDate,
    invoiceDate: sale.invoiceDate,
    dueDate: sale.dueDate,
    status: sale.status,
    createdAt: sale.createdAt,
    updatedAt: sale.updatedAt,
  };
}
