import { ApiProperty } from "@nestjs/swagger";
import {
  IsString,
  IsUUID,
  IsOptional,
  IsN<PERSON>ber,
  IsBoolean,
  Min,
  Max,
} from "class-validator";

export class CreateAccountSettingsDto {
  @ApiProperty({
    example: "018ea2bb-6b8d-7e9a-8a2a-7e9a8a2a7e9a",
    description:
      "UUIDv7 of the user who owns these account settings (required)",
  })
  @IsUUID("all")
  userUuid: string;

  @ApiProperty({
    example: "en",
    description: "Preferred language for the user interface",
    required: false,
  })
  @IsOptional()
  @IsString()
  preferredLanguage?: string;

  @ApiProperty({
    example: "light",
    description: "Preferred theme (light/dark)",
    required: false,
  })
  @IsOptional()
  @IsString()
  preferredTheme?: string;

  @ApiProperty({
    example: "cash",
    description: "Preferred payment method for sales",
    required: false,
  })
  @IsOptional()
  @IsString()
  preferredPaymentMethod?: string;

  @ApiProperty({
    example: "standard",
    description: "Invoice format preference",
    required: false,
  })
  @IsOptional()
  @IsString()
  invoiceFormat?: string;

  @ApiProperty({
    example: 20.0,
    description: "Preferred tax rate percentage (0-100)",
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(100)
  preferredTaxRate?: number;

  @ApiProperty({
    example: true,
    description: "Flag to indicate if tax should be used by default",
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  preferredUseTax?: boolean;

  @ApiProperty({
    example: "basic",
    description: "User account plan type",
    required: false,
  })
  @IsOptional()
  @IsString()
  userAccountPlan?: string;
}
