"use client";
import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useSearchParams } from 'next/navigation';
import { toast } from 'sonner';
import ProtectedRoute from "@/components/ProtectedRoute";
import { FiUser } from 'react-icons/fi';

// Import modular components and hooks
import {
  SalesHeader,
  POSComponent,
  ListComponent,
  SaleDetailsModal,
  SalesCancelModal,
  InvoicePrint
} from './components';
import { useSalesData, useSalesActions } from './hooks';
import { useSalesPOSState } from './hooks/useSalesPOSState';
import { parsePOSUrlParams } from './pos/config/posConfig';
import { updateUrlParams } from './salesHelpers';

// View modes
type ViewMode = 'pos' | 'list';

const SalesPage = () => {
  const { user, refreshAccessToken } = useAuth();
  const warehouseUuid = user?.warehouseUuid;
  const searchParams = useSearchParams();
  
  // View mode state - default to 'list' unless POS is specifically requested
  const [viewMode, setViewMode] = useState<ViewMode>('list');
  
  // Use custom hooks for sales data and actions
  const salesData = useSalesData();
  const salesActions = useSalesActions(salesData.refetch);
  const posState = useSalesPOSState();
  
  // Check URL parameters for POS mode
  const { editSaleUuid, loadSaleUuid, customerUuid } = parsePOSUrlParams(searchParams);
  const shouldShowPOS = searchParams.get('view') === 'pos' || !!salesActions?.editSaleData;
  
  // Set initial view mode based on URL
  useEffect(() => {
    if (shouldShowPOS) {
      setViewMode('pos');
    } else {
      setViewMode('list');
    }
  }, [shouldShowPOS]);

  // Listen for URL parameter changes (for programmatic URL updates)
  useEffect(() => {
    const handleUrlChange = () => {
      const { editSaleUuid, loadSaleUuid, customerUuid } = parsePOSUrlParams(searchParams);
      const newShouldShowPOS = searchParams.get('view') === 'pos' || !!salesActions?.editSaleData;
      
      if (newShouldShowPOS && viewMode !== 'pos') {
        setViewMode('pos');
      } else if (!newShouldShowPOS && viewMode !== 'list') {
        setViewMode('list');
      }
    };

    // Listen for the custom event we dispatch in updateUrlParams
    window.addEventListener('urlParamsChanged', handleUrlChange);
    
    return () => {
      window.removeEventListener('urlParamsChanged', handleUrlChange);
    };
  }, [searchParams, viewMode, salesActions?.editSaleData]);

  // Handle sale completion in POS mode
  const handleSaleComplete = () => {
    // Refresh sales data and switch to list view
    salesData.refetch();
    setViewMode('list');
    // Clear edit sale data when completing sale
    salesActions.clearEditSaleData();
  };

  // Handle view mode changes
  const handleViewModeChange = (mode: ViewMode) => {
    setViewMode(mode);
    // Clear edit sale data when switching away from POS
    if (mode === 'list') {
      salesActions.clearEditSaleData();
    }
  };

  // Handle new sale - switch to POS mode and trigger new sale in POS component
  const handleNewSale = () => {
    const wasInPOSMode = viewMode === 'pos';
    
    setViewMode('pos');
    // Clear URL parameters to start fresh - clear edit parameter and set view to pos
    updateUrlParams({ view: 'pos', edit: '' });
    // Clear edit sale data when starting new sale
    salesActions.clearEditSaleData();
    
    // If we're already in POS mode, trigger immediately, otherwise wait for component mount
    if (wasInPOSMode) {
      window.dispatchEvent(new CustomEvent('newSaleRequest'));
    } else {
      // Trigger new sale in POS component after a short delay to ensure component is mounted
      setTimeout(() => {
        window.dispatchEvent(new CustomEvent('newSaleRequest'));
      }, 100);
    }
  };

  if (!warehouseUuid) {
    return (
      <ProtectedRoute>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <FiUser className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No warehouse assigned</h3>
            <p className="mt-1 text-sm text-gray-500">Please contact your administrator.</p>
          </div>
        </div>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute>
      <div className="h-full flex flex-col">
        {/* Header Component - Always visible */}
        <div className="py-3 px-6 bg-white border-b border-gray-200">
          <SalesHeader 
            viewMode={viewMode}
            onViewModeChange={handleViewModeChange}
            onNewSale={handleNewSale}
          />
        </div>

        {/* Main Content - POS or List Component */}
        <div className="flex-1">
          {viewMode === 'pos' ? (
            <POSComponent 
              onSaleComplete={handleSaleComplete} 
              onNewSale={handleNewSale}
              editSaleData={salesActions.editSaleData}
              posState={posState}
            />
          ) : (
            <ListComponent
              sales={salesData.sales}
              totalPages={salesData.totalPages}
              currentPage={salesData.pagination.page || 1}
              isLoading={salesData.isLoading}
              isLoadingInvoice={salesActions.isLoadingInvoice}
              isLoadingEditSale={salesActions.isLoadingEditSale}
              isLoadingViewDetails={salesActions.isLoadingViewDetails}
              isLoadingPrintInvoice={salesActions.isLoadingPrintInvoice}
              isLoadingCancel={salesActions.isLoadingCancel}
              showFilters={salesData.showFilters}
              filter={salesData.filter}
              onPageChange={salesData.handlePageChange}
              onViewDetails={salesActions.handleViewDetails}
              onEdit={salesActions.handleEdit}
              onPrintInvoice={salesActions.handlePrintInvoice}
              onCancel={salesActions.handleCancel}
              onFilterChange={salesData.updateFilter}
              onClearFilters={salesData.clearFilters}
              onToggleFilters={salesData.toggleFilters}
            />
          )}
        </div>

        {/* Modals - Shared between both views */}
        <SaleDetailsModal
          sale={salesActions.selectedSale}
          isOpen={salesActions.isDetailsModalOpen}
          onClose={salesActions.closeDetailsModal}
        />

        <SalesCancelModal
          isOpen={salesActions.isCancelModalOpen}
          saleToCancel={salesActions.saleToCancel}
          isCancelling={salesActions.isCancelling}
          onConfirm={salesActions.handleConfirmCancel}
          onClose={salesActions.handleCancelModalClose}
        />

        {/* Invoice Print Modal */}
        {salesActions.isInvoiceModalOpen && 
         salesActions.selectedSale && 
         salesActions.companyInfo && 
         salesActions.customerInfo && (
          <InvoicePrint
            sale={salesActions.selectedSale}
            companyInfo={salesActions.companyInfo}
            customerInfo={salesActions.customerInfo}
            isOpen={salesActions.isInvoiceModalOpen}
            onClose={salesActions.closeInvoiceModal}
          />
        )}
      </div>
    </ProtectedRoute>
  );
};

export default SalesPage; 