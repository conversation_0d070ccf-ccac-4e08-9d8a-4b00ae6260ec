"use client";

import React, { useState, useEffect } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { ItemsTable } from "@/components/itemsTable/ItemsTable";
import { Warehouse, createWarehouse, updateWarehouse, deleteWarehouse, fetchWarehouses } from "@/app/settings/warehousesApi";
import { toast } from "react-hot-toast";
import { FiPlus, FiEdit, FiTrash2, FiGrid } from "react-icons/fi";
import ProtectedRoute from "@/components/ProtectedRoute";

// Warehouse form modal component
const WarehouseModal: React.FC<{
  isOpen: boolean;
  onClose: () => void;
  warehouse?: Warehouse;
  onSubmit: (data: { name: string; description?: string }) => Promise<void>;
  isLoading: boolean;
}> = ({ isOpen, onClose, warehouse, onSubmit, isLoading }) => {
  const [name, setName] = useState("");
  const [description, setDescription] = useState("");
  const [errors, setErrors] = useState<{ name?: string; description?: string }>({});

  useEffect(() => {
    if (isOpen) {
      setName(warehouse?.name || "");
      setDescription(warehouse?.description || "");
      setErrors({});
      // Auto-focus first field
      setTimeout(() => {
        const input = document.querySelector('input[name="name"]') as HTMLInputElement;
        if (input) input.focus();
      }, 100);
    }
  }, [isOpen, warehouse]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setErrors({});

    // Validation
    if (!name.trim()) {
      setErrors({ name: "Name is required" });
      return;
    }

    try {
      await onSubmit({ name: name.trim(), description: description.trim() || undefined });
      onClose();
    } catch (error) {
      console.error("Error submitting warehouse:", error);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Escape") {
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-sm mx-4" role="dialog" aria-modal="true" aria-labelledby="warehouse-modal-title">
        <div className="p-6">
          <h2 id="warehouse-modal-title" className="text-xl font-semibold mb-2">
            {warehouse ? "Edit Warehouse" : "Add Warehouse"}
          </h2>
          <p className="text-gray-600 mb-6">
            {warehouse ? "Update warehouse information" : "Create a new warehouse"}
          </p>

          <form onSubmit={handleSubmit} onKeyDown={handleKeyDown} className="space-y-4">
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                Name <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="name"
                name="name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  errors.name ? "border-red-500" : "border-gray-300"
                }`}
                placeholder="Enter warehouse name"
                required
              />
              {errors.name && (
                <p className="mt-1 text-sm text-red-600 flex items-center">
                  <FiGrid className="mr-1" />
                  {errors.name}
                </p>
              )}
            </div>

            <div>
              <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
                Description
              </label>
              <textarea
                id="description"
                name="description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Enter warehouse description (optional)"
                rows={3}
              />
              {errors.description && (
                <p className="mt-1 text-sm text-red-600">{errors.description}</p>
              )}
            </div>

            <div className="flex space-x-3 pt-4">
              <button
                type="button"
                onClick={onClose}
                className="flex-1 px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isLoading}
                className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? (
                  <span className="flex items-center justify-center">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    {warehouse ? "Updating..." : "Creating..."}
                  </span>
                ) : (
                  warehouse ? "Update Warehouse" : "Create Warehouse"
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

const WarehousesPage: React.FC = () => {
  const { user } = useAuth();
  const [warehouses, setWarehouses] = useState<Warehouse[]>([]);
  const [loading, setLoading] = useState(true);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingWarehouse, setEditingWarehouse] = useState<Warehouse | undefined>();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Inject warehouseUuid and userUuid from AuthContext (following project convention)
  const warehouseUuid = user?.warehouseUuid;
  const userUuid = user?.uuid;

  const loadWarehouses = async () => {
    if (!userUuid) return;
    
    // Check if we have a valid token before making API calls
    const storedToken = localStorage.getItem('dido_token');
    if (!storedToken) {
      console.log('WarehousesPage: No valid token found, skipping warehouse fetch');
      return;
    }
    
    setLoading(true);
    setError(null);
    try {
      const data = await fetchWarehouses(userUuid);
      setWarehouses(data);
    } catch (err) {
      console.error("Failed to load warehouses:", err);
      setError("Failed to load warehouses. Please try again.");
      toast.error("Failed to load warehouses");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadWarehouses();
  }, [userUuid]);

  const handleCreateWarehouse = async (data: { name: string; description?: string }) => {
    if (!userUuid) {
      toast.error("User not authenticated");
      return;
    }

    setIsSubmitting(true);
    try {
      await createWarehouse({
        ...data,
        userUuid, // Inject from context
      });
      toast.success("Warehouse created successfully");
      loadWarehouses();
    } catch (err) {
      console.error("Failed to create warehouse:", err);
      toast.error("Failed to create warehouse");
      throw err;
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleUpdateWarehouse = async (data: { name: string; description?: string }) => {
    if (!editingWarehouse) return;

    setIsSubmitting(true);
    try {
      await updateWarehouse(editingWarehouse.uuid, {
        ...data,
        userUuid, // Inject from context
      });
      toast.success("Warehouse updated successfully");
      loadWarehouses();
    } catch (err) {
      console.error("Failed to update warehouse:", err);
      toast.error("Failed to update warehouse");
      throw err;
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDeleteWarehouse = async (warehouse: Warehouse) => {
    if (!confirm(`Are you sure you want to delete "${warehouse.name}"?`)) return;

    try {
      await deleteWarehouse(warehouse.uuid);
      toast.success("Warehouse deleted successfully");
      loadWarehouses();
    } catch (err) {
      console.error("Failed to delete warehouse:", err);
      toast.error("Failed to delete warehouse");
    }
  };

  const handleEditWarehouse = (warehouse: Warehouse) => {
    setEditingWarehouse(warehouse);
    setIsModalOpen(true);
  };

  const handleAddWarehouse = () => {
    setEditingWarehouse(undefined);
    setIsModalOpen(true);
  };

  const handleModalClose = () => {
    setIsModalOpen(false);
    setEditingWarehouse(undefined);
  };

  const handleModalSubmit = async (data: { name: string; description?: string }) => {
    if (editingWarehouse) {
      await handleUpdateWarehouse(data);
    } else {
      await handleCreateWarehouse(data);
    }
  };

  // Keyboard shortcut for adding warehouse (F2)
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === "F2" && !isModalOpen) {
        e.preventDefault();
        handleAddWarehouse();
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [isModalOpen]);

  const columns = [
    { key: "name", header: "Name" },
    { key: "description", header: "Description" },
    {
      key: "actions",
      header: "Actions",
      render: (_: any, row: Warehouse) => (
        <div className="flex items-center justify-center gap-3">
          <button
            className="p-2 rounded-full hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-blue-400"
            title="Edit"
            aria-label="Edit"
            onClick={() => handleEditWarehouse(row)}
          >
            <FiEdit className="w-4 h-4 text-blue-600" />
          </button>
          <button
            className="p-2 rounded-full hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-red-400"
            title="Delete"
            aria-label="Delete"
            onClick={() => handleDeleteWarehouse(row)}
          >
            <FiTrash2 className="w-4 h-4 text-red-600" />
          </button>
        </div>
      ),
      headerClassName: "text-center",
      cellClassName: "text-center align-middle",
    },
  ];

  if (loading) {
    return (
      <ProtectedRoute>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute>
      <div className="p-6">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Warehouses</h1>
            <p className="text-gray-600">Manage your warehouse locations</p>
          </div>
          <button
            onClick={handleAddWarehouse}
            className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <FiPlus className="mr-2" />
            Add Warehouse (F2)
          </button>
        </div>

        {error && (
          <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-md">
            <p className="text-red-600">{error}</p>
          </div>
        )}

        <ItemsTable
          columns={columns}
          data={warehouses}
          noDataText={
            <span className="text-gray-400">
              No warehouses found. Click "Add Warehouse" to create your first warehouse.
            </span>
          }
          containerClassName="bg-white rounded-xl shadow-lg border border-gray-200 w-full"
        />

        <WarehouseModal
          isOpen={isModalOpen}
          onClose={handleModalClose}
          warehouse={editingWarehouse}
          onSubmit={handleModalSubmit}
          isLoading={isSubmitting}
        />
      </div>
    </ProtectedRoute>
  );
};

export default WarehousesPage; 