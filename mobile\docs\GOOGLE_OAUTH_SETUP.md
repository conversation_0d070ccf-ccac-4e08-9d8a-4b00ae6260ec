# Google OAuth Setup Guide for Mobile App

This guide will help you fix the **ApiException: 10 error** that occurs during Google Sign-In in the mobile app.

## 🔍 Problem Analysis

The error logs show:
```
The ApiException: 10 error typically means:
- Backend connectivity check failed: TimeoutException after 0:00:05.000000
- Google login exception: DioException [connection timeout]
```

This indicates two main issues:
1. **SHA-1 Fingerprint Mismatch**: Google OAuth client configuration doesn't match your actual debug keystore
2. **Backend Connectivity**: Network timeout issues preventing authentication

## 🛠️ Step-by-Step Fix

### Step 1: Get Your Actual SHA-1 Fingerprint

The mobile app expects SHA-1: `33:54:56:67:B6:80:EE:B7:EF:52:8A:D7:9C:6D:80:CA:97:1A:19:B8`

But your actual debug keystore likely has a different SHA-1. To get the correct one:

#### Option A: Using Gradle (Recommended)
```bash
cd mobile/android
./gradlew signingReport
```

Look for the SHA1 value under "Variant: debug" section.

#### Option B: Using keytool directly
```bash
keytool -list -v -keystore ~/.android/debug.keystore -alias androiddebugkey -storepass android -keypass android
```

### Step 2: Update Google Cloud Console

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Select your project
3. Navigate to **APIs & Services** → **Credentials**
4. Find your **Android OAuth 2.0 Client ID**
5. Click **Edit**
6. In the **SHA-1 certificate fingerprints** section:
   - Remove the old SHA-1 if it exists
   - Add your **actual SHA-1 fingerprint** from Step 1
7. Click **Save**

### Step 3: Verify Client ID Configuration

Ensure your Android OAuth client has:
- **Package name**: `com.example.dido_distribution_mobile`
- **SHA-1 fingerprint**: Your actual SHA-1 from Step 1

### Step 4: Create Mobile Environment Configuration

Create a `.env` file in the `mobile/` directory:

```env
# Backend API Configuration
API_BASE_URL=http://********:8000
API_TIMEOUT=60000

# Google OAuth Configuration
GOOGLE_CLIENT_SECRET=your_google_client_secret_here

# Development/Debug Settings
DEBUG_MODE=true
LOG_LEVEL=debug
```

**Important**: Replace `your_google_client_secret_here` with your actual Google Client Secret from Google Cloud Console.

### Step 5: Update API Base URL for Your Environment

The default `http://********:8000` works for Android emulator. For other environments:

- **iOS Simulator**: `http://localhost:8000`
- **Physical Device**: `http://YOUR_COMPUTER_IP:8000`

### Step 6: Verify Backend Configuration

Ensure your backend `.env` file has:
```env
GOOGLE_CLIENT_ID=your_google_client_id_here
GOOGLE_CLIENT_SECRET=your_google_client_secret_here
GOOGLE_CALLBACK_URL=http://localhost:8000/auth/google/callback
```

## 🧪 Testing the Fix

1. **Clean and rebuild** your mobile app:
   ```bash
   cd mobile
   flutter clean
   flutter pub get
   flutter run
   ```

2. **Check debug output** for successful configuration:
   ```
   ✅ CONNECTIVITY: Backend is accessible
   ✅ Google Sign-In initialized with scopes: [email, profile]
   ✅ Backend authentication successful!
   ```

3. **Test Google Sign-In** - you should no longer see ApiException: 10

## 🔧 Troubleshooting

### Still Getting ApiException: 10?

1. **Double-check SHA-1 fingerprint** - it must match exactly
2. **Wait 5-10 minutes** after updating Google Cloud Console
3. **Clear app data** and try again
4. **Verify package name** matches exactly

### Backend Connectivity Issues?

1. **Check backend is running**: `curl http://localhost:8000/docs`
2. **Verify network connectivity** from mobile device/emulator
3. **Update API_BASE_URL** in `.env` for your environment
4. **Check firewall settings** if using physical device

### Authentication Still Failing?

1. **Check backend logs** for detailed error messages
2. **Verify Google Client ID** matches between mobile and backend
3. **Ensure user has proper role** assigned in backend
4. **Check JWT token generation** in backend

## 📱 Platform-Specific Notes

### Android
- OAuth Client ID is configured in `android/app/src/main/res/values/strings.xml`
- SHA-1 fingerprint must be added to Google Cloud Console
- Package name: `com.example.dido_distribution_mobile`

### iOS (Future)
- OAuth Client ID will be in `ios/Runner/Info.plist`
- No SHA-1 fingerprint required
- Bundle ID configuration needed

## 🔐 Security Considerations

- Never commit `.env` files to version control
- Use different OAuth clients for debug/release builds
- Rotate client secrets regularly
- Monitor OAuth usage in Google Cloud Console

## 📞 Support

If you're still experiencing issues:
1. Check the mobile app debug logs for detailed error messages
2. Verify all configuration steps above
3. Test with a fresh Google account
4. Ensure backend is accessible and properly configured

The improved error handling in the mobile app will now provide more detailed feedback about what's failing during the authentication process. 