import { Injectable, NotFoundException } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";
import { AccountSettings } from "./account-settings.entity";
import { CreateAccountSettingsDto } from "./dto/create-account-settings.dto";
import { UpdateAccountSettingsDto } from "./dto/update-account-settings.dto";
import { AccountSettingsResponseDto } from "./dto/account-settings-response.dto";

@Injectable()
export class AccountSettingsService {
  constructor(
    @InjectRepository(AccountSettings)
    private accountSettingsRepository: Repository<AccountSettings>,
  ) {}

  async create(createAccountSettingsDto: CreateAccountSettingsDto): Promise<AccountSettingsResponseDto> {
    const accountSettings = this.accountSettingsRepository.create({
      id: AccountSettings.generateId(),
      ...createAccountSettingsDto,
    });

    const savedSettings = await this.accountSettingsRepository.save(accountSettings);
    return this.mapToResponseDto(savedSettings);
  }

  async findByUserId(userId: string): Promise<AccountSettingsResponseDto | null> {
    const accountSettings = await this.accountSettingsRepository.findOne({
      where: { userId, isDeleted: false },
      relations: ["user"],
    });

    return accountSettings ? this.mapToResponseDto(accountSettings) : null;
  }

  async findOrCreateByUserId(userId: string): Promise<AccountSettingsResponseDto> {
    let accountSettings = await this.accountSettingsRepository.findOne({
      where: { userId, isDeleted: false },
      relations: ["user"],
    });

    if (!accountSettings) {
      // Create default account settings
      accountSettings = this.accountSettingsRepository.create({
        id: AccountSettings.generateId(),
        userId,
        preferredLanguage: "en",
        preferredTheme: "light",
        preferredPaymentMethod: "cash",
        invoiceFormat: "standard",
        preferredTaxRate: 20.0,
        preferredUseTax: true,
        userAccountPlan: "basic",
        isDeleted: false,
      });

      accountSettings = await this.accountSettingsRepository.save(accountSettings);
    }

    return this.mapToResponseDto(accountSettings);
  }

  async update(userId: string, updateAccountSettingsDto: UpdateAccountSettingsDto): Promise<AccountSettingsResponseDto> {
    const accountSettings = await this.accountSettingsRepository.findOne({
      where: { userId, isDeleted: false },
    });

    if (!accountSettings) {
      throw new NotFoundException(`Account settings for user ${userId} not found`);
    }

    Object.assign(accountSettings, updateAccountSettingsDto);
    const updatedSettings = await this.accountSettingsRepository.save(accountSettings);
    
    return this.mapToResponseDto(updatedSettings);
  }

  async remove(userId: string): Promise<void> {
    const accountSettings = await this.accountSettingsRepository.findOne({
      where: { userId, isDeleted: false },
    });

    if (!accountSettings) {
      throw new NotFoundException(`Account settings for user ${userId} not found`);
    }

    accountSettings.isDeleted = true;
    await this.accountSettingsRepository.save(accountSettings);
  }

  async findAll(): Promise<AccountSettingsResponseDto[]> {
    const accountSettings = await this.accountSettingsRepository.find({
      where: { isDeleted: false },
      relations: ["user"],
    });

    return accountSettings.map(settings => this.mapToResponseDto(settings));
  }

  async findByUserUuid(userUuid: string): Promise<AccountSettingsResponseDto[]> {
    const accountSettings = await this.accountSettingsRepository.find({
      where: { userId: userUuid, isDeleted: false },
      relations: ["user"],
    });

    return accountSettings.map(settings => this.mapToResponseDto(settings));
  }

  async findOne(uuid: string): Promise<AccountSettingsResponseDto> {
    const accountSettings = await this.accountSettingsRepository.findOne({
      where: { id: uuid, isDeleted: false },
      relations: ["user"],
    });

    if (!accountSettings) {
      throw new NotFoundException(`Account settings with UUID ${uuid} not found`);
    }

    return this.mapToResponseDto(accountSettings);
  }

  async deleteAllAccountSettings(): Promise<{ message: string; deletedCount: number }> {
    const result = await this.accountSettingsRepository.update(
      { isDeleted: false },
      { isDeleted: true }
    );

    return {
      message: "All account settings have been soft deleted",
      deletedCount: result.affected || 0,
    };
  }

  private mapToResponseDto(accountSettings: AccountSettings): AccountSettingsResponseDto {
    return {
      uuid: accountSettings.id,
      userUuidString: accountSettings.userId,
      preferredLanguage: accountSettings.preferredLanguage,
      preferredTheme: accountSettings.preferredTheme,
      preferredPaymentMethod: accountSettings.preferredPaymentMethod,
      invoiceFormat: accountSettings.invoiceFormat,
      preferredTaxRate: accountSettings.preferredTaxRate,
      preferredUseTax: accountSettings.preferredUseTax,
      userAccountPlan: accountSettings.userAccountPlan,
      isDeleted: accountSettings.isDeleted,
      createdAt: accountSettings.createdAt,
      updatedAt: accountSettings.updatedAt,
    };
  }
} 