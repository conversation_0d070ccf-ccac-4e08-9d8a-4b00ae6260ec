name: dido_distribution_mobile
description: Mobile application for Dido Distribution warehouse management system
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  
  # UI & Design
  cupertino_icons: ^1.0.6
  flutter_svg: ^2.0.9
  cached_network_image: ^3.3.0
  shimmer: ^3.0.0
  lottie: ^2.7.0
  
  # State Management
  provider: ^6.1.1
  riverpod: ^2.4.9
  flutter_riverpod: ^2.4.9
  
  # Navigation
  go_router: ^12.1.3
  
  # HTTP & API
  dio: ^5.4.0
  retrofit: ^4.0.3
  json_annotation: ^4.8.1
  
  # Local Storage
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  shared_preferences: ^2.2.2
  flutter_secure_storage: ^9.0.0
  sqflite: ^2.3.0
  
  # Authentication & Security
  local_auth: ^2.1.7
  crypto: ^3.0.3
  google_sign_in: ^6.2.1
  
  # Device Features
  camera: ^0.10.5+5
  image_picker: ^1.0.4
  geolocator: ^10.1.0
  permission_handler: ^11.1.0
  device_info_plus: ^9.1.1
  package_info_plus: ^4.2.0
  
  # Barcode & QR Code
  mobile_scanner: ^3.5.2
  qr_flutter: ^4.1.0
  
  # Connectivity & Sync
  connectivity_plus: ^5.0.2
  internet_connection_checker: ^1.0.0+1
  
  # Push Notifications (commented out to simplify initial setup)
  # firebase_core: ^2.24.2
  # firebase_messaging: ^14.7.10
  # flutter_local_notifications: ^16.3.0
  
  # Printing & Bluetooth (commented out due to namespace issues)
  # flutter_bluetooth_serial: ^0.4.0
  # esc_pos_utils: ^1.1.0
  
  # File Handling (simplified)
  path_provider: ^2.1.1
  # file_picker: ^6.1.1
  # open_file: ^3.3.2
  
  # Date & Time
  intl: ^0.18.1
  
  # Utilities
  uuid: ^4.2.1
  equatable: ^2.0.5
  freezed_annotation: ^2.4.1
  
  # Logging
  logger: ^2.0.2+1
  
  # Environment Configuration
  flutter_dotenv: ^5.1.0
  
  # Form Validation
  formz: ^0.6.1
  
  # Charts & Analytics
  fl_chart: ^0.65.0
  
  # Animations
  animations: ^2.0.8
  
  # Responsive Design
  flutter_screenutil: ^5.9.0
  
  # Testing Utilities (for development)
  mockito: ^5.4.4

dev_dependencies:
  flutter_test:
    sdk: flutter
  
  # Linting
  flutter_lints: ^3.0.1
  
  # Code Generation
  build_runner: ^2.4.7
  retrofit_generator: ^8.0.4
  json_serializable: ^6.7.1
  hive_generator: ^2.0.1
  freezed: ^2.4.6
  
  # Testing
  mockito: ^5.4.4
  integration_test:
    sdk: flutter
  
  # Icons
  flutter_launcher_icons: ^0.11.0

flutter:
  uses-material-design: true
  
  # Assets
  assets:
    - .env

# Flutter Launcher Icons Configuration
flutter_icons:
  android: true
  ios: true
  image_path: "assets/icons/app_icon.png"
  adaptive_icon_background: "#2563EB"
  adaptive_icon_foreground: "assets/icons/app_icon_foreground.png"

# Build Configuration
# Note: flutter configuration is already defined above 