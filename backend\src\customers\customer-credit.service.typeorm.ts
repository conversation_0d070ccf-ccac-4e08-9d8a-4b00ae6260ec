import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository, DataSource } from "typeorm";
import { Customer } from "./customer.entity";
import { CreditAdjustment, CreditAdjustmentType } from "./credit-adjustment.entity";
import { CreateCreditAdjustmentDto } from "./dto/create-credit-adjustment.dto";
import {
  CreditAdjustmentResponseDto,
  toCreditAdjustmentResponseDto,
} from "./dto/credit-adjustment-response.dto";
import { Uuid7 } from "../utils/uuid7";

@Injectable()
export class CustomerCreditService {
  constructor(
    @InjectRepository(Customer)
    private customerRepository: Repository<Customer>,
    @InjectRepository(CreditAdjustment)
    private creditAdjustmentRepository: Repository<CreditAdjustment>,
    private dataSource: DataSource,
  ) {}

  async createCreditAdjustment(
    createAdjustmentDto: CreateCreditAdjustmentDto,
  ): Promise<CreditAdjustmentResponseDto> {
    // Verify customer exists
    const customer = await this.customerRepository.findOne({
      where: { id: createAdjustmentDto.customerUuid, isDeleted: false }
    });

    if (!customer) {
      throw new NotFoundException(`Customer with UUID ${createAdjustmentDto.customerUuid} not found`);
    }

    // Validate amount
    if (createAdjustmentDto.amountAdjusted === 0) {
      throw new BadRequestException("Amount adjusted cannot be zero");
    }

    // Use transaction to ensure data consistency
    return await this.dataSource.transaction(async (manager) => {
      const adjustment = new CreditAdjustment();
      adjustment.id = CreditAdjustment.generateId();
      adjustment.customerUuid = createAdjustmentDto.customerUuid;
      adjustment.userUuid = createAdjustmentDto.userUuid;
      adjustment.warehouseUuid = createAdjustmentDto.warehouseUuid;
      adjustment.saleUuid = createAdjustmentDto.saleUuid;
      adjustment.adjustmentType = createAdjustmentDto.adjustmentType;
      adjustment.amountAdjusted = createAdjustmentDto.amountAdjusted;
      adjustment.previousBalance = customer.currentCredit;
      adjustment.newBalance = customer.currentCredit + createAdjustmentDto.amountAdjusted;
      adjustment.reason = createAdjustmentDto.reason;

      // Save adjustment
      const savedAdjustment = await manager.save(CreditAdjustment, adjustment);

      // Update customer credit
      customer.currentCredit = adjustment.newBalance;
      await manager.save(Customer, customer);

      return toCreditAdjustmentResponseDto(savedAdjustment);
    });
  }

  async getCustomerCreditBalance(customerUuid: string): Promise<number> {
    const customer = await this.customerRepository.findOne({
      where: { id: customerUuid, isDeleted: false }
    });

    if (!customer) {
      throw new NotFoundException(`Customer with UUID ${customerUuid} not found`);
    }

    return customer.currentCredit;
  }

  async getCustomerCreditHistory(
    customerUuid: string,
    page: number = 1,
    limit: number = 10,
  ): Promise<{
    data: CreditAdjustment[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    // Verify customer exists
    const customer = await this.customerRepository.findOne({
      where: { id: customerUuid, isDeleted: false }
    });

    if (!customer) {
      throw new NotFoundException(`Customer with UUID ${customerUuid} not found`);
    }

    const queryBuilder = this.creditAdjustmentRepository.createQueryBuilder('adjustment');
    queryBuilder.where('adjustment.customerUuid = :customerUuid', { customerUuid });

    // Get total count
    const total = await queryBuilder.getCount();

    // Apply pagination
    const offset = (page - 1) * limit;
    queryBuilder.skip(offset).take(limit);

    // Apply ordering
    queryBuilder.orderBy('adjustment.createdAt', 'DESC');

    const data = await queryBuilder.getMany();
    const totalPages = Math.ceil(total / limit);

    return {
      data,
      total,
      page,
      limit,
      totalPages,
    };
  }

  async adjustCustomerCredit(
    customerUuid: string,
    amount: number,
    reason: string,
    userUuid: string,
    warehouseUuid: string,
    adjustmentType: CreditAdjustmentType = CreditAdjustmentType.MANUAL,
    saleUuid?: string,
  ): Promise<CreditAdjustmentResponseDto> {
    if (amount === 0) {
      throw new BadRequestException("Amount cannot be zero");
    }

    const createAdjustmentDto: CreateCreditAdjustmentDto = {
      customerUuid,
      userUuid,
      warehouseUuid,
      saleUuid,
      adjustmentType,
      amountAdjusted: amount,
      reason,
    };

    return this.createCreditAdjustment(createAdjustmentDto);
  }

  async getCreditAdjustment(uuid: string): Promise<CreditAdjustmentResponseDto> {
    const adjustment = await this.creditAdjustmentRepository.findOne({
      where: { id: uuid }
    });

    if (!adjustment) {
      throw new NotFoundException(`Credit adjustment with UUID ${uuid} not found`);
    }

    return toCreditAdjustmentResponseDto(adjustment);
  }

  async getAllCreditAdjustments(
    page: number = 1,
    limit: number = 10,
  ): Promise<{
    data: CreditAdjustment[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    const queryBuilder = this.creditAdjustmentRepository.createQueryBuilder('adjustment');

    // Get total count
    const total = await queryBuilder.getCount();

    // Apply pagination
    const offset = (page - 1) * limit;
    queryBuilder.skip(offset).take(limit);

    // Apply ordering
    queryBuilder.orderBy('adjustment.createdAt', 'DESC');

    const data = await queryBuilder.getMany();
    const totalPages = Math.ceil(total / limit);

    return {
      data,
      total,
      page,
      limit,
      totalPages,
    };
  }

  async getCustomerCredit(customerUuid: string): Promise<number> {
    return this.getCustomerCreditBalance(customerUuid);
  }

  async adjustCustomerCreditAllowingNegative(
    createAdjustmentDto: CreateCreditAdjustmentDto,
  ): Promise<CreditAdjustmentResponseDto> {
    // This method allows negative credit adjustments
    return this.createCreditAdjustment(createAdjustmentDto);
  }

  async getCreditAdjustmentHistory(
    customerUuid: string,
    page: number = 1,
    limit: number = 10,
  ): Promise<{
    data: CreditAdjustment[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    return this.getCustomerCreditHistory(customerUuid, page, limit);
  }
} 