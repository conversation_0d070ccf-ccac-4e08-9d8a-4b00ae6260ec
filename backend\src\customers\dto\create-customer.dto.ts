import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>al,
  IsEmail,
  IsIn,
  IsNumber,
  ValidateIf,
  Min,
  IsUUID,
} from "class-validator";
import { ApiProperty } from "@nestjs/swagger";

export class CreateCustomerDto {
  @IsString()
  @ApiProperty({ example: "<PERSON> Do<PERSON>", description: "Customer name (required)" })
  name: string;

  @ValidateIf((o) => o.fiscalId !== undefined && o.fiscalId !== "")
  @IsString()
  @ApiProperty({
    example: "12345678901",
    description: "Fiscal identification number (tax ID, optional)",
    required: false,
  })
  fiscalId?: string;

  @ValidateIf((o) => o.email !== undefined && o.email !== "")
  @IsEmail({}, { message: "Invalid email format" })
  @ApiProperty({
    example: "<EMAIL>",
    description: "Customer email (optional)",
    required: false,
  })
  email?: string;

  @ValidateIf((o) => o.phone !== undefined && o.phone !== "")
  @IsString()
  @ApiProperty({
    example: "+1234567890",
    description: "Customer phone (optional)",
    required: false,
  })
  phone?: string;

  @ValidateIf((o) => o.address !== undefined && o.address !== "")
  @IsString()
  @ApiProperty({
    example: "123 Main St, City, Country",
    description: "Customer address (optional)",
    required: false,
  })
  address?: string;

  @ValidateIf((o) => o.rc !== undefined && o.rc !== "")
  @IsString()
  @ApiProperty({
    example: "RC123456",
    description: "Commercial Register number (optional)",
    required: false,
  })
  rc?: string;

  @ValidateIf((o) => o.articleNumber !== undefined && o.articleNumber !== "")
  @IsString()
  @ApiProperty({
    example: "ART001",
    description: "Article number (optional)",
    required: false,
  })
  articleNumber?: string;

  @IsString()
  @IsIn(["retail", "wholesale", "mid-wholesale", "institutional"])
  @ApiProperty({
    example: "retail",
    description: "Customer type (required)",
    enum: ["retail", "wholesale", "mid-wholesale", "institutional"],
  })
  customerType: "retail" | "wholesale" | "mid-wholesale" | "institutional";

  @ValidateIf((o) => o.latitude !== undefined && o.latitude !== 0)
  @IsNumber()
  @ApiProperty({
    example: 40.7128,
    description: "Latitude coordinate for customer location (optional)",
    required: false,
  })
  latitude?: number;

  @ValidateIf((o) => o.longitude !== undefined && o.longitude !== 0)
  @IsNumber()
  @ApiProperty({
    example: -74.006,
    description: "Longitude coordinate for customer location (optional)",
    required: false,
  })
  longitude?: number;

  @ValidateIf((o) => o.warehouseUuid !== undefined && o.warehouseUuid !== "")
  @IsUUID("7")
  @ApiProperty({
    example: "uuid-v7-string",
    description: "UUID of the warehouse this customer belongs to (optional)",
    required: false,
  })
  warehouseUuid?: string;

  @ValidateIf((o) => o.regionUuid !== undefined && o.regionUuid !== "")
  @IsUUID("7")
  @ApiProperty({
    example: "uuid-v7-string",
    description: "UUID of the region this customer belongs to (optional)",
    required: false,
  })
  regionUuid?: string;

  @ValidateIf((o) => o.currentCredit !== undefined && o.currentCredit !== 0)
  @IsNumber()
  @Min(0)
  @ApiProperty({
    example: 0.0,
    description:
      "Initial credit balance for the customer (optional, defaults to 0)",
    required: false,
  })
  currentCredit?: number;
}
