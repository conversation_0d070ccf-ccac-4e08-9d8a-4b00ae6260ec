const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Get migration name from command line arguments
const migrationName = process.argv[2];

if (!migrationName) {
    console.error('Please provide a migration name');
    console.error('Usage: node scripts/generate-migration.js <migration-name>');
    process.exit(1);
}

try {
    // Generate the migration
    console.log(`Generating migration: ${migrationName}`);
    execSync(`npm run typeorm migration:generate -- -d src/data-source.ts ${migrationName}`, { 
        stdio: 'inherit',
        cwd: __dirname + '/..'
    });

    // Find the generated migration file in the current directory
    const files = fs.readdirSync('.');
    const migrationFile = files.find(file => 
        file.endsWith('.ts') && 
        file.match(/^\d+-.*\.ts$/) && 
        file.toLowerCase().includes(migrationName.toLowerCase())
    );

    if (migrationFile) {
        // Move the file to the migration directory
        const sourcePath = path.join('.', migrationFile);
        const destPath = path.join('migration', migrationFile);
        
        fs.renameSync(sourcePath, destPath);
        console.log(`Migration moved to: ${destPath}`);
    } else {
        console.log('Migration file not found in current directory');
    }

} catch (error) {
    console.error('Error generating migration:', error.message);
    process.exit(1);
} 