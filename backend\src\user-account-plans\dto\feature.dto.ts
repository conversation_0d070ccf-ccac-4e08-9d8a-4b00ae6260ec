import { ApiProperty } from "@nestjs/swagger";
import { FeatureIdentifier } from "../enums/feature.enum";

export class FeatureDto {
  @ApiProperty({
    example: "INVENTORY_TRACKING",
    description: "The identifier of the feature",
    enum: FeatureIdentifier,
  })
  identifier: FeatureIdentifier;

  @ApiProperty({
    example: "Inventory Tracking",
    description: "The display name of the feature",
  })
  name: string;
}
