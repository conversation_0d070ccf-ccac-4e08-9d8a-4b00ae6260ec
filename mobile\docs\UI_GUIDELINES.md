# Mobile UI Guidelines

## Overview

This document outlines the UI/UX guidelines for the Dido Distribution mobile application. These guidelines ensure consistency, accessibility, and optimal user experience across all mobile platforms while maintaining brand identity and usability standards.

## 🎨 Design System

### Color Palette

#### Primary Colors
```dart
class AppColors {
  // Primary Brand Colors
  static const Color primary = Color(0xFF2563EB);      // Blue-600
  static const Color primaryLight = Color(0xFF3B82F6);  // Blue-500
  static const Color primaryDark = Color(0xFF1D4ED8);   // Blue-700
  
  // Secondary Colors
  static const Color secondary = Color(0xFF64748B);     // Slate-500
  static const Color secondaryLight = Color(0xFF94A3B8); // Slate-400
  static const Color secondaryDark = Color(0xFF475569);  // Slate-600
  
  // Accent Colors
  static const Color accent = Color(0xFF10B981);        // Emerald-500
  static const Color accentLight = Color(0xFF34D399);   // Emerald-400
  static const Color accentDark = Color(0xFF059669);    // Emerald-600
}
```

#### Status Colors
```dart
class StatusColors {
  // Success States
  static const Color success = Color(0xFF10B981);       // Green-500
  static const Color successLight = Color(0xFFD1FAE5);  // Green-100
  static const Color successDark = Color(0xFF065F46);   // Green-800
  
  // Warning States
  static const Color warning = Color(0xFFF59E0B);       // Amber-500
  static const Color warningLight = Color(0xFFFEF3C7);  // Amber-100
  static const Color warningDark = Color(0xFF92400E);   // Amber-800
  
  // Error States
  static const Color error = Color(0xFFEF4444);         // Red-500
  static const Color errorLight = Color(0xFFFEE2E2);    // Red-100
  static const Color errorDark = Color(0xFF991B1B);     // Red-800
  
  // Info States
  static const Color info = Color(0xFF3B82F6);          // Blue-500
  static const Color infoLight = Color(0xFFDBEAFE);     // Blue-100
  static const Color infoDark = Color(0xFF1E40AF);      // Blue-800
}
```

#### Neutral Colors
```dart
class NeutralColors {
  static const Color white = Color(0xFFFFFFFF);
  static const Color black = Color(0xFF000000);
  
  // Gray Scale
  static const Color gray50 = Color(0xFFF9FAFB);
  static const Color gray100 = Color(0xFFF3F4F6);
  static const Color gray200 = Color(0xFFE5E7EB);
  static const Color gray300 = Color(0xFFD1D5DB);
  static const Color gray400 = Color(0xFF9CA3AF);
  static const Color gray500 = Color(0xFF6B7280);
  static const Color gray600 = Color(0xFF4B5563);
  static const Color gray700 = Color(0xFF374151);
  static const Color gray800 = Color(0xFF1F2937);
  static const Color gray900 = Color(0xFF111827);
}
```

### Typography

#### Font Families
```dart
class AppFonts {
  static const String primary = 'Inter';
  static const String secondary = 'Roboto';
  static const String monospace = 'JetBrains Mono';
}
```

#### Text Styles
```dart
class AppTextStyles {
  // Headings
  static const TextStyle h1 = TextStyle(
    fontSize: 32,
    fontWeight: FontWeight.bold,
    height: 1.2,
    letterSpacing: -0.5,
  );
  
  static const TextStyle h2 = TextStyle(
    fontSize: 24,
    fontWeight: FontWeight.bold,
    height: 1.3,
    letterSpacing: -0.25,
  );
  
  static const TextStyle h3 = TextStyle(
    fontSize: 20,
    fontWeight: FontWeight.w600,
    height: 1.4,
  );
  
  static const TextStyle h4 = TextStyle(
    fontSize: 18,
    fontWeight: FontWeight.w600,
    height: 1.4,
  );
  
  // Body Text
  static const TextStyle bodyLarge = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.normal,
    height: 1.5,
  );
  
  static const TextStyle bodyMedium = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.normal,
    height: 1.5,
  );
  
  static const TextStyle bodySmall = TextStyle(
    fontSize: 12,
    fontWeight: FontWeight.normal,
    height: 1.4,
  );
  
  // Labels
  static const TextStyle labelLarge = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w500,
    height: 1.4,
  );
  
  static const TextStyle labelMedium = TextStyle(
    fontSize: 12,
    fontWeight: FontWeight.w500,
    height: 1.3,
  );
  
  static const TextStyle labelSmall = TextStyle(
    fontSize: 10,
    fontWeight: FontWeight.w500,
    height: 1.2,
  );
}
```

### Spacing System

```dart
class AppSpacing {
  // Base spacing unit (4px)
  static const double unit = 4.0;
  
  // Spacing scale
  static const double xs = unit;           // 4px
  static const double sm = unit * 2;       // 8px
  static const double md = unit * 3;       // 12px
  static const double lg = unit * 4;       // 16px
  static const double xl = unit * 6;       // 24px
  static const double xxl = unit * 8;      // 32px
  static const double xxxl = unit * 12;    // 48px
  
  // Component-specific spacing
  static const double cardPadding = lg;
  static const double screenPadding = lg;
  static const double listItemPadding = md;
  static const double buttonPadding = md;
}
```

### Border Radius

```dart
class AppBorderRadius {
  static const double xs = 4.0;
  static const double sm = 6.0;
  static const double md = 8.0;
  static const double lg = 12.0;
  static const double xl = 16.0;
  static const double xxl = 24.0;
  static const double full = 999.0;
  
  // Component-specific radius
  static const double button = md;
  static const double card = lg;
  static const double input = md;
  static const double modal = xl;
}
```

## 📱 Component Guidelines

### Buttons

#### Primary Button
```dart
class PrimaryButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final bool isLoading;
  final IconData? icon;
  
  const PrimaryButton({
    Key? key,
    required this.text,
    this.onPressed,
    this.isLoading = false,
    this.icon,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      onPressed: isLoading ? null : onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(
          horizontal: AppSpacing.lg,
          vertical: AppSpacing.md,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppBorderRadius.button),
        ),
        elevation: 2,
      ),
      child: isLoading
          ? const SizedBox(
              height: 20,
              width: 20,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            )
          : Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (icon != null) ...[
                  Icon(icon, size: 18),
                  const SizedBox(width: AppSpacing.sm),
                ],
                Text(text, style: AppTextStyles.labelLarge),
              ],
            ),
    );
  }
}
```

#### Secondary Button
```dart
class SecondaryButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final IconData? icon;
  
  const SecondaryButton({
    Key? key,
    required this.text,
    this.onPressed,
    this.icon,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return OutlinedButton(
      onPressed: onPressed,
      style: OutlinedButton.styleFrom(
        foregroundColor: AppColors.primary,
        side: const BorderSide(color: AppColors.primary),
        padding: const EdgeInsets.symmetric(
          horizontal: AppSpacing.lg,
          vertical: AppSpacing.md,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppBorderRadius.button),
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (icon != null) ...[
            Icon(icon, size: 18),
            const SizedBox(width: AppSpacing.sm),
          ],
          Text(text, style: AppTextStyles.labelLarge),
        ],
      ),
    );
  }
}
```

### Form Components

#### Text Input Field
```dart
class AppTextField extends StatelessWidget {
  final String label;
  final String? hint;
  final String? errorText;
  final TextEditingController? controller;
  final TextInputType? keyboardType;
  final bool obscureText;
  final bool required;
  final IconData? prefixIcon;
  final IconData? suffixIcon;
  final VoidCallback? onSuffixTap;
  final FormFieldValidator<String>? validator;
  
  const AppTextField({
    Key? key,
    required this.label,
    this.hint,
    this.errorText,
    this.controller,
    this.keyboardType,
    this.obscureText = false,
    this.required = false,
    this.prefixIcon,
    this.suffixIcon,
    this.onSuffixTap,
    this.validator,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        RichText(
          text: TextSpan(
            text: label,
            style: AppTextStyles.labelMedium.copyWith(
              color: NeutralColors.gray700,
            ),
            children: [
              if (required)
                const TextSpan(
                  text: ' *',
                  style: TextStyle(color: StatusColors.error),
                ),
            ],
          ),
        ),
        const SizedBox(height: AppSpacing.sm),
        TextFormField(
          controller: controller,
          keyboardType: keyboardType,
          obscureText: obscureText,
          validator: validator,
          decoration: InputDecoration(
            hintText: hint,
            prefixIcon: prefixIcon != null ? Icon(prefixIcon) : null,
            suffixIcon: suffixIcon != null
                ? IconButton(
                    icon: Icon(suffixIcon),
                    onPressed: onSuffixTap,
                  )
                : null,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppBorderRadius.input),
              borderSide: const BorderSide(color: NeutralColors.gray300),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppBorderRadius.input),
              borderSide: const BorderSide(color: NeutralColors.gray300),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppBorderRadius.input),
              borderSide: const BorderSide(color: AppColors.primary, width: 2),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppBorderRadius.input),
              borderSide: const BorderSide(color: StatusColors.error),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppBorderRadius.input),
              borderSide: const BorderSide(color: StatusColors.error, width: 2),
            ),
            filled: true,
            fillColor: NeutralColors.gray50,
            contentPadding: const EdgeInsets.symmetric(
              horizontal: AppSpacing.md,
              vertical: AppSpacing.md,
            ),
          ),
        ),
        if (errorText != null) ...[
          const SizedBox(height: AppSpacing.xs),
          Text(
            errorText!,
            style: AppTextStyles.bodySmall.copyWith(
              color: StatusColors.error,
            ),
          ),
        ],
      ],
    );
  }
}
```

#### Dropdown Field
```dart
class AppDropdownField<T> extends StatelessWidget {
  final String label;
  final T? value;
  final List<DropdownMenuItem<T>> items;
  final ValueChanged<T?>? onChanged;
  final String? hint;
  final bool required;
  final FormFieldValidator<T>? validator;
  
  const AppDropdownField({
    Key? key,
    required this.label,
    required this.items,
    this.value,
    this.onChanged,
    this.hint,
    this.required = false,
    this.validator,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        RichText(
          text: TextSpan(
            text: label,
            style: AppTextStyles.labelMedium.copyWith(
              color: NeutralColors.gray700,
            ),
            children: [
              if (required)
                const TextSpan(
                  text: ' *',
                  style: TextStyle(color: StatusColors.error),
                ),
            ],
          ),
        ),
        const SizedBox(height: AppSpacing.sm),
        DropdownButtonFormField<T>(
          value: value,
          items: items,
          onChanged: onChanged,
          validator: validator,
          decoration: InputDecoration(
            hintText: hint,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppBorderRadius.input),
              borderSide: const BorderSide(color: NeutralColors.gray300),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppBorderRadius.input),
              borderSide: const BorderSide(color: NeutralColors.gray300),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppBorderRadius.input),
              borderSide: const BorderSide(color: AppColors.primary, width: 2),
            ),
            filled: true,
            fillColor: NeutralColors.gray50,
            contentPadding: const EdgeInsets.symmetric(
              horizontal: AppSpacing.md,
              vertical: AppSpacing.md,
            ),
          ),
        ),
      ],
    );
  }
}
```

### Cards and Containers

#### App Card
```dart
class AppCard extends StatelessWidget {
  final Widget child;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final Color? backgroundColor;
  final double? elevation;
  final VoidCallback? onTap;
  
  const AppCard({
    Key? key,
    required this.child,
    this.padding,
    this.margin,
    this.backgroundColor,
    this.elevation,
    this.onTap,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin,
      child: Material(
        color: backgroundColor ?? Colors.white,
        elevation: elevation ?? 2,
        borderRadius: BorderRadius.circular(AppBorderRadius.card),
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(AppBorderRadius.card),
          child: Padding(
            padding: padding ?? const EdgeInsets.all(AppSpacing.lg),
            child: child,
          ),
        ),
      ),
    );
  }
}
```

#### List Item Card
```dart
class ListItemCard extends StatelessWidget {
  final String title;
  final String? subtitle;
  final Widget? leading;
  final Widget? trailing;
  final VoidCallback? onTap;
  final List<Widget>? actions;
  
  const ListItemCard({
    Key? key,
    required this.title,
    this.subtitle,
    this.leading,
    this.trailing,
    this.onTap,
    this.actions,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return AppCard(
      onTap: onTap,
      margin: const EdgeInsets.symmetric(
        horizontal: AppSpacing.lg,
        vertical: AppSpacing.sm,
      ),
      child: Row(
        children: [
          if (leading != null) ...[
            leading!,
            const SizedBox(width: AppSpacing.md),
          ],
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                if (subtitle != null) ...[
                  const SizedBox(height: AppSpacing.xs),
                  Text(
                    subtitle!,
                    style: AppTextStyles.bodySmall.copyWith(
                      color: NeutralColors.gray600,
                    ),
                  ),
                ],
                if (actions != null) ...[
                  const SizedBox(height: AppSpacing.sm),
                  Row(
                    children: actions!,
                  ),
                ],
              ],
            ),
          ),
          if (trailing != null) ...[
            const SizedBox(width: AppSpacing.md),
            trailing!,
          ],
        ],
      ),
    );
  }
}
```

### Status Indicators

#### Status Badge
```dart
class StatusBadge extends StatelessWidget {
  final String text;
  final StatusBadgeType type;
  final bool isLarge;
  
  const StatusBadge({
    Key? key,
    required this.text,
    required this.type,
    this.isLarge = false,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    final colors = _getColors(type);
    
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: isLarge ? AppSpacing.md : AppSpacing.sm,
        vertical: isLarge ? AppSpacing.sm : AppSpacing.xs,
      ),
      decoration: BoxDecoration(
        color: colors.background,
        borderRadius: BorderRadius.circular(AppBorderRadius.full),
        border: Border.all(color: colors.border),
      ),
      child: Text(
        text,
        style: (isLarge ? AppTextStyles.labelMedium : AppTextStyles.labelSmall)
            .copyWith(color: colors.text),
      ),
    );
  }
  
  _BadgeColors _getColors(StatusBadgeType type) {
    switch (type) {
      case StatusBadgeType.success:
        return _BadgeColors(
          background: StatusColors.successLight,
          border: StatusColors.success,
          text: StatusColors.successDark,
        );
      case StatusBadgeType.warning:
        return _BadgeColors(
          background: StatusColors.warningLight,
          border: StatusColors.warning,
          text: StatusColors.warningDark,
        );
      case StatusBadgeType.error:
        return _BadgeColors(
          background: StatusColors.errorLight,
          border: StatusColors.error,
          text: StatusColors.errorDark,
        );
      case StatusBadgeType.info:
        return _BadgeColors(
          background: StatusColors.infoLight,
          border: StatusColors.info,
          text: StatusColors.infoDark,
        );
    }
  }
}

enum StatusBadgeType { success, warning, error, info }

class _BadgeColors {
  final Color background;
  final Color border;
  final Color text;
  
  _BadgeColors({
    required this.background,
    required this.border,
    required this.text,
  });
}
```

### Loading States

#### Loading Spinner
```dart
class LoadingSpinner extends StatelessWidget {
  final double size;
  final Color? color;
  final double strokeWidth;
  
  const LoadingSpinner({
    Key? key,
    this.size = 24,
    this.color,
    this.strokeWidth = 2,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: size,
      width: size,
      child: CircularProgressIndicator(
        strokeWidth: strokeWidth,
        valueColor: AlwaysStoppedAnimation<Color>(
          color ?? AppColors.primary,
        ),
      ),
    );
  }
}
```

#### Loading Overlay
```dart
class LoadingOverlay extends StatelessWidget {
  final Widget child;
  final bool isLoading;
  final String? message;
  
  const LoadingOverlay({
    Key? key,
    required this.child,
    required this.isLoading,
    this.message,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        child,
        if (isLoading)
          Container(
            color: Colors.black.withOpacity(0.5),
            child: Center(
              child: AppCard(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const LoadingSpinner(size: 32),
                    if (message != null) ...[
                      const SizedBox(height: AppSpacing.md),
                      Text(
                        message!,
                        style: AppTextStyles.bodyMedium,
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ],
                ),
              ),
            ),
          ),
      ],
    );
  }
}
```

## 📱 Screen Layout Guidelines

### Screen Structure
```dart
class AppScreen extends StatelessWidget {
  final String title;
  final Widget body;
  final List<Widget>? actions;
  final Widget? floatingActionButton;
  final Widget? bottomNavigationBar;
  final bool showBackButton;
  
  const AppScreen({
    Key? key,
    required this.title,
    required this.body,
    this.actions,
    this.floatingActionButton,
    this.bottomNavigationBar,
    this.showBackButton = true,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(title),
        actions: actions,
        automaticallyImplyLeading: showBackButton,
        backgroundColor: Colors.white,
        foregroundColor: NeutralColors.gray800,
        elevation: 1,
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(AppSpacing.screenPadding),
          child: body,
        ),
      ),
      floatingActionButton: floatingActionButton,
      bottomNavigationBar: bottomNavigationBar,
    );
  }
}
```

### List Screen Template
```dart
class ListScreen<T> extends StatelessWidget {
  final String title;
  final List<T> items;
  final Widget Function(T item) itemBuilder;
  final VoidCallback? onAdd;
  final String? emptyMessage;
  final bool isLoading;
  final VoidCallback? onRefresh;
  
  const ListScreen({
    Key? key,
    required this.title,
    required this.items,
    required this.itemBuilder,
    this.onAdd,
    this.emptyMessage,
    this.isLoading = false,
    this.onRefresh,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return AppScreen(
      title: title,
      actions: [
        if (onAdd != null)
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: onAdd,
          ),
      ],
      body: LoadingOverlay(
        isLoading: isLoading,
        child: RefreshIndicator(
          onRefresh: onRefresh ?? () async {},
          child: items.isEmpty
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.inbox_outlined,
                        size: 64,
                        color: NeutralColors.gray400,
                      ),
                      const SizedBox(height: AppSpacing.lg),
                      Text(
                        emptyMessage ?? 'No items found',
                        style: AppTextStyles.bodyLarge.copyWith(
                          color: NeutralColors.gray600,
                        ),
                      ),
                    ],
                  ),
                )
              : ListView.builder(
                  itemCount: items.length,
                  itemBuilder: (context, index) => itemBuilder(items[index]),
                ),
        ),
      ),
    );
  }
}
```

## 🎭 Animation Guidelines

### Page Transitions
```dart
class SlidePageRoute<T> extends PageRouteBuilder<T> {
  final Widget child;
  final AxisDirection direction;
  
  SlidePageRoute({
    required this.child,
    this.direction = AxisDirection.left,
  }) : super(
          pageBuilder: (context, animation, secondaryAnimation) => child,
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            final begin = _getBeginOffset(direction);
            const end = Offset.zero;
            const curve = Curves.ease;
            
            final tween = Tween(begin: begin, end: end);
            final curvedAnimation = CurvedAnimation(
              parent: animation,
              curve: curve,
            );
            
            return SlideTransition(
              position: tween.animate(curvedAnimation),
              child: child,
            );
          },
        );
  
  Offset _getBeginOffset(AxisDirection direction) {
    switch (direction) {
      case AxisDirection.up:
        return const Offset(0.0, 1.0);
      case AxisDirection.down:
        return const Offset(0.0, -1.0);
      case AxisDirection.right:
        return const Offset(-1.0, 0.0);
      case AxisDirection.left:
        return const Offset(1.0, 0.0);
    }
  }
}
```

### Micro-interactions
```dart
class AnimatedTapButton extends StatefulWidget {
  final Widget child;
  final VoidCallback? onTap;
  final Duration duration;
  final double scaleValue;
  
  const AnimatedTapButton({
    Key? key,
    required this.child,
    this.onTap,
    this.duration = const Duration(milliseconds: 150),
    this.scaleValue = 0.95,
  }) : super(key: key);
  
  @override
  State<AnimatedTapButton> createState() => _AnimatedTapButtonState();
}

class _AnimatedTapButtonState extends State<AnimatedTapButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  
  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.duration,
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: widget.scaleValue,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
  }
  
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: (_) => _controller.forward(),
      onTapUp: (_) => _controller.reverse(),
      onTapCancel: () => _controller.reverse(),
      onTap: widget.onTap,
      child: AnimatedBuilder(
        animation: _scaleAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: widget.child,
          );
        },
      ),
    );
  }
  
  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
}
```

## 🌍 Accessibility Guidelines

### Semantic Labels
```dart
class AccessibleButton extends StatelessWidget {
  final String label;
  final String? semanticLabel;
  final String? tooltip;
  final VoidCallback? onPressed;
  final Widget child;
  
  const AccessibleButton({
    Key? key,
    required this.label,
    this.semanticLabel,
    this.tooltip,
    this.onPressed,
    required this.child,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return Semantics(
      label: semanticLabel ?? label,
      button: true,
      enabled: onPressed != null,
      child: Tooltip(
        message: tooltip ?? label,
        child: child,
      ),
    );
  }
}
```

### Focus Management
```dart
class FocusableContainer extends StatefulWidget {
  final Widget child;
  final VoidCallback? onFocusChange;
  
  const FocusableContainer({
    Key? key,
    required this.child,
    this.onFocusChange,
  }) : super(key: key);
  
  @override
  State<FocusableContainer> createState() => _FocusableContainerState();
}

class _FocusableContainerState extends State<FocusableContainer> {
  late FocusNode _focusNode;
  
  @override
  void initState() {
    super.initState();
    _focusNode = FocusNode();
    _focusNode.addListener(() {
      if (widget.onFocusChange != null) {
        widget.onFocusChange!();
      }
    });
  }
  
  @override
  Widget build(BuildContext context) {
    return Focus(
      focusNode: _focusNode,
      child: widget.child,
    );
  }
  
  @override
  void dispose() {
    _focusNode.dispose();
    super.dispose();
  }
}
```

## 📱 Platform-Specific Guidelines

### Material Design (Android)
- Use Material Design 3 components
- Follow Android navigation patterns
- Implement proper back button handling
- Use Android-specific animations and transitions

### Cupertino Design (iOS)
- Use Cupertino widgets for iOS-specific UI
- Follow iOS Human Interface Guidelines
- Implement iOS-style navigation
- Use iOS-specific gestures and interactions

### Adaptive Design
```dart
class AdaptiveButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  
  const AdaptiveButton({
    Key? key,
    required this.text,
    this.onPressed,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    if (Theme.of(context).platform == TargetPlatform.iOS) {
      return CupertinoButton(
        onPressed: onPressed,
        child: Text(text),
      );
    } else {
      return ElevatedButton(
        onPressed: onPressed,
        child: Text(text),
      );
    }
  }
}
```

## 🎨 Theme Configuration

### Light Theme
```dart
ThemeData get lightTheme => ThemeData(
  useMaterial3: true,
  colorScheme: ColorScheme.fromSeed(
    seedColor: AppColors.primary,
    brightness: Brightness.light,
  ),
  textTheme: const TextTheme(
    displayLarge: AppTextStyles.h1,
    displayMedium: AppTextStyles.h2,
    displaySmall: AppTextStyles.h3,
    headlineMedium: AppTextStyles.h4,
    bodyLarge: AppTextStyles.bodyLarge,
    bodyMedium: AppTextStyles.bodyMedium,
    bodySmall: AppTextStyles.bodySmall,
    labelLarge: AppTextStyles.labelLarge,
    labelMedium: AppTextStyles.labelMedium,
    labelSmall: AppTextStyles.labelSmall,
  ),
  appBarTheme: const AppBarTheme(
    backgroundColor: Colors.white,
    foregroundColor: NeutralColors.gray800,
    elevation: 1,
  ),
  elevatedButtonTheme: ElevatedButtonThemeData(
    style: ElevatedButton.styleFrom(
      backgroundColor: AppColors.primary,
      foregroundColor: Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppBorderRadius.button),
      ),
    ),
  ),
);
```

### Dark Theme
```dart
ThemeData get darkTheme => ThemeData(
  useMaterial3: true,
  colorScheme: ColorScheme.fromSeed(
    seedColor: AppColors.primary,
    brightness: Brightness.dark,
  ),
  // ... additional dark theme configuration
);
```

---

These UI guidelines ensure a consistent, accessible, and platform-appropriate user experience across the Dido Distribution mobile application. 