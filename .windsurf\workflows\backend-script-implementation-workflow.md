---
description: Backend script workflow with mandatory full backup for Yugabyte PostgreSQL
---

# ⚠️ CRITICAL STEP: FULL DATABASE BACKUP (NO EXCEPTIONS)

**Every script must begin with an automated, full database backup using `pg_dump` (or equivalent).**

> **Use the script:** `backend/scripts/create_backup.py` for all full database backups. This is the only approved way to perform backups for backend scripts. Do not use manual or alternative backup methods.

- _Partial, manual, or selective backups are strictly forbidden._
- _Any script PR or script without a full, automated backup step is INVALID and must be rejected._
- _This is a non-negotiable requirement for all contributors and reviewers._

---

# Workflow

1. **Analyse Documentation:**
   - Carefully review the full content of the following documentation (up to 500 lines each): `README.md`, `UUID_USAGE_GUIDELINES.md`.
2. **Analyse User-Provided Files:**
   - Review all other files provided by the user for the script.
3. **Report Issues:**
   - List all missing functions, features, or issues found and prompt the user for clarification or action.
4. **Fulfill the User Request:**
   - Implement the script as specified by the user request.
5. **Maintain a TODO List:**
   - Keep and regularly update a TODO list for outstanding script-related tasks.
6. **Report Errors:**
   - Proactively report all errors and potential issues encountered during the process.

# Script Requirements

- **Scripts go in the `/script/` folder.**
- **MANDATORY: Before each script runs, a FULL DATABASE BACKUP must be performed.**
  - The backup must use `pg_dump` (or equivalent) to back up the entire database, not just selected tables.
  - The backup step must be automated at the start of the script (not manual, not partial, not only affected tables).
  - The backup location and timestamp must be logged for traceability.
  - If the backup fails, the script must abort and report the failure.

- **Provide Command Lines:**
  - Supply the user with command lines to execute the script and to rollback manually if applicable.

---

## ⚠️ Environment Variable and .env Handling

- **All scripts must use the `DATABASE_URL` environment variable for the database connection string.**
- **The `.env` file in the backend root must define:**
  ```
  DATABASE_URL=postgresql://username:password@host:port/database
  ```
- **Scripts must load the `.env` file automatically using the `dotenv` package:**
  ```js
  require('dotenv').config({ path: require('path').resolve(__dirname, '../.env') });
  ```
- **Never use a hardcoded URI or a differently named variable (e.g., `POSTGRES_URI`).**
- **If the variable is missing or empty, the script must abort with a clear error.**
- **This ensures consistency, prevents accidental connection to the wrong database, and avoids failures due to misconfiguration.**

---

## Database Connection Guidelines

- **Use TypeORM for database operations** as the primary ORM for the project.
- **For direct database connections in scripts, use the `pg` (node-postgres) package.**
- **For API-based operations, use `axios` to call the backend endpoints.**
- **Prefer API calls over direct database connections when possible** to maintain consistency with the application layer.
- **When direct database access is required, use connection pooling and proper error handling.**

---

# Script Author/Reviewer Checklist

- [ ] Automated full DB backup using `pg_dump` (or equivalent) is present at the start of the script
- [ ] No manual, partial, or selective backup logic
- [ ] Backup location and timestamp are logged
- [ ] Script aborts on backup failure
- [ ] Command lines for script execution/rollback are provided if applicable
- [ ] Script loads `.env` and uses `DATABASE_URL` (never `POSTGRES_URI` or any other variable)
- [ ] Clear error if `DATABASE_URL` is missing or invalid
- [ ] Uses TypeORM or `pg` package for database operations
- [ ] Prefers API calls over direct database connections when appropriate
- [ ] Implements proper connection pooling and error handling for direct DB access

---

# Additional Notes

- Never skip, partially implement, or move the backup step outside the script.
- Review this workflow before every script to ensure compliance.
- Update this workflow if backup, environment variable, or script procedures change.
- Yugabyte PostgreSQL is compatible with PostgreSQL tools and drivers, so standard PostgreSQL practices apply.
- Consider the distributed nature of Yugabyte when designing backup and restore procedures. 