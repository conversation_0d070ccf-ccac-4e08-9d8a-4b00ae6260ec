'use client';

import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import ItemsTable from '@/components/itemsTable/ItemsTable';
import { FiFilter, FiRefresh<PERSON>w, <PERSON><PERSON>ye, FiUser, FiX } from 'react-icons/fi';
import { logsApi, type Log } from './logsApi';
import { fetchUserById } from '@/app/settings/users/usersApi';
import { UserModal } from '@/components/UserModal';
import type { User } from '@/app/settings/users/usersApi';
import ProtectedRoute from '@/components/ProtectedRoute';
import { LogDetailsModal } from './components/LogDetailsModal';

interface LogFilter {
  userUuid?: string;
  operation?: string;
  entity?: string;
  description?: string;
  page: number;
  limit: number;
}

export default function LogsPage() {
  const { user } = useAuth();
  const [logs, setLogs] = useState<Log[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [filters, setFilters] = useState<LogFilter>({
    page: 1,
    limit: 20,
  });
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    totalItems: 0,
    itemsPerPage: 20,
  });
  const [availableOperations, setAvailableOperations] = useState<string[]>([]);
  const [availableEntities, setAvailableEntities] = useState<string[]>([]);
  const [showFilters, setShowFilters] = useState(false);
  const [userNames, setUserNames] = useState<Record<string, string>>({});
  const [showUserModal, setShowUserModal] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [showLogDetailsModal, setShowLogDetailsModal] = useState(false);
  const [selectedLog, setSelectedLog] = useState<Log | null>(null);

  // Fetch available operations and entities
  const fetchAvailableOptions = useCallback(async () => {
    try {
      const [operationsRes, entitiesRes] = await Promise.all([
        logsApi.getAvailableOperations(),
        logsApi.getAvailableEntities(),
      ]);
      setAvailableOperations(operationsRes.operations);
      setAvailableEntities(entitiesRes.entities);
    } catch (error) {
      console.error('Error fetching available options:', error);
    }
  }, []);

  // Fetch user names for logs
  const fetchUserNames = useCallback(async (userUuids: string[]) => {
    const newUserNames: Record<string, string> = {};
    const uniqueUuids = Array.from(new Set(userUuids));
    
    for (const uuid of uniqueUuids) {
      if (!userNames[uuid]) {
        try {
          const user = await fetchUserById(uuid);
          newUserNames[uuid] = user.name;
        } catch (error) {
          console.error(`Error fetching user ${uuid}:`, error);
          newUserNames[uuid] = 'Unknown User';
        }
      }
    }
    
    if (Object.keys(newUserNames).length > 0) {
      setUserNames(prev => ({ ...prev, ...newUserNames }));
    }
  }, [userNames]);

  // Fetch logs with current filters
  const fetchLogs = useCallback(async () => {
    setIsLoading(true);
    try {
      const response = await logsApi.getLogs(filters);
      setLogs(response.data);
      
      setPagination({
        currentPage: response.meta.page,
        totalPages: response.meta.totalPages,
        totalItems: response.meta.total,
        itemsPerPage: response.meta.limit,
      });
      
      // Fetch user names for the logs
      const userUuids = response.data.map(log => log.userUuid);
      await fetchUserNames(userUuids);
    } catch (error) {
      console.error('Error fetching logs:', error);
    } finally {
      setIsLoading(false);
    }
  }, [filters, fetchUserNames]);

  // Load data on mount
  useEffect(() => {
    fetchAvailableOptions();
    fetchLogs();
  }, [fetchAvailableOptions, fetchLogs]);

  // Handle filter changes
  const handleFilterChange = (key: keyof LogFilter, value: string | number) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
      page: 1, // Reset to first page when filters change
    }));
  };

  // Handle page change
  const handlePageChange = (page: number) => {
    setFilters(prev => ({ ...prev, page }));
  };

  // Clear all filters
  const clearFilters = () => {
    setFilters({
      page: 1,
      limit: 20,
    });
    setSelectedUser(null);
  };

  // Handle user selection from modal
  const handleUserSelect = (user: User) => {
    setSelectedUser(user);
    handleFilterChange('userUuid', user.uuid);
  };

  // Handle log details modal
  const handleViewLogDetails = (log: Log) => {
    setSelectedLog(log);
    setShowLogDetailsModal(true);
  };

  // Get operation color based on operation type
  const getOperationColor = (operation: string) => {
    const op = operation.toLowerCase();
    if (op.includes('create') || op.includes('add')) {
      return 'bg-green-100 text-green-800 border-green-200';
    } else if (op.includes('update') || op.includes('edit') || op.includes('modify')) {
      return 'bg-blue-100 text-blue-800 border-blue-200';
    } else if (op.includes('delete') || op.includes('remove')) {
      return 'bg-red-100 text-red-800 border-red-200';
    } else if (op.includes('cancel') || op.includes('reject')) {
      return 'bg-orange-100 text-orange-800 border-orange-200';
    } else if (op.includes('approve') || op.includes('complete')) {
      return 'bg-purple-100 text-purple-800 border-purple-200';
    } else if (op.includes('transfer') || op.includes('move')) {
      return 'bg-indigo-100 text-indigo-800 border-indigo-200';
    } else if (op.includes('adjust') || op.includes('modify')) {
      return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    } else {
      return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  // Format date for display
  const formatDate = (date: Date | string) => {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return dateObj.toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    });
  };

  const columns = [
    {
      key: 'operation',
      header: 'Operation',
      render: (value: string) => (
        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${getOperationColor(value)}`}>
          {value}
        </span>
      ),
    },
    {
      key: 'entity',
      header: 'Entity',
      render: (value: string) => (
        <span className="font-medium text-gray-900">{value}</span>
      ),
    },
    {
      key: 'description',
      header: 'Description',
      render: (value: string) => (
        <span className="text-gray-700 text-sm">
          {value || 'No description available'}
        </span>
      ),
    },
    {
      key: 'userUuid',
      header: 'User',
      render: (value: string) => (
        <span className="text-gray-700 font-medium">
          {userNames[value] || 'Loading...'}
        </span>
      ),
    },
    {
      key: 'createdAt',
      header: 'Timestamp',
      render: (value: Date) => (
        <span className="text-gray-600 text-sm">{formatDate(value)}</span>
      ),
    },
    {
      key: 'actions',
      header: 'Actions',
      render: (_: any, row: Log) => (
        <div className="flex items-center justify-center gap-2">
          <button
            onClick={() => handleViewLogDetails(row)}
            className="p-1.5 rounded-full hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-blue-400 transition-colors"
            title="View Details"
            aria-label="View Details"
          >
            <FiEye className="w-4 h-4 text-blue-600" />
          </button>
        </div>
      ),
      headerClassName: 'text-center',
      cellClassName: 'text-center align-middle',
    },
  ];

  return (
    <ProtectedRoute>
      <div className="bg-gray-50 p-6">
        <div className="max-w-7xl mx-auto">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">System Logs</h1>
            <p className="text-gray-600">Monitor system activities and user actions across the platform</p>
          </div>

        {/* Filters Section */}
        <div className="bg-white rounded-xl shadow-lg border border-gray-200 p-6 mb-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold text-gray-900">Filters</h2>
            <div className="flex items-center gap-3">
              <button
                onClick={() => setShowFilters(!showFilters)}
                className="flex items-center gap-2 px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-400 transition-colors"
              >
                <FiFilter className="w-4 h-4" />
                {showFilters ? 'Hide' : 'Show'} Filters
              </button>
              <button
                onClick={clearFilters}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-400 transition-colors"
              >
                Clear All
              </button>
              <button
                onClick={fetchLogs}
                disabled={isLoading}
                className="flex items-center gap-2 px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-400 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                <FiRefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
                Refresh
              </button>
            </div>
          </div>

          {showFilters && (
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              {/* User Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  User
                </label>
                <div className="flex gap-2">
                  <input
                    type="text"
                    value={selectedUser ? selectedUser.name : ''}
                    placeholder="Select a user..."
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-transparent bg-gray-50"
                    readOnly
                  />
                  <button
                    type="button"
                    onClick={() => setShowUserModal(true)}
                    className="px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-400 transition-colors"
                    title="Select User"
                  >
                    <FiUser className="w-4 h-4" />
                  </button>
                  {selectedUser && (
                    <button
                      type="button"
                      onClick={() => {
                        setSelectedUser(null);
                        handleFilterChange('userUuid', '');
                      }}
                      className="px-3 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-400 transition-colors"
                      title="Clear User"
                    >
                      <FiX className="w-4 h-4" />
                    </button>
                  )}
                </div>
              </div>

              {/* Operation Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Operation
                </label>
                <select
                  value={filters.operation || ''}
                  onChange={(e) => handleFilterChange('operation', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-transparent"
                >
                  <option value="">All Operations</option>
                  {availableOperations.map((op) => (
                    <option key={op} value={op}>
                      {op}
                    </option>
                  ))}
                </select>
              </div>

              {/* Entity Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Entity
                </label>
                <select
                  value={filters.entity || ''}
                  onChange={(e) => handleFilterChange('entity', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-transparent"
                >
                  <option value="">All Entities</option>
                  {availableEntities.map((entity) => (
                    <option key={entity} value={entity}>
                      {entity}
                    </option>
                  ))}
                </select>
              </div>

              {/* Description Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Description
                </label>
                <input
                  type="text"
                  value={filters.description || ''}
                  onChange={(e) => handleFilterChange('description', e.target.value)}
                  placeholder="Filter by description..."
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-transparent"
                />
              </div>
            </div>
          )}
        </div>

        {/* Logs Table */}
        <ItemsTable
          columns={columns}
          data={logs}
          isLoading={isLoading}
          loadingText="Loading logs..."
          noDataText={
            <div className="text-center">
              <div className="text-gray-400 text-lg font-medium mb-2">No logs found</div>
              <div className="text-gray-500 text-sm">
                {Object.values(filters).some(v => v && v !== 1 && v !== 20) 
                  ? 'Try adjusting your filters or clear them to see all logs.'
                  : 'No system logs are available at the moment.'
                }
              </div>
            </div>
          }
          containerClassName="bg-white rounded-xl shadow-lg border border-gray-200 w-full"
          pagination={{
            currentPage: pagination.currentPage,
            totalPages: pagination.totalPages,
            onPageChange: handlePageChange,
            totalItems: pagination.totalItems,
            itemsPerPage: pagination.itemsPerPage,
          }}
        />

        {/* Summary Stats */}
        <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="bg-white rounded-xl shadow-lg border border-gray-200 p-4">
            <div className="text-sm font-medium text-gray-500">Total Logs</div>
            <div className="text-2xl font-bold text-gray-900">{pagination.totalItems}</div>
          </div>
          <div className="bg-white rounded-xl shadow-lg border border-gray-200 p-4">
            <div className="text-sm font-medium text-gray-500">Current Page</div>
            <div className="text-2xl font-bold text-gray-900">{pagination.currentPage} / {pagination.totalPages}</div>
          </div>
        </div>
      </div>
    </div>

      {/* User Selection Modal */}
      <UserModal
        isOpen={showUserModal}
        onSelect={handleUserSelect}
        onClose={() => setShowUserModal(false)}
        title="Select User to Filter Logs"
      />

      {/* Log Details Modal */}
      <LogDetailsModal
        isOpen={showLogDetailsModal}
        log={selectedLog}
        onClose={() => {
          setShowLogDetailsModal(false);
          setSelectedLog(null);
        }}
        userName={selectedLog ? userNames[selectedLog.userUuid] : undefined}
      />
    </ProtectedRoute>
  );
} 