import { <PERSON>tity, PrimaryColumn, Column, CreateDateColumn, UpdateDateColumn, Index } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Uuid7 } from '../utils/uuid7';

export enum CustomerType {
  RETAIL = "retail",
  WHOLESALE = "wholesale",
  MID_WHOLESALE = "mid-wholesale",
  INSTITUTIONAL = "institutional",
}

@Entity('customers')
export class Customer {
  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the customer (primary key)",
  })
  @PrimaryColumn('uuid')
  id: string;

  @ApiProperty({ example: "Customer Name" })
  @Column()
  name: string;

  @ApiProperty({
    example: "12345678901",
    description: "Fiscal identification number (tax ID)",
    required: false,
  })
  @Column({ nullable: true })
  fiscalId?: string;

  @ApiProperty({ example: "<EMAIL>", required: false })
  @Column({ nullable: true })
  email?: string;

  @ApiProperty({ example: "+1234567890", required: false })
  @Column({ nullable: true })
  phone?: string;

  @ApiProperty({ example: "123 Main St, City, Country", required: false })
  @Column({ nullable: true })
  address?: string;

  @ApiProperty({
    example: "RC123456",
    description: "Commercial Register number",
    required: false,
  })
  @Column({ nullable: true })
  rc?: string;

  @ApiProperty({
    example: "ART001",
    description: "Article number",
    required: false,
  })
  @Column({ nullable: true })
  articleNumber?: string;

  @ApiProperty({
    example: "retail",
    description: "Customer type",
    enum: Object.values(CustomerType),
  })
  @Column({
    type: 'enum',
    enum: CustomerType,
  })
  customerType: CustomerType;

  @ApiProperty({
    example: 40.7128,
    description: "Latitude coordinate for customer location",
    required: false,
  })
  @Column('decimal', { precision: 10, scale: 8, nullable: true })
  latitude?: number;

  @ApiProperty({
    example: -74.006,
    description: "Longitude coordinate for customer location",
    required: false,
  })
  @Column('decimal', { precision: 11, scale: 8, nullable: true })
  longitude?: number;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the warehouse",
  })
  @Column('uuid', { nullable: true })
  @Index()
  warehouseUuid?: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the region",
  })
  @Column('uuid', { nullable: true })
  @Index()
  regionUuid?: string;

  @ApiProperty({
    example: 0.0,
    description: "Current credit balance for the customer",
  })
  @Column('decimal', { precision: 10, scale: 2, default: 0.0 })
  @Index()
  currentCredit: number;

  @ApiProperty({ example: false, description: "Soft delete flag" })
  @Column({ default: false })
  isDeleted: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Helper method to generate UUID
  static generateId(): string {
    return new Uuid7().toString();
  }

  // Helper method to generate UUID
  static fromBinary(binary: any): string {
    if (!binary) return null;
    try {
      return new Uuid7(binary).toString();
    } catch {
      return null;
    }
  }
} 