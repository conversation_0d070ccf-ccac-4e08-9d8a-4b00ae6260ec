import React from "react";

interface TableActionButtonsProps {
  onEdit?: () => void;
  onDelete?: () => void;
  onView?: () => void;
  editLabel?: string;
  deleteLabel?: string;
  viewLabel?: string;
  editDisabled?: boolean;
  deleteDisabled?: boolean;
  viewDisabled?: boolean;
}

const TableActionButtons: React.FC<TableActionButtonsProps> = ({
  onEdit,
  onDelete,
  onView,
  editLabel = "Edit",
  deleteLabel = "Delete",
  viewLabel = "View QR Code",
  editDisabled = false,
  deleteDisabled = false,
  viewDisabled = false,
}) => (
  <div className="flex items-center justify-center gap-3">
    {onView && (
      <button
        className="p-2 rounded-full hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-400 action-button"
        title={viewLabel}
        aria-label={viewLabel}
        onClick={onView}
        disabled={viewDisabled}
      >
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-black" fill="currentColor" viewBox="0 0 24 24">
          <path d="M3 3h6v6H3V3zm1 1v4h4V4H4zm1 1h2v2H5V5zm9-4h6v6h-6V1zm1 1v4h4V2h-4zm1 1h2v2h-2V3zM3 13h6v6H3v-6zm1 1v4h4v-4H4zm1 1h2v2H5v-2zm8-2h2v2h-2v-2zm4 0h2v2h-2v-2zm-2 2h2v2h-2v-2zm-2 2h2v2h-2v-2zm2 2h2v2h-2v-2zm2-2h2v2h-2v-2z"/>
        </svg>
      </button>
    )}
    {onEdit && (
      <button
        className="p-2 rounded-full hover:bg-green-100 focus:outline-none focus:ring-2 focus:ring-green-400 disabled:opacity-50 action-button"
        title={editLabel}
        aria-label={editLabel}
        onClick={onEdit}
        disabled={editDisabled}
      >
        <svg className="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
        </svg>
      </button>
    )}
    {onDelete && (
      <button
        className="p-2 rounded-full hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-red-400 disabled:opacity-50 action-button"
        title={deleteLabel}
        aria-label={deleteLabel}
        onClick={onDelete}
        disabled={deleteDisabled}
      >
        <svg className="w-4 h-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
        </svg>
      </button>
    )}
  </div>
);

export default TableActionButtons;
