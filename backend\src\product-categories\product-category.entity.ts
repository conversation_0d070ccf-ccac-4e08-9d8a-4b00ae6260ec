import { <PERSON>tity, PrimaryColumn, Column, CreateDateColumn, UpdateDateColumn, Index } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';

@Entity('product_categories')
export class ProductCategory {
  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the product category (primary key)",
  })
  @PrimaryColumn('uuid')
  id: string;

  @ApiProperty({
    example: "Electronics",
    description: "Name of the product category",
  })
  @Column()
  name: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the warehouse this category belongs to",
  })
  @Column('uuid')
  @Index()
  warehouseUuid: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the user who created the category",
  })
  @Column('uuid')
  @Index()
  createdBy: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the user who last updated the category",
  })
  @Column('uuid')
  @Index()
  updatedBy: string;

  @ApiProperty({ example: false, description: "Soft delete flag" })
  @Column({ default: false })
  isDeleted: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
} 