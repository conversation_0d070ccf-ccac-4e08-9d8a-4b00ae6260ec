import { Injectable, UnauthorizedException } from "@nestjs/common";
import { UsersService } from "../users/users.service";
import * as bcrypt from "bcrypt";

@Injectable()
export class UnifiedStrategy {
  constructor(private usersService: UsersService) {}

  async validate(identifier: string, password?: string): Promise<any> {
    // Try to find user by different methods
    let user = null;

    // First, try to find by UUID (if identifier looks like a UUID)
    if (this.isValidUUID(identifier)) {
      user = await this.usersService.findByUuid(identifier);
    }

    // If not found by UUID, try email (if identifier looks like an email)
    if (!user && this.isValidEmail(identifier)) {
      user = await this.usersService.findByEmail(identifier);
    }

    // If not found by UUID or email, try name
    if (!user) {
      user = await this.usersService.findByName(identifier);
    }

    if (!user) {
      throw new UnauthorizedException(
        "User not found with this identifier (UUID, email, or name)",
      );
    }

    // Treat empty string as no password
    const hasNoPassword = !user.password;
    const providedNoPassword = !password || password === "";

    // If user has no password set, allow login without password validation
    if (hasNoPassword) {
      if (providedNoPassword) {
        return user;
      } else {
        throw new UnauthorizedException(
          "This account is set up for passwordless login. Please leave the password field empty.",
        );
      }
    }

    // If user has a password set, validate it
    if (providedNoPassword) {
      throw new UnauthorizedException(
        "This account requires a password. Please provide your password to login.",
      );
    }

    // Check if user is sending a bcrypt hash as password
    if (password.startsWith("$2b$") && password.length === 60) {
      throw new UnauthorizedException(
        "Invalid password format. Please enter your plaintext password, not a hash.",
      );
    }

    const valid = await bcrypt.compare(password, user.password);

    if (!valid) {
      throw new UnauthorizedException(
        "Invalid password. Please check your password and try again.",
      );
    }

    return user;
  }

  private isValidUUID(str: string): boolean {
    const uuidRegex =
      /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    return uuidRegex.test(str);
  }

  private isValidEmail(str: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(str);
  }
}
