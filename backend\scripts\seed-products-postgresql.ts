/**
 * Product Seeding Script for PostgreSQL/YugabyteDB
 * 
 * Usage: npx ts-node scripts/seed-products-postgresql.ts
 * 
 * ⚠️ CRITICAL: This script performs a full database backup before execution
 */

import axios from 'axios';
const { exec } = require('child_process');
const { promisify } = require('util');
const path = require('path');

// Node.js global declarations
declare const process: any;
declare const require: any;
declare const module: any;
declare const __dirname: string;

// Configuration
const API_BASE_URL = 'http://localhost:8000';
const PRODUCTS_PER_WAREHOUSE = 1000;
const BATCH_SIZE = 50; // Insert products in batches to avoid overwhelming the server

// Sample data for generating realistic product categories
const productCategoryNames = [
  'Electronics', 'Clothing', 'Home & Garden', 'Sports & Outdoors', 'Books',
  'Health & Beauty', 'Toys & Games', 'Automotive', 'Food & Beverages',
  'Office Supplies', 'Pet Supplies', 'Baby & Kids', 'Jewelry & Accessories',
  'Tools & Hardware', 'Music & Movies', 'Arts & Crafts', 'Industrial',
  'Medical Supplies', 'Furniture', 'Kitchen & Dining'
];

const productNames = [
  'Premium Widget', 'Deluxe Gadget', 'Standard Tool', 'Professional Kit',
  'Essential Item', 'Advanced Device', 'Basic Component', 'Ultra Product',
  'Smart Solution', 'Efficient System', 'Reliable Equipment', 'Quality Part',
  'Innovative Design', 'Compact Unit', 'Powerful Machine', 'Versatile Accessory',
  'Durable Material', 'Lightweight Option', 'Heavy-Duty Version', 'Eco-Friendly Choice'
];

const productDescriptors = [
  'High-quality', 'Durable', 'Lightweight', 'Compact', 'Efficient', 'Reliable',
  'Innovative', 'Professional', 'Premium', 'Standard', 'Advanced', 'Basic',
  'Ultra', 'Smart', 'Powerful', 'Versatile', 'Eco-friendly', 'Heavy-duty',
  'Portable', 'Wireless', 'Digital', 'Analog', 'Automatic', 'Manual'
];

interface Warehouse {
  uuid: string;
  name: string;
  description?: string;
  userUuidString: string;
  mainStorageUuidString?: string;
  isDeleted: boolean;
  createdAt: Date;
  updatedAt: Date;
}

interface CreateProductCategoryDto {
  name: string;
  warehouseUuid: string;
  userUuid: string;
}

interface ProductCategory {
  uuid: string;
  name: string;
  warehouseUuidString?: string;
  createdByString?: string;
  updatedByString?: string;
  createdAt: Date;
  updatedAt: Date;
}

interface CreateProductDto {
  warehouseUuid: string;
  name: string;
  description?: string;
  sku?: string;
  barcode?: string;
  productCategoryUuid?: string;
  retailPrice?: number;
  wholesalePrice?: number;
  midWholesalePrice?: number;
  institutionalPrice?: number;
  cost?: number;
}

// Generate random product data with product category assignment
function generateRandomProduct(warehouseUuid: string, productCategories: ProductCategory[]): CreateProductDto {
  // Select a random product category from the created categories
  const selectedCategory = productCategories[Math.floor(Math.random() * productCategories.length)];
  const categoryName = selectedCategory.name;
  
  const baseName = productNames[Math.floor(Math.random() * productNames.length)];
  const descriptor = productDescriptors[Math.floor(Math.random() * productDescriptors.length)];
  
  const name = `${descriptor} ${baseName}`;
  const description = `${descriptor} ${baseName.toLowerCase()} designed for ${categoryName.toLowerCase()} applications. Features premium quality construction and reliable performance.`;
  
  // Generate SKU (format: CAT-XXXXX)
  const sku = `${categoryName.substring(0, 3).toUpperCase()}-${Math.floor(Math.random() * 90000) + 10000}`;
  
  // Generate barcode (13 digits)
  const barcode = Array.from({ length: 13 }, () => Math.floor(Math.random() * 10)).join('');
  
  // Generate realistic cost and prices
  const cost = Math.floor(Math.random() * 50000) / 100; // $0.00 to $500.00
  const retailMarkup = 1.4 + Math.random() * 1.6; // 40% to 200% markup for retail
  const wholesaleMarkup = 1.2 + Math.random() * 0.8; // 20% to 100% markup for wholesale
  const midWholesaleMarkup = 1.15 + Math.random() * 0.6; // 15% to 75% markup for mid-wholesale
  const institutionalMarkup = 1.1 + Math.random() * 0.4; // 10% to 50% markup for institutional
  
  const retailPrice = Math.floor(cost * retailMarkup * 100) / 100;
  const wholesalePrice = Math.floor(cost * wholesaleMarkup * 100) / 100;
  const midWholesalePrice = Math.floor(cost * midWholesaleMarkup * 100) / 100;
  const institutionalPrice = Math.floor(cost * institutionalMarkup * 100) / 100;
  
  return {
    warehouseUuid,
    name,
    description,
    sku,
    barcode,
    productCategoryUuid: selectedCategory.uuid,  // Assign the actual category UUID
    retailPrice,
    wholesalePrice,
    midWholesalePrice,
    institutionalPrice,
    cost
  };
}

// Fetch all warehouses with timeout
async function fetchWarehouses(): Promise<Warehouse[]> {
  try {
    console.log('Fetching warehouses...');
    const response = await axios.get(`${API_BASE_URL}/warehouses/list`, {
      timeout: 10000, // 10 second timeout
      headers: {
        'Connection': 'close'
      }
    });
    // Handle both paginated and non-paginated responses
    const warehouses = response.data.data || response.data;
    const activeWarehouses = warehouses.filter((warehouse: Warehouse) => !warehouse.isDeleted);
    console.log(`Found ${activeWarehouses.length} active warehouses`);
    return activeWarehouses;
  } catch (error) {
    console.error('Error fetching warehouses:', error.message || error);
    throw error;
  }
}

// Create a single product category with retry logic
async function createProductCategory(category: CreateProductCategoryDto, retries = 3): Promise<ProductCategory | null> {
  for (let attempt = 1; attempt <= retries; attempt++) {
    try {
      const response = await axios.post(`${API_BASE_URL}/product-categories`, category, {
        timeout: 10000, // 10 second timeout
        headers: {
          'Connection': 'close' // Force connection close to prevent pooling issues
        }
      });
      return response.data;
    } catch (error) {
      console.error(`Error creating product category ${category.name} (attempt ${attempt}/${retries}):`, error.message || error);
      
      if (attempt < retries) {
        // Wait before retry with exponential backoff
        const delay = Math.min(1000 * Math.pow(2, attempt - 1), 5000);
        console.log(`Retrying in ${delay}ms...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }
  return null;
}

// Create product categories for a warehouse
async function createProductCategories(warehouse: Warehouse): Promise<ProductCategory[]> {
  console.log(`\n📁 Creating product categories for warehouse: ${warehouse.name}`);
  
  const createdCategories: ProductCategory[] = [];
  
  for (const categoryName of productCategoryNames) {
    const categoryData: CreateProductCategoryDto = {
      name: categoryName,
      warehouseUuid: warehouse.uuid, // Use uuid field from warehouse DTO
      userUuid: warehouse.userUuidString // Use userUuidString field from warehouse DTO
    };
    
    const createdCategory = await createProductCategory(categoryData);
    if (createdCategory) {
      createdCategories.push(createdCategory);
      console.log(`  ✅ Created category: ${categoryName}`);
    } else {
      console.log(`  ❌ Failed to create category: ${categoryName}`);
    }
    
    // Small delay to avoid overwhelming the server
    await new Promise(resolve => setTimeout(resolve, 200));
  }
  
  console.log(`Completed category creation: ${createdCategories.length}/${productCategoryNames.length} categories created`);
  return createdCategories;
}

// Create a single product with retry logic
async function createProduct(product: CreateProductDto, retries = 3): Promise<boolean> {
  for (let attempt = 1; attempt <= retries; attempt++) {
    try {
      await axios.post(`${API_BASE_URL}/products`, product, {
        timeout: 10000, // 10 second timeout
        headers: {
          'Connection': 'close' // Force connection close to prevent pooling issues
        }
      });
      return true;
    } catch (error) {
      console.error(`Error creating product ${product.name} (attempt ${attempt}/${retries}):`, error.message || error);
      
      if (attempt < retries) {
        // Wait before retry with exponential backoff
        const delay = Math.min(1000 * Math.pow(2, attempt - 1), 5000);
        console.log(`Retrying in ${delay}ms...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }
  return false;
}

// Create products in batches
async function createProductsBatch(products: CreateProductDto[]): Promise<number> {
  const promises = products.map(product => createProduct(product));
  const results = await Promise.all(promises);
  return results.filter(result => result).length;
}

// Seed products for a specific warehouse
async function seedProductsForWarehouse(warehouse: Warehouse): Promise<void> {
  console.log(`\nSeeding ${PRODUCTS_PER_WAREHOUSE} products for warehouse: ${warehouse.name} (${warehouse.uuid})`);
  
  // First, create product categories for this warehouse
  const productCategories = await createProductCategories(warehouse);
  
  if (productCategories.length === 0) {
    console.error(`❌ No product categories created for warehouse ${warehouse.name}. Skipping product seeding.`);
    return;
  }
  
  let totalCreated = 0;
  const totalBatches = Math.ceil(PRODUCTS_PER_WAREHOUSE / BATCH_SIZE);
  
  console.log(`\n📦 Creating ${PRODUCTS_PER_WAREHOUSE} products for warehouse: ${warehouse.name}`);
  
  for (let batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
    const batchStart = batchIndex * BATCH_SIZE;
    const batchEnd = Math.min(batchStart + BATCH_SIZE, PRODUCTS_PER_WAREHOUSE);
    const batchSize = batchEnd - batchStart;
    
    // Generate products for this batch
    const products: CreateProductDto[] = [];
    for (let i = 0; i < batchSize; i++) {
      products.push(generateRandomProduct(warehouse.uuid, productCategories));
    }
    
    // Create products in batch
    const created = await createProductsBatch(products);
    totalCreated += created;
    
    const progress = ((batchIndex + 1) / totalBatches * 100).toFixed(1);
    console.log(`  Batch ${batchIndex + 1}/${totalBatches} (${progress}%): ${created}/${batchSize} products created successfully`);
    
    // Longer delay to avoid overwhelming the server
    await new Promise(resolve => setTimeout(resolve, 500));
  }
  
  console.log(`Completed seeding for ${warehouse.name}: ${totalCreated}/${PRODUCTS_PER_WAREHOUSE} products created`);
}

// ⚠️ CRITICAL: Full Database Backup Function using Backend API
async function performFullDatabaseBackup(): Promise<void> {
  console.log('🔄 Starting FULL DATABASE BACKUP (mandatory)...');
  
  try {
    // Get backup API configuration
    const BACKUP_API_BASE_URL = process.env.BACKUP_API_BASE_URL || 'http://localhost:5000';
    
    // Validate backup API connectivity
    try {
      const healthResponse = await axios.get(`${BACKUP_API_BASE_URL}/health/`, { timeout: 10000 });
      if (healthResponse.status !== 200 || healthResponse.data.status !== 'healthy') {
        throw new Error('Backup API is not healthy');
      }
      console.log('✅ Backup API is healthy and accessible');
    } catch (healthError) {
      console.log('❌ Backup API is not accessible, attempting fallback backup...');
      throw new Error('Backup API not available');
    }
    
    // Create backup using the API
    console.log('🔄 Creating backup via API...');
    const backupResponse = await axios.post(`${BACKUP_API_BASE_URL}/backup/`, {
      prefix: 'seed-products'
    }, {
      headers: { 'Content-Type': 'application/json' },
      timeout: 30000
    });
    
    if (backupResponse.data.status === 'success') {
      const backupData = backupResponse.data.data;
      const backupName = backupData.backup_name;
      const timestamp = backupData.timestamp;
      const sizeBytes = backupData.size_bytes || 0;
      const sizeMb = sizeBytes / (1024 * 1024);
      
      console.log('✅ FULL DATABASE BACKUP completed successfully');
      console.log(`   [NAME] ${backupName}`);
      console.log(`   [TIME] ${timestamp}`);
      console.log(`   [SIZE] ${sizeMb.toFixed(2)} MB`);
      console.log(`   [RESTORE] To restore: python scripts/migration_manager_ysqlsh.py restore-from ${backupName}`);
      
    } else {
      throw new Error(`Backup API returned error: ${backupResponse.data}`);
    }
    
  } catch (error) {
    console.error('❌ CRITICAL ERROR: Full database backup failed');
    console.error('🔴 Script execution aborted for safety');
    console.error('📋 Error details:', error instanceof Error ? error.message : String(error));
    console.error('\n💡 Troubleshooting:');
    console.error('   1. Ensure the backup backend is running');
    console.error('   2. Check BACKUP_API_BASE_URL environment variable');
    console.error('   3. Verify network connectivity to backup API');
    process.exit(1);
  }
}

// Main function
async function main() {
  console.log('🌱 Starting product seeding process for PostgreSQL/YugabyteDB...');
  console.log(`Configuration: ${PRODUCTS_PER_WAREHOUSE} products per warehouse, batch size: ${BATCH_SIZE}`);
  
  try {
    // ⚠️ CRITICAL: Perform full database backup first
    await performFullDatabaseBackup();
    
    // Fetch all warehouses
    const warehouses = await fetchWarehouses();
    
    if (warehouses.length === 0) {
      console.log('No active warehouses found. Please create at least one warehouse first.');
      return;
    }
    
    console.log(`\nWarehouses to seed:`);
    warehouses.forEach((warehouse, index) => {
      console.log(`  ${index + 1}. ${warehouse.name} (${warehouse.uuid})`);
    });
    
    // Seed products for each warehouse
    let totalProductsCreated = 0;
    for (const warehouse of warehouses) {
      await seedProductsForWarehouse(warehouse);
      totalProductsCreated += PRODUCTS_PER_WAREHOUSE;
    }
    
    console.log(`\n✅ Product seeding completed successfully!`);
    console.log(`Total product categories: ${productCategoryNames.length} per warehouse`);
    console.log(`Total products created: ${totalProductsCreated} across ${warehouses.length} warehouses`);
    console.log(`Each product is assigned to a random product category`);
    console.log(`Database: PostgreSQL/YugabyteDB`);
    
  } catch (error) {
    console.error('❌ Error during product seeding:', error);
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  main();
} 