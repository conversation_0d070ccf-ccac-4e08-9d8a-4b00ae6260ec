import React from 'react';
import Link from 'next/link';

interface QuickActionButtonProps {
  title: string;
  icon: string;
  color: 'blue' | 'green' | 'orange' | 'purple' | 'teal' | 'indigo' | 'gray' | 'red';
  link?: string;
  onClick?: () => void;
}

const colorClasses = {
  blue: 'bg-blue-500 hover:bg-blue-600 text-white',
  green: 'bg-green-500 hover:bg-green-600 text-white',
  orange: 'bg-orange-500 hover:bg-orange-600 text-white',
  purple: 'bg-purple-500 hover:bg-purple-600 text-white',
  teal: 'bg-teal-500 hover:bg-teal-600 text-white',
  indigo: 'bg-indigo-500 hover:bg-indigo-600 text-white',
  gray: 'bg-gray-500 hover:bg-gray-600 text-white',
  red: 'bg-red-500 hover:bg-red-600 text-white'
};

export const QuickActionButton: React.FC<QuickActionButtonProps> = ({
  title,
  icon,
  color,
  link,
  onClick
}) => {
  const buttonContent = (
    <div className={`flex items-center gap-3 px-4 py-3 rounded-lg transition-all duration-200 ${colorClasses[color]}`}>
      <span className="text-lg">{icon}</span>
      <span className="font-medium">{title}</span>
    </div>
  );

  if (link) {
    return (
      <Link href={link} className="block">
        {buttonContent}
      </Link>
    );
  }

  if (onClick) {
    return (
      <button onClick={onClick} className="w-full text-left">
        {buttonContent}
      </button>
    );
  }

  return buttonContent;
}; 