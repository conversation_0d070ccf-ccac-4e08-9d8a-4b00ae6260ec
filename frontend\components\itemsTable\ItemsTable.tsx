import React, { useEffect, useState } from "react";
import { ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight } from 'lucide-react';
import "./ItemsTable.fadein.css"; // Import the fade-in CSS

export interface ItemsTableColumn<T> {
  /**
   * Unique key for the column, corresponding to the property in data
   */
  key: keyof T | string;
  /**
   * Header label for the column
   */
  header: React.ReactNode;
  /**
   * Optional: Custom cell renderer (value, row, rowIndex) => ReactNode
   */
  render?: (value: any, row: T, rowIndex: number) => React.ReactNode;
  /**
   * Optional: Custom class for the <td> cell
   */
  cellClassName?: string;
  /**
   * Optional: Custom class for the <th> header
   */
  headerClassName?: string;
  /**
   * Optional: Custom style for the <td> cell
   */
  cellStyle?: React.CSSProperties;
  /**
   * Optional: Custom style for the <th> header
   */
  headerStyle?: React.CSSProperties;
}

export interface ItemsTableProps<T extends object = Record<string, any>> {
  columns: ItemsTableColumn<T>[];
  data: T[];
  /**
   * Optional: Show a message if there is no data
   */
  noDataText?: React.ReactNode;
  /**
   * Optional: Table className
   */
  className?: string;
  /**
   * Optional: Table container className
   */
  containerClassName?: string;
  /**
   * Optional: Loading state - shows circular spinner when true
   */
  isLoading?: boolean;
  /**
   * Optional: Loading text to display with the spinner
   */
  loadingText?: string;
  /**
   * Optional: Fixed height for table body (prevents resizing during loading)
   */
  tableBodyHeight?: string;
  /**
   * Optional: Pagination props
   */
  pagination?: {
    currentPage: number;
    totalPages: number;
    onPageChange: (page: number) => void;
    totalItems: number;
    itemsPerPage: number;
  };
}

// Circular loading spinner component
const CircularSpinner: React.FC<{ text?: string }> = ({ text = "Loading..." }) => {
  return (
    <div className="flex flex-col items-center justify-center space-y-4">
      <div className="relative">
        {/* Outer ring */}
        <div className="w-12 h-12 border-4 border-gray-200 rounded-full"></div>
        {/* Spinning inner ring */}
        <div className="absolute top-0 left-0 w-12 h-12 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
      </div>
      <div className="text-gray-500 text-sm font-medium">{text}</div>
    </div>
  );
};

// Pagination component
const Pagination: React.FC<{
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  totalItems: number;
  itemsPerPage: number;
  isLoading?: boolean;
}> = ({
  currentPage,
  totalPages,
  onPageChange,
  totalItems,
  itemsPerPage,
  isLoading = false,
}) => {
  const getPageNumbers = () => {
    const pages = [];
    const maxVisiblePages = 5;
    
    if (totalPages <= maxVisiblePages) {
      // Show all pages if total is small
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      // Show pages around current page
      let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
      let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);
      
      // Adjust if we're near the end
      if (endPage - startPage + 1 < maxVisiblePages) {
        startPage = Math.max(1, endPage - maxVisiblePages + 1);
      }
      
      for (let i = startPage; i <= endPage; i++) {
        pages.push(i);
      }
    }
    
    return pages;
  };

  const pageNumbers = getPageNumbers();
  const startItem = (currentPage - 1) * itemsPerPage + 1;
  const endItem = Math.min(currentPage * itemsPerPage, totalItems);

  // Don't hide pagination when loading, even if there's only 1 page
  // Also handle edge cases where totalPages might be 0 or negative
  if (totalPages <= 1 && !isLoading) {
    return null;
  }

  return (
    <div className="flex items-center justify-between bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
      {/* Items info */}
      <div className="flex flex-1 justify-between sm:hidden">
        <button
          onClick={() => onPageChange(currentPage - 1)}
          disabled={currentPage === 1 || isLoading}
          className="relative inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Previous
        </button>
        <button
          onClick={() => onPageChange(currentPage + 1)}
          disabled={currentPage === totalPages || isLoading}
          className="relative ml-3 inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Next
        </button>
      </div>
      
      <div className="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
        <div>
          <p className="text-sm text-gray-700">
            Showing <span className="font-medium">{startItem}</span>-<span className="font-medium">{endItem}</span> of{' '}
            <span className="font-medium">{totalItems}</span> items
          </p>
        </div>
        
        <div>
          <nav className="isolate inline-flex -space-x-px rounded-md shadow-sm" aria-label="Pagination">
            {/* First page */}
            <button
              onClick={() => onPageChange(1)}
              disabled={currentPage === 1 || isLoading}
              className="relative inline-flex items-center rounded-l-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <span className="sr-only">First</span>
              <ChevronsLeft className="h-5 w-5" />
            </button>
            
            {/* Previous page */}
            <button
              onClick={() => onPageChange(currentPage - 1)}
              disabled={currentPage === 1 || isLoading}
              className="relative inline-flex items-center px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <span className="sr-only">Previous</span>
              <ChevronLeft className="h-5 w-5" />
            </button>
            
            {/* Page numbers */}
            {pageNumbers.map((pageNumber) => (
              <button
                key={pageNumber}
                onClick={() => onPageChange(pageNumber)}
                disabled={isLoading}
                className={`relative inline-flex items-center px-4 py-2 text-sm font-semibold ${
                  pageNumber === currentPage
                    ? 'z-10 bg-blue-600 text-white focus:z-20 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600'
                    : 'text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0'
                } ${isLoading ? 'disabled:opacity-50 disabled:cursor-not-allowed' : ''}`}
              >
                {pageNumber}
              </button>
            ))}
            
            {/* Next page */}
            <button
              onClick={() => onPageChange(currentPage + 1)}
              disabled={currentPage === totalPages || isLoading}
              className="relative inline-flex items-center px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <span className="sr-only">Next</span>
              <ChevronRight className="h-5 w-5" />
            </button>
            
            {/* Last page */}
            <button
              onClick={() => onPageChange(totalPages)}
              disabled={currentPage === totalPages || isLoading}
              className="relative inline-flex items-center rounded-r-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <span className="sr-only">Last</span>
              <ChevronsRight className="h-5 w-5" />
            </button>
          </nav>
        </div>
      </div>
    </div>
  );
};

function ItemsTable<T extends object = Record<string, any>>({
  columns,
  data,
  noDataText = "No items found.",
  className = "",
  containerClassName = "",
  isLoading = false,
  loadingText = "Loading...",
  tableBodyHeight,
  pagination,
}: ItemsTableProps<T>) {
  const [showTable, setShowTable] = useState(false);
  const [dataKey, setDataKey] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);
  const [isTransitioning, setIsTransitioning] = useState(false);
  
  useEffect(() => {
    // Start animating state and show table with entrance animation
    setIsAnimating(true);
    setShowTable(true);
    
    // Remove animating state after entrance animation completes with extra buffer
    const timer = setTimeout(() => {
      setIsTransitioning(true);
      // Gradual transition out of animation state
      setTimeout(() => {
        setIsAnimating(false);
        setIsTransitioning(false);
      }, 200);
    }, 1500); // Extended duration to prevent premature scrollbar appearance
    
    return () => clearTimeout(timer);
  }, []);

  // Reset animation when data changes
  useEffect(() => {
    if (!isLoading && data.length > 0) {
      setIsAnimating(true);
      setDataKey(prev => prev + 1);
      
      // Remove animating state after staggered animations complete with extra buffer
      const timer = setTimeout(() => {
        setIsTransitioning(true);
        // Gradual transition out of animation state
        setTimeout(() => {
          setIsAnimating(false);
          setIsTransitioning(false);
        }, 200);
      }, 1800); // Extended time for all staggered animations to complete
      
      return () => clearTimeout(timer);
    }
  }, [data.length, isLoading]);

  // Handle loading state changes
  useEffect(() => {
    if (isLoading) {
      setIsAnimating(true);
    } else {
      // Keep animating state during transition out of loading with extended buffer
      const timer = setTimeout(() => {
        setIsTransitioning(true);
        // Gradual transition out of animation state
        setTimeout(() => {
          setIsAnimating(false);
          setIsTransitioning(false);
        }, 200);
      }, 1000); // Extended buffer to prevent scrollbar flash
      
      return () => clearTimeout(timer);
    }
  }, [isLoading]);

  // Determine container classes based on loading state
  const containerClasses = `bg-white rounded-xl shadow-lg border border-gray-200 w-full table-container ${containerClassName} ${
    isLoading ? 'loading' : 'fade-in'
  } ${showTable ? 'table-entrance' : ''}`;

  return (
    <div 
      className={`overflow-x-auto py-2 max-w-7xl mx-auto w-full ${isAnimating || isTransitioning ? 'animating' : ''}`} 
      style={{ 
        overflowX: isAnimating || isTransitioning ? 'hidden' : undefined,
        overflowY: isAnimating || isTransitioning ? 'hidden' : undefined 
      }}
    >
      <div className={containerClasses} style={{ overflow: 'hidden' }}>
        <div 
          className={tableBodyHeight ? "overflow-y-auto" : ""}
          style={tableBodyHeight ? { maxHeight: tableBodyHeight } : undefined}
        >
          <table className={`w-full rounded-xl overflow-hidden ${className}`}>
          <thead className="table-header">
            <tr className="bg-gray-50 border-b border-gray-200 shadow-inner">
              {columns.map((col) => (
                <th
                  key={col.key as string}
                  className={`text-center align-middle px-4 py-2 break-words overflow-hidden text-ellipsis text-sm font-semibold ${col.headerClassName || ''}`}
                  style={col.headerStyle}
                >
                  {col.header}
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {isLoading ? (
              <tr>
                <td colSpan={columns.length} className="p-0">
                  <div 
                    className="flex items-center justify-center"
                    style={tableBodyHeight ? { 
                      height: tableBodyHeight,
                      minHeight: tableBodyHeight
                    } : { height: '400px' }}
                  >
                    <CircularSpinner text={loadingText} />
                  </div>
                </td>
              </tr>
            ) : data.length > 0 ? (
              data.map((row, rowIndex) => (
                <tr key={`${dataKey}-${rowIndex}`} className="border-b border-gray-100 hover:bg-gray-50 table-row">
                  {columns.map((col, colIndex) => (
                    <td
                      key={col.key as string}
                      className={`text-center align-middle px-4 py-2 break-words overflow-hidden text-ellipsis text-sm ${col.cellClassName || ''}`}
                      style={col.cellStyle}
                    >
                      {col.render
                        ? col.render(row[col.key as keyof T], row, rowIndex)
                        : String(row[col.key as keyof T] ?? "")}
                    </td>
                  ))}
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={columns.length} className="p-0">
                  <div 
                    className="flex items-center justify-center text-gray-500"
                    style={tableBodyHeight ? { 
                      height: tableBodyHeight,
                      minHeight: tableBodyHeight
                    } : { height: '400px' }}
                  >
                    {noDataText}
                  </div>
                </td>
              </tr>
            )}
          </tbody>
        </table>
        </div>
        
        {/* Pagination - Always show when pagination prop exists, even during loading */}
        {pagination && (
          <Pagination
            currentPage={pagination.currentPage}
            totalPages={pagination.totalPages}
            onPageChange={pagination.onPageChange}
            totalItems={pagination.totalItems}
            itemsPerPage={pagination.itemsPerPage}
            isLoading={isLoading}
          />
        )}
      </div>
    </div>
  );
}

export default ItemsTable;
