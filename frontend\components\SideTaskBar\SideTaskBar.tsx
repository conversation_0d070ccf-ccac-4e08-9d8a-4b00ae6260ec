'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import styles from './SideTaskBar.module.css';
import { useAuth } from '@/contexts/AuthContext';
import { 
  FiHome, 
  FiSettings, 
  FiUsers, 
  FiPackage, 
  FiFileText, 
  FiChevronRight, 
  FiShoppingCart, 
  FiTruck, 
  FiPieChart, 
  FiDollarSign,
  FiAlertCircle,
  FiLogOut,
  FiUser,
  FiMap,
  FiGrid
} from 'react-icons/fi';

// Define types for navigation items, allowing for recursive sub-items
type SideSubItem = {
  id: string;
  label: string;
  icon: React.ReactNode;
  path?: string;
  onClick?: () => void;
  subItems?: SideSubItem[]; // Sub-items can also have sub-items (third level)
};

type SideTaskItem = {
  id: string;
  label: string;
  icon: React.ReactNode;
  path?: string; // Path for primary items without sub-items, or for primary item itself
  subItems?: SideSubItem[]; // Primary items can have sub-items
};

type SideTaskBarProps = {
  items?: SideTaskItem[];
  className?: string;
};

// Helper function to find active primary and sub-items based on current path
const findActivePaths = (pathname: string, items: SideTaskItem[]) => {
  let activePrimary: string | null = null;
  let activeSub: string | null = null;

  // Function to recursively search for a path
  const searchItems = (currentItems: SideTaskItem[] | SideSubItem[], parentPrimaryId: string | null): boolean => {
    for (const item of currentItems) {
      if (item.path && pathname.startsWith(item.path)) { // Use startsWith for partial matches, e.g., /sales/pos -> /sales
        activePrimary = parentPrimaryId || item.id; // If it's a top-level item, its own ID, otherwise its parent
        activeSub = item.id; // Mark the specific item as active in the second bar
        return true;
      }
      if (item.subItems && searchItems(item.subItems, parentPrimaryId || item.id)) {
        activePrimary = parentPrimaryId || item.id; // Ensure primary is set
        return true;
      }
    }
    return false;
  };

  searchItems(items, null); // Start searching from top-level items

  // If a sub-item is active, the primary item should be its top-level parent
  if (activeSub) {
    for (const item of items) {
      if (item.id === activeSub) { // If the sub is actually a primary
        activePrimary = item.id;
        break;
      }
      if (item.subItems) {
        const foundParent = item.subItems.find(sub => sub.id === activeSub || (sub.subItems && sub.subItems.some(s => s.id === activeSub)));
        if (foundParent) {
          activePrimary = item.id;
          break;
        }
      }
    }
  }

  // Refine for initial load: if a sub-sub-item is active, ensure its direct sub-item parent is also 'activeSub'
  // Example: /inventory/stock/transfers active, 'Stock Levels' should be activeSub
  if (activePrimary) {
    const currentPrimaryItem = items.find(item => item.id === activePrimary);
    if (currentPrimaryItem?.subItems) {
      for (const sub of currentPrimaryItem.subItems) {
        if (sub.path === pathname || (sub.subItems && sub.subItems.some(ss => ss.path === pathname))) {
          activeSub = sub.id;
          break;
        }
      }
    }
  }

  return { activePrimary, activeSub };
};

const defaultItems: SideTaskItem[] = [
  {
    id: 'dashboard',
    label: 'Dashboard',
    icon: <FiHome />,
    path: '/dashboard',
  },

  {
    id: 'sales',
    label: 'Sales',
    icon: <FiDollarSign />,
    path: '/sales/sales',
    subItems: [
      { id: 'sales-pos', label: 'Sales', icon: <FiShoppingCart />, path: '/sales/sales' },
      { id: 'customers', label: 'Customers', icon: <FiUsers />, path: '/sales/customers' },
      { id: 'customer-payments', label: 'Customer Payments', icon: <FiDollarSign />, path: '/sales/customers/payments' },
      { id: 'customer-locations', label: 'Customer Locations', icon: <FiUsers />, path: '/sales/customers/locations' },
      { id: 'cash-register', label: 'Cash Register', icon: <FiDollarSign />, path: '/sales/cash-register' },
      { id: 'returns', label: 'Returns', icon: <FiShoppingCart />, path: '/sales/returns' },
    ],
  },
  {
    id: 'purchasing',
    label: 'Purchasing',
    icon: <FiShoppingCart />,
    subItems: [
      { id: 'purchase', label: 'Purchase', icon: <FiShoppingCart />, path: '/purchase' },
      { id: 'suppliers', label: 'Suppliers', icon: <FiUsers />, path: '/purchasing/suppliers' },
      { id: 'purchase-orders', label: 'Purchase Orders', icon: <FiFileText />, path: '/purchasing/orders' },
      { id: 'goods-receipt', label: 'Goods Receipt', icon: <FiPackage />, path: '/purchasing/goods-receipt' },
      { id: 'returns', label: 'Returns', icon: <FiShoppingCart />, path: '/purchasing/returns' }
    ],
  },
  {
    id: 'inventory',
    label: 'Inventory',
    icon: <FiPackage />,
    subItems: [
      { id: 'products', label: 'Products', icon: <FiPackage />, path: '/inventory/products' },
      { id: 'categories', label: 'Categories', icon: <FiPackage />, path: '/inventory/products/categories' },
      { id: 'stock-levels', label: 'Stock Levels', icon: <FiPackage />, path: '/inventory/stock-levels' },
      { id: 'stock-transfers', label: 'Stock Transfers', icon: <FiTruck />, path: '/inventory/stock/transfers' },
      { id: 'stock-adjustments', label: 'Stock Adjustments', icon: <FiSettings />, path: '/inventory/stock/adjustments' },
      { id: 'stock-movements', label: 'Stock Movements', icon: <FiTruck />, path: '/inventory/stock/movements' },
      { id: 'low-stock-alerts', label: 'Low Stock Alerts', icon: <FiAlertCircle />, path: '/inventory/stock/alerts' },
    ],
  },
  {
    id: 'logistics',
    label: 'Logistics',
    icon: <FiTruck />,
    subItems: [
      { id: 'warehouses', label: 'Warehouses', icon: <FiGrid />, path: '/logistics/warehouses' },
      { id: 'regions', label: 'Regions', icon: <FiMap />, path: '/logistics/regions' },
      { id: 'vans', label: 'Vans', icon: <FiTruck />, path: '/logistics/vans' },
      { id: 'van-stock', label: 'Van Stock', icon: <FiPackage />, path: '/logistics/van-stock' },
      { id: 'van-loading', label: 'Van Loading', icon: <FiTruck />, path: '/logistics/van-loading' },
      { id: 'routes', label: 'Routes', icon: <FiTruck />, path: '/logistics/routes' },
    ],
  },
  {
    id: 'reports',
    label: 'Reports',
    icon: <FiPieChart />,
    subItems: [
      { id: 'sales-reports', label: 'Sales', icon: <FiDollarSign />, path: '/reports/sales' },
      { id: 'inventory-reports', label: 'Inventory', icon: <FiPackage />, path: '/reports/inventory' },
      { id: 'van-performance', label: 'Van Performance', icon: <FiTruck />, path: '/reports/van-performance' },
      { id: 'financial-reports', label: 'Financial', icon: <FiDollarSign />, path: '/reports/financial' },
    ],
  },
  {
    id: 'settings',
    label: 'Settings',
    icon: <FiSettings />,
    subItems: [
      { id: 'profile', label: 'Profile', icon: <FiUser />, path: '/settings/profile' },
      { id: 'users', label: 'Users', icon: <FiUser />, path: '/settings/users' },
      { id: 'roles', label: 'Roles', icon: <FiSettings />, path: '/settings/roles' },
      { id: 'system', label: 'System', icon: <FiSettings />, path: '/settings/system' },
      { id: 'data', label: 'Data', icon: <FiSettings />, path: '/settings/data' },
      { id: 'logs', label: 'Logs', icon: <FiFileText />, path: '/logs' },
    ],
  },
];

export const SideTaskBar = ({ items = defaultItems, className = '' }: SideTaskBarProps) => {
  const router = useRouter();
  const pathname = usePathname(); // Get current path
  const [activePrimaryItem, setActivePrimaryItem] = useState<string | null>(null);
  const [activeSubItem, setActiveSubItem] = useState<string | null>(null);
  const { user, checkUserExists } = useAuth();

  // Effect to set active items based on current URL on mount or path change
  useEffect(() => {
    const { activePrimary, activeSub } = findActivePaths(pathname, items);
    setActivePrimaryItem(activePrimary);
    setActiveSubItem(activeSub);
  }, [pathname, items]);

  // Helper function to validate user existence before navigation
  const validateUserAndNavigate = useCallback(async (navigationAction: () => void) => {
    // Check if user exists locally
    if (!user?.uuid) {
      router.push('/auth');
      return;
    }

    try {
      // Check if user exists in backend
      const userExists = await checkUserExists(user.uuid);
      if (!userExists) {
        // User not found in backend, redirect to auth
        router.push('/auth');
        return;
      }

      // User exists, proceed with navigation
      navigationAction();
    } catch (error) {
      console.error('Error checking user existence:', error);
      // On error, redirect to auth for safety
      router.push('/auth');
    }
  }, [user, checkUserExists, router]);

  const handlePrimaryItemClick = useCallback((item: SideTaskItem) => {
    validateUserAndNavigate(() => {
      if (item.subItems && item.subItems.length > 0) {
        // If item has sub-items, navigate to the main path AND show sub-items
        if (item.path) {
          router.push(item.path);
        }
        setActivePrimaryItem(activePrimaryItem === item.id ? null : item.id);
        setActiveSubItem(null); // Reset sub-item when primary is toggled
      } else if (item.path) {
        // If primary item has no sub-items but has a path, navigate directly
        router.push(item.path);
        setActivePrimaryItem(item.id);
        setActiveSubItem(null);
      }
    });
  }, [activePrimaryItem, router, validateUserAndNavigate]);

  const handleSubItemClick = useCallback((subItem: SideSubItem) => {
    validateUserAndNavigate(() => {
      if (subItem.subItems && subItem.subItems.length > 0) {
        setActiveSubItem(activeSubItem === subItem.id ? null : subItem.id);
      } else if (subItem.onClick) {
        subItem.onClick();
        setActiveSubItem(subItem.id);
      } else if (subItem.path) {
        router.push(subItem.path);
        setActiveSubItem(subItem.id);
      }
    });
  }, [activeSubItem, router, validateUserAndNavigate]);

  // Find the currently active primary item to display its sub-items
  const currentActivePrimaryItem = activePrimaryItem ? items.find(item => item.id === activePrimaryItem) : null;

  return (
    <div className={`${styles.taskBar} ${className}`}>
      {/* Primary Navigation Column (Left Bar) */}
      <div className={styles.primaryNavContainer}>
        <div className={styles.logo}>
          <div className={styles.logoIcon}>D</div>
          <span className={styles.logoText}>Dido</span> {/* Always visible */}
        </div>
        
        <nav className={styles.primaryNav}>
          {items.map((item) => (
            <div key={item.id} className={styles.navItem}>
              <button
                className={`${styles.navButton} ${activePrimaryItem === item.id ? styles.active : ''}`}
                onClick={() => handlePrimaryItemClick(item)}
                aria-expanded={activePrimaryItem === item.id}
              >
                <span className={styles.icon}>{item.icon}</span>
                <span className={styles.label}>{item.label}</span> {/* Always visible */}
                {item.subItems && item.subItems.length > 0 && (
                  <FiChevronRight className={`${styles.chevron} ${activePrimaryItem === item.id ? styles.rotated : ''}`} />
                )}
              </button>
            </div>
          ))}
        </nav>
        
        {/* Footer for Primary Nav (Always expanded) */}
        <SideTaskBarFooter />
      </div>
      
      {/* Secondary Navigation Column (Right Bar - Conditional) */}
      {currentActivePrimaryItem && currentActivePrimaryItem.subItems && currentActivePrimaryItem.subItems.length > 0 && (
        <div className={styles.secondaryNavContainer}>
          <div className={styles.secondaryHeader}>
            <h3 className={styles.activeItemName}>{currentActivePrimaryItem.label}</h3>
            <div className={styles.headerDivider} />
          </div>
          <nav className={styles.secondaryNav}>
            {currentActivePrimaryItem.subItems.map((subItem) => (
              <div key={subItem.id} className={styles.subNavItem}>
                <button
                  className={`${styles.subNavLink} ${activeSubItem === subItem.id ? styles.active : ''}`}
                  onClick={() => handleSubItemClick(subItem)}
                >
                  <span className={styles.subIcon}>{subItem.icon}</span>
                  <span>{subItem.label}</span>
                  {subItem.subItems && subItem.subItems.length > 0 && (
                    <FiChevronRight className={`${styles.chevron} ${activeSubItem === subItem.id ? styles.rotated : ''}`} />
                  )}
                </button>
                
                {/* Nested Sub-Menu (Third level of navigation) */}
                {activeSubItem === subItem.id && subItem.subItems && subItem.subItems.length > 0 && (
                  <div className={styles.nestedSubMenu}>
                    {subItem.subItems.map((subSubItem) => (
                      <button
                        key={subSubItem.id}
                        className={styles.subSubMenuItem}
                        onClick={() => handleSubItemClick(subSubItem)} // Re-use for navigation
                      >
                        <span className={styles.subSubIcon}>{subSubItem.icon}</span>
                        <span>{subSubItem.label}</span>
                      </button>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </nav>
        </div>
      )}
    </div>
  );
};

// Footer component for user info and logout
const SideTaskBarFooter = () => {
  const { user, logout } = useAuth();

  // Helper function to get display name
  const getDisplayName = () => {
    if (!user) return 'Not logged in';
    
    // Try to construct name from firstName and lastName
    if (user.firstName || user.lastName) {
      const firstName = user.firstName || '';
      const lastName = user.lastName || '';
      return `${firstName} ${lastName}`.trim();
    }
    
    // Fallback to legacy name field
    if (user.name) {
      return user.name;
    }
    
    // Fallback to email
    if (user.email) {
      return user.email;
    }
    
    return 'User';
  };

  return (
    <div className={styles.footer}>
      <div className={styles.profile}>
        <div className={styles.avatar}><FiUser /></div>
        <div className={styles.profileInfo}>
          <div className={styles.userName}>
            {getDisplayName()}
          </div>
          <button className={styles.logoutButton} onClick={logout}>
            <FiLogOut /> Logout
          </button>
        </div>
      </div>
    </div>
  );
};

export default SideTaskBar;
