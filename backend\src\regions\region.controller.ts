import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Patch,
  Delete,
  Query,
  ParseUUIDPipe,
  UseGuards,
} from "@nestjs/common";
import { RegionService } from "./region.service";
import { CreateRegionDto } from "./dto/create-region.dto";
import { UpdateRegionDto } from "./dto/update-region.dto";
import { FilterRegionDto } from "./dto/filter-region.dto";
import { RegionResponseDto } from "./dto/region-response.dto";
import { HardDeleteResponseDto } from "./dto/hard-delete-response.dto";
import {
  PaginationQueryDto,
  PaginatedResponseDto,
} from "../dto/pagination.dto";
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBody,
} from "@nestjs/swagger";
@ApiTags("regions")
@Controller("regions")
export class RegionController {
  constructor(private readonly regionService: RegionService) {}

  @Post()
  @ApiOperation({ summary: "Create a new region" })
  @ApiBody({ type: CreateRegionDto, description: "Region data to create" })
  @ApiResponse({
    status: 201,
    description: "Region created successfully",
    type: RegionResponseDto,
  })
  @ApiResponse({ status: 400, description: "Invalid input data" })
  create(@Body() createRegionDto: CreateRegionDto): Promise<RegionResponseDto> {
    return this.regionService.create(createRegionDto);
  }

  @Get()
  @ApiOperation({ summary: "Get all regions with pagination and filtering" })
  @ApiQuery({
    name: "page",
    required: false,
    type: Number,
    example: 1,
    description: "Page number (1-based)",
  })
  @ApiQuery({
    name: "limit",
    required: false,
    type: Number,
    example: 10,
    description: "Number of items per page",
  })
  @ApiQuery({
    name: "warehouseUuid",
    required: false,
    type: String,
    description: "Filter by warehouse UUID",
  })
  @ApiQuery({
    name: "name",
    required: false,
    type: String,
    description: "Filter by region name (partial match)",
  })
  @ApiQuery({
    name: "description",
    required: false,
    type: String,
    description: "Filter by region description (partial match)",
  })
  @ApiResponse({
    status: 200,
    description: "Regions retrieved successfully",
    type: PaginatedResponseDto,
  })
  findAll(
    @Query() paginationQuery: PaginationQueryDto,
    @Query() filter: FilterRegionDto,
  ): Promise<PaginatedResponseDto<RegionResponseDto>> {
    return this.regionService.findAll(paginationQuery, filter);
  }

  @Get(":uuid")
  @ApiOperation({ summary: "Get a region by UUID" })
  @ApiParam({
    name: "uuid",
    type: String,
    example: "uuid-v7-string",
    description: "Region UUID",
  })
  @ApiResponse({
    status: 200,
    description: "Region retrieved successfully",
    type: RegionResponseDto,
  })
  @ApiResponse({ status: 404, description: "Region not found" })
  findOne(
    @Param("uuid", ParseUUIDPipe) uuid: string,
  ): Promise<RegionResponseDto> {
    return this.regionService.findOne(uuid);
  }

  @Patch(":uuid")
  @ApiOperation({ summary: "Update a region by UUID" })
  @ApiParam({
    name: "uuid",
    type: String,
    example: "uuid-v7-string",
    description: "Region UUID",
  })
  @ApiBody({ type: UpdateRegionDto, description: "Region data to update" })
  @ApiResponse({
    status: 200,
    description: "Region updated successfully",
    type: RegionResponseDto,
  })
  @ApiResponse({ status: 404, description: "Region not found" })
  @ApiResponse({ status: 400, description: "Invalid input data" })
  update(
    @Param("uuid", ParseUUIDPipe) uuid: string,
    @Body() updateDto: UpdateRegionDto,
  ): Promise<RegionResponseDto> {
    return this.regionService.update(uuid, updateDto);
  }

  @Delete("all")
  @ApiOperation({
    summary:
      "Hard delete all regions (permanently removes all regions from the database)",
  })
  @ApiResponse({
    status: 200,
    description: "All regions deleted successfully",
    type: HardDeleteResponseDto,
  })
  @ApiResponse({ status: 500, description: "Internal server error" })
  hardDeleteAll(): Promise<HardDeleteResponseDto> {
    return this.regionService.hardDeleteAll();
  }

  @Delete(":uuid")
  @ApiOperation({
    summary: "Soft delete a region by UUID (sets isDeleted=true)",
  })
  @ApiParam({
    name: "uuid",
    type: String,
    example: "uuid-v7-string",
    description: "Region UUID",
  })
  @ApiResponse({ status: 200, description: "Region deleted successfully" })
  @ApiResponse({ status: 404, description: "Region not found" })
  remove(@Param("uuid", ParseUUIDPipe) uuid: string): Promise<void> {
    return this.regionService.remove(uuid);
  }
}
