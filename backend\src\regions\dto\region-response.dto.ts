import { ApiProperty } from "@nestjs/swagger";
import { Region } from "../region.entity";

export class RegionResponseDto {
  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the region",
  })
  uuid: string;

  @ApiProperty({ example: "Downtown District", description: "Region name" })
  name: string;

  @ApiProperty({
    example: "Central business district with high customer density",
    description: "Region description",
  })
  description?: string;

  @ApiProperty({
    example: 40.7128,
    description: "Latitude coordinate for region center",
  })
  latitude?: number;

  @ApiProperty({
    example: -74.006,
    description: "Longitude coordinate for region center",
  })
  longitude?: number;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the warehouse",
  })
  warehouseUuidString: string;

  @ApiProperty({ example: false, description: "Soft delete flag" })
  isDeleted: boolean;

  @ApiProperty({
    example: "2024-01-01T00:00:00.000Z",
    description: "Creation timestamp",
  })
  createdAt: Date;

  @ApiProperty({
    example: "2024-01-01T00:00:00.000Z",
    description: "Last update timestamp",
  })
  updatedAt: Date;
}

export function toRegionResponseDto(region: Region): RegionResponseDto {
  return {
    uuid: region.id,
    name: region.name,
    description: region.description,
    latitude: region.latitude,
    longitude: region.longitude,
    warehouseUuidString: region.warehouseUuid,
    isDeleted: region.isDeleted,
    createdAt: region.createdAt,
    updatedAt: region.updatedAt,
  };
}
