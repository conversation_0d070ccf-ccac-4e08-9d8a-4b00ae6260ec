// All API calls for users/roles management are defined here.
// Uses the /api/ prefix so requests are proxied to the backend (see next.config.js)
import { getAuthHeadersWithContentType } from '@/utils/authHeaders';

export type User = {
  uuid: string;
  warehouseUuidString: string;
  vanUuidString?: string;
  email?: string;
  name: string;
  roleUuid?: string; // Now stores the role UUID
  roleUuidString?: string; // From backend
  userType: 'super' | 'user';
  isDeleted: boolean;
};

export type Role = {
  uuid: string;
  warehouseUuid: string;
  name: string;
  permissions: string[];
  isDeleted: boolean;
};

export type AddUserInput = {
  name: string;
  email: string;
  password: string;
  userType: 'super' | 'user';
  warehouseUuid: string;
  roleUuid: string; // Required: roleUuid (UUIDv7) per backend contract
};

export type UpdateUserInput = {
  name?: string;
};

export type UpdateUserPasswordInput = {
  oldPassword: string;
  newPassword: string;
};

export const fetchUsers = async (warehouseUuid: string): Promise<User[]> => {
  if (!warehouseUuid) return [];
  const res = await fetch(`/api/users/by-warehouse/${warehouseUuid}`, {
    headers: getAuthHeadersWithContentType(),
  });
  if (!res.ok) throw new Error('Failed to fetch users');
  const users = await res.json();
  console.log('Raw users from backend:', users);
  // Patch: Map roleUuidString to roleUuid for frontend compatibility
  return users.map((u: any) => ({
    ...u,
    roleUuid: u.roleUuid ?? u.roleUuidString ?? undefined,
  }));
};

// Fetch a single user by UUID
export const fetchUserById = async (userUuid: string): Promise<User> => {
  const res = await fetch(`/api/users/${userUuid}`, {
    headers: getAuthHeadersWithContentType(),
  });
  if (!res.ok) {
    let message = 'Failed to fetch user details';
    try {
      const error = await res.json();
      if (error && error.message) message = error.message;
    } catch {}
    throw new Error(message);
  }
  const user = await res.json();
  console.log('Raw user from backend:', user);
  // Patch: Map roleUuidString to roleUuid for frontend compatibility
  return {
    ...user,
    roleUuid: user.roleUuid ?? user.roleUuidString ?? undefined,
  };
};

// Fetch all roles for a warehouse (excluding deleted)
// GET /users/roles/by-warehouse/:warehouseUuid
export const fetchRoles = async (warehouseUuid: string): Promise<Role[]> => {
  if (!warehouseUuid) return [];
  const res = await fetch(`/api/users/roles/by-warehouse/${warehouseUuid}`, {
    headers: getAuthHeadersWithContentType(),
  });
  if (!res.ok) throw new Error('Failed to fetch roles');
  const roles = await res.json();
  console.log('Raw roles from backend:', roles);
  return roles;
};

// For user creation, email and userType ARE required and sent
export const addUser = async (data: AddUserInput) => {
  const payload = {
    name: data.name,
    email: data.email,
    password: data.password,
    userType: data.userType,
    warehouseUuid: data.warehouseUuid,
    roleUuid: data.roleUuid, // Pass roleUuid to backend
  };
  const res = await fetch('/api/users', {
    method: 'POST',
    headers: getAuthHeadersWithContentType(),
    body: JSON.stringify(payload),
  });
  if (!res.ok) {
    let message = 'Failed to add user';
    try {
      const error = await res.json();
      if (error && error.message) message = error.message;
    } catch {}
    throw new Error(message);
  }
  return res.json();
};

// Assign a role to a user
// POST /users/:uuid/assign-role with { roleUuid, assignerUuid }
export const setUserRole = async ({ userUuid, roleUuid, assignerUuid }: { userUuid: string; roleUuid: string; assignerUuid: string }) => {
  const res = await fetch(`/api/users/${userUuid}/assign-role`, {
    method: 'POST',
    headers: getAuthHeadersWithContentType(),
    body: JSON.stringify({ roleUuid, assignerUuid }),
  });
  if (!res.ok) {
    let message = 'Failed to assign role';
    try {
      const error = await res.json();
      // Log the full error for debugging
      console.error('Role assignment error:', error);
      if (error && error.message) message = error.message;
      if (error && error.statusCode) message += ` (Status: ${error.statusCode})`;
    } catch (e) {
      console.error('Error parsing role assignment error response', e);
    }
    throw new Error(message);
  }
  return res.json();
};

// Update user details (name/email)
export const updateUser = async (uuid: string, data: UpdateUserInput) => {
  const res = await fetch(`/api/users/${uuid}`, {
    method: 'PATCH',
    headers: getAuthHeadersWithContentType(),
    body: JSON.stringify(data),
  });
  if (!res.ok) {
    let message = 'Failed to update user';
    try {
      const error = await res.json();
      console.error('Update user error:', error);
      if (error && error.message) message = error.message;
      if (error && error.statusCode) message += ` (Status: ${error.statusCode})`;
    } catch (e) {
      console.error('Error parsing update user error response', e);
    }
    throw new Error(message);
  }
  return res.json();
};

// Update user profile (name and/or password)
export type UpdateUserProfileInput = {
  name?: string;
  oldPassword?: string;
  newPassword?: string;
};

export const updateUserProfile = async (uuid: string, data: UpdateUserProfileInput) => {
  const res = await fetch(`/api/users/${uuid}/update-profile`, {
    method: 'PATCH',
    headers: getAuthHeadersWithContentType(),
    body: JSON.stringify(data),
  });
  if (!res.ok) {
    let message = 'Failed to update user profile';
    try {
      const error = await res.json();
      console.error('Update user profile error:', error);
      if (error && error.message) message = error.message;
      if (error && error.statusCode) message += ` (Status: ${error.statusCode})`;
    } catch (e) {
      console.error('Error parsing update user profile error response', e);
    }
    throw new Error(message);
  }
  return res.json();
};

// Assign van to a user (only mobile sale agents)
// PATCH /users/:uuid/assign-van with { vanUuid }
export const assignUserVan = async (userUuid: string, vanUuid: string) => {
  const res = await fetch(`/api/users/${userUuid}/assign-van`, {
    method: 'PATCH',
    headers: getAuthHeadersWithContentType(),
    body: JSON.stringify({ vanUuid }),
  });
  if (!res.ok) {
    let message = 'Failed to assign van';
    try {
      const error = await res.json();
      console.error('Assign van error:', error);
      if (error && error.message) message = error.message;
      if (error && error.statusCode) message += ` (Status: ${error.statusCode})`;
    } catch (e) {
      console.error('Error parsing assign van error response', e);
    }
    throw new Error(message);
  }
  return res.json();
};

// Change user password
export const updateUserPassword = async (uuid: string, data: UpdateUserPasswordInput) => {
  const res = await fetch(`/api/users/${uuid}/password`, {
    method: 'PATCH',
    headers: getAuthHeadersWithContentType(),
    body: JSON.stringify(data),
  });
  if (!res.ok) {
    let message = 'Failed to update password';
    try {
      const error = await res.json();
      console.error('Update password error:', error);
      if (error && error.message) message = error.message;
      if (error && error.statusCode) message += ` (Status: ${error.statusCode})`;
    } catch (e) {
      console.error('Error parsing update password error response', e);
    }
    throw new Error(message);
  }
  return res.json();
};

// Soft delete user
export const deleteUser = async (uuid: string) => {
  const res = await fetch(`/api/users/${uuid}`, {
    method: 'DELETE',
    headers: getAuthHeadersWithContentType(),
  });
  if (!res.ok) {
    let message = 'Failed to delete user';
    try {
      const error = await res.json();
      console.error('Delete user error:', error);
      if (error && error.message) message = error.message;
      if (error && error.statusCode) message += ` (Status: ${error.statusCode})`;
    } catch (e) {
      console.error('Error parsing delete user error response', e);
    }
    throw new Error(message);
  }
  return res.json();
};
