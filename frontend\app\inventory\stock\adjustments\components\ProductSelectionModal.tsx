import React, { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { filterProducts, Product } from '@/app/inventory/products/productsApi';
import { getAllProductCategoriesRaw, ProductCategory } from '@/app/inventory/products/productCategoriesApi';
import { Search, Filter, X, Package } from 'lucide-react';
import toast from 'react-hot-toast';

interface ProductSelectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSelect: (product: Product) => void;
  selectedProductUuids: string[];
  title?: string;
}

const ProductSelectionModal: React.FC<ProductSelectionModalProps> = ({
  isOpen,
  onClose,
  onSelect,
  selectedProductUuids,
  title = "Select Product"
}) => {
  const { user } = useAuth();
  const [products, setProducts] = useState<Product[]>([]);
  const [categories, setCategories] = useState<ProductCategory[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Search and filter state
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [minPrice, setMinPrice] = useState('');
  const [maxPrice, setMaxPrice] = useState('');

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  const [hasNext, setHasNext] = useState(false);
  const [hasPrev, setHasPrev] = useState(false);

  const pageSize = 20;

  // Fetch categories
  useEffect(() => {
    if (!user?.warehouseUuid || !isOpen) return;

    const fetchCategories = async () => {
      try {
        const categoriesData = await getAllProductCategoriesRaw(user.warehouseUuid);
        setCategories(categoriesData);
      } catch (error) {
        console.error('Error fetching categories:', error);
      }
    };

    fetchCategories();
  }, [user?.warehouseUuid, isOpen]);

  // Fetch products with filters and pagination
  const fetchProducts = useCallback(async () => {
    if (!user?.warehouseUuid) return;

    setLoading(true);
    setError(null);

    try {
      const filters: any = {};
      
      if (searchQuery.trim()) {
        filters.search = searchQuery.trim();
      }
      
      if (selectedCategory) {
        filters.productCategoryUuid = selectedCategory;
      }
      
      if (minPrice) {
        const min = parseFloat(minPrice);
        if (!isNaN(min)) filters.minRetailPrice = min;
      }
      
      if (maxPrice) {
        const max = parseFloat(maxPrice);
        if (!isNaN(max)) filters.maxRetailPrice = max;
      }

      console.log('[ProductSelectionModal] Fetching products with filters:', filters);

      const response = await filterProducts(
        user.warehouseUuid,
        filters,
        { page: currentPage, limit: pageSize }
      );

      setProducts(response.data);
      setTotalPages(Math.ceil(response.total / pageSize));
      setTotalItems(response.total);
      setHasNext(response.hasNext);
      setHasPrev(response.hasPrev);

    } catch (error) {
      console.error('Error fetching products:', error);
      setError('Failed to fetch products');
      toast.error('Failed to fetch products');
    } finally {
      setLoading(false);
    }
  }, [user?.warehouseUuid, searchQuery, selectedCategory, minPrice, maxPrice, currentPage]);

  // Fetch products when dependencies change
  useEffect(() => {
    if (isOpen) {
      fetchProducts();
    }
  }, [fetchProducts, isOpen]);

  // Reset pagination when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [searchQuery, selectedCategory, minPrice, maxPrice]);

  // Clear filters
  const clearFilters = () => {
    setSearchQuery('');
    setSelectedCategory('');
    setMinPrice('');
    setMaxPrice('');
    setCurrentPage(1);
  };

  // Handle product selection
  const handleProductSelect = (product: Product) => {
    onSelect(product);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-xl font-semibold flex items-center gap-2">
            <Package className="w-5 h-5" />
            {title}
          </h2>
          <button
            onClick={onClose}
            className="p-1 hover:bg-gray-100 rounded"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Search and Filters */}
        <div className="p-6 border-b bg-gray-50">
          {/* Search Bar */}
          <div className="relative mb-4">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Search by name, SKU, or barcode..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          {/* Filter Toggle */}
          <div className="flex items-center justify-between">
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center gap-2 px-4 py-2 text-sm text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg"
            >
              <Filter className="w-4 h-4" />
              {showFilters ? 'Hide Filters' : 'Show Filters'}
            </button>
            
            {(searchQuery || selectedCategory || minPrice || maxPrice) && (
              <button
                onClick={clearFilters}
                className="text-sm text-blue-600 hover:text-blue-800"
              >
                Clear All Filters
              </button>
            )}
          </div>

          {/* Advanced Filters */}
          {showFilters && (
            <div className="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Category
                </label>
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">All Categories</option>
                  {categories.map((category) => (
                    <option key={category.uuid} value={category.uuid}>
                      {category.name}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Min Price
                </label>
                <input
                  type="number"
                  step="0.01"
                  value={minPrice}
                  onChange={(e) => setMinPrice(e.target.value)}
                  placeholder="0.00"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Max Price
                </label>
                <input
                  type="number"
                  step="0.01"
                  value={maxPrice}
                  onChange={(e) => setMaxPrice(e.target.value)}
                  placeholder="999.99"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>
          )}
        </div>

        {/* Results Info */}
        <div className="px-6 py-3 bg-gray-50 border-b">
          <div className="flex items-center justify-between text-sm text-gray-600">
            <span>
              {loading ? 'Loading...' : `${totalItems} products found`}
            </span>
            <span>
              Page {currentPage} of {totalPages}
            </span>
          </div>
        </div>

        {/* Product List */}
        <div className="flex-1 overflow-auto">
          {error && (
            <div className="p-6 text-center text-red-600">
              {error}
            </div>
          )}

          {!loading && !error && products.length === 0 && (
            <div className="p-6 text-center text-gray-500">
              No products found matching your criteria.
            </div>
          )}

          {loading && (
            <div className="p-6 text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
              <p className="mt-2 text-gray-500">Loading products...</p>
            </div>
          )}

          {!loading && !error && products.length > 0 && (
            <div className="divide-y divide-gray-200">
              {products.map((product) => {
                const isSelected = selectedProductUuids.includes(product.uuid);
                const categoryName = categories.find(c => c.uuid === product.productCategoryUuid)?.name;
                
                return (
                  <button
                    key={product.uuid}
                    onClick={() => handleProductSelect(product)}
                    disabled={isSelected}
                    className={`w-full p-4 text-left hover:bg-gray-50 transition-colors ${
                      isSelected ? 'bg-gray-100 cursor-not-allowed opacity-50' : ''
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <h3 className="font-medium text-gray-900">
                          {product.name}
                          {isSelected && (
                            <span className="ml-2 text-xs text-gray-500">(Already selected)</span>
                          )}
                        </h3>
                        <div className="flex items-center gap-4 mt-1 text-sm text-gray-500">
                          {product.sku && (
                            <span>SKU: {product.sku}</span>
                          )}
                          {product.barcode && (
                            <span>Barcode: {product.barcode}</span>
                          )}
                          {categoryName && (
                            <span>Category: {categoryName}</span>
                          )}
                        </div>
                        {product.description && (
                          <p className="mt-1 text-sm text-gray-600 line-clamp-2">
                            {product.description}
                          </p>
                        )}
                      </div>
                      <div className="text-right">
                        {product.retailPrice && (
                          <div className="font-medium text-gray-900">
                            ${(() => {
                              const price = typeof product.retailPrice === 'number' 
                                ? product.retailPrice 
                                : parseFloat(product.retailPrice.toString());
                              return isNaN(price) ? '0.00' : price.toFixed(2);
                            })()}
                          </div>
                        )}
                      </div>
                    </div>
                  </button>
                );
              })}
            </div>
          )}
        </div>

        {/* Pagination */}
        {!loading && !error && totalPages > 1 && (
          <div className="flex items-center justify-between px-6 py-4 border-t bg-gray-50">
            <button
              onClick={() => setCurrentPage(currentPage - 1)}
              disabled={!hasPrev}
              className="px-4 py-2 text-sm border border-gray-300 rounded-lg hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Previous
            </button>
            
            <div className="flex items-center gap-2">
              {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                const page = i + 1;
                const isCurrentPage = page === currentPage;
                
                return (
                  <button
                    key={page}
                    onClick={() => setCurrentPage(page)}
                    className={`px-3 py-1 text-sm rounded ${
                      isCurrentPage
                        ? 'bg-blue-500 text-white'
                        : 'text-gray-600 hover:bg-gray-100'
                    }`}
                  >
                    {page}
                  </button>
                );
              })}
            </div>

            <button
              onClick={() => setCurrentPage(currentPage + 1)}
              disabled={!hasNext}
              className="px-4 py-2 text-sm border border-gray-300 rounded-lg hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Next
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default ProductSelectionModal; 