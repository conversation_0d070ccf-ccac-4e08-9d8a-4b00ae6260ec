import { ApiProperty } from "@nestjs/swagger";
import { IsString, IsOptional, IsUUID } from "class-validator";

export class UnifiedLoginDto {
  @ApiProperty({
    example: "<EMAIL>",
    description: "User identifier - can be UUID, email, or name",
    type: String,
  })
  @IsString()
  identifier: string;

  @ApiProperty({
    example: "password123",
    description:
      "User password (optional - only required if user has password set)",
    required: false,
    type: String,
  })
  @IsOptional()
  @IsString()
  password?: string;
}

export class UnifiedLoginResponseDto {
  @ApiProperty({
    example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    description: "JWT access token",
    type: String,
  })
  accessToken: string;

  @ApiProperty({
    example: null,
    description: "Refresh token (currently not used)",
    type: String,
    nullable: true,
  })
  refreshToken: string | null;

  @ApiProperty({
    description: "User information",
    type: Object,
  })
  user: {
    uuid: string;
    email: string;
    firstName: string | null;
    lastName: string | null;
    phone: string | null;
    isActive: boolean;
    roles: any[];
    warehouseUuid: string | null;
    vanUuid: string | null;
    createdAt: string;
    updatedAt: string;
  };

  @ApiProperty({
    example: 3600,
    description: "Token expiration time in seconds",
    type: Number,
  })
  expiresIn: number;
}
